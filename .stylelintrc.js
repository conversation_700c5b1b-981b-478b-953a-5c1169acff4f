module.exports = {
  extends: [
    'stylelint-config-standard-scss',
    'stylelint-config-recess-order'
  ],
  plugins: [
    'stylelint-scss'
  ],
  rules: {
    // SCSS specific
    'scss/at-rule-no-unknown': true,
    'scss/selector-no-redundant-nesting-selector': true,
    'scss/no-duplicate-dollar-variables': true,
    
    // General CSS
    'color-hex-case': 'lower',
    'color-hex-length': 'short',
    'color-no-invalid-hex': true,
    
    // Units
    'unit-case': 'lower',
    'unit-no-unknown': true,
    'length-zero-no-unit': true,
    
    // Properties
    'property-case': 'lower',
    'property-no-unknown': true,
    'property-no-vendor-prefix': true,
    
    // Values
    'value-keyword-case': 'lower',
    'value-no-vendor-prefix': true,
    
    // Selectors
    'selector-class-pattern': '^[a-z][a-z0-9]*(-[a-z0-9]+)*$',
    'selector-id-pattern': '^[a-z][a-z0-9]*(-[a-z0-9]+)*$',
    'selector-max-id': 1,
    'selector-max-universal': 1,
    'selector-no-vendor-prefix': true,
    
    // At-rules
    'at-rule-case': 'lower',
    'at-rule-no-vendor-prefix': true,
    
    // Comments
    'comment-no-empty': true,
    
    // General
    'no-duplicate-selectors': true,
    'no-empty-source': true,
    'no-invalid-double-slash-comments': true,
    
    // Disable some rules for CSS custom properties
    'custom-property-pattern': null,
    'selector-class-pattern': null,
    
    // Allow vendor prefixes for certain properties
    'property-no-vendor-prefix': [
      true,
      {
        ignoreProperties: [
          'appearance',
          'backdrop-filter',
          'user-select',
          'overflow-scrolling'
        ]
      }
    ]
  },
  ignoreFiles: [
    'dist/**/*',
    'node_modules/**/*',
    'assets/css/tailwind.min.css'
  ]
};
