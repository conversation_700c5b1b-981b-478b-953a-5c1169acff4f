import { defineConfig } from 'vite'
import { resolve } from 'path'

export default defineConfig({
  root: '.',
  base: './',
  
  // Build configuration
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'terser',
    
    // Rollup options for optimization
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html')
      },
      output: {
        manualChunks: {
          vendor: ['@fortawesome/fontawesome-free'],
          utils: ['./src/js/utils/index.js']
        }
      }
    },
    
    // Terser options for maximum compression
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug']
      }
    }
  },
  
  // CSS preprocessing
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "./src/styles/abstracts/variables.scss";`
      }
    },
    postcss: {
      plugins: [
        require('autoprefixer'),
        require('cssnano')({
          preset: 'default'
        })
      ]
    }
  },
  
  // Server configuration for development
  server: {
    port: 3000,
    open: true,
    cors: true
  },
  
  // Optimization
  optimizeDeps: {
    include: ['@fortawesome/fontawesome-free']
  },
  
  // Plugin configuration
  plugins: [
    // Add plugins as needed
  ]
})
