<!DOCTYPE html>
<html lang="fr" prefix="og: https://ogp.me/ns#">
<head>
    <!-- Meta tags et liens CSS -->
    <script>
        // Fonction pour charger les includes (simulation)
        function loadInclude(elementId, filePath) {
            fetch(filePath)
                .then(response => response.text())
                .then(data => {
                    document.getElementById(elementId).innerHTML = data;
                })
                .catch(error => console.error('Error loading include:', error));
        }
    </script>
    
    <!-- Meta tags essentiels (inline pour performance critique) -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <!-- Title et description critiques -->
    <title>GolfinThaï - Expert Golf Thaïlande | Voyages Sur Mesure</title>
    <meta name="description" content="🏌️ Expert golf Thaïlande depuis 10 ans. Voyages sur mesure Bangkok, Phuket, Chiang Mai. Parcours premium, accompagnement local.">
    
    <!-- CSS Critique inline -->
    <link rel="stylesheet" href="./assets/css/critical.css">
    
    <!-- CSS IMPORTANTS - Chargés immédiatement pour éviter FOUC -->
    <link rel="stylesheet" href="./assets/css/layout.css">
    <link rel="stylesheet" href="./assets/css/components.css">
    <link rel="stylesheet" href="./assets/css/main.css">
    <link rel="stylesheet" href="./assets/css/tailwind.min.css">
    <link rel="stylesheet" href="./assets/css/seo-animations.css">
    <link rel="stylesheet" href="./assets/css/mobile-flags-fix.css">
    
    <!-- CSS States pour JavaScript - Version Nettoyée -->
    <link rel="stylesheet" href="./assets/css/mobile-unified-clean.css">
    <link rel="stylesheet" href="./assets/css/dropdown-langue-native.css">

    <!-- CSS Fix pour z-index et clics -->
    <link rel="stylesheet" href="./assets/css/z-index-fix.css">
    
    <!-- CSS Fix Modal Scroll -->
    <link rel="stylesheet" href="./assets/css/modal-scroll-fix.css">
    
    <!-- 🔥 LOGO SENIOR DEV - CLEAN MODERN SOLUTION -->
    <link rel="stylesheet" href="./assets/css/logo-senior-dev.css">
    
    <!-- 🎯 HERO TITLE CENTERING FIX - PERFECT ALIGNMENT -->
    <link rel="stylesheet" href="./assets/css/hero-title-centering-fix.css">
    
    <!-- 🎨 HERO TITLE PLAYFAIR DISPLAY FC - ELEGANT TYPOGRAPHY -->
    <link rel="stylesheet" href="./assets/css/hero-title-playfair-fix.css">
    
    <!-- 🎠 CAROUSEL FIX - SMOOTH TRANSITIONS GUARANTEED -->
    <link rel="stylesheet" href="./assets/css/carousel-fix.css">
    
    <!-- CSS CORRECTIONS FINALES CONSOLIDÉES -->
    <link rel="stylesheet" href="./assets/css/golfinthai-fixes.css">
    
    <!-- 🖼️ SIMPLE IMAGE MODAL - NO BULLSHIT CSS -->
    <link rel="stylesheet" href="./assets/css/simple-image-modal.css">
    
    <!-- 🧹 TEXT CLEANUP - NO MORE "LIRE LA SUITE" CHAOS -->
    <link rel="stylesheet" href="./assets/css/text-cleanup.css">
    
    <!-- 🎯 Z-INDEX PRIORITY FIX - LOGO ALWAYS ON TOP -->
    <link rel="stylesheet" href="./assets/css/z-index-priority-fix.css">
    
    <!-- 🎯 MOBILE HEADER MICRO-LAYOUT SURGERY - TEMPERATURE FIX -->
    <link rel="stylesheet" href="./assets/css/mobile-header-micro-layout.css">
    
    <!-- 🔥 QUANTUM MOBILE TEMPERATURE FIX - NUCLEAR PRECISION -->
    <link rel="stylesheet" href="./assets/css/quantum-mobile-temperature-fix.css">
    
    <!-- 🎯 QUANTUM LOGO VERTICAL LAYOUT - GENIUS MODE -->
    <link rel="stylesheet" href="./assets/css/quantum-logo-vertical-layout.css">
    
    <!-- 🔧 QUANTUM MICRO-ADJUSTMENTS - PRECISION LEVEL -->
    <link rel="stylesheet" href="./assets/css/quantum-micro-adjustments.css">
    
    <!-- 🔥 QUANTUM CLEAN MOBILE LAYOUT - ULTRA PERFECT -->
    <link rel="stylesheet" href="./assets/css/quantum-clean-mobile-layout.css">
    
    <!-- 🎯 QUANTUM MOBILE FINAL PERFECTION - FRÉRO'S VISION -->
    <link rel="stylesheet" href="./assets/css/quantum-mobile-final-perfection.css">
    
    <!-- 🚨 QUANTUM EMERGENCY FIX - URGENT REPAIR -->
    <link rel="stylesheet" href="./assets/css/quantum-emergency-fix.css">
    
    <!-- 🎯 QUANTUM ULTRA-COMPACT 50PX - FRÉRO'S PERFECT VISION -->
    <link rel="stylesheet" href="./assets/css/quantum-ultra-compact-50px.css">
    
    <!-- 🎯 QUANTUM RIGHT-SIDE GROUPING - FINAL PERFECT -->
    <link rel="stylesheet" href="./assets/css/quantum-right-side-grouping.css">
    
    <!-- 🏆 GOLF COURSES PREMIUM STYLING - PROFESSIONAL DISPLAY -->
    <link rel="stylesheet" href="./assets/css/golf-courses-premium-styling.css">
    
    <!-- 🎯 QUANTUM LOGO CLEAN FIX - REMOVE UGLY "GT" -->
    <link rel="stylesheet" href="./assets/css/quantum-logo-clean-fix.css">
    
    <!-- 🔥 MODAL & DRAPEAUX PREMIUM FIX - SILICON VALLEY EDITION -->
    <link rel="stylesheet" href="./assets/css/modal-premium-fix.css">
    
    <!-- DRAPEAUX ET ANIMATIONS - SYSTÈME UNIQUE -->
    <style>
        /* RESET TOTAL DRAPEAUX */
        .flag-icon, .flag-icon::before, .flag-icon::after {
            all: unset !important;
            display: inline-block !important;
            font-size: 16px !important;
            margin-right: 8px !important;
            vertical-align: middle !important;
        }
        
        /* DRAPEAUX SIMPLES */
        .flag-icon[data-lang="fr"] { content: '🇫🇷' !important; }
        .flag-icon[data-lang="en"] { content: '🇬🇧' !important; }
        
        /* ANIMATIONS GRADIENT */
        .text-gradient {
            background: linear-gradient(90deg, #00574B, #A3D1C8, #4fcfa6, #2d8b7f, #A3D1C8, #00574B) !important;
            background-size: 300% 100% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            animation: slideGradient 3s linear infinite !important;
        }
        
        .header-logo-gradient {
            background: linear-gradient(90deg, #00574B, #A3D1C8, #4fcfa6, #A3D1C8, #00574B) !important;
            background-size: 200% 100% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            animation: slideGradient 4s linear infinite !important;
        }
        
        @keyframes slideGradient {
            0% { background-position: 0% 0%; }
            100% { background-position: 100% 0%; }
        }
    </style>
    
    <!-- 🚨 IMAGES CLICKABLE FIX - OVERLAYS BLOCKING CLICKS -->
    <link rel="stylesheet" href="./assets/css/images-clickable-fix.css">
    
    <!-- 🚨 SERVICE IMAGES CLICKABLE FIX - FORCE CLICKABLE -->
    <link rel="stylesheet" href="./assets/css/service-images-clickable-fix.css">
    
    <!-- 🚨 QUANTUM LOGO EMERGENCY RESTORE - BRING BACK IMAGE -->
    <link rel="stylesheet" href="./assets/css/quantum-logo-emergency-restore.css">
    
    <!-- 🎯 QUANTUM NAVIGATION BUTTON - PREMIUM LEVEL -->
    <link rel="stylesheet" href="./assets/css/quantum-nav-button.css">
    
    <!-- 📱 QUANTUM MOBILE UX FIXES - APPLE LEVEL -->
    <link rel="stylesheet" href="./assets/css/quantum-mobile-ux-fixes.css">
    
    <!-- ⚡ QUANTUM ADVANCED OPTIMIZATIONS - SECRET SAUCE -->
    <link rel="stylesheet" href="./assets/css/quantum-advanced-optimizations.css">
    
    <!-- 🍔 MOBILE MENU SPACING FIX -->
    <link rel="stylesheet" href="./assets/css/mobile-menu-spacing-fix.css">
    
    <!-- 🎨 GOLF DESCRIPTIONS GREEN HIGHLIGHT -->
    <link rel="stylesheet" href="./assets/css/golf-descriptions-green-fix.css">
    
    <!-- 📱 MOBILE HEADER V2 - LANGUAGE SWITCHER RÉDUIT + LOGO SPACE AUGMENTÉ -->
    <link rel="stylesheet" href="./assets/css/mobile-header-optimized-v2.css">
    
    <!-- 🌍 MOBILE LANGUAGE SWITCHER - CLEAN LETTERS ONLY -->
    <link rel="stylesheet" href="./assets/css/mobile-language-switcher-clean.css">
    
    <!-- 🔥 HOSTINGER ANTI-COMPRESSION - FORCE HIGH QUALITY -->
    <link rel="stylesheet" href="./assets/css/hostinger-anti-compression.css">
    
    <!-- 🔥 ULTRA-MODERN CAROUSEL - SILICON VALLEY EDITION V2 - ANTI-PIXELIZATION -->
    <link rel="stylesheet" href="./assets/css/ultra-modern-carousel-silicon-valley-fixed.css">

    <!-- 📱 iPhone Display Fix -->
    <link rel="stylesheet" href="./assets/css/iphone-fix.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="./favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="./assets/images/logo-golfinthai.jpg">
    
    <!-- Preload ressources critiques -->
    <link rel="preload" as="image" href="./assets/images/BlackMountainGolfClubHuHin.jpeg" fetchpriority="high">
    <link rel="preload" as="image" href="./assets/images/logo-golfinthai.jpg" fetchpriority="high">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Forum&family=Marcellus+SC&family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700&family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Configuration JavaScript -->
    <script src="./assets/js/config.js"></script>
    
    <!-- Scripts de tests et débogage désactivés pour éviter les messages gênants -->
    <!-- 
    <script src="./assets/js/quantum-mobile-temperature-tester.js"></script>
    <script src="./assets/js/quantum-logo-temperature-tester.js"></script>
    <script src="./assets/js/quantum-micro-adjustments-tester.js"></script>
    <script src="./assets/js/quantum-ultra-clean-tester.js"></script>
    <script src="./assets/js/quantum-final-perfection-tester.js"></script>
    <script src="./assets/js/quantum-emergency-tester.js"></script>
    <script src="./assets/js/quantum-ultra-compact-50px-tester.js"></script>
    <script src="./assets/js/quantum-right-side-grouping-tester.js"></script>
    <script src="./assets/js/quantum-logo-clean-tester.js"></script>
    -->
    
    <!-- 🚀 NEW SCRIPTS V3 - Ultra Premium Edition -->
    <script src="./assets/js/luxury-image-modal-iphone-ultimate-v3.js" defer></script>
    <script src="./assets/js/hamburger-menu-fix-v2.js" defer></script>
    
    <!-- 🔥 TESTIMONIALS TRANSLATION TESTER - Silicon Valley Edition -->
    <script src="./testimonials-translation-tester.js" defer></script>
    
    <!-- 🔧 SIMPLE TEST -->
    <script src="./test-simple.js" defer></script>
    
    <!-- SYSTÈME BRUTAL DRAPEAUX -->
    <script>
        function brutalFlagFix() {
            // SUPPRIME TOUT ET REMET PROPREMENT
            document.querySelectorAll('.flag-icon').forEach(flag => {
                const lang = flag.getAttribute('data-lang');
                // RESET TOTAL
                flag.removeAttribute('style');
                flag.innerHTML = '';
                flag.className = 'flag-icon';
                // FORCE STYLE
                flag.style.setProperty('display', 'inline-block', 'important');
                flag.style.setProperty('margin-right', '8px', 'important');
                flag.style.setProperty('font-size', '16px', 'important');
                flag.style.setProperty('background', 'none', 'important');
                flag.style.setProperty('border', 'none', 'important');
                flag.style.setProperty('position', 'static', 'important');
                // CONTENU
                if (lang === 'fr') flag.textContent = '🇫🇷';
                if (lang === 'en') flag.textContent = '🇬🇧';
            });
            
            // FORCE ANIMATIONS
            document.querySelectorAll('.text-gradient, .header-logo-gradient').forEach(el => {
                el.style.setProperty('animation', 'moveGrad 2s linear infinite', 'important');
            });
        }
        
        // EXECUTION BRUTALE
        document.addEventListener('DOMContentLoaded', brutalFlagFix);
        window.addEventListener('load', brutalFlagFix);
        setInterval(brutalFlagFix, 500);
        
        // FORCE AU CLIC
        document.addEventListener('click', function() {
            setTimeout(brutalFlagFix, 100);
        });
        
        // FORCE MOBILE LAYOUT BRUTAL
        function forceMobileLayout() {
            if (window.innerWidth <= 768) {
                // CONTENEUR PRINCIPAL
                const container = document.querySelector('.container.mx-auto');
                if (container) {
                    container.style.setProperty('padding-left', '6px', 'important');
                    container.style.setProperty('padding-right', '6px', 'important');
                    container.style.setProperty('max-width', '100%', 'important');
                }
                
                // MOBILE CONTROLS
                const mobileControls = document.querySelector('.lg\\:hidden ');
                if (mobileControls) {
                    mobileControls.style.setProperty('min-width', '130px', 'important');
                    mobileControls.style.setProperty('gap', '10px', 'important');
                    mobileControls.style.setProperty('flex-shrink', '0', 'important');
                }
                
                // LANGUAGE SWITCHER
                const langSwitcher = document.querySelector('.language-switcher-mobile');
                if (langSwitcher) {
                    langSwitcher.style.setProperty('min-width', '85px', 'important');
                    langSwitcher.style.setProperty('width', '85px', 'important');
                    langSwitcher.style.setProperty('flex-shrink', '0', 'important');
                }
                
                // BOUTON LANGUAGE
                const langBtn = document.querySelector('.lang-btn-mobile');
                if (langBtn) {
                    langBtn.style.setProperty('min-width', '80px', 'important');
                    langBtn.style.setProperty('width', '80px', 'important');
                    langBtn.style.setProperty('height', '44px', 'important');
                    langBtn.style.setProperty('padding', '10px 14px', 'important');
                    langBtn.style.setProperty('font-size', '16px', 'important');
                    langBtn.style.setProperty('background', 'rgba(163, 209, 200, 0.25)', 'important');
                    langBtn.style.setProperty('border', '2px solid rgba(163, 209, 200, 0.6)', 'important');
                    langBtn.style.setProperty('border-radius', '14px', 'important');
                }
                
                // DROPDOWN COMPACT
                const dropdown = document.querySelector('.lang-dropdown-mobile');
                if (dropdown) {
                    dropdown.style.setProperty('min-width', '100px', 'important');
                    dropdown.style.setProperty('width', '100px', 'important');
                    dropdown.style.setProperty('right', '0', 'important');
                    dropdown.style.setProperty('left', 'auto', 'important');
                    dropdown.style.setProperty('padding', '4px', 'important');
                }
                
                // OPTIONS DROPDOWN COMPACTES
                const options = document.querySelectorAll('.lang-option-mobile');
                options.forEach(option => {
                    option.style.setProperty('padding', '8px 12px', 'important');
                    option.style.setProperty('font-size', '14px', 'important');
                });
                
                console.log('📱 Mobile layout forcé');
            }
        }
        
        // EXÉCUTER AU CHARGEMENT ET RESIZE
        window.addEventListener('load', forceMobileLayout);
        window.addEventListener('resize', forceMobileLayout);
        setTimeout(forceMobileLayout, 1000);
        setInterval(forceMobileLayout, 2000);
        
        // FIX DE DERRIÈRE LES FAGOTS - ANIMATIONS GRADIENT
        function forceGradientAnimations() {
            const gradientElements = document.querySelectorAll('.text-gradient, .header-logo-gradient');
            
            gradientElements.forEach(el => {
                // RESET COMPLET
                el.style.removeProperty('background');
                el.style.removeProperty('background-image');
                el.style.removeProperty('background-size');
                el.style.removeProperty('-webkit-background-clip');
                el.style.removeProperty('-webkit-text-fill-color');
                el.style.removeProperty('animation');
                
                // FORCE NOUVEAU GRADIENT
                const isLogo = el.classList.contains('header-logo-gradient');
                const gradient = isLogo ? 
                    'linear-gradient(90deg, #00574B 0%, #A3D1C8 25%, #4fcfa6 50%, #A3D1C8 75%, #00574B 100%)' :
                    'linear-gradient(90deg, #00574B 0%, #A3D1C8 20%, #4fcfa6 40%, #2d8b7f 60%, #A3D1C8 80%, #00574B 100%)';
                
                el.style.setProperty('background', gradient, 'important');
                el.style.setProperty('background-size', '300% 100%', 'important');
                el.style.setProperty('-webkit-background-clip', 'text', 'important');
                el.style.setProperty('-webkit-text-fill-color', 'transparent', 'important');
                el.style.setProperty('background-clip', 'text', 'important');
                el.style.setProperty('animation', 'gradientSlide 3s linear infinite', 'important');
                
                // FORCE KEYFRAMES DANS LE DOM
                if (!document.getElementById('gradient-keyframes')) {
                    const style = document.createElement('style');
                    style.id = 'gradient-keyframes';
                    style.textContent = `
                        @keyframes gradientSlide {
                            0% { background-position: 0% 0%; }
                            100% { background-position: 300% 0%; }
                        }
                    `;
                    document.head.appendChild(style);
                }
            });
            
            console.log('🌈 Animations gradient forcées:', gradientElements.length);
        }
        
        // EXÉCUTER LES ANIMATIONS
        window.addEventListener('load', forceGradientAnimations);
        setTimeout(forceGradientAnimations, 500);
        setTimeout(forceGradientAnimations, 2000);
        setInterval(forceGradientAnimations, 5000);
    </script>

</head>

<body itemscope itemtype="https://schema.org/WebPage">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="skip-link">Aller au contenu principal</a>
    
    <!-- Header DIRECT - Plus de fetch -->
    <header role="banner">
        <nav class="nav-bar" id="main-navigation" role="navigation" aria-label="Navigation principale">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-20 lg:h-24">
                    <!-- Logo & Brand -->
                    <div class="flex items-center space-x-3" itemscope itemtype="https://schema.org/Organization">
                        <!-- 🎯 SENIOR DEV LOGO - PROPRE ET EFFICACE -->
                        <div class="logo-wrapper">
                            <img src="./assets/images/logo-golfinthai.jpg" 
                                 alt="Logo GolfinThaï - Voyages Golf Thaïlande"
                                 class="logo-image"
                                 title="GolfinThaï"
                                 itemprop="logo"
                                 width="44"
                                 height="44"
                                 loading="eager">
                        </div>
                        <div>
                            <h1 class="font-display text-xl lg:text-2xl font-bold header-logo-gradient" itemprop="name">
                                GolfinThaï
                            </h1>
                        </div>
                    </div>
                    
                    <!-- Weather Widget Desktop -->
                    <div class="hidden xl:block weather-widget-mini" role="complementary" aria-label="Météo Bangkok">
                        <div id="weather-display" class="flex items-center space-x-2">
                            <i id="weather-icon" class="fas fa-sun text-luxury-emerald weather-icon" aria-hidden="true"></i>
                            <span id="weather-temp" class="weather-temp" aria-label="Température actuelle à Bangkok">--°C</span>
                            <span class="weather-location">Bangkok</span>
                        </div>
                    </div>
                    
                    <!-- Weather Widget Mobile -->
                    <div class="block xl:hidden weather-widget-mobile" role="complementary" aria-label="Météo Bangkok">
                        <div id="weather-display-mobile" class="flex flex-col items-center justify-center">
                            <div class="flex items-center space-x-1">
                                <i id="weather-icon-mobile" class="fas fa-sun text-luxury-emerald weather-icon text-sm" aria-hidden="true"></i>
                                <span id="weather-temp-mobile" class="weather-temp-mobile" aria-label="Température actuelle à Bangkok">--°</span>
                            </div>
                            <span class="weather-location-mobile">BKK</span>
                        </div>
                    </div>
                    
                    <!-- Desktop Menu -->
                    <nav class="hidden lg:flex items-center space-x-6" role="navigation" aria-label="Menu principal">
                        <a href="#home" class="nav-link" aria-label="Aller à la section Accueil">
                            <span data-translate="nav.home">Accueil</span>
                        </a>
                        <a href="#destinations" class="nav-link" aria-label="Aller à la section Destinations">
                            <span data-translate="nav.destinations">Destinations</span>
                        </a>
                        <a href="#apropos" class="nav-link" aria-label="Aller à la section À Propos">
                            <span data-translate="nav.about">À Propos</span>
                        </a>
                        <a href="#temoignages" class="nav-link" aria-label="Aller à la section Témoignages">
                            <span data-translate="nav.testimonials">Témoignages</span>
                        </a>
                        <a href="#contact" class="btn-primary" aria-label="Nous contacter">
                            <span data-translate="nav.contact">Contactez-Nous</span>
                        </a>
                        
                        <!-- Language Switcher Desktop -->
                        <div class="language-switcher-desktop">
                            <button id="lang-toggle-desktop" class="lang-btn" aria-label="Changer de langue" aria-expanded="false">
                                <span class="flag-icon" data-lang="fr"></span>
                                <span class="lang-text" data-translate="lang.current">FR</span>
                                <i class="fas fa-chevron-down lang-arrow"></i>
                            </button>
                            <div class="lang-dropdown" id="lang-dropdown-desktop" style="display: none;">
                                <button class="lang-option" data-lang="fr" aria-label="Français">
                                    <span class="flag-icon" data-lang="fr"></span>
                                    <span>Français</span>
                                </button>
                                <button class="lang-option" data-lang="en" aria-label="English">
                                    <span class="flag-icon" data-lang="en"></span>
                                    <span>English</span>
                                </button>
                            </div>
                        </div>
                    </nav>
                    
                    <!-- Mobile Controls -->
                    <div class="lg:hidden flex items-center space-x-3">
                        <!-- Language Switcher Mobile -->
                        <div class="language-switcher-mobile">
                            <button id="lang-toggle-mobile" class="lang-btn-mobile" aria-label="Changer de langue" aria-expanded="false">
                                <span class="flag-icon" data-lang="fr"></span>
                                <span class="lang-text-mobile" data-translate="lang.current">FR</span>
                            </button>
                            <div class="lang-dropdown-mobile" id="lang-dropdown-mobile" style="display: none;">
                                <button class="lang-option-mobile" data-lang="fr" aria-label="Français">
                                    <span class="flag-icon" data-lang="fr"></span>
                                    <span>FR</span>
                                </button>
                                <button class="lang-option-mobile" data-lang="en" aria-label="English">
                                    <span class="flag-icon" data-lang="en"></span>
                                    <span>EN</span>
                                </button>
                            </div>
                        </div>
                        <!-- Mobile Menu Button -->
                        <button class="text-luxury-emerald text-2xl p-2" 
                                id="mobile-menu-btn" 
                                aria-label="Ouvrir le menu mobile"
                                aria-expanded="false"
                                aria-controls="mobile-menu">
                            <i class="fas fa-bars" aria-hidden="true"></i>
                        </button>
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- Mobile Menu Overlay -->
        <nav class="mobile-menu-overlay" 
             id="mobile-menu" 
             role="navigation" 
             aria-label="Menu mobile"
             aria-hidden="true">
            <button class="mobile-menu-close" 
                    id="close-menu"
                    aria-label="Fermer le menu mobile">
                <i class="fas fa-times" aria-hidden="true"></i>
            </button>
            
            <div class="flex flex-col items-center justify-center h-full space-y-8">
                <a href="#home" class="mobile-menu-link" onclick="setTimeout(() => document.getElementById('close-menu').click(), 300)">Accueil</a>
                <a href="#destinations" class="mobile-menu-link" onclick="setTimeout(() => document.getElementById('close-menu').click(), 300)">Destinations</a>
                <a href="#apropos" class="mobile-menu-link" onclick="setTimeout(() => document.getElementById('close-menu').click(), 300)">À Propos</a>
                <a href="#temoignages" class="mobile-menu-link" onclick="setTimeout(() => document.getElementById('close-menu').click(), 300)">Témoignages</a>
                <a href="#contact" class="mobile-menu-link" onclick="setTimeout(() => document.getElementById('close-menu').click(), 300)">Contact</a>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main id="main-content" role="main">
        <!-- Hero Section -->
        <section id="home" class="hero-section" role="banner" aria-labelledby="hero-title">
            <!-- 🔥 ULTRA-MODERN CAROUSEL - SILICON VALLEY EDITION -->
            <div class="hero-carousel" role="img" aria-label="Carrousel des parcours de golf">
                
                <!-- SLIDE 1: Black Mountain Golf Club -->
                <div class="carousel-slide active" aria-label="Black Mountain Golf Club Hua Hin">
                    <picture class="carousel-image">
                        <img src="./assets/images/BlackMountainGolfClubHuHin.jpeg" 
                             alt="Black Mountain Golf Club Hua Hin - Ultra HD"
                             loading="eager" 
                             fetchpriority="high"
                             decoding="async"
                             width="1920" 
                             height="1080">
                    </picture>
                </div>
                
                <!-- SLIDE 2: Aquella Golf Country Club -->
                <div class="carousel-slide" aria-label="Aquella Golf Country Club">
                    <picture class="carousel-image">
                        <img src="./assets/images/AquellaGolfCountryClub.jpeg" 
                             alt="Aquella Golf Country Club - Crystal Clear"
                             loading="lazy" 
                             decoding="async"
                             width="1920" 
                             height="1080">
                    </picture>
                </div>
                
                <!-- SLIDE 3: Nouveau parcours exclusif -->
                <div class="carousel-slide" aria-label="Nouveau parcours de golf exclusif">
                    <picture class="carousel-image">
                        <img src="./assets/images/nouvelleimage2.jpeg" 
                             alt="Nouveau parcours de golf exclusif - Netflix Quality"
                             loading="lazy" 
                             decoding="async"
                             width="1920" 
                             height="1080">
                    </picture>
                </div>
                
                <!-- SLIDE 4: Chiang Mai Highlands Golf -->
                <div class="carousel-slide" aria-label="Chiang Mai Highlands Golf">
                    <picture class="carousel-image">
                        <img src="./assets/images/ChiangMaiHighlandsGolfSpaResortChiangMai.jpeg" 
                             alt="Chiang Mai Highlands Golf - TikTok Sharp"
                             loading="lazy" 
                             decoding="async"
                             width="1920" 
                             height="1080">
                    </picture>
                </div>
                
                <!-- SLIDE 5: Santiburi Samui Country Club -->
                <div class="carousel-slide" aria-label="Santiburi Samui Country Club">
                    <picture class="carousel-image">
                        <img src="./assets/images/SantiburiSamuiCountryClubKohSamui.jpeg" 
                             alt="Santiburi Samui Country Club - Ultra Sharp"
                             loading="lazy" 
                             decoding="async"
                             width="1920" 
                             height="1080">
                    </picture>
                </div>
                
                <!-- SLIDE 6: Nouveau parcours exclusif 3 -->
                <div class="carousel-slide" aria-label="Nouveau parcours de golf exclusif">
                    <picture class="carousel-image">
                        <img src="./assets/images/nouvelleimage3.jpeg" 
                             alt="Nouveau parcours de golf exclusif - Apple Retina Quality"
                             loading="lazy" 
                             decoding="async"
                             width="1920" 
                             height="1080">
                    </picture>
                </div>
                
                <!-- 🎯 PAGINATION ULTRA-MODERNE -->
                <div class="carousel-pagination" role="tablist" aria-label="Pagination du carrousel">
                    <span class="pagination-bullet active" onclick="golfCarousel.goToSlide(0)" role="button" tabindex="0" aria-label="Slide 1"></span>
                    <span class="pagination-bullet" onclick="golfCarousel.goToSlide(1)" role="button" tabindex="0" aria-label="Slide 2"></span>
                    <span class="pagination-bullet" onclick="golfCarousel.goToSlide(2)" role="button" tabindex="0" aria-label="Slide 3"></span>
                    <span class="pagination-bullet" onclick="golfCarousel.goToSlide(3)" role="button" tabindex="0" aria-label="Slide 4"></span>
                    <span class="pagination-bullet" onclick="golfCarousel.goToSlide(4)" role="button" tabindex="0" aria-label="Slide 5"></span>
                    <span class="pagination-bullet" onclick="golfCarousel.goToSlide(5)" role="button" tabindex="0" aria-label="Slide 6"></span>
                </div>
            </div>
            
            <!-- Hero Content -->
            <div class="hero-content">
                <div class="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <h1 id="hero-title" class="hero-title">
                        <span data-translate="hero.title">GOLFEZ<br>AUTREMENT</span>
                    </h1>
                </div>
            </div>
        </section>
        
        <!-- Introduction Section -->
        <section class="intro-section" role="region" aria-labelledby="intro-title">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="max-w-4xl mx-auto text-center">
                    <div class="reveal-animation">
                        <p class="intro-text">
                            <span data-translate="intro.text1">En tant qu'expert de la Thaïlande, où j'ai vécu de nombreuses années, je vous accompagne personnellement 
                            à chaque étape, de la conception de votre séjour jusqu'à la fin de votre aventure. Mon rôle ? Vous guider, 
                            vous conseiller et vous permettre de découvrir ce pays extraordinaire selon vos envies.</span>
                        </p>
                        <p class="intro-text">
                            <span data-translate="intro.text2">Profitez de parcours de golf de renommée mondiale tout en explorant les lieux incontournables et en 
                            savourant la cuisine thaïlandaise, reconnue pour sa richesse et ses saveurs exotiques.</span>
                        </p>
                        <p class="intro-text">
                            <span data-translate="intro.text3">Et pour la langue ? Pas d'inquiétude, même sans être polyglottes ! Je serai à vos côtés pour rendre 
                            chaque moment simple et agréable. Vous pourrez ainsi vous concentrer sur l'essentiel : le plaisir de 
                            jouer et de découvrir la Thaïlande autrement.</span>
                        </p>
                        <div class="intro-highlight">
                            <p class="highlight-text">
                                <span data-translate="intro.highlight">Faites confiance à GolfinThaï pour un voyage authentique, 
                                adapté à vos goûts et rythmé par des moments inoubliables.</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Services Section -->
        <section id="prestations" class="services-section" role="region" aria-labelledby="services-title">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <header class="section-header text-center">
                    <h2 id="services-title" class="section-title">
                        <span data-translate="services.title">Mon</span> <span class="text-gradient">Accompagnement</span>
                    </h2>
                </header>

                <div class="services-grid" role="list">
                    <article class="service-card" role="listitem">
                        <div class="service-image-container">
                            <img src="./assets/images/acceuil1.jpg" 
                                 alt="Service d'accompagnement sur mesure pour voyages golf" 
                                 class="service-image"
                                 loading="lazy"
                                 width="400" height="256"
                                 onclick="openSimpleModal('./assets/images/acceuil1.jpg', 'Service Accompagnement Sur Mesure')"
                                 style="cursor: pointer;">
                        </div>
                        <div class="service-content">
                            <h3 class="service-title">
                                <span data-translate="service1.title">Accompagnement sur mesure</span>
                            </h3>
                            <p class="service-description">
                                <span data-translate="service1.desc">Un service personnalisé qui s'adapte à vos envies et votre budget pour créer le voyage golf parfait.</span>
                            </p>
                        </div>
                    </article>
                    
                    <article class="service-card" role="listitem">
                        <div class="service-image-container">
                            <img src="./assets/images/acceuil2.jpg" 
                                 alt="Séjours golf personnalisés en Thaïlande" 
                                 class="service-image"
                                 loading="lazy"
                                 width="400" height="256"
                                 onclick="openSimpleModal('./assets/images/acceuil2.jpg', 'Séjours Golf Personnalisés')"
                                 style="cursor: pointer;">
                        </div>
                        <div class="service-content">
                            <h3 class="service-title">
                                <span data-translate="service2.title">Séjours personnalisés</span>
                            </h3>
                            <p class="service-description">
                                <span data-translate="service2.desc">Bénéficiez de ma connaissance approfondie de la Thaïlande pour découvrir les meilleurs parcours et expériences.</span>
                            </p>
                        </div>
                    </article>
                    
                    <article class="service-card" role="listitem">
                        <div class="service-image-container">
                            <img src="./assets/images/Ayutthaya.webp" 
                                 alt="Golf historique Ayutthaya - Expertise locale authentique Thaïlande" 
                                 class="service-image"
                                 loading="lazy"
                                 width="400" height="256"
                                 onclick="openSimpleModal('./assets/images/Ayutthaya.webp', 'Golf Historique Ayutthaya - Expertise Locale')"
                                 style="cursor: pointer;">
                        </div>
                        <div class="service-content">
                            <h3 class="service-title">
                                <span data-translate="service3.title">Expertise locale</span>
                            </h3>
                            <p class="service-description">
                                <span data-translate="service3.desc">Une assistance complète pendant tout votre séjour pour que vous puissiez profiter en toute sérénité.</span>
                            </p>
                        </div>
                    </article>
                </div>
            </div>
        </section>

        <!-- Destinations Section Include -->
        <!-- Destinations Section -->
<section id="destinations" class="destinations-section" role="region" aria-labelledby="destinations-title">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <header class="section-header text-center">
            <h2 id="destinations-title" class="section-title">
                <span data-translate="destinations.title">Nos</span> <span class="text-gradient" data-translate="destinations.subtitle">Destinations</span>
            </h2>
            <p class="section-description">
                <span data-translate="destinations.description">La Thaïlande est une destination de choix pour les golfeurs en quête d'expériences uniques. 
                Du dynamisme de Bangkok à la sérénité des îles tropicales, le pays offre une diversité de parcours 
                à travers des paysages aussi variés qu'inoubliables.</span>
            </p>
        </header>

        <!-- Introduction Content -->
        <div class="destinations-intro">
            <div class="intro-grid">
                <div class="intro-content">
                    <h3 class="intro-subtitle">
                        <span data-translate="destinations.intro.title">Le Golf en Thaïlande : Un Cadre Exceptionnel</span>
                    </h3>
                    <div class="intro-paragraphs">
                        <p>
                            <span data-translate="destinations.intro.p1">Aux abords de la capitale, des clubs prestigieux comme l'Alpine Golf Club et le Thai Country Club 
                            allient des installations modernes à des vues luxuriantes, offrant un équilibre parfait entre 
                            accessibilité et excellence.</span>
                        </p>
                        <p>
                            <span data-translate="destinations.intro.p2">Au nord, près de Chiang Mai et Chiang Rai, les parcours serpentent aux pieds des montagnes et 
                            s'intègrent à merveille dans des paysages naturels, tout en reflétant la culture thaïlandaise authentique.</span>
                        </p>
                        <p>
                            <span data-translate="destinations.intro.p3">Sur les côtes paradisiaques, notamment à Phuket, des parcours offrent des vues spectaculaires sur 
                            les plages et les eaux turquoise, ajoutant une touche de luxe et de détente à chaque partie.</span>
                        </p>
                    </div>
                </div>
                <div class="intro-image-container">
                    <img src="./assets/images/nouvelleimage1.jpeg" 
                         alt="Nouveau parcours de golf exclusif avec vue panoramique" 
                         class="intro-image"
                         loading="lazy"
                         width="600" height="384"
                         onclick="openSimpleModal('./assets/images/nouvelleimage1.jpeg', 'Parcours de Golf Premium Thaïlande')"
                         style="cursor: pointer;">
                </div>
            </div>
        </div>

        <!-- Golf Courses -->
        <div class="golf-courses">
            <header class="courses-header">
                <h3 class="courses-title">
                    <span data-translate="courses.title">Quelques Incontournables du Golf en Thaïlande</span>
                </h3>
            </header>
            
            <div class="courses-grid" role="list">
                <!-- Course 1: Aquella Golf & Country Club -->
                <article class="course-card" role="listitem" data-course="aquella-golf">
                    <div class="course-image-container">
                        <img src="./assets/images/AquellaGolfCountryClub.jpeg" 
                             alt="Aquella Golf Club - 18 trous championship avec 2,5 km plage privée - Phang Nga renaissance post-tsunami" 
                             class="course-image"
                             loading="lazy"
                             width="400" height="192"
                             onclick="openSimpleModal('./assets/images/AquellaGolfCountryClub.jpeg', 'Aquella Golf & Country Club')"
                             style="cursor: pointer;">
                        <div class="course-overlay">
                            <span class="course-location-badge badge-teal">Phang Nga</span>
                            <button class="course-expand-btn" aria-label="Voir plus d'informations sur Aquella Golf Club">
                                <i class="fas fa-expand" aria-hidden="true"></i>
                            </button>
                        </div>
                    </div>
                    <div class="course-content">
                        <h4 class="course-title">Aquella Golf & Country Club</h4>
                        <p class="course-description">
                            <span data-translate="course.aquella.desc">Parcours championship côtier unique construit sur les vestiges du tsunami de 2004, incarnant renaissance et résilience. **18 trous** intégrés harmonieusement dans un écosystème de **2,5 km de plage privée**. Tunnels de bambou authentiques et vue panoramique sur la mer d'Andaman.</span>
                        </p>
                        <div class="course-meta">
                            <span class="course-features">
                                <i class="fas fa-water" aria-hidden="true"></i><span data-translate="course.aquella.features">18 Trous • Bord de mer • Plage privée</span>
                            </span>
                        </div>
                    </div>
                </article>
                
                <!-- Course 2: Black Mountain Golf Club -->
                <article class="course-card" role="listitem" data-course="black-mountain">
                    <div class="course-image-container">
                        <img src="./assets/images/BlackMountainGolfClubHuHin.jpeg" 
                             alt="Black Mountain Golf Club - 27 trous championship montagne granitique noire - Design Phil Ryan Hua Hin" 
                             class="course-image"
                             loading="lazy"
                             width="400" height="192"
                             onclick="openSimpleModal('./assets/images/BlackMountainGolfClubHuHin.jpeg', 'Black Mountain Golf Club Hua Hin')"
                             style="cursor: pointer;">
                        <div class="course-overlay">
                            <span class="course-location-badge badge-gray">Hua Hin</span>
                            <button class="course-expand-btn" aria-label="Voir plus d'informations sur Black Mountain Golf Club">
                                <i class="fas fa-expand" aria-hidden="true"></i>
                            </button>
                        </div>
                    </div>
                    <div class="course-content">
                        <h4 class="course-title">Black Mountain Golf Club</h4>
                        <p class="course-description">
                            <span data-translate="course.blackmountain.desc">Parcours championship de renommée internationale niché au pied des montagnes granitiques noires. **27 trous** répartis en trois circuits distincts (East, North, West) offrant des défis variés pour tous les niveaux. Architecture spectaculaire signée Phil Ryan dans un cadre naturel préservé.</span>
                        </p>
                        <div class="course-meta">
                            <span class="course-features">
                                <i class="fas fa-mountain" aria-hidden="true"></i><span data-translate="course.blackmountain.features">27 Trous • Championship • Design Phil Ryan</span>
                            </span>
                        </div>
                    </div>
                </article>

                <!-- Course 3: Santiburi Samui Country Club -->
                <article class="course-card" role="listitem" data-course="santiburi-samui">
                    <div class="course-image-container">
                        <img src="./assets/images/SantiburiSamuiCountryClubKohSamui.jpeg" 
                             alt="Santiburi Samui Country Club - 18 trous dénivelé 180m altitude - Seul parcours Koh Samui vues océan" 
                             class="course-image"
                             loading="lazy"
                             width="400" height="192"
                             onclick="openSimpleModal('./assets/images/SantiburiSamuiCountryClubKohSamui.jpeg', 'Santiburi Samui Country Club')"
                             style="cursor: pointer;">
                        <div class="course-overlay">
                            <span class="course-location-badge badge-green">Koh Samui</span>
                            <button class="course-expand-btn" aria-label="Voir plus d'informations sur Santiburi Samui Country Club">
                                <i class="fas fa-expand" aria-hidden="true"></i>
                            </button>
                        </div>
                    </div>
                    <div class="course-content">
                        <h4 class="course-title">Santiburi Samui Country Club</h4>
                        <p class="course-description">
                            <span data-translate="course.santiburi.desc">Parcours unique de **18 trous** sur l'île de Koh Samui, sculpté dans la topographie dramatique avec **dénivelés jusqu'à 180 mètres**. Design montagneux offrant des panoramas époustouflants sur le Golfe de Thaïlande. Treize trous avec obstacles d'eau naturels alimentés par cascades.</span>
                        </p>
                        <div class="course-meta">
                            <span class="course-features">
                                <i class="fas fa-tree" aria-hidden="true"></i><span data-translate="course.santiburi.features">18 Trous • Resort intégré • Vues océan</span>
                            </span>
                        </div>
                    </div>
                </article>

                <!-- Course 4: Chiang Mai Highlands Golf -->
                <article class="course-card" role="listitem" data-course="chiang-mai-highlands">
                    <div class="course-image-container">
                        <img src="./assets/images/Highlands.jpeg" 
                             alt="Chiang Mai Highlands Golf - 27 trous Schmidt-Curley altitude site spirituel - Climat montagnard unique" 
                             class="course-image"
                             loading="lazy"
                             width="400" height="192"
                             onclick="openSimpleModal('./assets/images/Highlands.jpeg', 'Chiang Mai Highlands Golf & Spa Resort')"
                             style="cursor: pointer;">
                        <div class="course-overlay">
                            <span class="course-location-badge badge-orange">Chiang Mai</span>
                            <button class="course-expand-btn" aria-label="Voir plus d'informations sur Chiang Mai Highlands Golf">
                                <i class="fas fa-expand" aria-hidden="true"></i>
                            </button>
                        </div>
                    </div>
                    <div class="course-content">
                        <h4 class="course-title">Chiang Mai Highlands Golf & Spa Resort</h4>
                        <p class="course-description">
                            <span data-translate="course.chiangmai.desc">Golf montagnard d'exception avec **27 trous** répartis en trois parcours distincts (Valley, Highlands, Mountain). Design signé **Schmidt-Curley** sur site spirituel historique. Climat frais d'altitude unique en Thaïlande et vues panoramiques 360° sur les montagnes du nord.</span>
                        </p>
                        <div class="course-meta">
                            <span class="course-features">
                                <i class="fas fa-mountain" aria-hidden="true"></i><span data-translate="course.chiangmai.features">27 Trous • Altitude • Schmidt-Curley Design</span>
                            </span>
                        </div>
                    </div>
                </article>
                
                <!-- Course 5: Red Mountain Golf Club -->
                <article class="course-card" role="listitem" data-course="red-mountain">
                    <div class="course-image-container">
                        <img src="./assets/images/RedMountainGolfClubPhuket.jpeg" 
                             alt="Red Mountain Golf Club - 18 trous mine étain transformée roches rouges - Trou 17 Drop Shot 50m Phuket" 
                             class="course-image"
                             loading="lazy"
                             width="400" height="192"
                             onclick="openSimpleModal('./assets/images/RedMountainGolfClubPhuket.jpeg', 'Red Mountain Golf Club Phuket')"
                             style="cursor: pointer;">
                        <div class="course-overlay">
                            <span class="course-location-badge badge-red">Phuket</span>
                            <button class="course-expand-btn" aria-label="Voir plus d'informations sur Red Mountain Golf Club">
                                <i class="fas fa-expand" aria-hidden="true"></i>
                            </button>
                        </div>
                    </div>
                    <div class="course-content">
                        <h4 class="course-title">Red Mountain Golf Club</h4>
                        <p class="course-description">
                            <span data-translate="course.redmountain.desc">Parcours révolutionnaire de **18 trous** sculpté dans une ancienne mine d'étain. Formations rocheuses rouges iconiques émergent des fairways émeraude. Design unique exploitant **150 pieds de dénivelé naturel** avec le légendaire 17e trou "Drop Shot" - chute vertigineuse de 50 mètres.</span>
                        </p>
                        <div class="course-meta">
                            <span class="course-features">
                                <i class="fas fa-mountain" aria-hidden="true"></i><span data-translate="course.redmountain.features">18 Trous • Mine d'étain • Design unique</span>
                            </span>
                        </div>
                    </div>
                </article>

                <!-- Course 6: Blue Canyon Country Club -->
                <article class="course-card" role="listitem" data-course="blue-canyon">
                    <div class="course-image-container">
                        <img src="./assets/images/blue-canyon-golf-course-thailand.jpg" 
                             alt="Blue Canyon Country Club - 2 parcours championship Canyon & Lakes - Institution légendaire Tiger Woods Phuket" 
                             class="course-image"
                             loading="lazy"
                             width="400" height="192"
                             onclick="openSimpleModal('./assets/images/blue-canyon-golf-course-thailand.jpg', 'Blue Canyon Country Club Phuket')"
                             style="cursor: pointer;">
                        <div class="course-overlay">
                            <span class="course-location-badge badge-blue">Phuket</span>
                            <button class="course-expand-btn" aria-label="Voir plus d'informations sur Blue Canyon Country Club">
                                <i class="fas fa-expand" aria-hidden="true"></i>
                            </button>
                        </div>
                    </div>
                    <div class="course-content">
                        <h4 class="course-title">Blue Canyon Country Club</h4>
                        <p class="course-description">
                            <span data-translate="course.bluecanyon.desc">Institution légendaire du golf mondial avec **deux parcours championship** (Canyon Course et Lakes Course). Architecture technique signée Yoshikazu Kato où ont joué Tiger Woods et Greg Norman. Le parcours le plus prestigieux de Phuket, situé à proximité immédiate de l'aéroport.</span>
                        </p>
                        <div class="course-meta">
                            <span class="course-features">
                                <i class="fas fa-trophy" aria-hidden="true"></i><span data-translate="course.bluecanyon.features">2 Parcours • Championship • Légende</span>
                            </span>
                        </div>
                    </div>
                </article>
            </div>
        </div>

        <!-- Regional Features -->
        <div class="regional-features">
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-city" aria-hidden="true"></i>
                    </div>
                    <h4 class="feature-title" data-translate="regions.bangkok.title">Bangkok & Région</h4>
                    <p class="feature-description" data-translate="regions.bangkok.desc">
                        Clubs prestigieux aux abords de la capitale, alliant modernité et excellence golfique.
                    </p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-mountain" aria-hidden="true"></i>
                    </div>
                    <h4 class="feature-title" data-translate="regions.north.title">Nord - Chiang Mai</h4>
                    <p class="feature-description" data-translate="regions.north.desc">
                        Parcours montagnards intégrés dans des paysages naturels et culture authentique.
                    </p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-umbrella-beach" aria-hidden="true"></i>
                    </div>
                    <h4 class="feature-title" data-translate="regions.coast.title">Côtes - Phuket</h4>
                    <p class="feature-description" data-translate="regions.coast.desc">
                        Vues spectaculaires sur plages paradisiaques et eaux turquoise cristallines.
                    </p>
                </div>
            </div>
            
            <!-- Additional Premium Feature - Centered -->
            <div class="flex justify-center mt-8">
                <div class="feature-item text-center max-w-md">
                    <div class="feature-icon">
                        <i class="fas fa-crown" aria-hidden="true"></i>
                    </div>
                    <h4 class="feature-title" data-translate="regions.premium.title">Partout en Thaïlande</h4>
                    <p class="feature-description">
                        <span data-translate="regions.premium.desc1">Services Premium GolfinThaï</span><br>
                        <span data-translate="regions.premium.desc2">Accompagnement expert personnalisé</span>
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

        <!-- About Section -->
        <section id="apropos" class="about-section section" role="region" aria-labelledby="about-title">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <header class="section-header text-center mb-16">
                    <h2 id="about-title" class="section-title">
                        <span data-translate="about.title">À</span> <span class="text-gradient" data-translate="about.subtitle">Propos</span> de GolfinThaï
                    </h2>
                </header>

                <div class="about-content">
                    <!-- About Introduction -->
                    <div class="about-intro mb-16">
                        <div class="glass-card rounded-3xl p-8 lg:p-12 text-center reveal-animation">
                            <p class="intro-paragraph mb-6">
                                <span data-translate="about.intro.p1">GolfinThaï, c'est avant tout un projet porté par des passionnés, animés par l'envie de créer des séjours golfiques d'exception. 
                                Chaque voyage est conçu avec soin, en s'appuyant sur une expérience approfondie du terrain et un réseau de partenaires de confiance à travers la Thaïlande.</span>
                            </p>
                            <p class="intro-paragraph">
                                <span data-translate="about.intro.p2">Au fil des années, nous avons développé une approche basée sur l'écoute et l'accompagnement, afin d'offrir des expériences adaptées aux attentes de chaque golfeur. 
                                Loin des circuits standardisés, nous privilégions des itinéraires pensés dans les moindres détails, où chaque parcours, chaque hôtel et chaque activité contribuent à rendre le voyage unique.</span>
                            </p>
                        </div>
                    </div>

                    <!-- About Details -->
                    <div class="about-details mb-16">
                        <!-- Parcours et Expertise avec image -->
                        <div class="mb-12">
                            <div class="text-center mb-8 reveal-animation">
                                <img src="./assets/images/missionomg.jpg" 
                                     alt="Expertise golf et parcours d'exception en Thaïlande" 
                                     class="w-full max-w-md mx-auto h-80 lg:h-96 object-cover rounded-3xl shadow-lg"
                                     loading="lazy"
                                     width="400" height="320"
                                     onclick="openSimpleModal('./assets/images/missionomg.jpg', 'Expertise Golf et Parcours Exception')"
                                     style="cursor: pointer;">
                            </div>
                            <article class="detail-item reveal-animation text-center max-w-4xl mx-auto">
                                <header class="detail-header mb-6">
                                    <div class="detail-icon mx-auto mb-4">
                                        <i class="fas fa-golf-ball" aria-hidden="true"></i>
                                    </div>
                                    <h3 class="detail-title" data-translate="about.expertise.title">Parcours et Expertise</h3>
                                </header>
                                <div class="detail-content">
                                    <p class="detail-paragraph mb-6" data-translate="about.expertise.p1">
                                        Passionné de golf, j'ai eu l'opportunité unique de vivre en Thaïlande, où j'ai joué sur de nombreux parcours emblématiques à travers le pays, 
                                        de Bangkok à Chiang Mai, en passant par Phuket et Hua Hin.
                                    </p>
                                    <p class="detail-paragraph" data-translate="about.expertise.p2">
                                        Cette expérience m'a permis de tisser des liens avec des professionnels du golf sur place et de mieux comprendre les attentes des golfeurs internationaux. 
                                        Mon objectif est de vous offrir une expérience sur mesure, qu'il s'agisse de jouer sur les terrains les plus prestigieux ou de découvrir des parcours plus confidentiels.
                                    </p>
                                </div>
                            </article>
                        </div>

                        <!-- Accompagnement Personnalisé avec image -->
                        <div class="mb-12">
                            <div class="text-center mb-8 reveal-animation">
                                <img src="./assets/images/hl03-high.jpg" 
                                     alt="Golf premium avec vues époustouflantes Thaïlande" 
                                     class="w-full max-w-md mx-auto h-80 lg:h-96 object-cover rounded-3xl shadow-lg"
                                     loading="lazy"
                                     width="400" height="320"
                                     onclick="openSimpleModal('./assets/images/hl03-high.jpg', 'Golf Premium Vues Époustouflantes')"
                                     style="cursor: pointer;">
                            </div>
                            <article class="detail-item reveal-animation text-center max-w-4xl mx-auto">
                                <header class="detail-header mb-6">
                                    <div class="detail-icon mx-auto mb-4">
                                        <i class="fas fa-user-friends" aria-hidden="true"></i>
                                    </div>
                                    <h3 class="detail-title" data-translate="about.personalized.title">Accompagnement Personnalisé</h3>
                                </header>
                                <div class="detail-content">
                                    <p class="detail-paragraph mb-6" data-translate="about.personalized.p1">
                                        Chez GolfinThaï, chaque client bénéficie d'une attention unique. Que vous voyagiez seul(e), en couple ou en groupe, 
                                        j'organise des séjours sur mesure pensés pour répondre à vos envies et à votre rythme.
                                    </p>
                                    <p class="detail-paragraph" data-translate="about.personalized.p2">
                                        Si vous voyagez seul(e) ou souhaitez un partenaire de jeu, je suis également disponible pour partager des parties de golf à votre convenance. 
                                        Passionné de golf, je prends le temps de comprendre vos attentes pour créer un programme qui vous ressemble.
                                    </p>
                                </div>
                            </article>
                        </div>
                    </div>

                    <!-- Authenticity Section -->
                    <div class="authenticity-section mb-16">
                        <!-- Image du Wat Phra Kaew -->
                        <div class="text-center mb-8 reveal-animation">
                            <div class="relative overflow-hidden rounded-3xl group max-w-md mx-auto">
                                <img src="./assets/images/watphrakaew-high.jpg" 
                                     alt="Culture Authentique Thaïlandaise - Wat Phra Kaew Temple du Bouddha d'Émeraude Bangkok" 
                                     class="w-full h-80 lg:h-96 object-cover group-hover:scale-110 transition-transform duration-500"
                                     loading="lazy"
                                     width="400" height="320"
                                     onclick="openSimpleModal('./assets/images/watphrakaew-high.jpg', 'Wat Phra Kaew - Temple du Bouddha Émeraude')"
                                     style="cursor: pointer;">
                                <div class="image-overlay">
                                    <h4 class="image-title">Wat Phra Kaew</h4>
                                    <p class="image-description">Temple du Bouddha d'Émeraude - Bangkok</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 lg:grid-cols-1 gap-12 items-center max-w-5xl mx-auto reveal-animation">
                            <div class="text-center">
                                <div class="mb-8">
                                    <div class="detail-icon mx-auto mb-4">
                                        <i class="fas fa-heart" aria-hidden="true"></i>
                                    </div>
                                    <h3 class="intro-subtitle" data-translate="about.authenticity.title">Engagement envers l'Authenticité</h3>
                                </div>
                                <p class="detail-paragraph mb-6" data-translate="about.authenticity.p1">
                                    En vivant en Thaïlande pendant une décennie, j'ai pu tisser des liens solides avec des partenaires locaux, découvrir des lieux authentiques et développer une véritable maîtrise de la langue thaïlandaise.
                                </p>
                                <p class="detail-paragraph" data-translate="about.authenticity.p2">
                                    Je ne me contente pas de vous accompagner sur les parcours de golf ; je vous fais également découvrir les trésors culturels, les traditions et la gastronomie thaïlandaise. 
                                    Avec GolfinThaï, mon objectif est de vous offrir un séjour enrichissant, où le golf se mêle aux découvertes locales.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Founder Section -->
                    <div class="founder-section">
                        <div class="glass-card rounded-3xl p-6 sm:p-8 lg:p-12 reveal-animation">
                            <div class="max-w-4xl mx-auto">
                                <h3 class="intro-subtitle text-center mb-8" data-translate="about.founder.title">L'Histoire derrière cette Aventure</h3>
                                <div class="flex flex-col lg:flex-row items-center gap-8">
                                    <div class="w-full sm:w-80 lg:w-72 flex-shrink-0">
                                        <div class="founder-image-container center-face">
                                            <img src="./assets/images/PR.jpg" 
                                                 alt="Sébastien Marciano - Fondateur GolfinThaï expert voyages golf Thaïlande" 
                                                 class="founder-image"
                                                 loading="lazy"
                                                 width="288" height="320"
                                                 onclick="openSimpleModal('./assets/images/PR.jpg', 'Sébastien Marciano - Fondateur GolfinThaï')"
                                                 style="cursor: pointer;">
                                            <div class="founder-info">
                                                <div class="text-center">
                                                    <h4 class="founder-name">Sébastien Marciano</h4>
                                                    <p class="founder-title" data-translate="about.founder.role">Fondateur & Guide Expert</p>
                                                    <div class="founder-rating">
                                                        <i class="fas fa-star" aria-hidden="true"></i>
                                                        <i class="fas fa-star" aria-hidden="true"></i>
                                                        <i class="fas fa-star" aria-hidden="true"></i>
                                                        <i class="fas fa-star" aria-hidden="true"></i>
                                                        <i class="fas fa-star" aria-hidden="true"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex-1 text-center lg:text-left">
                                        <p class="founder-description mb-4" data-translate="about.founder.p1">
                                            Je m'appelle <span class="highlight-name">Sébastien Marciano</span>, et avec GolfinThaï, j'ai le plaisir de partager deux de mes passions : le golf et la Thaïlande.
                                        </p>
                                        <p class="founder-description mb-4" data-translate="about.founder.p2">
                                            Mon premier séjour dans ce pays fascinant remonte à plus de 20 ans, et j'y ai vécu pendant près de 10 ans, acquérant une compréhension approfondie de sa culture, de ses coutumes et de ses paysages uniques.
                                        </p>
                                        <p class="founder-description mb-6" data-translate="about.founder.p3">
                                            C'est en Thaïlande que j'ai découvert et appris le golf, explorant presque tous les plus beaux parcours du pays. Aujourd'hui, je mets cette expertise au service de voyages golfiques sur mesure, 
                                            conçus pour offrir une expérience authentique, enrichissante, et parfaitement adaptée aux attentes de chaque client.
                                        </p>
                                        <div class="founder-stats">
                                            <div class="stat-item">
                                                <div class="stat-value">20+</div>
                                                <div class="stat-label" data-translate="about.founder.stat1">Années d'Expérience</div>
                                            </div>
                                            <div class="stat-item">
                                                <div class="stat-value">10</div>
                                                <div class="stat-label" data-translate="about.founder.stat2">Ans en Thaïlande</div>
                                            </div>
                                            <div class="stat-item">
                                                <div class="stat-value">50+</div>
                                                <div class="stat-label" data-translate="about.founder.stat3">Parcours Explorés</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Testimonials Section Include -->
        <!-- Testimonials Section -->
<section id="temoignages" class="testimonials-section section" role="region" aria-labelledby="testimonials-title">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <header class="section-header text-center mb-12 lg:mb-16">
            <h2 id="testimonials-title" class="section-title mb-6">
                <span class="text-gradient" data-translate="testimonials.title">Témoignages</span> <span data-translate="testimonials.subtitle2">de nos Golfeurs</span>
            </h2>
            <p class="section-description" data-translate="testimonials.description">
                Découvrez les expériences authentiques de nos clients qui ont vécu l'aventure GolfinThai. 
                Chaque témoignage reflète notre engagement à offrir des séjours golfiques d'exception en Thaïlande.
            </p>
        </header>

        <!-- Testimonials Grid -->
        <div class="testimonials-grid">
            <!-- Testimonial 1 -->
            <article class="testimonial-card reveal-animation" style="animation-delay: 0.1s;">
                <div class="testimonial-content">
                    <div class="testimonial-quote">
                        <i class="fas fa-quote-left quote-icon" aria-hidden="true"></i>
                        <p class="testimonial-text" data-translate="testimonials.daniel.text">
                            Super expérience avec Sébastien ! Tout était parfaitement organisé, du transport aux réservations de golf, mais aussi pour les excursions, restaurants et autres activités. On n'avait rien à gérer, juste à profiter. Il était toujours disponible et réactif, ce qui a vraiment fait la différence. Un vrai plaisir !
                        </p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-info">
                            <h4 class="author-name">Daniel M.</h4>
                            <div class="author-rating">
                                <i class="fas fa-star" aria-hidden="true"></i>
                                <i class="fas fa-star" aria-hidden="true"></i>
                                <i class="fas fa-star" aria-hidden="true"></i>
                                <i class="fas fa-star" aria-hidden="true"></i>
                                <i class="fas fa-star" aria-hidden="true"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </article>

            <!-- Testimonial 2 -->
            <article class="testimonial-card reveal-animation" style="animation-delay: 0.2s;">
                <div class="testimonial-content">
                    <div class="testimonial-quote">
                        <i class="fas fa-quote-left quote-icon" aria-hidden="true"></i>
                        <p class="testimonial-text" data-translate="testimonials.sophie.text">
                            Mon mari a pleinement savouré son expérience sur les parcours de golf, tandis que j'ai profité de tout ce que la Thaïlande a à offrir : des massages d'exception, une gastronomie raffinée, des découvertes culturelles fascinantes et surtout, une hospitalité incomparable. L'organisation était impeccable, nous permettant de vivre un séjour fluide et sans le moindre souci. Un moment privilégié que nous renouvellerons avec grand plaisir.
                        </p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-info">
                            <h4 class="author-name">Sophie C.</h4>
                            <div class="author-rating">
                                <i class="fas fa-star" aria-hidden="true"></i>
                                <i class="fas fa-star" aria-hidden="true"></i>
                                <i class="fas fa-star" aria-hidden="true"></i>
                                <i class="fas fa-star" aria-hidden="true"></i>
                                <i class="fas fa-star" aria-hidden="true"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </article>

            <!-- Testimonial 3 -->
            <article class="testimonial-card reveal-animation" style="animation-delay: 0.3s;">
                <div class="testimonial-content">
                    <div class="testimonial-quote">
                        <i class="fas fa-quote-left quote-icon" aria-hidden="true"></i>
                        <p class="testimonial-text" data-translate="testimonials.alain.text">
                            Un séjour remarquable ! L'organisation était irréprochable, avec des hébergements haut de gamme et une sélection de parcours de golf sublimes. Le parfait équilibre entre moments de jeu et découvertes culturelles. Tout était pensé dans les moindres détails pour que nous profitions pleinement.
                        </p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-info">
                            <h4 class="author-name">Alain C.</h4>
                            <div class="author-rating">
                                <i class="fas fa-star" aria-hidden="true"></i>
                                <i class="fas fa-star" aria-hidden="true"></i>
                                <i class="fas fa-star" aria-hidden="true"></i>
                                <i class="fas fa-star" aria-hidden="true"></i>
                                <i class="fas fa-star" aria-hidden="true"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </article>

            <!-- Testimonial 4 -->
            <article class="testimonial-card reveal-animation" style="animation-delay: 0.4s;">
                <div class="testimonial-content">
                    <div class="testimonial-quote">
                        <i class="fas fa-quote-left quote-icon" aria-hidden="true"></i>
                        <p class="testimonial-text" data-translate="testimonials.francois.text">
                            Quelle découverte ! Les parcours de golf en Thaïlande sont d'un niveau exceptionnel, et l'accompagnement de Sébastien a rendu cette expérience encore plus mémorable. Sa connaissance des meilleurs spots et restaurants locaux a transformé ce voyage en une aventure culturelle et golfique inoubliable.
                        </p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-info">
                            <h4 class="author-name">François D.</h4>
                            <div class="author-rating">
                                <i class="fas fa-star" aria-hidden="true"></i>
                                <i class="fas fa-star" aria-hidden="true"></i>
                                <i class="fas fa-star" aria-hidden="true"></i>
                                <i class="fas fa-star" aria-hidden="true"></i>
                                <i class="fas fa-star" aria-hidden="true"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </article>

            <!-- Testimonial 5 -->
            <article class="testimonial-card reveal-animation" style="animation-delay: 0.5s;">
                <div class="testimonial-content">
                    <div class="testimonial-quote">
                        <i class="fas fa-quote-left quote-icon" aria-hidden="true"></i>
                        <p class="testimonial-text" data-translate="testimonials.berengere.text">
                            Un voyage au-delà de nos attentes ! Chaque journée était parfaitement rythmée entre golf et découvertes, sans que nous ayons à nous soucier de quoi que ce soit. Les parcours étaient superbes, les suggestions de visites et de restaurants toujours judicieuses, et l'accompagnement discret mais efficace. Une parenthèse enchantée que nous avons savourée du début à la fin.
                        </p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-info">
                            <h4 class="author-name">Bérengère R.</h4>
                            <div class="author-rating">
                                <i class="fas fa-star" aria-hidden="true"></i>
                                <i class="fas fa-star" aria-hidden="true"></i>
                                <i class="fas fa-star" aria-hidden="true"></i>
                                <i class="fas fa-star" aria-hidden="true"></i>
                                <i class="fas fa-star" aria-hidden="true"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </article>

            <!-- CTA Card -->
            <article class="testimonial-card testimonial-cta reveal-animation" style="animation-delay: 0.6s;">
                <div class="cta-content">
                    <div class="cta-icon">
                        <i class="fas fa-golf-ball" aria-hidden="true"></i>
                    </div>
                    <h3 class="cta-title" data-translate="testimonials.cta.title">Votre Témoignage ?</h3>
                    <p class="cta-description" data-translate="testimonials.cta.desc">
                        Rejoignez nos clients satisfaits et vivez votre propre aventure golfique en Thaïlande.
                    </p>
                    <a href="#contact" class="cta-button">
                        <span data-translate="testimonials.cta.button">Commencer l'Aventure</span>
                        <i class="fas fa-arrow-right" aria-hidden="true"></i>
                    </a>
                </div>
            </article>
        </div>
    </div>
</section>
        
        <!-- Contact Section déjà définie plus haut -->
        <section id="contact" class="contact-section section" role="region" aria-labelledby="contact-title">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="max-w-5xl mx-auto">
                    <header class="section-header text-center mb-12">
                        <h2 id="contact-title" class="section-title">
                            <span class="text-gradient" data-translate="contact.title">Contactez-Nous</span>
                        </h2>
                    </header>

                    <div class="contact-content">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                            <!-- Contact Information -->
                            <div class="contact-info space-y-8 reveal-animation">
                                <div class="contact-item">
                                    <div class="contact-icon">
                                        <i class="fab fa-whatsapp" aria-hidden="true"></i>
                                    </div>
                                    <div class="contact-details">
                                        <h3 class="contact-label" data-translate="contact.phone.label">Téléphone / WhatsApp</h3>
                                        <a href="https://wa.me/***********" 
                                           class="contact-link" 
                                           target="_blank" 
                                           rel="noopener noreferrer"
                                           aria-label="Contacter via WhatsApp ou téléphone">
                                            +33 6 09 79 91 80
                                        </a>
                                    </div>
                                </div>

                                <div class="contact-item">
                                    <div class="contact-icon">
                                        <i class="fas fa-envelope" aria-hidden="true"></i>
                                    </div>
                                    <div class="contact-details">
                                        <h3 class="contact-label" data-translate="contact.email.label">Email</h3>
                                        <a href="mailto:<EMAIL>" 
                                           class="contact-link"
                                           aria-label="Envoyer un email à <EMAIL>">
                                            <EMAIL>
                                        </a>
                                    </div>
                                </div>

                                <div class="contact-item">
                                    <div class="contact-icon">
                                        <i class="fab fa-instagram" aria-hidden="true"></i>
                                    </div>
                                    <div class="contact-details">
                                        <h3 class="contact-label" data-translate="contact.instagram.label">Instagram</h3>
                                        <a href="https://instagram.com/golfinthai_" 
                                           class="contact-link"
                                           target="_blank" 
                                           rel="noopener noreferrer"
                                           aria-label="Suivre sur Instagram">
                                            @golfinthai_
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Form -->
                            <div class="contact-form-container reveal-animation" style="animation-delay: 0.2s;">
                                <div class="glass-card rounded-3xl p-8">
                                    <form action="https://formspree.io/f/mgegenll" 
                                          method="POST" 
                                          class="contact-form"
                                          novalidate>
                                        <div class="form-group-row">
                                            <div class="form-group">
                                                <label for="nom" class="sr-only" data-translate="contact.form.name">Votre nom</label>
                                                <input type="text" 
                                                       id="nom"
                                                       name="nom" 
                                                       data-translate-placeholder="contact.form.name.placeholder"
                                                       placeholder="Votre Nom" 
                                                       required 
                                                       class="form-input"
                                                       autocomplete="name"
                                                       aria-describedby="nom-error">
                                            </div>
                                            <div class="form-group">
                                                <label for="email" class="sr-only" data-translate="contact.form.email">Votre email</label>
                                                <input type="email" 
                                                       id="email"
                                                       name="email" 
                                                       data-translate-placeholder="contact.form.email.placeholder"
                                                       placeholder="Votre Email" 
                                                       required 
                                                       class="form-input"
                                                       autocomplete="email"
                                                       aria-describedby="email-error">
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="telephone" class="sr-only" data-translate="contact.form.phone">Votre téléphone</label>
                                            <input type="tel" 
                                                   id="telephone"
                                                   name="telephone" 
                                                   data-translate-placeholder="contact.form.phone.placeholder"
                                                   placeholder="Téléphone" 
                                                   class="form-input"
                                                   autocomplete="tel"
                                                   aria-describedby="telephone-error">
                                        </div>

                                        <div class="form-group">
                                            <label for="message" class="sr-only" data-translate="contact.form.message">Votre message</label>
                                            <textarea id="message"
                                                      name="message" 
                                                      data-translate-placeholder="contact.form.message.placeholder"
                                                      placeholder="Votre message..." 
                                                      rows="5" 
                                                      required 
                                                      class="form-input form-textarea"
                                                      aria-describedby="message-error"></textarea>
                                        </div>

                                        <button type="submit" class="btn btn-primary w-full">
                                            <i class="fas fa-paper-plane mr-2" aria-hidden="true"></i>
                                            <span data-translate="contact.form.send">Envoyer le Message</span>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    
    <!-- Footer Include -->
    <!-- Footer -->
<footer class="footer-section" role="contentinfo">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="footer-content">
            <div class="text-center">
                <h3 class="footer-title">GolfinThaï</h3>
                <p class="footer-description" data-translate="footer.description">
                    Découvrez la Thaïlande à travers le Golf : Une Expérience sur Mesure
                </p>
                
                <!-- Social Links -->
                <div class="social-links">
                    <a href="https://wa.me/***********" 
                       class="social-link" 
                       target="_blank" 
                       rel="noopener noreferrer"
                       aria-label="Contacter via WhatsApp/Téléphone">
                        <i class="fab fa-whatsapp" aria-hidden="true"></i>
                    </a>
                    <a href="mailto:<EMAIL>" 
                       class="social-link"
                       aria-label="Envoyer un email">
                        <i class="fas fa-envelope" aria-hidden="true"></i>
                    </a>
                    <a href="https://instagram.com/golfinthai_" 
                       class="social-link"
                       target="_blank" 
                       rel="noopener noreferrer"
                       aria-label="Suivre sur Instagram">
                        <i class="fab fa-instagram" aria-hidden="true"></i>
                    </a>
                </div>
                
                <!-- Copyright -->
                <p class="footer-copyright">
                    © <span class="copyright-year">2024</span>-2025 GolfinThaï - <span data-translate="footer.copyright">Tous droits réservés</span>
                </p>
                
                <!-- SIRET -->
                <p class="footer-siret">
                    SIRET N° 98816351500014
                </p>
            </div>
        </div>
    </div>
</footer>

<!-- WhatsApp Floating Button -->
<a href="https://wa.me/***********" 
   class="whatsapp-float" 
   target="_blank" 
   rel="noopener noreferrer"
   aria-label="Contacter via WhatsApp"
   title="Contactez-nous sur WhatsApp">
    <i class="fab fa-whatsapp" aria-hidden="true"></i>
</a>

    <!-- Course Modal Template (Hidden) -->
    <div id="course-modal" class="course-modal" role="dialog" aria-modal="true" aria-hidden="true" style="display: none;">
        <div class="modal-backdrop"></div>
        <div class="modal-content">
            <button class="modal-close" aria-label="Fermer la modal">
                <i class="fas fa-times" aria-hidden="true"></i>
            </button>
            <div class="modal-header">
                <img class="modal-image" src="" alt="" loading="lazy">
                <div class="modal-info">
                    <span class="modal-location"></span>
                    <h3 class="modal-title"></h3>
                    <p class="modal-subtitle"></p>
                </div>
            </div>
            <div class="modal-body"></div>
        </div>
    </div>

    <!-- Chargement des CSS non-critiques après le DOM -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // CSS SECONDAIRES seulement - Les principaux sont déjà dans le head
            const cssFiles = [
                './assets/css/utilities.css',
                './assets/css/sections.css',
                './assets/css/responsive.css',
                './assets/css/language-dropdown.css'
                // './assets/css/drapeaux-clean.css' // ❌ DÉSACTIVÉ - Cause superposition drapeaux
            ];
            
            cssFiles.forEach(href => {
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = href;
                document.head.appendChild(link);
            });
            
            // Plus de CSS mobile-touch-override nécessaire
            
            // Charger les includes avec traduction automatique
            let includesLoaded = 0;
            const totalIncludes = 1; // Seulement header car les autres sont déjà dans le HTML
            
            function checkAllIncludesLoaded() {
                includesLoaded++;
                if (includesLoaded >= totalIncludes) {
                    // Tous les includes sont chargés, forcer la traduction
                    setTimeout(() => {
                        if (window.forceTranslate) {
                            window.forceTranslate();
                        }
                    }, 500);
                }
            }
            
            // Charger les meta tags
            fetch('./includes/head-meta.html')
                .then(response => response.text())
                .then(data => {
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = data;
                    const metaTags = tempDiv.querySelectorAll('meta[property], meta[name="robots"], meta[name="googlebot"], meta[name="bingbot"], link[rel="canonical"], link[rel="alternate"]');
                    metaTags.forEach(tag => document.head.appendChild(tag.cloneNode(true)));
                });
                
            // Header maintenant intégré directement - plus de fetch nécessaire
        });
    </script>

    <!-- External Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/js/all.min.js" 
            crossorigin="anonymous" 
            referrerpolicy="no-referrer"
            defer></script>
    
    <!-- Application Scripts - VERSION CLEAN -->
    <script src="./assets/js/translation-simple.js" defer></script>
    <script src="./assets/js/weather.js" defer></script>
    <script src="./assets/js/forms.js" defer></script>
    
    <!-- CARROUSEL RENFORCÉ -->
    <script src="./assets/js/robust-carousel.js" defer></script>

    <!-- Production Ready -->
    <script>
        window.addEventListener('load', function() {
            console.log('🎯 GolfinThaï - Production Ready');
        });
    </script>

    <!-- Service Worker Registration -->
    <script>
        if ('serviceWorker' in navigator && (location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1')) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('./sw.js')
                    .then(registration => {
                        registration.addEventListener('updatefound', () => {
                            // Silent update handling
                        });
                    })
                    .catch(registrationError => {
                        // Silent error handling
                    });
            });
        }
    </script>

    <!-- Structured Data for SEO MONSTER -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["TravelAgency", "LocalBusiness", "TouristInformationCenter"],
        "name": "GolfinThaï",
        "alternateName": ["Golf in Thai", "Golf Thaïlande"],
        "description": "Expert en voyages golf Thaïlande depuis 10 ans. Séjours sur mesure, parcours premium, accompagnement local professionnel.",
        "url": "https://golfinthai.com",
        "logo": {
            "@type": "ImageObject",
            "url": "https://golfinthai.com/assets/images/logo-golfinthai.jpg",
            "width": 300,
            "height": 300
        },
        "image": [
            "https://golfinthai.com/assets/images/BlackMountainGolfClubHuHin.jpeg",
            "https://golfinthai.com/assets/images/AquellaGolfCountryClub.jpeg",
            "https://golfinthai.com/assets/images/SantiburiSamuiCountryClubKohSamui.jpeg"
        ],
        "telephone": "+***********",
        "email": "<EMAIL>",
        "founder": {
            "@type": "Person",
            "name": "Sébastien Marciano",
            "jobTitle": "Expert Golf Thaïlande",
            "description": "Expert golf avec 10 ans d'expérience en Thaïlande, plus de 50 parcours explorés",
            "knowsAbout": ["Golf Thaïlande", "Voyages sur mesure", "Culture thaïlandaise", "Parcours championship"],
            "hasOccupation": {
                "@type": "Occupation",
                "name": "Guide expert golf Thaïlande",
                "occupationLocation": {
                    "@type": "Country",
                    "name": "Thailand"
                }
            }
        },
        "areaServed": {
            "@type": "Country",
            "name": "Thailand"
        },
        "serviceType": "Voyages golf sur mesure",
        "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": "Services Golf Thaïlande",
            "itemListElement": [
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "Accompagnement sur mesure",
                        "description": "Service personnalisé adapté à vos envies et budget"
                    }
                },
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "Séjours personnalisés",
                        "description": "Découverte des meilleurs parcours avec expertise locale"
                    }
                }
            ]
        },
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "5.0",
            "reviewCount": "25",
            "bestRating": "5",
            "worstRating": "1"
        },
        "review": [
            {
                "@type": "Review",
                "author": {
                    "@type": "Person",
                    "name": "Daniel M."
                },
                "reviewRating": {
                    "@type": "Rating",
                    "ratingValue": "5"
                },
                "reviewBody": "Super expérience avec Sébastien ! Tout était parfaitement organisé."
            }
        ],
        "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+***********",
            "contactType": "customer service",
            "availableLanguage": ["French", "English", "Thai"],
            "areaServed": "FR"
        },
        "sameAs": [
            "https://wa.me/***********",
            "https://instagram.com/golfinthai_"
        ],
        "knowsAbout": [
            "Golf Bangkok", "Golf Phuket", "Golf Chiang Mai", "Golf Hua Hin",
            "Black Mountain Golf Club", "Blue Canyon Country Club", "Red Mountain Golf Club"
        ],
        "priceRange": "€€€",
        "currenciesAccepted": "EUR",
        "paymentAccepted": "Cash, Credit Card, Bank Transfer"
    }
    </script>
    
    <!-- SEO MONSTER META TAGS -->
    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1">
    <meta name="googlebot" content="index, follow">
    <meta name="bingbot" content="index, follow">
    <meta name="author" content="Sébastien Marciano - GolfinThaï">
    <meta name="copyright" content="© 2024-2025 GolfinThaï">
    
    <!-- CANONICAL & HREFLANG -->
    <link rel="canonical" href="https://golfinthai.com/">
    <link rel="alternate" hreflang="fr" href="https://golfinthai.com/">
    <link rel="alternate" hreflang="en" href="https://golfinthai.com/en/">
    <link rel="alternate" hreflang="x-default" href="https://golfinthai.com/">
    
    <!-- OPEN GRAPH -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="GolfinThaï - Expert Golf Thaïlande">
    <meta property="og:description" content="🏌️ Expert golf Thaïlande depuis 10 ans. Voyages sur mesure, parcours premium.">
    <meta property="og:url" content="https://golfinthai.com">
    <meta property="og:image" content="https://golfinthai.com/assets/images/BlackMountainGolfClubHuHin.jpeg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:locale" content="fr_FR">
    
    <!-- TWITTER CARDS -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="GolfinThaï - Expert Golf Thaïlande">
    <meta name="twitter:description" content="🏌️ Expert golf Thaïlande depuis 10 ans. Voyages sur mesure, parcours premium.">
    <meta name="twitter:image" content="https://golfinthai.com/assets/images/BlackMountainGolfClubHuHin.jpeg">
    
    <!-- GEO TARGETING -->
    <meta name="geo.region" content="TH">
    <meta name="geo.placename" content="Thailand">
    <meta name="geo.position" content="13.7563;100.5018">
    
    <!-- MANIFEST PWA -->
    <link rel="manifest" href="/manifest.json">
    
    <!-- PRELOAD CRITICAL RESOURCES -->
    <link rel="preload" href="./assets/css/critical.css" as="style" fetchpriority="high">
    <link rel="preload" href="./assets/css/main.css" as="style">
    <link rel="preload" href="./assets/images/BlackMountainGolfClubHuHin.jpeg" as="image" fetchpriority="high">

    <!-- CSS d'impression -->
    <style media="print">
        @page { margin: 1cm; }
        .nav-bar, .whatsapp-float, .course-modal { display: none !important; }
        .hero-section { height: auto !important; min-height: auto !important; }
    </style>

    <!-- CSS Final Clean -->
    <style>
        /* PROTECTION GÉNÉRALE - Empêcher les zones de disparaître */
        main, section, article, div, 
        .hero-section, .intro-section, .services-section, 
        .destinations-section, .about-section, 
        .testimonials-section, .contact-section {
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* Menu hamburger simple */
        #mobile-menu-btn {
            display: flex;
            touch-action: manipulation;
            cursor: pointer;
            min-width: 44px;
            min-height: 44px;
        }
        
        /* Header protection */
        .nav-bar {
            z-index: 1020 !important;
        }
        
        /* MOBILE FLAGS FIX NUCLEAR */
        @media (max-width: 768px) {
            .container.mx-auto.px-4.sm\:px-6.lg\:px-8 {
                padding-left: 8px !important;
                padding-right: 8px !important;
                max-width: 100% !important;
            }
            
            .flex.justify-between.items-center.h-20.lg\:h-24 {
                gap: 6px !important;
            }
            
            .lg\:hidden {
                gap: 8px !important;
                min-width: 120px !important;
                flex-shrink: 0 !important;
            }
            
            .language-switcher-mobile {
                min-width: 80px !important;
                width: 80px !important;
                flex-shrink: 0 !important;
            }
            
            .lang-btn-mobile {
                padding: 8px 12px !important;
                background: rgba(163, 209, 200, 0.2) !important;
                border: 1px solid rgba(163, 209, 200, 0.5) !important;
                border-radius: 12px !important;
                min-width: 75px !important;
                width: 75px !important;
                height: 42px !important;
                font-size: 16px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }
            
            .lang-dropdown-mobile {
                right: 0 !important;
                left: auto !important;
                min-width: 160px !important;
                width: 160px !important;
                margin-top: 8px !important;
                background: rgba(13, 27, 26, 0.98) !important;
                border: 2px solid rgba(163, 209, 200, 0.6) !important;
                border-radius: 15px !important;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5) !important;
                z-index: 9999 !important;
            }
            
            .lang-option-mobile {
                padding: 12px 18px !important;
                font-size: 16px !important;
                width: 100% !important;
            }
        }
        
        @media (max-width: 480px) {
            .container.mx-auto.px-4.sm\:px-6.lg\:px-8 {
                padding-left: 4px !important;
                padding-right: 4px !important;
            }
            
            .language-switcher-mobile {
                min-width: 70px !important;
                width: 70px !important;
            }
            
            .lang-btn-mobile {
                min-width: 65px !important;
                width: 65px !important;
                height: 38px !important;
                font-size: 15px !important;
            }
        }
    </style>

    <!-- 🏆 GOLF DESCRIPTIONS FORMATTER - PREMIUM TEXT STYLING -->
    <script src="./assets/js/golf-descriptions-formatter.js" defer></script>

    <!-- 🚀 ULTIMATE iPhone Safari MODAL SOLUTION - PROPER LOADING ORDER -->
    <!-- <script src="./assets/js/luxury-image-modal-ultimate-iphone-fix.js" defer></script> -->
    <!-- Replaced by V2 in head -->

</body>
</html>
