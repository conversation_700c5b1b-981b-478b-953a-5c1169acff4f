// =============================================================================
// GOLFINTHAI MODERN - MAIN APPLICATION
// Silicon Valley Architecture - ES6+ Modular Approach
// =============================================================================

import { App } from './core/App.js';
import { Logger } from './utils/Logger.js';

// Initialize logger
const logger = new Logger('GolfinThai');

// Application configuration
const config = {
  debug: import.meta.env.DEV,
  version: '2.0.0',
  name: 'GolfinThaï Modern'
};

// Initialize application when DOM is ready
document.addEventListener('DOMContentLoaded', async () => {
  try {
    logger.info('🚀 Initializing GolfinThaï Modern Application');
    
    // Create and initialize app instance
    const app = new App(config);
    await app.init();
    
    // Make app globally available for debugging in development
    if (config.debug) {
      window.__GOLFINTHAI_APP__ = app;
      logger.debug('App instance available at window.__GOLFINTHAI_APP__');
    }
    
    logger.success('✅ Application initialized successfully');
    
  } catch (error) {
    logger.error('❌ Failed to initialize application:', error);
    
    // Show user-friendly error message
    showErrorMessage('Une erreur est survenue lors du chargement de l\'application.');
  }
});

// Global error handler
window.addEventListener('error', (event) => {
  logger.error('Global error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  logger.error('Unhandled promise rejection:', event.reason);
  event.preventDefault();
});

// Performance monitoring
if ('performance' in window) {
  window.addEventListener('load', () => {
    setTimeout(() => {
      const perfData = performance.getEntriesByType('navigation')[0];
      logger.info('Performance metrics:', {
        domContentLoaded: Math.round(perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart),
        loadComplete: Math.round(perfData.loadEventEnd - perfData.loadEventStart),
        totalTime: Math.round(perfData.loadEventEnd - perfData.fetchStart)
      });
    }, 0);
  });
}

// Service Worker registration for PWA capabilities
if ('serviceWorker' in navigator && !config.debug) {
  window.addEventListener('load', async () => {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js');
      logger.info('Service Worker registered:', registration);
    } catch (error) {
      logger.warn('Service Worker registration failed:', error);
    }
  });
}

// Utility function to show error messages
function showErrorMessage(message) {
  const errorDiv = document.createElement('div');
  errorDiv.className = 'error-message';
  errorDiv.innerHTML = `
    <div class="error-content">
      <h3>Erreur</h3>
      <p>${message}</p>
      <button onclick="location.reload()">Recharger la page</button>
    </div>
  `;
  
  document.body.appendChild(errorDiv);
  
  // Auto-remove after 10 seconds
  setTimeout(() => {
    if (errorDiv.parentNode) {
      errorDiv.parentNode.removeChild(errorDiv);
    }
  }, 10000);
}
