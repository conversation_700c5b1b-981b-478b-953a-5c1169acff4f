// =============================================================================
// LANGUAGE MANAGER
// Modern language switching with dynamic content
// =============================================================================

import { Logger } from '../utils/Logger.js';

export class LanguageManager {
  constructor(app) {
    this.app = app;
    this.logger = new Logger('Language');
    
    // State
    this.currentLanguage = 'fr';
    this.isLoading = false;
    
    // Elements
    this.languageButton = null;
    this.languageDropdown = null;
    this.languageOptions = [];
    
    // Translations
    this.translations = {
      fr: {
        'nav.home': 'Accueil',
        'nav.courses': 'Parcours',
        'nav.destinations': 'Destinations',
        'nav.services': 'Services',
        'nav.contact': 'Contact',
        'hero.title': 'Découvrez le Golf en Thaïlande',
        'hero.subtitle': 'Des parcours exceptionnels dans un cadre paradisiaque',
        'courses.title': 'Nos Parcours Premium',
        'courses.subtitle': 'Découvrez une sélection des plus beaux parcours de golf de Thaïlande'
      },
      en: {
        'nav.home': 'Home',
        'nav.courses': 'Courses',
        'nav.destinations': 'Destinations',
        'nav.services': 'Services',
        'nav.contact': 'Contact',
        'hero.title': 'Discover Golf in Thailand',
        'hero.subtitle': 'Exceptional courses in a paradise setting',
        'courses.title': 'Our Premium Courses',
        'courses.subtitle': 'Discover a selection of the most beautiful golf courses in Thailand'
      },
      th: {
        'nav.home': 'หน้าแรก',
        'nav.courses': 'สนามกอล์ฟ',
        'nav.destinations': 'จุดหมายปลายทาง',
        'nav.services': 'บริการ',
        'nav.contact': 'ติดต่อ',
        'hero.title': 'ค้นพบกอล์ฟในประเทศไทย',
        'hero.subtitle': 'สนามกอล์ฟที่ยอดเยี่ยมในสวรรค์',
        'courses.title': 'สนามกอล์ฟพรีเมียมของเรา',
        'courses.subtitle': 'ค้นพบสนามกอล์ฟที่สวยที่สุดในประเทศไทย'
      }
    };
    
    // Bind methods
    this.handleLanguageButtonClick = this.handleLanguageButtonClick.bind(this);
    this.handleLanguageOptionClick = this.handleLanguageOptionClick.bind(this);
    this.handleOutsideClick = this.handleOutsideClick.bind(this);
  }

  /**
   * Initialize language manager
   */
  async init() {
    try {
      this.logger.debug('Initializing language manager...');
      
      this.findElements();
      this.setupEventListeners();
      this.loadSavedLanguage();
      
      this.logger.success('Language manager initialized');
      
    } catch (error) {
      this.logger.error('Failed to initialize language manager:', error);
      throw error;
    }
  }

  /**
   * Find language elements
   */
  findElements() {
    this.languageButton = document.querySelector('.language-button');
    this.languageDropdown = document.querySelector('.language-dropdown');
    this.languageOptions = Array.from(document.querySelectorAll('.language-option'));
    
    this.logger.debug(`Found language elements: button=${!!this.languageButton}, dropdown=${!!this.languageDropdown}, options=${this.languageOptions.length}`);
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Language button click
    this.languageButton?.addEventListener('click', this.handleLanguageButtonClick);
    
    // Language option clicks
    this.languageOptions.forEach(option => {
      option.addEventListener('click', this.handleLanguageOptionClick);
    });
    
    // Outside click to close dropdown
    document.addEventListener('click', this.handleOutsideClick);
  }

  /**
   * Handle language button click
   */
  handleLanguageButtonClick(event) {
    event.preventDefault();
    event.stopPropagation();
    
    const isOpen = this.languageDropdown?.classList.contains('active');
    
    if (isOpen) {
      this.closeDropdown();
    } else {
      this.openDropdown();
    }
  }

  /**
   * Handle language option click
   */
  handleLanguageOptionClick(event) {
    event.preventDefault();
    event.stopPropagation();
    
    const option = event.currentTarget;
    const language = option.dataset.lang;
    
    if (language && language !== this.currentLanguage) {
      this.changeLanguage(language);
    }
    
    this.closeDropdown();
  }

  /**
   * Handle outside click to close dropdown
   */
  handleOutsideClick(event) {
    const languageSwitcher = event.target.closest('.language-switcher');
    
    if (!languageSwitcher && this.languageDropdown?.classList.contains('active')) {
      this.closeDropdown();
    }
  }

  /**
   * Open language dropdown
   */
  openDropdown() {
    this.languageDropdown?.classList.add('active');
    this.languageButton?.setAttribute('aria-expanded', 'true');
    
    // Focus first option
    const firstOption = this.languageOptions[0];
    firstOption?.focus();
  }

  /**
   * Close language dropdown
   */
  closeDropdown() {
    this.languageDropdown?.classList.remove('active');
    this.languageButton?.setAttribute('aria-expanded', 'false');
  }

  /**
   * Change language
   */
  async changeLanguage(language) {
    if (this.isLoading || language === this.currentLanguage) return;
    
    this.isLoading = true;
    this.logger.debug(`Changing language to: ${language}`);
    
    try {
      // Update current language
      this.currentLanguage = language;
      
      // Save to localStorage
      localStorage.setItem('golfinthai-language', language);
      
      // Update UI
      this.updateLanguageButton(language);
      this.updateLanguageOptions(language);
      this.translateContent(language);
      
      // Update document language
      document.documentElement.lang = language;
      
      // Emit event
      this.app.emit('language:changed', { language });
      
      this.logger.success(`Language changed to: ${language}`);
      
    } catch (error) {
      this.logger.error(`Failed to change language: ${error.message}`);
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Update language button display
   */
  updateLanguageButton(language) {
    if (!this.languageButton) return;
    
    const flagImg = this.languageButton.querySelector('.flag');
    const languageCode = this.languageButton.querySelector('.language-code');
    
    if (flagImg) {
      flagImg.src = `./src/assets/images/flags/${language}.svg`;
      flagImg.alt = this.getLanguageName(language);
    }
    
    if (languageCode) {
      languageCode.textContent = language.toUpperCase();
    }
  }

  /**
   * Update language options active state
   */
  updateLanguageOptions(language) {
    this.languageOptions.forEach(option => {
      const optionLang = option.dataset.lang;
      
      if (optionLang === language) {
        option.classList.add('active');
      } else {
        option.classList.remove('active');
      }
    });
  }

  /**
   * Translate content on the page
   */
  translateContent(language) {
    const translations = this.translations[language];
    if (!translations) return;
    
    // Find all elements with data-translate attribute
    const translatableElements = document.querySelectorAll('[data-translate]');
    
    translatableElements.forEach(element => {
      const key = element.dataset.translate;
      const translation = translations[key];
      
      if (translation) {
        if (element.tagName === 'INPUT' && element.type === 'submit') {
          element.value = translation;
        } else if (element.hasAttribute('placeholder')) {
          element.placeholder = translation;
        } else {
          element.textContent = translation;
        }
      }
    });
    
    this.logger.debug(`Translated ${translatableElements.length} elements`);
  }

  /**
   * Get language name
   */
  getLanguageName(language) {
    const names = {
      fr: 'Français',
      en: 'English',
      th: 'ไทย'
    };
    
    return names[language] || language;
  }

  /**
   * Load saved language from localStorage
   */
  loadSavedLanguage() {
    const savedLanguage = localStorage.getItem('golfinthai-language');
    
    if (savedLanguage && this.translations[savedLanguage]) {
      this.changeLanguage(savedLanguage);
    } else {
      // Detect browser language
      const browserLanguage = navigator.language.split('-')[0];
      const supportedLanguage = this.translations[browserLanguage] ? browserLanguage : 'fr';
      this.changeLanguage(supportedLanguage);
    }
  }

  /**
   * Add translation
   */
  addTranslation(language, key, value) {
    if (!this.translations[language]) {
      this.translations[language] = {};
    }
    
    this.translations[language][key] = value;
  }

  /**
   * Get translation
   */
  getTranslation(key, language = this.currentLanguage) {
    return this.translations[language]?.[key] || key;
  }

  /**
   * Cleanup
   */
  cleanup() {
    // Remove event listeners
    this.languageButton?.removeEventListener('click', this.handleLanguageButtonClick);
    
    this.languageOptions.forEach(option => {
      option.removeEventListener('click', this.handleLanguageOptionClick);
    });
    
    document.removeEventListener('click', this.handleOutsideClick);
    
    this.logger.debug('Language manager cleaned up');
  }
}
