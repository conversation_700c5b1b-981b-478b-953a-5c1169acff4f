// =============================================================================
// NAVIGATION MANAGER
// Modern navigation with smooth scrolling and active states
// =============================================================================

import { Logger } from '../utils/Logger.js';

export class NavigationManager {
  constructor(app) {
    this.app = app;
    this.logger = new Logger('Navigation');
    
    // State
    this.activeSection = 'home';
    this.isScrolling = false;
    
    // Elements
    this.navLinks = [];
    this.sections = [];
    
    // Configuration
    this.config = {
      offset: 80, // Header height offset
      smoothScrollDuration: 800
    };
    
    // Bind methods
    this.handleLinkClick = this.handleLinkClick.bind(this);
    this.handleScroll = this.handleScroll.bind(this);
    this.handleHashChange = this.handleHashChange.bind(this);
  }

  /**
   * Initialize navigation
   */
  async init() {
    try {
      this.logger.debug('Initializing navigation...');
      
      this.findElements();
      this.setupEventListeners();
      this.updateActiveSection();
      
      this.logger.success('Navigation initialized');
      
    } catch (error) {
      this.logger.error('Failed to initialize navigation:', error);
      throw error;
    }
  }

  /**
   * Find navigation elements
   */
  findElements() {
    // Find all navigation links
    this.navLinks = Array.from(document.querySelectorAll('a[href^="#"]'));
    
    // Find all sections
    this.sections = Array.from(document.querySelectorAll('section[id]'));
    
    this.logger.debug(`Found ${this.navLinks.length} nav links and ${this.sections.length} sections`);
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Navigation link clicks
    this.navLinks.forEach(link => {
      link.addEventListener('click', this.handleLinkClick);
    });
    
    // Scroll events (throttled)
    let scrollTimeout;
    window.addEventListener('scroll', () => {
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
      scrollTimeout = setTimeout(this.handleScroll, 10);
    });
    
    // Hash change events
    window.addEventListener('hashchange', this.handleHashChange);
  }

  /**
   * Handle navigation link clicks
   */
  handleLinkClick(event) {
    const link = event.currentTarget;
    const href = link.getAttribute('href');
    
    if (!href || !href.startsWith('#')) return;
    
    event.preventDefault();
    
    const targetId = href.substring(1);
    this.scrollToSection(targetId);
  }

  /**
   * Scroll to section with smooth animation
   */
  scrollToSection(sectionId) {
    const targetSection = document.getElementById(sectionId);
    
    if (!targetSection) {
      this.logger.warning(`Section not found: ${sectionId}`);
      return;
    }
    
    this.isScrolling = true;
    
    const targetPosition = targetSection.offsetTop - this.config.offset;
    
    // Smooth scroll
    window.scrollTo({
      top: targetPosition,
      behavior: 'smooth'
    });
    
    // Update URL hash
    history.pushState(null, null, `#${sectionId}`);
    
    // Update active section
    this.setActiveSection(sectionId);
    
    // Reset scrolling flag after animation
    setTimeout(() => {
      this.isScrolling = false;
    }, this.config.smoothScrollDuration);
    
    this.logger.debug(`Scrolled to section: ${sectionId}`);
  }

  /**
   * Handle scroll events for active section detection
   */
  handleScroll() {
    if (this.isScrolling) return;
    
    const scrollPosition = window.pageYOffset + this.config.offset + 50;
    
    // Find current section
    let currentSection = 'home';
    
    for (const section of this.sections) {
      const sectionTop = section.offsetTop;
      const sectionBottom = sectionTop + section.offsetHeight;
      
      if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
        currentSection = section.id;
        break;
      }
    }
    
    if (currentSection !== this.activeSection) {
      this.setActiveSection(currentSection);
    }
  }

  /**
   * Handle hash change events
   */
  handleHashChange() {
    const hash = window.location.hash.substring(1);
    if (hash && hash !== this.activeSection) {
      this.scrollToSection(hash);
    }
  }

  /**
   * Set active section and update navigation
   */
  setActiveSection(sectionId) {
    this.activeSection = sectionId;
    
    // Update navigation links
    this.navLinks.forEach(link => {
      const href = link.getAttribute('href');
      const linkSectionId = href ? href.substring(1) : '';
      
      if (linkSectionId === sectionId) {
        link.classList.add('active');
      } else {
        link.classList.remove('active');
      }
    });
    
    // Emit event
    this.app.emit('navigation:section-changed', {
      section: sectionId,
      previousSection: this.activeSection
    });
    
    this.logger.debug(`Active section changed to: ${sectionId}`);
  }

  /**
   * Update active section based on current hash
   */
  updateActiveSection() {
    const hash = window.location.hash.substring(1);
    const sectionId = hash || 'home';
    this.setActiveSection(sectionId);
  }

  /**
   * Setup navigation (called by App)
   */
  async setupNavigation() {
    // Re-find elements in case DOM changed
    this.findElements();
    this.updateActiveSection();
    this.logger.debug('Navigation setup completed');
  }

  /**
   * Cleanup
   */
  cleanup() {
    // Remove event listeners
    this.navLinks.forEach(link => {
      link.removeEventListener('click', this.handleLinkClick);
    });
    
    window.removeEventListener('scroll', this.handleScroll);
    window.removeEventListener('hashchange', this.handleHashChange);
    
    this.logger.debug('Navigation cleaned up');
  }
}
