// =============================================================================
// ANIMATION MANAGER
// Modern animations with Intersection Observer
// =============================================================================

import { Logger } from '../utils/Logger.js';

export class AnimationManager {
  constructor(app) {
    this.app = app;
    this.logger = new Logger('Animation');
    
    // State
    this.observers = new Map();
    this.animatedElements = new Set();
    
    // Configuration
    this.config = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px',
      animationDelay: 100,
      staggerDelay: 150
    };
    
    // Animation classes
    this.animationClasses = {
      'fade-in': 'animate-fade-in',
      'slide-up': 'animate-slide-up',
      'slide-down': 'animate-slide-down',
      'slide-left': 'animate-slide-left',
      'slide-right': 'animate-slide-right',
      'scale-in': 'animate-scale-in',
      'rotate-in': 'animate-rotate-in'
    };
  }

  /**
   * Initialize animation manager
   */
  async init() {
    try {
      this.logger.debug('Initializing animation manager...');
      
      this.setupIntersectionObserver();
      this.findAnimatableElements();
      this.setupScrollAnimations();
      
      this.logger.success('Animation manager initialized');
      
    } catch (error) {
      this.logger.error('Failed to initialize animation manager:', error);
      throw error;
    }
  }

  /**
   * Setup intersection observer
   */
  setupIntersectionObserver() {
    if (!('IntersectionObserver' in window)) {
      this.logger.warning('IntersectionObserver not supported, animations disabled');
      return;
    }
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.animateElement(entry.target);
        }
      });
    }, {
      threshold: this.config.threshold,
      rootMargin: this.config.rootMargin
    });
    
    this.observers.set('main', observer);
  }

  /**
   * Find elements that should be animated
   */
  findAnimatableElements() {
    const selectors = [
      '[data-animate]',
      '.animate-on-scroll',
      '.fade-in-on-scroll',
      '.slide-up-on-scroll',
      '.course-card',
      '.section-header',
      '.testimonial-card'
    ];
    
    const elements = document.querySelectorAll(selectors.join(', '));
    
    elements.forEach(element => {
      this.prepareElement(element);
    });
    
    this.logger.debug(`Found ${elements.length} animatable elements`);
  }

  /**
   * Prepare element for animation
   */
  prepareElement(element) {
    // Get animation type
    const animationType = element.dataset.animate || 'fade-in';
    
    // Add initial state class
    element.classList.add('animate-initial');
    
    // Set animation type
    element.dataset.animationType = animationType;
    
    // Add to observer
    const observer = this.observers.get('main');
    if (observer) {
      observer.observe(element);
    }
  }

  /**
   * Animate element when it comes into view
   */
  animateElement(element) {
    if (this.animatedElements.has(element)) return;
    
    const animationType = element.dataset.animationType || 'fade-in';
    const delay = parseInt(element.dataset.animateDelay) || 0;
    
    // Mark as animated
    this.animatedElements.add(element);
    
    // Apply animation with delay
    setTimeout(() => {
      element.classList.remove('animate-initial');
      element.classList.add('animate-in');
      
      // Add specific animation class
      const animationClass = this.animationClasses[animationType];
      if (animationClass) {
        element.classList.add(animationClass);
      }
      
      // Emit event
      this.app.emit('animation:element-animated', { element, animationType });
      
    }, delay);
    
    // Stop observing this element
    const observer = this.observers.get('main');
    if (observer) {
      observer.unobserve(element);
    }
    
    this.logger.debug(`Animated element with type: ${animationType}`);
  }

  /**
   * Setup scroll-based animations
   */
  setupScrollAnimations() {
    // Parallax elements
    const parallaxElements = document.querySelectorAll('[data-parallax]');
    
    if (parallaxElements.length > 0) {
      this.setupParallaxAnimation(parallaxElements);
    }
    
    // Header scroll effects
    this.setupHeaderScrollEffects();
  }

  /**
   * Setup parallax animation
   */
  setupParallaxAnimation(elements) {
    let ticking = false;
    
    const updateParallax = () => {
      const scrollY = window.pageYOffset;
      
      elements.forEach(element => {
        const speed = parseFloat(element.dataset.parallax) || 0.5;
        const yPos = -(scrollY * speed);
        element.style.transform = `translateY(${yPos}px)`;
      });
      
      ticking = false;
    };
    
    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(updateParallax);
        ticking = true;
      }
    };
    
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    this.logger.debug(`Setup parallax for ${elements.length} elements`);
  }

  /**
   * Setup header scroll effects
   */
  setupHeaderScrollEffects() {
    const header = document.querySelector('.header');
    if (!header) return;
    
    let lastScrollY = 0;
    let ticking = false;
    
    const updateHeader = () => {
      const scrollY = window.pageYOffset;
      
      // Add scrolled class when scrolled
      if (scrollY > 50) {
        header.classList.add('scrolled');
      } else {
        header.classList.remove('scrolled');
      }
      
      // Hide/show header on scroll direction
      if (scrollY > lastScrollY && scrollY > 100) {
        header.classList.add('header-hidden');
      } else {
        header.classList.remove('header-hidden');
      }
      
      lastScrollY = scrollY;
      ticking = false;
    };
    
    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(updateHeader);
        ticking = true;
      }
    };
    
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    this.logger.debug('Header scroll effects setup');
  }

  /**
   * Animate elements with stagger effect
   */
  animateStaggered(elements, animationType = 'fade-in') {
    elements.forEach((element, index) => {
      const delay = index * this.config.staggerDelay;
      
      setTimeout(() => {
        element.classList.remove('animate-initial');
        element.classList.add('animate-in');
        
        const animationClass = this.animationClasses[animationType];
        if (animationClass) {
          element.classList.add(animationClass);
        }
      }, delay);
    });
    
    this.logger.debug(`Animated ${elements.length} elements with stagger`);
  }

  /**
   * Add custom animation
   */
  addAnimation(name, className) {
    this.animationClasses[name] = className;
  }

  /**
   * Trigger animation manually
   */
  triggerAnimation(element, animationType = 'fade-in') {
    if (typeof element === 'string') {
      element = document.querySelector(element);
    }
    
    if (!element) return;
    
    this.animateElement(element);
  }

  /**
   * Reset element animation
   */
  resetAnimation(element) {
    if (typeof element === 'string') {
      element = document.querySelector(element);
    }
    
    if (!element) return;
    
    // Remove animation classes
    Object.values(this.animationClasses).forEach(className => {
      element.classList.remove(className);
    });
    
    element.classList.remove('animate-in');
    element.classList.add('animate-initial');
    
    // Remove from animated set
    this.animatedElements.delete(element);
    
    // Re-observe element
    const observer = this.observers.get('main');
    if (observer) {
      observer.observe(element);
    }
  }

  /**
   * Setup animations (called by App)
   */
  async setupAnimations() {
    // Re-find elements in case DOM changed
    this.findAnimatableElements();
    this.logger.debug('Animation setup completed');
  }

  /**
   * Cleanup
   */
  cleanup() {
    // Disconnect all observers
    for (const [name, observer] of this.observers) {
      observer.disconnect();
    }
    
    this.observers.clear();
    this.animatedElements.clear();
    
    this.logger.debug('Animation manager cleaned up');
  }
}
