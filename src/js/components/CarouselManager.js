// =============================================================================
// CAROUSEL MANAGER
// Modern carousel with touch support and accessibility
// =============================================================================

import { Logger } from '../utils/Logger.js';

export class CarouselManager {
  constructor(app) {
    this.app = app;
    this.logger = new Logger('Carousel');
    
    // Active carousels
    this.carousels = new Map();
    
    // Configuration
    this.config = {
      autoPlay: true,
      autoPlayInterval: 5000,
      pauseOnHover: true,
      pauseOnFocus: true,
      enableTouch: true,
      enableKeyboard: true,
      loop: true,
      animationDuration: 500
    };
  }

  /**
   * Initialize carousel system
   */
  async init() {
    try {
      this.logger.debug('Initializing carousel system...');
      
      await this.setupCarousels();
      
      this.logger.success('Carousel system initialized');
      
    } catch (error) {
      this.logger.error('Failed to initialize carousel system:', error);
      throw error;
    }
  }

  /**
   * Setup all carousels
   */
  async setupCarousels() {
    const carouselElements = document.querySelectorAll('[data-carousel], .carousel');
    
    for (const element of carouselElements) {
      try {
        await this.initializeCarousel(element);
      } catch (error) {
        this.logger.error('Failed to initialize carousel:', error);
      }
    }
    
    this.logger.debug(`Initialized ${carouselElements.length} carousels`);
  }

  /**
   * Initialize individual carousel
   */
  async initializeCarousel(element) {
    const id = element.id || `carousel-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    element.id = id;
    
    // Create carousel instance
    const carousel = new Carousel(element, this.config, this.app);
    await carousel.init();
    
    // Store carousel
    this.carousels.set(id, carousel);
    
    this.logger.debug(`Carousel ${id} initialized`);
  }

  /**
   * Get carousel by ID
   */
  getCarousel(id) {
    return this.carousels.get(id);
  }

  /**
   * Cleanup
   */
  cleanup() {
    for (const [id, carousel] of this.carousels) {
      carousel.cleanup();
    }
    
    this.carousels.clear();
    this.logger.debug('Carousel system cleaned up');
  }
}

/**
 * Individual Carousel Class
 */
class Carousel {
  constructor(element, config, app) {
    this.element = element;
    this.config = { ...config, ...this.parseDataAttributes() };
    this.app = app;
    this.logger = new Logger(`Carousel:${element.id}`);
    
    // State
    this.currentIndex = 0;
    this.isAnimating = false;
    this.autoPlayTimer = null;
    this.isPaused = false;
    
    // Elements
    this.slides = [];
    this.indicators = [];
    this.prevButton = null;
    this.nextButton = null;
    this.indicatorContainer = null;
    
    // Touch handling
    this.touchStartX = 0;
    this.touchEndX = 0;
    this.touchThreshold = 50;
    
    // Bind methods
    this.handlePrevClick = this.handlePrevClick.bind(this);
    this.handleNextClick = this.handleNextClick.bind(this);
    this.handleIndicatorClick = this.handleIndicatorClick.bind(this);
    this.handleKeyDown = this.handleKeyDown.bind(this);
    this.handleTouchStart = this.handleTouchStart.bind(this);
    this.handleTouchEnd = this.handleTouchEnd.bind(this);
    this.handleMouseEnter = this.handleMouseEnter.bind(this);
    this.handleMouseLeave = this.handleMouseLeave.bind(this);
    this.handleFocus = this.handleFocus.bind(this);
    this.handleBlur = this.handleBlur.bind(this);
  }

  /**
   * Parse data attributes for configuration
   */
  parseDataAttributes() {
    const data = this.element.dataset;
    return {
      autoPlay: data.autoplay !== 'false',
      autoPlayInterval: parseInt(data.interval) || 5000,
      loop: data.loop !== 'false',
      pauseOnHover: data.pauseOnHover !== 'false',
      enableTouch: data.touch !== 'false',
      enableKeyboard: data.keyboard !== 'false'
    };
  }

  /**
   * Initialize carousel
   */
  async init() {
    try {
      this.setupStructure();
      this.setupEventListeners();
      this.setupAccessibility();
      
      if (this.config.autoPlay) {
        this.startAutoPlay();
      }
      
      this.logger.debug('Carousel initialized');
      
    } catch (error) {
      this.logger.error('Failed to initialize carousel:', error);
      throw error;
    }
  }

  /**
   * Setup carousel structure
   */
  setupStructure() {
    // Find or create slides container
    let slidesContainer = this.element.querySelector('.carousel-slides');
    if (!slidesContainer) {
      slidesContainer = document.createElement('div');
      slidesContainer.className = 'carousel-slides';
      
      // Move existing children to slides container
      while (this.element.firstChild) {
        slidesContainer.appendChild(this.element.firstChild);
      }
      
      this.element.appendChild(slidesContainer);
    }
    
    // Get slides
    this.slides = Array.from(slidesContainer.children);
    
    if (this.slides.length === 0) {
      throw new Error('No slides found in carousel');
    }
    
    // Add slide classes and setup
    this.slides.forEach((slide, index) => {
      slide.classList.add('carousel-slide');
      slide.setAttribute('data-slide-index', index);
      
      if (index === 0) {
        slide.classList.add('active');
      }
    });
    
    // Create navigation buttons
    this.createNavigationButtons();
    
    // Create indicators
    this.createIndicators();
    
    // Set initial state
    this.updateCarousel(false);
  }

  /**
   * Create navigation buttons
   */
  createNavigationButtons() {
    // Previous button
    this.prevButton = document.createElement('button');
    this.prevButton.className = 'carousel-btn carousel-prev';
    this.prevButton.innerHTML = '<i class="fas fa-chevron-left"></i>';
    this.prevButton.setAttribute('aria-label', 'Image précédente');
    
    // Next button
    this.nextButton = document.createElement('button');
    this.nextButton.className = 'carousel-btn carousel-next';
    this.nextButton.innerHTML = '<i class="fas fa-chevron-right"></i>';
    this.nextButton.setAttribute('aria-label', 'Image suivante');
    
    // Add to carousel
    this.element.appendChild(this.prevButton);
    this.element.appendChild(this.nextButton);
  }

  /**
   * Create indicators
   */
  createIndicators() {
    if (this.slides.length <= 1) return;
    
    this.indicatorContainer = document.createElement('div');
    this.indicatorContainer.className = 'carousel-indicators';
    
    this.slides.forEach((_, index) => {
      const indicator = document.createElement('button');
      indicator.className = 'carousel-indicator';
      indicator.setAttribute('data-slide-to', index);
      indicator.setAttribute('aria-label', `Aller à l'image ${index + 1}`);
      
      if (index === 0) {
        indicator.classList.add('active');
      }
      
      this.indicators.push(indicator);
      this.indicatorContainer.appendChild(indicator);
    });
    
    this.element.appendChild(this.indicatorContainer);
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Navigation buttons
    this.prevButton?.addEventListener('click', this.handlePrevClick);
    this.nextButton?.addEventListener('click', this.handleNextClick);
    
    // Indicators
    this.indicators.forEach(indicator => {
      indicator.addEventListener('click', this.handleIndicatorClick);
    });
    
    // Keyboard navigation
    if (this.config.enableKeyboard) {
      this.element.addEventListener('keydown', this.handleKeyDown);
    }
    
    // Touch events
    if (this.config.enableTouch) {
      this.element.addEventListener('touchstart', this.handleTouchStart, { passive: true });
      this.element.addEventListener('touchend', this.handleTouchEnd, { passive: true });
    }
    
    // Pause on hover/focus
    if (this.config.pauseOnHover) {
      this.element.addEventListener('mouseenter', this.handleMouseEnter);
      this.element.addEventListener('mouseleave', this.handleMouseLeave);
    }
    
    if (this.config.pauseOnFocus) {
      this.element.addEventListener('focusin', this.handleFocus);
      this.element.addEventListener('focusout', this.handleBlur);
    }
  }

  /**
   * Setup accessibility
   */
  setupAccessibility() {
    this.element.setAttribute('role', 'region');
    this.element.setAttribute('aria-label', 'Carrousel d\'images');
    this.element.setAttribute('tabindex', '0');
    
    // Live region for screen readers
    const liveRegion = document.createElement('div');
    liveRegion.className = 'carousel-live-region sr-only';
    liveRegion.setAttribute('aria-live', 'polite');
    liveRegion.setAttribute('aria-atomic', 'true');
    this.element.appendChild(liveRegion);
    
    this.liveRegion = liveRegion;
  }

  /**
   * Handle previous button click
   */
  handlePrevClick(event) {
    event.preventDefault();
    this.prev();
  }

  /**
   * Handle next button click
   */
  handleNextClick(event) {
    event.preventDefault();
    this.next();
  }

  /**
   * Handle indicator click
   */
  handleIndicatorClick(event) {
    event.preventDefault();
    const index = parseInt(event.target.getAttribute('data-slide-to'));
    this.goTo(index);
  }

  /**
   * Handle keyboard navigation
   */
  handleKeyDown(event) {
    switch (event.key) {
      case 'ArrowLeft':
        event.preventDefault();
        this.prev();
        break;
      case 'ArrowRight':
        event.preventDefault();
        this.next();
        break;
      case 'Home':
        event.preventDefault();
        this.goTo(0);
        break;
      case 'End':
        event.preventDefault();
        this.goTo(this.slides.length - 1);
        break;
    }
  }

  /**
   * Handle touch start
   */
  handleTouchStart(event) {
    this.touchStartX = event.touches[0].clientX;
  }

  /**
   * Handle touch end
   */
  handleTouchEnd(event) {
    this.touchEndX = event.changedTouches[0].clientX;
    this.handleSwipe();
  }

  /**
   * Handle swipe gesture
   */
  handleSwipe() {
    const diff = this.touchStartX - this.touchEndX;
    
    if (Math.abs(diff) > this.touchThreshold) {
      if (diff > 0) {
        this.next(); // Swipe left - next slide
      } else {
        this.prev(); // Swipe right - previous slide
      }
    }
  }

  /**
   * Handle mouse enter (pause autoplay)
   */
  handleMouseEnter() {
    this.pause();
  }

  /**
   * Handle mouse leave (resume autoplay)
   */
  handleMouseLeave() {
    this.resume();
  }

  /**
   * Handle focus (pause autoplay)
   */
  handleFocus() {
    this.pause();
  }

  /**
   * Handle blur (resume autoplay)
   */
  handleBlur() {
    this.resume();
  }

  /**
   * Go to previous slide
   */
  prev() {
    const newIndex = this.currentIndex === 0 
      ? (this.config.loop ? this.slides.length - 1 : 0)
      : this.currentIndex - 1;
    
    this.goTo(newIndex);
  }

  /**
   * Go to next slide
   */
  next() {
    const newIndex = this.currentIndex === this.slides.length - 1
      ? (this.config.loop ? 0 : this.slides.length - 1)
      : this.currentIndex + 1;
    
    this.goTo(newIndex);
  }

  /**
   * Go to specific slide
   */
  async goTo(index) {
    if (index === this.currentIndex || this.isAnimating) return;
    
    this.isAnimating = true;
    
    try {
      const previousIndex = this.currentIndex;
      this.currentIndex = index;
      
      await this.updateCarousel(true);
      
      // Update live region for screen readers
      if (this.liveRegion) {
        this.liveRegion.textContent = `Image ${index + 1} sur ${this.slides.length}`;
      }
      
      this.app.emit('carousel:slide-changed', {
        carousel: this,
        currentIndex: index,
        previousIndex
      });
      
    } finally {
      this.isAnimating = false;
    }
  }

  /**
   * Update carousel display
   */
  async updateCarousel(animate = true) {
    // Update slides
    this.slides.forEach((slide, index) => {
      slide.classList.toggle('active', index === this.currentIndex);
    });
    
    // Update indicators
    this.indicators.forEach((indicator, index) => {
      indicator.classList.toggle('active', index === this.currentIndex);
    });
    
    // Update navigation buttons
    if (!this.config.loop) {
      this.prevButton?.classList.toggle('disabled', this.currentIndex === 0);
      this.nextButton?.classList.toggle('disabled', this.currentIndex === this.slides.length - 1);
    }
    
    if (animate) {
      await this.wait(this.config.animationDuration);
    }
  }

  /**
   * Start autoplay
   */
  startAutoPlay() {
    if (!this.config.autoPlay || this.slides.length <= 1) return;
    
    this.autoPlayTimer = setInterval(() => {
      if (!this.isPaused) {
        this.next();
      }
    }, this.config.autoPlayInterval);
  }

  /**
   * Stop autoplay
   */
  stopAutoPlay() {
    if (this.autoPlayTimer) {
      clearInterval(this.autoPlayTimer);
      this.autoPlayTimer = null;
    }
  }

  /**
   * Pause autoplay
   */
  pause() {
    this.isPaused = true;
  }

  /**
   * Resume autoplay
   */
  resume() {
    this.isPaused = false;
  }

  /**
   * Utility: Wait for specified duration
   */
  wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Cleanup
   */
  cleanup() {
    this.stopAutoPlay();
    
    // Remove event listeners
    this.prevButton?.removeEventListener('click', this.handlePrevClick);
    this.nextButton?.removeEventListener('click', this.handleNextClick);
    
    this.indicators.forEach(indicator => {
      indicator.removeEventListener('click', this.handleIndicatorClick);
    });
    
    this.element.removeEventListener('keydown', this.handleKeyDown);
    this.element.removeEventListener('touchstart', this.handleTouchStart);
    this.element.removeEventListener('touchend', this.handleTouchEnd);
    this.element.removeEventListener('mouseenter', this.handleMouseEnter);
    this.element.removeEventListener('mouseleave', this.handleMouseLeave);
    this.element.removeEventListener('focusin', this.handleFocus);
    this.element.removeEventListener('focusout', this.handleBlur);
    
    this.logger.debug('Carousel cleaned up');
  }
}
