// =============================================================================
// MOBILE MENU MANAGER
// Modern mobile navigation with smooth animations
// =============================================================================

import { Logger } from '../utils/Logger.js';

export class MobileMenuManager {
  constructor(app) {
    this.app = app;
    this.logger = new Logger('MobileMenu');
    
    // State
    this.isOpen = false;
    this.isAnimating = false;
    
    // Elements
    this.menuButton = null;
    this.closeButton = null;
    this.menuOverlay = null;
    this.menuContent = null;
    this.menuLinks = [];
    
    // Configuration
    this.config = {
      animationDuration: 300,
      breakpoint: 1024, // Mobile menu active below this width
      closeOnLinkClick: true,
      closeOnOutsideClick: true
    };
    
    // Bind methods
    this.handleMenuToggle = this.handleMenuToggle.bind(this);
    this.handleCloseClick = this.handleCloseClick.bind(this);
    this.handleOverlayClick = this.handleOverlayClick.bind(this);
    this.handleLinkClick = this.handleLinkClick.bind(this);
    this.handleKeyDown = this.handleKeyDown.bind(this);
    this.handleResize = this.handleResize.bind(this);
  }

  /**
   * Initialize mobile menu
   */
  async init() {
    try {
      this.logger.debug('Initializing mobile menu...');
      
      await this.findElements();
      this.setupEventListeners();
      this.setupAccessibility();
      
      this.logger.success('Mobile menu initialized');
      
    } catch (error) {
      this.logger.error('Failed to initialize mobile menu:', error);
      throw error;
    }
  }

  /**
   * Find DOM elements
   */
  async findElements() {
    // Wait for elements to be available
    await this.waitForElement('#mobile-menu-btn');
    
    this.menuButton = document.getElementById('mobile-menu-btn');
    this.closeButton = document.getElementById('mobile-menu-close');
    this.menuOverlay = document.getElementById('mobile-menu-overlay');
    this.menuContent = document.getElementById('mobile-menu-content');
    
    if (!this.menuButton || !this.menuOverlay) {
      throw new Error('Required mobile menu elements not found');
    }
    
    // Find menu links
    this.menuLinks = Array.from(this.menuOverlay.querySelectorAll('a[href^="#"]'));
    
    this.logger.debug('Mobile menu elements found:', {
      button: !!this.menuButton,
      overlay: !!this.menuOverlay,
      links: this.menuLinks.length
    });
  }

  /**
   * Wait for element to be available in DOM
   */
  waitForElement(selector, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const element = document.querySelector(selector);
      
      if (element) {
        resolve(element);
        return;
      }
      
      const observer = new MutationObserver((mutations, obs) => {
        const element = document.querySelector(selector);
        if (element) {
          obs.disconnect();
          resolve(element);
        }
      });
      
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
      
      // Timeout fallback
      setTimeout(() => {
        observer.disconnect();
        reject(new Error(`Element ${selector} not found within ${timeout}ms`));
      }, timeout);
    });
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Menu toggle button
    this.menuButton?.addEventListener('click', this.handleMenuToggle);
    
    // Close button
    this.closeButton?.addEventListener('click', this.handleCloseClick);
    
    // Overlay click (close on outside click)
    if (this.config.closeOnOutsideClick) {
      this.menuOverlay?.addEventListener('click', this.handleOverlayClick);
    }
    
    // Menu links
    if (this.config.closeOnLinkClick) {
      this.menuLinks.forEach(link => {
        link.addEventListener('click', this.handleLinkClick);
      });
    }
    
    // Keyboard events
    document.addEventListener('keydown', this.handleKeyDown);
    
    // Window resize
    window.addEventListener('resize', this.handleResize);
    
    // App events
    this.app.on('app:resize', this.handleResize);
  }

  /**
   * Setup accessibility attributes
   */
  setupAccessibility() {
    if (this.menuButton) {
      this.menuButton.setAttribute('aria-expanded', 'false');
      this.menuButton.setAttribute('aria-controls', 'mobile-menu-overlay');
      this.menuButton.setAttribute('aria-label', 'Ouvrir le menu de navigation');
    }
    
    if (this.menuOverlay) {
      this.menuOverlay.setAttribute('role', 'dialog');
      this.menuOverlay.setAttribute('aria-modal', 'true');
      this.menuOverlay.setAttribute('aria-label', 'Menu de navigation');
    }
  }

  /**
   * Handle menu toggle
   */
  async handleMenuToggle(event) {
    event.preventDefault();
    
    if (this.isAnimating) return;
    
    if (this.isOpen) {
      await this.closeMenu();
    } else {
      await this.openMenu();
    }
  }

  /**
   * Handle close button click
   */
  async handleCloseClick(event) {
    event.preventDefault();
    await this.closeMenu();
  }

  /**
   * Handle overlay click (close on outside click)
   */
  async handleOverlayClick(event) {
    if (event.target === this.menuOverlay) {
      await this.closeMenu();
    }
  }

  /**
   * Handle menu link click
   */
  async handleLinkClick(event) {
    // Close menu after a short delay to allow navigation
    setTimeout(async () => {
      await this.closeMenu();
    }, 150);
  }

  /**
   * Handle keyboard events
   */
  async handleKeyDown(event) {
    if (!this.isOpen) return;
    
    switch (event.key) {
      case 'Escape':
        event.preventDefault();
        await this.closeMenu();
        break;
        
      case 'Tab':
        this.handleTabNavigation(event);
        break;
    }
  }

  /**
   * Handle tab navigation within menu
   */
  handleTabNavigation(event) {
    if (!this.menuOverlay) return;
    
    const focusableElements = this.menuOverlay.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];
    
    if (event.shiftKey) {
      if (document.activeElement === firstElement) {
        event.preventDefault();
        lastElement.focus();
      }
    } else {
      if (document.activeElement === lastElement) {
        event.preventDefault();
        firstElement.focus();
      }
    }
  }

  /**
   * Handle window resize
   */
  handleResize() {
    // Close menu on desktop
    if (window.innerWidth >= this.config.breakpoint && this.isOpen) {
      this.closeMenu();
    }
  }

  /**
   * Open menu
   */
  async openMenu() {
    if (this.isOpen || this.isAnimating) return;
    
    this.isAnimating = true;
    this.logger.debug('Opening mobile menu');
    
    try {
      // Update state
      this.isOpen = true;
      
      // Update accessibility
      this.menuButton?.setAttribute('aria-expanded', 'true');
      
      // Show overlay
      if (this.menuOverlay) {
        this.menuOverlay.classList.add('active');
        
        // Prevent body scroll
        document.body.style.overflow = 'hidden';
        
        // Focus first focusable element
        setTimeout(() => {
          const firstFocusable = this.menuOverlay.querySelector(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
          );
          firstFocusable?.focus();
        }, 100);
      }
      
      // Wait for animation
      await this.wait(this.config.animationDuration);
      
      // Emit event
      this.app.emit('mobileMenu:opened');
      
    } finally {
      this.isAnimating = false;
    }
  }

  /**
   * Close menu
   */
  async closeMenu() {
    if (!this.isOpen || this.isAnimating) return;
    
    this.isAnimating = true;
    this.logger.debug('Closing mobile menu');
    
    try {
      // Update state
      this.isOpen = false;
      
      // Update accessibility
      this.menuButton?.setAttribute('aria-expanded', 'false');
      
      // Hide overlay
      if (this.menuOverlay) {
        this.menuOverlay.classList.remove('active');
        
        // Restore body scroll
        document.body.style.overflow = '';
        
        // Return focus to menu button
        this.menuButton?.focus();
      }
      
      // Wait for animation
      await this.wait(this.config.animationDuration);
      
      // Emit event
      this.app.emit('mobileMenu:closed');
      
    } finally {
      this.isAnimating = false;
    }
  }

  /**
   * Setup mobile menu (called by App)
   */
  async setupMobileMenu() {
    // This method is called by the App class
    // The actual initialization happens in init()
    this.logger.debug('Mobile menu setup completed');
  }

  /**
   * Utility: Wait for specified duration
   */
  wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Cleanup
   */
  cleanup() {
    // Remove event listeners
    this.menuButton?.removeEventListener('click', this.handleMenuToggle);
    this.closeButton?.removeEventListener('click', this.handleCloseClick);
    this.menuOverlay?.removeEventListener('click', this.handleOverlayClick);
    
    this.menuLinks.forEach(link => {
      link.removeEventListener('click', this.handleLinkClick);
    });
    
    document.removeEventListener('keydown', this.handleKeyDown);
    window.removeEventListener('resize', this.handleResize);
    
    // Restore body scroll
    document.body.style.overflow = '';
    
    this.logger.debug('Mobile menu cleaned up');
  }
}
