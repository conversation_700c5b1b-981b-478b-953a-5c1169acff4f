// =============================================================================
// FORM MANAGER
// Modern form handling with validation and submission
// =============================================================================

import { Logger } from '../utils/Logger.js';

export class FormManager {
  constructor(app) {
    this.app = app;
    this.logger = new Logger('Form');
    
    // State
    this.forms = new Map();
    
    // Configuration
    this.config = {
      validateOnInput: true,
      validateOnBlur: true,
      showSuccessMessage: true,
      autoHideMessages: 5000
    };
  }

  /**
   * Initialize form manager
   */
  async init() {
    try {
      this.logger.debug('Initializing form manager...');
      
      this.findForms();
      this.setupForms();
      
      this.logger.success('Form manager initialized');
      
    } catch (error) {
      this.logger.error('Failed to initialize form manager:', error);
      throw error;
    }
  }

  /**
   * Find all forms
   */
  findForms() {
    const formElements = document.querySelectorAll('form[data-form], .contact-form, .booking-form');
    
    formElements.forEach(formElement => {
      const formId = formElement.id || `form-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      formElement.id = formId;
      
      const form = new Form(formElement, this.config, this.app);
      this.forms.set(formId, form);
    });
    
    this.logger.debug(`Found ${this.forms.size} forms`);
  }

  /**
   * Setup all forms
   */
  setupForms() {
    for (const [id, form] of this.forms) {
      try {
        form.init();
      } catch (error) {
        this.logger.error(`Failed to setup form ${id}:`, error);
      }
    }
  }

  /**
   * Get form by ID
   */
  getForm(id) {
    return this.forms.get(id);
  }

  /**
   * Setup forms (called by App)
   */
  async setupForms() {
    // Re-find forms in case DOM changed
    this.findForms();
    this.logger.debug('Form setup completed');
  }

  /**
   * Cleanup
   */
  cleanup() {
    for (const [id, form] of this.forms) {
      form.cleanup();
    }
    
    this.forms.clear();
    this.logger.debug('Form manager cleaned up');
  }
}

/**
 * Individual Form Class
 */
class Form {
  constructor(element, config, app) {
    this.element = element;
    this.config = config;
    this.app = app;
    this.logger = new Logger(`Form:${element.id}`);
    
    // State
    this.isSubmitting = false;
    this.isValid = false;
    
    // Elements
    this.fields = [];
    this.submitButton = null;
    this.messageContainer = null;
    
    // Validation rules
    this.validationRules = {
      required: (value) => value.trim() !== '',
      email: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
      phone: (value) => /^[\+]?[1-9][\d]{0,15}$/.test(value.replace(/[\s\-\(\)]/g, '')),
      minLength: (value, min) => value.length >= min,
      maxLength: (value, max) => value.length <= max
    };
    
    // Bind methods
    this.handleSubmit = this.handleSubmit.bind(this);
    this.handleFieldInput = this.handleFieldInput.bind(this);
    this.handleFieldBlur = this.handleFieldBlur.bind(this);
  }

  /**
   * Initialize form
   */
  init() {
    this.findElements();
    this.setupEventListeners();
    this.setupValidation();
    
    this.logger.debug('Form initialized');
  }

  /**
   * Find form elements
   */
  findElements() {
    // Find form fields
    this.fields = Array.from(this.element.querySelectorAll('input, textarea, select'));
    
    // Find submit button
    this.submitButton = this.element.querySelector('button[type="submit"], input[type="submit"]');
    
    // Find or create message container
    this.messageContainer = this.element.querySelector('.form-message');
    if (!this.messageContainer) {
      this.messageContainer = document.createElement('div');
      this.messageContainer.className = 'form-message';
      this.element.appendChild(this.messageContainer);
    }
    
    this.logger.debug(`Found ${this.fields.length} fields`);
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Form submission
    this.element.addEventListener('submit', this.handleSubmit);
    
    // Field validation
    this.fields.forEach(field => {
      if (this.config.validateOnInput) {
        field.addEventListener('input', this.handleFieldInput);
      }
      
      if (this.config.validateOnBlur) {
        field.addEventListener('blur', this.handleFieldBlur);
      }
    });
  }

  /**
   * Setup validation
   */
  setupValidation() {
    this.fields.forEach(field => {
      // Add validation attributes based on field type
      if (field.type === 'email' && !field.hasAttribute('pattern')) {
        field.setAttribute('pattern', '[^@\\s]+@[^@\\s]+\\.[^@\\s]+');
      }
      
      // Add ARIA attributes
      if (field.hasAttribute('required')) {
        field.setAttribute('aria-required', 'true');
      }
    });
  }

  /**
   * Handle form submission
   */
  async handleSubmit(event) {
    event.preventDefault();
    
    if (this.isSubmitting) return;
    
    this.logger.debug('Form submission started');
    
    // Validate form
    const isValid = this.validateForm();
    
    if (!isValid) {
      this.showMessage('Veuillez corriger les erreurs dans le formulaire.', 'error');
      return;
    }
    
    this.isSubmitting = true;
    this.updateSubmitButton(true);
    
    try {
      // Get form data
      const formData = this.getFormData();
      
      // Submit form
      await this.submitForm(formData);
      
      // Show success message
      if (this.config.showSuccessMessage) {
        this.showMessage('Votre message a été envoyé avec succès !', 'success');
      }
      
      // Reset form
      this.resetForm();
      
      // Emit event
      this.app.emit('form:submitted', {
        form: this,
        data: formData
      });
      
      this.logger.success('Form submitted successfully');
      
    } catch (error) {
      this.logger.error('Form submission failed:', error);
      this.showMessage('Une erreur est survenue. Veuillez réessayer.', 'error');
    } finally {
      this.isSubmitting = false;
      this.updateSubmitButton(false);
    }
  }

  /**
   * Handle field input
   */
  handleFieldInput(event) {
    const field = event.target;
    this.validateField(field);
  }

  /**
   * Handle field blur
   */
  handleFieldBlur(event) {
    const field = event.target;
    this.validateField(field);
  }

  /**
   * Validate entire form
   */
  validateForm() {
    let isValid = true;
    
    this.fields.forEach(field => {
      if (!this.validateField(field)) {
        isValid = false;
      }
    });
    
    this.isValid = isValid;
    return isValid;
  }

  /**
   * Validate individual field
   */
  validateField(field) {
    const value = field.value.trim();
    const rules = this.getFieldRules(field);
    let isValid = true;
    let errorMessage = '';
    
    // Check each validation rule
    for (const rule of rules) {
      const result = this.applyValidationRule(value, rule);
      
      if (!result.isValid) {
        isValid = false;
        errorMessage = result.message;
        break;
      }
    }
    
    // Update field UI
    this.updateFieldUI(field, isValid, errorMessage);
    
    return isValid;
  }

  /**
   * Get validation rules for field
   */
  getFieldRules(field) {
    const rules = [];
    
    // Required rule
    if (field.hasAttribute('required')) {
      rules.push({ type: 'required', message: 'Ce champ est requis.' });
    }
    
    // Email rule
    if (field.type === 'email') {
      rules.push({ type: 'email', message: 'Veuillez entrer une adresse email valide.' });
    }
    
    // Phone rule
    if (field.type === 'tel') {
      rules.push({ type: 'phone', message: 'Veuillez entrer un numéro de téléphone valide.' });
    }
    
    // Length rules
    const minLength = field.getAttribute('minlength');
    if (minLength) {
      rules.push({ 
        type: 'minLength', 
        value: parseInt(minLength), 
        message: `Ce champ doit contenir au moins ${minLength} caractères.` 
      });
    }
    
    const maxLength = field.getAttribute('maxlength');
    if (maxLength) {
      rules.push({ 
        type: 'maxLength', 
        value: parseInt(maxLength), 
        message: `Ce champ ne peut pas dépasser ${maxLength} caractères.` 
      });
    }
    
    return rules;
  }

  /**
   * Apply validation rule
   */
  applyValidationRule(value, rule) {
    const validator = this.validationRules[rule.type];
    
    if (!validator) {
      return { isValid: true };
    }
    
    const isValid = rule.value !== undefined 
      ? validator(value, rule.value)
      : validator(value);
    
    return {
      isValid,
      message: isValid ? '' : rule.message
    };
  }

  /**
   * Update field UI based on validation
   */
  updateFieldUI(field, isValid, errorMessage) {
    // Remove existing error classes and messages
    field.classList.remove('error', 'valid');
    
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
      existingError.remove();
    }
    
    // Add appropriate class
    if (field.value.trim() !== '') {
      field.classList.add(isValid ? 'valid' : 'error');
    }
    
    // Show error message
    if (!isValid && errorMessage) {
      const errorElement = document.createElement('div');
      errorElement.className = 'field-error';
      errorElement.textContent = errorMessage;
      field.parentNode.appendChild(errorElement);
      
      // Set ARIA attributes
      field.setAttribute('aria-invalid', 'true');
      field.setAttribute('aria-describedby', `${field.id}-error`);
      errorElement.id = `${field.id}-error`;
    } else {
      field.removeAttribute('aria-invalid');
      field.removeAttribute('aria-describedby');
    }
  }

  /**
   * Get form data
   */
  getFormData() {
    const formData = new FormData(this.element);
    const data = {};
    
    for (const [key, value] of formData.entries()) {
      data[key] = value;
    }
    
    return data;
  }

  /**
   * Submit form data
   */
  async submitForm(data) {
    // Mock form submission for demo
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('Form data submitted:', data);
        resolve();
      }, 1000);
    });
    
    // Real implementation would look like this:
    /*
    const response = await fetch('/api/contact', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
    */
  }

  /**
   * Show form message
   */
  showMessage(message, type = 'info') {
    this.messageContainer.className = `form-message ${type}`;
    this.messageContainer.textContent = message;
    this.messageContainer.style.display = 'block';
    
    // Auto-hide message
    if (this.config.autoHideMessages) {
      setTimeout(() => {
        this.hideMessage();
      }, this.config.autoHideMessages);
    }
  }

  /**
   * Hide form message
   */
  hideMessage() {
    this.messageContainer.style.display = 'none';
  }

  /**
   * Update submit button state
   */
  updateSubmitButton(isSubmitting) {
    if (!this.submitButton) return;
    
    if (isSubmitting) {
      this.submitButton.disabled = true;
      this.submitButton.textContent = 'Envoi en cours...';
      this.submitButton.classList.add('loading');
    } else {
      this.submitButton.disabled = false;
      this.submitButton.textContent = this.submitButton.dataset.originalText || 'Envoyer';
      this.submitButton.classList.remove('loading');
    }
  }

  /**
   * Reset form
   */
  resetForm() {
    this.element.reset();
    
    // Clear validation states
    this.fields.forEach(field => {
      field.classList.remove('error', 'valid');
      field.removeAttribute('aria-invalid');
      field.removeAttribute('aria-describedby');
      
      const errorElement = field.parentNode.querySelector('.field-error');
      if (errorElement) {
        errorElement.remove();
      }
    });
    
    this.hideMessage();
  }

  /**
   * Cleanup
   */
  cleanup() {
    // Remove event listeners
    this.element.removeEventListener('submit', this.handleSubmit);
    
    this.fields.forEach(field => {
      field.removeEventListener('input', this.handleFieldInput);
      field.removeEventListener('blur', this.handleFieldBlur);
    });
    
    this.logger.debug('Form cleaned up');
  }
}
