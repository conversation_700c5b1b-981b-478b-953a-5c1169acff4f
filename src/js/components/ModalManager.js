// =============================================================================
// MODAL MANAGER
// Modern modal system with accessibility and mobile optimization
// =============================================================================

import { Logger } from '../utils/Logger.js';

export class ModalManager {
  constructor(app) {
    this.app = app;
    this.logger = new Logger('Modal');
    
    // State
    this.activeModal = null;
    this.isAnimating = false;
    this.scrollPosition = 0;
    
    // Configuration
    this.config = {
      animationDuration: 300,
      closeOnOverlayClick: true,
      closeOnEscape: true,
      preventBodyScroll: true,
      focusTrap: true
    };
    
    // Bind methods
    this.handleOverlayClick = this.handleOverlayClick.bind(this);
    this.handleKeyDown = this.handleKeyDown.bind(this);
    this.handleImageClick = this.handleImageClick.bind(this);
  }

  /**
   * Initialize modal system
   */
  async init() {
    try {
      this.logger.debug('Initializing modal system...');
      
      this.setupEventListeners();
      this.setupImageModals();
      
      this.logger.success('Modal system initialized');
      
    } catch (error) {
      this.logger.error('Failed to initialize modal system:', error);
      throw error;
    }
  }

  /**
   * Setup global event listeners
   */
  setupEventListeners() {
    // Keyboard events
    document.addEventListener('keydown', this.handleKeyDown);
    
    // Image click events for automatic modal creation
    document.addEventListener('click', this.handleImageClick);
  }

  /**
   * Setup image modals for clickable images
   */
  setupImageModals() {
    const clickableImages = document.querySelectorAll('[data-modal-image], .clickable-image, .course-image');
    
    clickableImages.forEach(image => {
      if (!image.hasAttribute('data-modal-setup')) {
        this.setupImageModal(image);
        image.setAttribute('data-modal-setup', 'true');
      }
    });
    
    this.logger.debug(`Setup ${clickableImages.length} image modals`);
  }

  /**
   * Setup individual image modal
   */
  setupImageModal(image) {
    // Make image focusable and add accessibility
    image.setAttribute('tabindex', '0');
    image.setAttribute('role', 'button');
    image.setAttribute('aria-label', 'Cliquer pour agrandir l\'image');
    
    // Add cursor pointer
    image.style.cursor = 'pointer';
    
    // Add click and keyboard event listeners
    const handleActivation = (event) => {
      if (event.type === 'keydown' && !['Enter', ' '].includes(event.key)) {
        return;
      }
      
      event.preventDefault();
      this.openImageModal(image);
    };
    
    image.addEventListener('click', handleActivation);
    image.addEventListener('keydown', handleActivation);
  }

  /**
   * Handle image click for automatic modal detection
   */
  handleImageClick(event) {
    const image = event.target.closest('img');
    
    if (!image) return;
    
    // Check if image should open in modal
    const shouldOpenModal = 
      image.hasAttribute('data-modal-image') ||
      image.classList.contains('clickable-image') ||
      image.classList.contains('course-image') ||
      image.closest('.course-card') ||
      image.closest('.gallery-item');
    
    if (shouldOpenModal && !image.hasAttribute('data-modal-setup')) {
      event.preventDefault();
      this.openImageModal(image);
    }
  }

  /**
   * Open image modal
   */
  async openImageModal(image) {
    if (this.isAnimating) return;
    
    this.logger.debug('Opening image modal');
    
    try {
      this.isAnimating = true;
      
      // Get image source and alt text
      const src = image.src || image.dataset.src;
      const alt = image.alt || 'Image';
      const title = image.title || image.dataset.title || alt;
      
      if (!src) {
        throw new Error('Image source not found');
      }
      
      // Create modal HTML
      const modalHTML = this.createImageModalHTML(src, alt, title);
      
      // Create and show modal
      const modal = this.createModal(modalHTML, 'image-modal');
      await this.showModal(modal);
      
    } catch (error) {
      this.logger.error('Failed to open image modal:', error);
    } finally {
      this.isAnimating = false;
    }
  }

  /**
   * Create image modal HTML
   */
  createImageModalHTML(src, alt, title) {
    return `
      <div class="modal-content image-modal-content">
        <button class="modal-close" aria-label="Fermer">
          <i class="fas fa-times"></i>
        </button>
        <div class="image-container">
          <img src="${src}" alt="${alt}" class="modal-image">
          ${title ? `<div class="image-title">${title}</div>` : ''}
        </div>
      </div>
    `;
  }

  /**
   * Create modal element
   */
  createModal(content, className = '') {
    const modal = document.createElement('div');
    modal.className = `modal-overlay ${className}`;
    modal.innerHTML = content;
    
    // Add event listeners
    const closeButton = modal.querySelector('.modal-close');
    if (closeButton) {
      closeButton.addEventListener('click', () => this.closeModal(modal));
    }
    
    if (this.config.closeOnOverlayClick) {
      modal.addEventListener('click', (event) => {
        if (event.target === modal) {
          this.closeModal(modal);
        }
      });
    }
    
    return modal;
  }

  /**
   * Show modal
   */
  async showModal(modal) {
    if (this.activeModal) {
      await this.closeModal(this.activeModal);
    }
    
    this.activeModal = modal;
    
    // Prevent body scroll
    if (this.config.preventBodyScroll) {
      this.scrollPosition = window.pageYOffset;
      document.body.style.position = 'fixed';
      document.body.style.top = `-${this.scrollPosition}px`;
      document.body.style.width = '100%';
    }
    
    // Add to DOM
    document.body.appendChild(modal);
    
    // Force reflow
    modal.offsetHeight;
    
    // Add active class for animation
    modal.classList.add('active');
    
    // Focus management
    if (this.config.focusTrap) {
      this.setupFocusTrap(modal);
    }
    
    // Wait for animation
    await this.wait(this.config.animationDuration);
    
    this.app.emit('modal:opened', { modal });
  }

  /**
   * Close modal
   */
  async closeModal(modal) {
    if (!modal || this.isAnimating) return;
    
    this.isAnimating = true;
    
    try {
      // Remove active class
      modal.classList.remove('active');
      
      // Wait for animation
      await this.wait(this.config.animationDuration);
      
      // Remove from DOM
      if (modal.parentNode) {
        modal.parentNode.removeChild(modal);
      }
      
      // Restore body scroll
      if (this.config.preventBodyScroll) {
        document.body.style.position = '';
        document.body.style.top = '';
        document.body.style.width = '';
        window.scrollTo(0, this.scrollPosition);
      }
      
      // Clear active modal
      if (this.activeModal === modal) {
        this.activeModal = null;
      }
      
      this.app.emit('modal:closed', { modal });
      
    } finally {
      this.isAnimating = false;
    }
  }

  /**
   * Setup focus trap for accessibility
   */
  setupFocusTrap(modal) {
    const focusableElements = modal.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    if (focusableElements.length === 0) return;
    
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];
    
    // Focus first element
    firstElement.focus();
    
    // Handle tab navigation
    const handleTabKey = (event) => {
      if (event.key !== 'Tab') return;
      
      if (event.shiftKey) {
        if (document.activeElement === firstElement) {
          event.preventDefault();
          lastElement.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          event.preventDefault();
          firstElement.focus();
        }
      }
    };
    
    modal.addEventListener('keydown', handleTabKey);
  }

  /**
   * Handle overlay click
   */
  handleOverlayClick(event) {
    if (event.target.classList.contains('modal-overlay')) {
      this.closeModal(event.target);
    }
  }

  /**
   * Handle keyboard events
   */
  handleKeyDown(event) {
    if (!this.activeModal) return;
    
    if (event.key === 'Escape' && this.config.closeOnEscape) {
      event.preventDefault();
      this.closeModal(this.activeModal);
    }
  }

  /**
   * Setup modals (called by App)
   */
  async setupModals() {
    // Re-setup image modals for dynamically added content
    this.setupImageModals();
    this.logger.debug('Modal setup completed');
  }

  /**
   * Utility: Wait for specified duration
   */
  wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Cleanup
   */
  cleanup() {
    // Close active modal
    if (this.activeModal) {
      this.closeModal(this.activeModal);
    }
    
    // Remove event listeners
    document.removeEventListener('keydown', this.handleKeyDown);
    document.removeEventListener('click', this.handleImageClick);
    
    // Restore body scroll
    document.body.style.position = '';
    document.body.style.top = '';
    document.body.style.width = '';
    
    this.logger.debug('Modal system cleaned up');
  }
}
