// =============================================================================
// WEATHER MANAGER
// Weather widget with API integration
// =============================================================================

import { Logger } from '../utils/Logger.js';

export class WeatherManager {
  constructor(app) {
    this.app = app;
    this.logger = new Logger('Weather');
    
    // State
    this.weatherData = null;
    this.isLoading = false;
    this.lastUpdate = null;
    
    // Elements
    this.weatherWidgets = [];
    
    // Configuration
    this.config = {
      apiKey: 'demo', // Replace with real API key
      city: 'Bangkok',
      country: 'TH',
      updateInterval: 30 * 60 * 1000, // 30 minutes
      timeout: 10000 // 10 seconds
    };
    
    // Mock weather data for demo
    this.mockWeatherData = {
      temperature: 32,
      condition: 'sunny',
      humidity: 65,
      windSpeed: 8,
      icon: '☀️',
      description: 'Ensoleillé'
    };
  }

  /**
   * Initialize weather manager
   */
  async init() {
    try {
      this.logger.debug('Initializing weather manager...');
      
      this.findWeatherWidgets();
      await this.loadWeatherData();
      this.startAutoUpdate();
      
      this.logger.success('Weather manager initialized');
      
    } catch (error) {
      this.logger.error('Failed to initialize weather manager:', error);
      this.showFallbackWeather();
    }
  }

  /**
   * Find weather widgets
   */
  findWeatherWidgets() {
    this.weatherWidgets = Array.from(document.querySelectorAll('.header-weather, .mobile-weather, [data-weather]'));
    this.logger.debug(`Found ${this.weatherWidgets.length} weather widgets`);
  }

  /**
   * Load weather data
   */
  async loadWeatherData() {
    if (this.isLoading) return;
    
    this.isLoading = true;
    this.logger.debug('Loading weather data...');
    
    try {
      // For demo purposes, use mock data
      // In production, replace with real API call
      const weatherData = await this.fetchWeatherData();
      
      this.weatherData = weatherData;
      this.lastUpdate = Date.now();
      
      this.updateWeatherWidgets();
      
      this.logger.success('Weather data loaded successfully');
      
    } catch (error) {
      this.logger.error('Failed to load weather data:', error);
      this.showFallbackWeather();
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Fetch weather data from API
   */
  async fetchWeatherData() {
    // Mock API call for demo
    return new Promise((resolve) => {
      setTimeout(() => {
        // Simulate some variation in temperature
        const baseTemp = 32;
        const variation = Math.floor(Math.random() * 6) - 3; // -3 to +3
        
        resolve({
          ...this.mockWeatherData,
          temperature: baseTemp + variation,
          timestamp: Date.now()
        });
      }, 500);
    });
    
    // Real API implementation would look like this:
    /*
    const response = await fetch(
      `https://api.openweathermap.org/data/2.5/weather?q=${this.config.city},${this.config.country}&appid=${this.config.apiKey}&units=metric&lang=fr`,
      {
        timeout: this.config.timeout
      }
    );
    
    if (!response.ok) {
      throw new Error(`Weather API error: ${response.status}`);
    }
    
    const data = await response.json();
    
    return {
      temperature: Math.round(data.main.temp),
      condition: data.weather[0].main.toLowerCase(),
      humidity: data.main.humidity,
      windSpeed: Math.round(data.wind.speed * 3.6), // Convert m/s to km/h
      icon: this.getWeatherIcon(data.weather[0].icon),
      description: data.weather[0].description,
      timestamp: Date.now()
    };
    */
  }

  /**
   * Update weather widgets
   */
  updateWeatherWidgets() {
    if (!this.weatherData) return;
    
    this.weatherWidgets.forEach(widget => {
      this.updateWeatherWidget(widget);
    });
  }

  /**
   * Update individual weather widget
   */
  updateWeatherWidget(widget) {
    const tempElement = widget.querySelector('.weather-temp, [data-weather="temp"]');
    const iconElement = widget.querySelector('.weather-icon, [data-weather="icon"]');
    const locationElement = widget.querySelector('.weather-location, [data-weather="location"]');
    const descriptionElement = widget.querySelector('.weather-description, [data-weather="description"]');
    
    if (tempElement) {
      tempElement.textContent = `${this.weatherData.temperature}°C`;
    }
    
    if (iconElement) {
      iconElement.textContent = this.weatherData.icon;
      iconElement.title = this.weatherData.description;
    }
    
    if (locationElement) {
      locationElement.textContent = this.config.city;
    }
    
    if (descriptionElement) {
      descriptionElement.textContent = this.weatherData.description;
    }
    
    // Add loading class removal
    widget.classList.remove('loading');
    widget.classList.add('loaded');
  }

  /**
   * Show fallback weather when API fails
   */
  showFallbackWeather() {
    this.weatherData = {
      temperature: '--',
      icon: '🌤️',
      description: 'Météo indisponible'
    };
    
    this.updateWeatherWidgets();
  }

  /**
   * Get weather icon based on condition
   */
  getWeatherIcon(condition) {
    const icons = {
      'clear': '☀️',
      'sunny': '☀️',
      'clouds': '☁️',
      'cloudy': '☁️',
      'rain': '🌧️',
      'drizzle': '🌦️',
      'thunderstorm': '⛈️',
      'snow': '❄️',
      'mist': '🌫️',
      'fog': '🌫️'
    };
    
    return icons[condition.toLowerCase()] || '🌤️';
  }

  /**
   * Start auto-update timer
   */
  startAutoUpdate() {
    // Update weather data periodically
    this.updateTimer = setInterval(() => {
      this.loadWeatherData();
    }, this.config.updateInterval);
    
    this.logger.debug('Weather auto-update started');
  }

  /**
   * Stop auto-update timer
   */
  stopAutoUpdate() {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
      this.updateTimer = null;
      this.logger.debug('Weather auto-update stopped');
    }
  }

  /**
   * Force refresh weather data
   */
  async refresh() {
    await this.loadWeatherData();
  }

  /**
   * Get current weather data
   */
  getCurrentWeather() {
    return this.weatherData;
  }

  /**
   * Check if weather data is stale
   */
  isDataStale() {
    if (!this.lastUpdate) return true;
    
    const now = Date.now();
    const staleThreshold = this.config.updateInterval * 2; // 2x update interval
    
    return (now - this.lastUpdate) > staleThreshold;
  }

  /**
   * Setup weather widgets (called by App)
   */
  async setupWeather() {
    this.findWeatherWidgets();
    
    if (this.isDataStale()) {
      await this.loadWeatherData();
    } else {
      this.updateWeatherWidgets();
    }
    
    this.logger.debug('Weather setup completed');
  }

  /**
   * Cleanup
   */
  cleanup() {
    this.stopAutoUpdate();
    this.logger.debug('Weather manager cleaned up');
  }
}
