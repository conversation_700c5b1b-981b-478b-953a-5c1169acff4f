// =============================================================================
// EVENT EMITTER UTILITY
// Simple event system for component communication
// =============================================================================

export class EventEmitter {
  constructor() {
    this.events = new Map();
  }

  /**
   * Add event listener
   */
  on(event, callback) {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    
    this.events.get(event).push(callback);
    
    // Return unsubscribe function
    return () => this.off(event, callback);
  }

  /**
   * Add one-time event listener
   */
  once(event, callback) {
    const onceCallback = (...args) => {
      callback(...args);
      this.off(event, onceCallback);
    };
    
    return this.on(event, onceCallback);
  }

  /**
   * Remove event listener
   */
  off(event, callback) {
    if (!this.events.has(event)) {
      return;
    }
    
    const callbacks = this.events.get(event);
    const index = callbacks.indexOf(callback);
    
    if (index > -1) {
      callbacks.splice(index, 1);
    }
    
    // Clean up empty event arrays
    if (callbacks.length === 0) {
      this.events.delete(event);
    }
  }

  /**
   * Emit event to all listeners
   */
  emit(event, ...args) {
    if (!this.events.has(event)) {
      return;
    }
    
    const callbacks = this.events.get(event).slice(); // Copy to avoid issues if listeners are removed during emit
    
    callbacks.forEach(callback => {
      try {
        callback(...args);
      } catch (error) {
        console.error(`Error in event listener for "${event}":`, error);
      }
    });
  }

  /**
   * Remove all listeners for an event
   */
  removeAllListeners(event) {
    if (event) {
      this.events.delete(event);
    } else {
      this.events.clear();
    }
  }

  /**
   * Get list of events with listeners
   */
  eventNames() {
    return Array.from(this.events.keys());
  }

  /**
   * Get number of listeners for an event
   */
  listenerCount(event) {
    return this.events.has(event) ? this.events.get(event).length : 0;
  }
}
