// =============================================================================
// LOGGER UTILITY
// Centralized logging with levels and formatting
// =============================================================================

export class Logger {
  constructor(context = 'App') {
    this.context = context;
    this.isDevelopment = import.meta.env?.DEV || process.env.NODE_ENV === 'development';
  }

  /**
   * Format log message with context and timestamp
   */
  formatMessage(level, message, ...args) {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const prefix = `[${timestamp}] [${this.context}] [${level.toUpperCase()}]`;
    
    return [prefix, message, ...args];
  }

  /**
   * Log debug messages (only in development)
   */
  debug(message, ...args) {
    if (this.isDevelopment) {
      console.debug(...this.formatMessage('debug', message, ...args));
    }
  }

  /**
   * Log info messages
   */
  info(message, ...args) {
    console.info(...this.formatMessage('info', message, ...args));
  }

  /**
   * Log success messages
   */
  success(message, ...args) {
    console.log(...this.formatMessage('success', message, ...args));
  }

  /**
   * Log warning messages
   */
  warn(message, ...args) {
    console.warn(...this.formatMessage('warn', message, ...args));
  }

  /**
   * Log error messages
   */
  error(message, ...args) {
    console.error(...this.formatMessage('error', message, ...args));
  }

  /**
   * Log performance measurements
   */
  time(label) {
    if (this.isDevelopment) {
      console.time(`[${this.context}] ${label}`);
    }
  }

  /**
   * End performance measurement
   */
  timeEnd(label) {
    if (this.isDevelopment) {
      console.timeEnd(`[${this.context}] ${label}`);
    }
  }

  /**
   * Group related log messages
   */
  group(label, collapsed = false) {
    if (this.isDevelopment) {
      if (collapsed) {
        console.groupCollapsed(`[${this.context}] ${label}`);
      } else {
        console.group(`[${this.context}] ${label}`);
      }
    }
  }

  /**
   * End log group
   */
  groupEnd() {
    if (this.isDevelopment) {
      console.groupEnd();
    }
  }

  /**
   * Log table data
   */
  table(data, columns) {
    if (this.isDevelopment) {
      console.table(data, columns);
    }
  }

  /**
   * Create a child logger with extended context
   */
  child(childContext) {
    return new Logger(`${this.context}:${childContext}`);
  }
}
