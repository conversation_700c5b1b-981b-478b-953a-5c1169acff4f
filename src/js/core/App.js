// =============================================================================
// CORE APPLICATION CLASS
// Main application orchestrator
// =============================================================================

import { EventEmitter } from '../utils/EventEmitter.js';
import { Logger } from '../utils/Logger.js';
import { NavigationManager } from '../components/NavigationManager.js';
import { MobileMenuManager } from '../components/MobileMenuManager.js';
import { ModalManager } from '../components/ModalManager.js';
import { CarouselManager } from '../components/CarouselManager.js';
import { LanguageManager } from '../components/LanguageManager.js';
import { WeatherManager } from '../components/WeatherManager.js';
import { FormManager } from '../components/FormManager.js';
import { AnimationManager } from '../components/AnimationManager.js';

export class App extends EventEmitter {
  constructor(config = {}) {
    super();
    
    this.config = {
      debug: false,
      version: '2.0.0',
      name: 'GolfinThaï',
      ...config
    };
    
    this.logger = new Logger('App');
    this.isInitialized = false;
    this.managers = new Map();
    
    // Bind methods
    this.handleResize = this.handleResize.bind(this);
    this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
  }

  /**
   * Initialize the application
   */
  async init() {
    if (this.isInitialized) {
      this.logger.warn('App already initialized');
      return;
    }

    try {
      this.logger.info('Initializing application...');
      
      // Initialize core managers
      await this.initializeManagers();
      
      // Setup event listeners
      this.setupEventListeners();
      
      // Initialize components
      await this.initializeComponents();
      
      // Mark as initialized
      this.isInitialized = true;
      
      // Emit ready event
      this.emit('app:ready');
      
      this.logger.success('Application initialized successfully');
      
    } catch (error) {
      this.logger.error('Failed to initialize application:', error);
      throw error;
    }
  }

  /**
   * Initialize all managers
   */
  async initializeManagers() {
    const managerConfigs = [
      { name: 'navigation', class: NavigationManager },
      { name: 'mobileMenu', class: MobileMenuManager },
      { name: 'modal', class: ModalManager },
      { name: 'carousel', class: CarouselManager },
      { name: 'language', class: LanguageManager },
      { name: 'weather', class: WeatherManager },
      { name: 'form', class: FormManager },
      { name: 'animation', class: AnimationManager }
    ];

    for (const { name, class: ManagerClass } of managerConfigs) {
      try {
        const manager = new ManagerClass(this);
        this.managers.set(name, manager);
        
        // Initialize manager if it has an init method
        if (typeof manager.init === 'function') {
          await manager.init();
        }
        
        this.logger.debug(`${name} manager initialized`);
        
      } catch (error) {
        this.logger.error(`Failed to initialize ${name} manager:`, error);
        // Continue with other managers even if one fails
      }
    }
  }

  /**
   * Setup global event listeners
   */
  setupEventListeners() {
    // Window resize
    window.addEventListener('resize', this.handleResize);
    
    // Visibility change (for performance optimization)
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
    
    // Hash change for navigation
    window.addEventListener('hashchange', () => {
      this.getManager('navigation')?.handleHashChange();
    });
    
    // Before unload (cleanup)
    window.addEventListener('beforeunload', () => {
      this.cleanup();
    });
  }

  /**
   * Initialize components after managers are ready
   */
  async initializeComponents() {
    // Wait for DOM to be fully loaded
    if (document.readyState === 'loading') {
      await new Promise(resolve => {
        document.addEventListener('DOMContentLoaded', resolve);
      });
    }

    // Initialize components that depend on DOM
    const initPromises = [
      this.initializeNavigation(),
      this.initializeMobileMenu(),
      this.initializeModals(),
      this.initializeCarousels(),
      this.initializeForms(),
      this.initializeAnimations()
    ];

    await Promise.allSettled(initPromises);
  }

  /**
   * Initialize navigation
   */
  async initializeNavigation() {
    const navigation = this.getManager('navigation');
    if (navigation) {
      await navigation.setupNavigation();
    }
  }

  /**
   * Initialize mobile menu
   */
  async initializeMobileMenu() {
    const mobileMenu = this.getManager('mobileMenu');
    if (mobileMenu) {
      await mobileMenu.setupMobileMenu();
    }
  }

  /**
   * Initialize modals
   */
  async initializeModals() {
    const modal = this.getManager('modal');
    if (modal) {
      await modal.setupModals();
    }
  }

  /**
   * Initialize carousels
   */
  async initializeCarousels() {
    const carousel = this.getManager('carousel');
    if (carousel) {
      await carousel.setupCarousels();
    }
  }

  /**
   * Initialize forms
   */
  async initializeForms() {
    const form = this.getManager('form');
    if (form) {
      await form.setupForms();
    }
  }

  /**
   * Initialize animations
   */
  async initializeAnimations() {
    const animation = this.getManager('animation');
    if (animation) {
      await animation.setupAnimations();
    }
  }

  /**
   * Get a manager by name
   */
  getManager(name) {
    return this.managers.get(name);
  }

  /**
   * Handle window resize
   */
  handleResize() {
    // Debounce resize events
    clearTimeout(this.resizeTimeout);
    this.resizeTimeout = setTimeout(() => {
      this.emit('app:resize', {
        width: window.innerWidth,
        height: window.innerHeight
      });
    }, 100);
  }

  /**
   * Handle visibility change
   */
  handleVisibilityChange() {
    if (document.hidden) {
      this.emit('app:hidden');
    } else {
      this.emit('app:visible');
    }
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    // Remove event listeners
    window.removeEventListener('resize', this.handleResize);
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    
    // Cleanup managers
    for (const [name, manager] of this.managers) {
      if (typeof manager.cleanup === 'function') {
        try {
          manager.cleanup();
        } catch (error) {
          this.logger.error(`Error cleaning up ${name} manager:`, error);
        }
      }
    }
    
    this.managers.clear();
    this.isInitialized = false;
    
    this.logger.info('Application cleaned up');
  }

  /**
   * Get application info
   */
  getInfo() {
    return {
      name: this.config.name,
      version: this.config.version,
      initialized: this.isInitialized,
      managers: Array.from(this.managers.keys())
    };
  }
}
