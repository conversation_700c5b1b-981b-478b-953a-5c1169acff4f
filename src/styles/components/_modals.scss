// =============================================================================
// MODAL COMPONENTS
// Modern modal system with mobile optimization
// =============================================================================

// Modal overlay
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  z-index: var(--z-modal-backdrop);
  
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-4);
  
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-base);
  
  &.active {
    opacity: 1;
    visibility: visible;
  }
  
  // Prevent scrolling on body when modal is open
  &.active ~ body {
    overflow: hidden;
  }
}

// Modal content container
.modal-content {
  background-color: var(--color-background);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  position: relative;
  
  transform: scale(0.9) translateY(20px);
  transition: all var(--transition-base);
  
  .modal-overlay.active & {
    transform: scale(1) translateY(0);
  }
  
  // Smooth scrolling within modal
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

// Modal close button
.modal-close {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  z-index: 10;
  
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  cursor: pointer;
  
  display: flex;
  align-items: center;
  justify-content: center;
  
  font-size: var(--text-lg);
  transition: all var(--transition-fast);
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
    transform: scale(1.1);
  }
  
  &:focus {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
  }
  
  // Mobile optimization
  @include respond-to('sm') {
    width: 44px;
    height: 44px;
    font-size: var(--text-xl);
  }
}

// Image modal specific styles
.image-modal-content {
  max-width: 95vw;
  max-height: 95vh;
  padding: 0;
  background: transparent;
  box-shadow: none;
  
  .image-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
  }
  
  .modal-image {
    max-width: 100%;
    max-height: 90vh;
    width: auto;
    height: auto;
    object-fit: contain;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-2xl);
    
    // Loading state
    &[src=""] {
      background: var(--color-neutral-200);
      animation: pulse 2s infinite;
    }
  }
  
  .image-title {
    margin-top: var(--space-4);
    padding: var(--space-3) var(--space-6);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: var(--radius-lg);
    font-size: var(--text-sm);
    text-align: center;
    max-width: 100%;
    
    @include respond-to('sm') {
      font-size: var(--text-base);
    }
  }
}

// Content modal styles
.content-modal {
  .modal-content {
    max-width: 800px;
    padding: var(--space-8);
    
    @include respond-to('sm') {
      padding: var(--space-12);
    }
  }
  
  .modal-header {
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--color-neutral-200);
    
    h2, h3 {
      margin: 0;
      color: var(--color-text-primary);
    }
  }
  
  .modal-body {
    margin-bottom: var(--space-6);
    
    p {
      margin-bottom: var(--space-4);
      line-height: var(--leading-relaxed);
    }
  }
  
  .modal-footer {
    padding-top: var(--space-4);
    border-top: 1px solid var(--color-neutral-200);
    display: flex;
    gap: var(--space-3);
    justify-content: flex-end;
    
    @include respond-to('sm') {
      gap: var(--space-4);
    }
  }
}

// Form modal styles
.form-modal {
  .modal-content {
    max-width: 600px;
    padding: var(--space-6);
    
    @include respond-to('sm') {
      padding: var(--space-8);
    }
  }
  
  .form-group {
    margin-bottom: var(--space-5);
    
    label {
      display: block;
      margin-bottom: var(--space-2);
      font-weight: var(--font-medium);
      color: var(--color-text-primary);
    }
    
    input,
    textarea,
    select {
      width: 100%;
      padding: var(--space-3);
      border: 2px solid var(--color-neutral-300);
      border-radius: var(--radius-lg);
      font-size: var(--text-base);
      transition: border-color var(--transition-fast);
      
      &:focus {
        outline: none;
        border-color: var(--color-primary-500);
      }
      
      &:invalid {
        border-color: var(--color-error);
      }
    }
    
    textarea {
      resize: vertical;
      min-height: 120px;
    }
  }
  
  .form-actions {
    display: flex;
    gap: var(--space-3);
    justify-content: flex-end;
    margin-top: var(--space-6);
    
    @include respond-to('sm') {
      gap: var(--space-4);
    }
  }
}

// Animation keyframes
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// Mobile optimizations
@media (max-width: 640px) {
  .modal-overlay {
    padding: var(--space-2);
  }
  
  .modal-content {
    max-width: 100%;
    max-height: 100%;
    border-radius: var(--radius-xl);
  }
  
  .image-modal-content {
    .modal-image {
      max-height: 85vh;
    }
    
    .image-title {
      margin-top: var(--space-2);
      padding: var(--space-2) var(--space-4);
      font-size: var(--text-xs);
    }
  }
  
  .content-modal .modal-content,
  .form-modal .modal-content {
    padding: var(--space-4);
  }
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .modal-overlay,
  .modal-content,
  .modal-close {
    transition: none;
  }
  
  .modal-content {
    transform: none;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .modal-overlay {
    background-color: rgba(0, 0, 0, 0.9);
  }
  
  .modal-close {
    background-color: var(--color-text-primary);
    color: var(--color-background);
    border: 2px solid var(--color-text-primary);
  }
}

// Print styles
@media print {
  .modal-overlay {
    display: none;
  }
}
