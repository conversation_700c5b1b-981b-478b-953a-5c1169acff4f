// =============================================================================
// CAROUSEL COMPONENTS
// Modern carousel with touch support and accessibility
// =============================================================================

// Carousel container
.carousel {
  position: relative;
  overflow: hidden;
  border-radius: var(--radius-xl);
  background-color: var(--color-surface);
  box-shadow: var(--shadow-base);
  
  // Focus styles for accessibility
  &:focus {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
  }
}

// Slides container
.carousel-slides {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  transition: transform var(--transition-base);
}

// Individual slide
.carousel-slide {
  flex: 0 0 100%;
  width: 100%;
  height: 100%;
  position: relative;
  opacity: 0;
  transition: opacity var(--transition-base);
  
  &.active {
    opacity: 1;
  }
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }
  
  // Slide content overlay
  .slide-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: var(--space-6) var(--space-4) var(--space-4);
    
    h3 {
      margin: 0 0 var(--space-2) 0;
      font-size: var(--text-lg);
      font-weight: var(--font-semibold);
      
      @include respond-to('sm') {
        font-size: var(--text-xl);
      }
    }
    
    p {
      margin: 0;
      font-size: var(--text-sm);
      opacity: 0.9;
      
      @include respond-to('sm') {
        font-size: var(--text-base);
      }
    }
  }
}

// Navigation buttons
.carousel-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  
  width: 44px;
  height: 44px;
  border-radius: var(--radius-full);
  background-color: rgba(255, 255, 255, 0.9);
  color: var(--color-text-primary);
  border: none;
  cursor: pointer;
  
  display: flex;
  align-items: center;
  justify-content: center;
  
  font-size: var(--text-lg);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-md);
  
  &:hover:not(.disabled) {
    background-color: white;
    transform: translateY(-50%) scale(1.1);
    box-shadow: var(--shadow-lg);
  }
  
  &:focus {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
  }
  
  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  // Previous button
  &.carousel-prev {
    left: var(--space-4);
  }
  
  // Next button
  &.carousel-next {
    right: var(--space-4);
  }
  
  // Mobile optimization
  @include respond-to('sm') {
    width: 48px;
    height: 48px;
    font-size: var(--text-xl);
  }
}

// Indicators
.carousel-indicators {
  position: absolute;
  bottom: var(--space-4);
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  
  display: flex;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: var(--radius-full);
  backdrop-filter: blur(4px);
}

.carousel-indicator {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  background-color: rgba(255, 255, 255, 0.5);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.8);
    transform: scale(1.2);
  }
  
  &:focus {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
  }
  
  &.active {
    background-color: white;
    width: 24px;
  }
  
  // Mobile optimization
  @include respond-to('sm') {
    width: 10px;
    height: 10px;
    
    &.active {
      width: 28px;
    }
  }
}

// Live region for screen readers (hidden)
.carousel-live-region {
  @include sr-only;
}

// Carousel variants
.carousel-hero {
  height: 60vh;
  min-height: 400px;
  
  @include respond-to('md') {
    height: 70vh;
    min-height: 500px;
  }
  
  .slide-content {
    text-align: center;
    padding: var(--space-8) var(--space-4) var(--space-6);
    
    h3 {
      font-size: var(--text-2xl);
      margin-bottom: var(--space-4);
      
      @include respond-to('md') {
        font-size: var(--text-4xl);
      }
    }
    
    p {
      font-size: var(--text-base);
      max-width: 600px;
      margin: 0 auto;
      
      @include respond-to('md') {
        font-size: var(--text-lg);
      }
    }
  }
}

.carousel-gallery {
  height: 300px;
  
  @include respond-to('sm') {
    height: 400px;
  }
  
  @include respond-to('md') {
    height: 500px;
  }
}

.carousel-testimonials {
  background-color: var(--color-primary-50);
  
  .carousel-slide {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-8) var(--space-4);
    text-align: center;
    
    .testimonial-content {
      max-width: 600px;
      
      blockquote {
        font-size: var(--text-lg);
        font-style: italic;
        margin-bottom: var(--space-4);
        color: var(--color-text-primary);
        
        @include respond-to('md') {
          font-size: var(--text-xl);
        }
      }
      
      cite {
        font-size: var(--text-base);
        font-weight: var(--font-medium);
        color: var(--color-text-secondary);
      }
    }
  }
}

// Touch feedback
.carousel-slides {
  touch-action: pan-y;
  
  &.dragging {
    cursor: grabbing;
    user-select: none;
  }
}

// Loading state
.carousel-loading {
  .carousel-slide {
    background: var(--color-neutral-200);
    animation: pulse 2s infinite;
    
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 40px;
      height: 40px;
      border: 3px solid var(--color-neutral-400);
      border-top-color: var(--color-primary-500);
      border-radius: var(--radius-full);
      animation: spin 1s linear infinite;
    }
  }
}

// Animation keyframes
@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

// Mobile optimizations
@media (max-width: 640px) {
  .carousel-btn {
    width: 36px;
    height: 36px;
    font-size: var(--text-base);
    
    &.carousel-prev {
      left: var(--space-2);
    }
    
    &.carousel-next {
      right: var(--space-2);
    }
  }
  
  .carousel-indicators {
    bottom: var(--space-2);
    padding: var(--space-1) var(--space-3);
  }
  
  .carousel-indicator {
    width: 6px;
    height: 6px;
    
    &.active {
      width: 18px;
    }
  }
  
  .carousel-hero {
    height: 50vh;
    min-height: 300px;
    
    .slide-content {
      padding: var(--space-4) var(--space-3) var(--space-4);
      
      h3 {
        font-size: var(--text-xl);
        margin-bottom: var(--space-2);
      }
      
      p {
        font-size: var(--text-sm);
      }
    }
  }
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .carousel-slides,
  .carousel-slide,
  .carousel-btn,
  .carousel-indicator {
    transition: none;
  }
  
  .carousel-loading .carousel-slide::after {
    animation: none;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .carousel-btn {
    background-color: var(--color-background);
    border: 2px solid var(--color-text-primary);
  }
  
  .carousel-indicators {
    background-color: var(--color-text-primary);
  }
  
  .carousel-indicator {
    background-color: var(--color-background);
    border: 1px solid var(--color-text-primary);
    
    &.active {
      background-color: var(--color-text-primary);
    }
  }
}
