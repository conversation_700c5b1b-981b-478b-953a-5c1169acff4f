// =============================================================================
// TYPOGRAPHY - Modern font system
// =============================================================================

// Base typography settings
html {
  font-size: 16px; // Base font size for rem calculations
  
  @include respond-to('lg') {
    font-size: 18px; // Slightly larger on desktop
  }
}

body {
  font-family: var(--font-primary);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--color-text-primary);
  background-color: var(--color-background);
}

// Headings
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  color: var(--color-text-primary);
  margin-bottom: var(--space-4);
}

h1 {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  
  @include respond-to('md') {
    font-size: var(--text-5xl);
  }
}

h2 {
  font-size: var(--text-3xl);
  
  @include respond-to('md') {
    font-size: var(--text-4xl);
  }
}

h3 {
  font-size: var(--text-2xl);
  
  @include respond-to('md') {
    font-size: var(--text-3xl);
  }
}

h4 {
  font-size: var(--text-xl);
  
  @include respond-to('md') {
    font-size: var(--text-2xl);
  }
}

h5 {
  font-size: var(--text-lg);
  
  @include respond-to('md') {
    font-size: var(--text-xl);
  }
}

h6 {
  font-size: var(--text-base);
  
  @include respond-to('md') {
    font-size: var(--text-lg);
  }
}

// Paragraphs
p {
  margin-bottom: var(--space-4);
  
  &:last-child {
    margin-bottom: 0;
  }
}

// Links
a {
  color: var(--color-primary-600);
  text-decoration: none;
  transition: color var(--transition-fast);
  
  &:hover {
    color: var(--color-primary-700);
    text-decoration: underline;
  }
  
  &:focus-visible {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
  }
}

// Lists
ul, ol {
  margin-bottom: var(--space-4);
  padding-left: var(--space-6);
}

li {
  margin-bottom: var(--space-2);
  
  &:last-child {
    margin-bottom: 0;
  }
}

// Blockquotes
blockquote {
  margin: var(--space-6) 0;
  padding: var(--space-4) var(--space-6);
  border-left: 4px solid var(--color-primary-500);
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  font-style: italic;
  
  p {
    margin-bottom: var(--space-2);
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  cite {
    display: block;
    margin-top: var(--space-2);
    font-size: var(--text-sm);
    color: var(--color-text-secondary);
    font-style: normal;
    
    &::before {
      content: '— ';
    }
  }
}

// Code
code {
  font-family: var(--font-mono);
  font-size: 0.875em;
  background-color: var(--color-neutral-100);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  color: var(--color-text-primary);
}

pre {
  font-family: var(--font-mono);
  background-color: var(--color-neutral-900);
  color: var(--color-neutral-100);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  overflow-x: auto;
  margin: var(--space-4) 0;
  
  code {
    background: none;
    padding: 0;
    color: inherit;
  }
}

// Small text
small {
  font-size: var(--text-sm);
  color: var(--color-text-secondary);
}

// Strong and emphasis
strong, b {
  font-weight: var(--font-semibold);
}

em, i {
  font-style: italic;
}

// Mark
mark {
  background-color: var(--color-secondary-200);
  color: var(--color-text-primary);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
}

// Horizontal rule
hr {
  border: none;
  height: 1px;
  background-color: var(--color-neutral-200);
  margin: var(--space-8) 0;
}

// Typography utility classes
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--color-primary-600); }
.text-secondary { color: var(--color-text-secondary); }
.text-muted { color: var(--color-text-muted); }
.text-inverse { color: var(--color-text-inverse); }

.font-light { font-weight: var(--font-light); }
.font-normal { font-weight: var(--font-normal); }
.font-medium { font-weight: var(--font-medium); }
.font-semibold { font-weight: var(--font-semibold); }
.font-bold { font-weight: var(--font-bold); }

.leading-tight { line-height: var(--leading-tight); }
.leading-normal { line-height: var(--leading-normal); }
.leading-relaxed { line-height: var(--leading-relaxed); }

.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }
.text-4xl { font-size: var(--text-4xl); }
.text-5xl { font-size: var(--text-5xl); }
