// =============================================================================
// MODERN CSS RESET - Based on <PERSON>'s CSS Reset
// =============================================================================

/*
  1. Use a more-intuitive box-sizing model.
*/
*,
*::before,
*::after {
  box-sizing: border-box;
}

/*
  2. Remove default margin
*/
* {
  margin: 0;
}

/*
  3. Allow percentage-based heights in the application
*/
html,
body {
  height: 100%;
}

/*
  Typographic tweaks!
  4. Add accessible line-height
  5. Improve text rendering
*/
body {
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeSpeed;
}

/*
  6. Improve media defaults
*/
img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
  height: auto;
}

/*
  7. Remove built-in form typography styles
*/
input,
button,
textarea,
select {
  font: inherit;
}

/*
  8. Avoid text overflows
*/
p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

/*
  9. Create a root stacking context
*/
#root,
#__next {
  isolation: isolate;
}

/*
  10. Remove list styles on ul, ol elements with a list role,
  which suggests default styling will be removed
*/
ul[role='list'],
ol[role='list'] {
  list-style: none;
}

/*
  11. Set core root defaults
*/
html:focus-within {
  scroll-behavior: smooth;
}

/*
  12. A elements that don't have a class get default styles
*/
a:not([class]) {
  text-decoration-skip-ink: auto;
}

/*
  13. Make images easier to work with
*/
img,
picture {
  max-width: 100%;
  display: block;
}

/*
  14. Inherit fonts for inputs and buttons
*/
input,
button,
textarea,
select {
  font: inherit;
}

/*
  15. Remove all animations, transitions and smooth scroll for people that prefer not to see them
*/
@media (prefers-reduced-motion: reduce) {
  html:focus-within {
   scroll-behavior: auto;
  }
  
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/*
  16. Remove default button styles
*/
button {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
}

/*
  17. Remove default fieldset styles
*/
fieldset {
  border: none;
  padding: 0;
}

/*
  18. Remove default legend styles
*/
legend {
  padding: 0;
}

/*
  19. Remove default table styles
*/
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/*
  20. Remove default address styles
*/
address {
  font-style: normal;
}

/*
  21. Remove default summary styles
*/
summary {
  cursor: pointer;
}

/*
  22. Set default focus styles
*/
:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}
