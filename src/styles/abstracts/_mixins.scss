// =============================================================================
// MIXINS - Reusable CSS patterns
// =============================================================================

// RESPONSIVE BREAKPOINTS
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

// CONTAINER - Responsive container with max-width
@mixin container($max-width: 1280px) {
  width: 100%;
  max-width: $max-width;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--space-4);
  padding-right: var(--space-4);
  
  @include respond-to('sm') {
    padding-left: var(--space-6);
    padding-right: var(--space-6);
  }
  
  @include respond-to('lg') {
    padding-left: var(--space-8);
    padding-right: var(--space-8);
  }
}

// FLEXBOX UTILITIES
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

// GRID UTILITIES
@mixin grid-auto-fit($min-width: 250px, $gap: var(--space-6)) {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax($min-width, 1fr));
  gap: $gap;
}

@mixin grid-auto-fill($min-width: 250px, $gap: var(--space-6)) {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax($min-width, 1fr));
  gap: $gap;
}

// TYPOGRAPHY
@mixin heading($size: 'xl', $weight: var(--font-semibold)) {
  font-family: var(--font-heading);
  font-size: var(--text-#{$size});
  font-weight: $weight;
  line-height: var(--leading-tight);
  color: var(--color-text-primary);
}

@mixin body-text($size: 'base') {
  font-family: var(--font-primary);
  font-size: var(--text-#{$size});
  line-height: var(--leading-normal);
  color: var(--color-text-primary);
}

// BUTTON STYLES
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  border: none;
  border-radius: var(--radius-lg);
  font-family: var(--font-primary);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-base);
  user-select: none;
  
  &:focus {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

@mixin button-primary {
  @include button-base;
  background-color: var(--color-primary-600);
  color: var(--color-text-inverse);
  
  &:hover:not(:disabled) {
    background-color: var(--color-primary-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
  }
  
  &:active {
    transform: translateY(0);
  }
}

@mixin button-secondary {
  @include button-base;
  background-color: transparent;
  color: var(--color-primary-600);
  border: 2px solid var(--color-primary-600);
  
  &:hover:not(:disabled) {
    background-color: var(--color-primary-600);
    color: var(--color-text-inverse);
  }
}

// CARD STYLES
@mixin card {
  background-color: var(--color-surface-elevated);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-base);
  overflow: hidden;
  transition: all var(--transition-base);
  
  &:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
  }
}

// MODAL STYLES
@mixin modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: var(--z-modal-backdrop);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-base);
  
  &.active {
    opacity: 1;
    visibility: visible;
  }
}

@mixin modal-content {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  background-color: var(--color-background);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  z-index: var(--z-modal);
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  transition: all var(--transition-base);
  
  .modal-backdrop.active & {
    transform: translate(-50%, -50%) scale(1);
  }
}

// ANIMATIONS
@mixin fade-in($duration: var(--transition-base)) {
  opacity: 0;
  animation: fadeIn $duration ease-out forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

@mixin slide-up($duration: var(--transition-base)) {
  opacity: 0;
  transform: translateY(20px);
  animation: slideUp $duration ease-out forwards;
}

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// ACCESSIBILITY
@mixin sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

@mixin focus-visible {
  &:focus-visible {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
  }
}

// ASPECT RATIO
@mixin aspect-ratio($width, $height) {
  aspect-ratio: $width / $height;
  
  @supports not (aspect-ratio: 1) {
    position: relative;
    
    &::before {
      content: '';
      display: block;
      padding-top: percentage($height / $width);
    }
    
    > * {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
}
