// =============================================================================
// GOLFINTHAI MODERN - MAIN STYLESHEET
// Silicon Valley Architecture - Mobile First Approach
// =============================================================================

// 1. ABSTRACTS - Variables, mixins, functions
@import 'abstracts/variables';
@import 'abstracts/mixins';
@import 'abstracts/functions';

// 2. VENDORS - Third-party CSS
@import 'vendors/normalize';

// 3. BASE - Basic styles, typography, reset
@import 'base/reset';
@import 'base/typography';
@import 'base/base';

// 4. LAYOUT - Grid, header, footer, navigation
@import 'layout/grid';
@import 'layout/header';
@import 'layout/navigation';
@import 'layout/footer';

// 5. COMPONENTS - Reusable UI components
@import 'components/buttons';
@import 'components/cards';
@import 'components/modals';
@import 'components/carousel';
@import 'components/forms';
@import 'components/language-switcher';

// 6. PAGES - Page-specific styles
@import 'pages/home';
@import 'pages/courses';

// 7. THEMES - Color schemes, dark mode
@import 'themes/default';

// 8. UTILITIES - Helper classes, responsive utilities
@import 'utilities/spacing';
@import 'utilities/display';
@import 'utilities/text';
@import 'utilities/responsive';
@import 'utilities/animations';
