// =============================================================================
// HEADER LAYOUT
// Modern header with mobile-first navigation
// =============================================================================

.header {
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  background-color: var(--color-background);
  border-bottom: 1px solid var(--color-neutral-200);
  backdrop-filter: blur(8px);
  transition: all var(--transition-base);
  
  // Scrolled state
  &.scrolled {
    box-shadow: var(--shadow-md);
    background-color: rgba(255, 255, 255, 0.95);
  }
}

.header-container {
  @include container;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 70px;
  padding-top: var(--space-3);
  padding-bottom: var(--space-3);
  
  @include respond-to('md') {
    min-height: 80px;
    padding-top: var(--space-4);
    padding-bottom: var(--space-4);
  }
}

// Logo section
.header-logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  text-decoration: none;
  color: var(--color-text-primary);
  font-weight: var(--font-semibold);
  transition: opacity var(--transition-fast);
  
  &:hover {
    opacity: 0.8;
  }
  
  &:focus {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
  }
  
  img {
    height: 40px;
    width: auto;
    
    @include respond-to('md') {
      height: 48px;
    }
  }
  
  .logo-text {
    font-family: var(--font-heading);
    font-size: var(--text-lg);
    font-weight: var(--font-bold);
    
    @include respond-to('md') {
      font-size: var(--text-xl);
    }
  }
}

// Desktop navigation
.header-nav {
  display: none;
  
  @include respond-to('lg') {
    display: flex;
    align-items: center;
    gap: var(--space-8);
  }
  
  .nav-list {
    display: flex;
    align-items: center;
    gap: var(--space-6);
    list-style: none;
    margin: 0;
    padding: 0;
  }
  
  .nav-link {
    color: var(--color-text-primary);
    text-decoration: none;
    font-weight: var(--font-medium);
    font-size: var(--text-base);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
    position: relative;
    
    &:hover {
      color: var(--color-primary-600);
      background-color: var(--color-primary-50);
    }
    
    &:focus {
      outline: 2px solid var(--color-primary-500);
      outline-offset: 2px;
    }
    
    &.active {
      color: var(--color-primary-600);
      background-color: var(--color-primary-100);
      
      &::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 6px;
        height: 6px;
        background-color: var(--color-primary-600);
        border-radius: var(--radius-full);
      }
    }
  }
}

// Header actions (language switcher, weather, etc.)
.header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  
  @include respond-to('md') {
    gap: var(--space-4);
  }
}

// Weather widget in header
.header-weather {
  display: none;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  color: var(--color-text-secondary);
  
  @include respond-to('md') {
    display: flex;
  }
  
  .weather-icon {
    width: 20px;
    height: 20px;
  }
  
  .weather-temp {
    font-weight: var(--font-medium);
    color: var(--color-text-primary);
  }
}

// Language switcher
.language-switcher {
  position: relative;
  
  .language-button {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-3);
    background-color: var(--color-surface);
    border: 1px solid var(--color-neutral-300);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    
    &:hover {
      background-color: var(--color-neutral-100);
      border-color: var(--color-neutral-400);
    }
    
    &:focus {
      outline: 2px solid var(--color-primary-500);
      outline-offset: 2px;
    }
    
    .flag {
      width: 20px;
      height: 15px;
      border-radius: var(--radius-sm);
      object-fit: cover;
    }
    
    .language-code {
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      text-transform: uppercase;
    }
    
    .chevron {
      font-size: var(--text-xs);
      transition: transform var(--transition-fast);
    }
    
    &[aria-expanded="true"] .chevron {
      transform: rotate(180deg);
    }
  }
  
  .language-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: var(--space-1);
    background-color: var(--color-background);
    border: 1px solid var(--color-neutral-300);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    min-width: 120px;
    z-index: var(--z-dropdown);
    
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all var(--transition-fast);
    
    &.active {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }
    
    .language-option {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-3);
      cursor: pointer;
      transition: background-color var(--transition-fast);
      
      &:hover {
        background-color: var(--color-primary-50);
      }
      
      &:focus {
        outline: 2px solid var(--color-primary-500);
        outline-offset: -2px;
      }
      
      &.active {
        background-color: var(--color-primary-100);
        color: var(--color-primary-700);
      }
      
      .flag {
        width: 20px;
        height: 15px;
        border-radius: var(--radius-sm);
        object-fit: cover;
      }
      
      .language-name {
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
      }
    }
  }
}

// Mobile menu button
.mobile-menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background-color: transparent;
  border: 2px solid var(--color-neutral-300);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  
  @include respond-to('lg') {
    display: none;
  }
  
  &:hover {
    background-color: var(--color-neutral-100);
    border-color: var(--color-neutral-400);
  }
  
  &:focus {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
  }
  
  &[aria-expanded="true"] {
    background-color: var(--color-primary-100);
    border-color: var(--color-primary-500);
    color: var(--color-primary-700);
  }
  
  .hamburger {
    position: relative;
    width: 20px;
    height: 16px;
    
    span {
      position: absolute;
      left: 0;
      width: 100%;
      height: 2px;
      background-color: currentColor;
      border-radius: var(--radius-full);
      transition: all var(--transition-base);
      
      &:nth-child(1) {
        top: 0;
      }
      
      &:nth-child(2) {
        top: 7px;
      }
      
      &:nth-child(3) {
        top: 14px;
      }
    }
  }
  
  // Animated hamburger to X
  &[aria-expanded="true"] .hamburger {
    span {
      &:nth-child(1) {
        top: 7px;
        transform: rotate(45deg);
      }
      
      &:nth-child(2) {
        opacity: 0;
      }
      
      &:nth-child(3) {
        top: 7px;
        transform: rotate(-45deg);
      }
    }
  }
}

// Mobile menu overlay
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: var(--z-modal-backdrop);
  
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-base);
  
  &.active {
    opacity: 1;
    visibility: visible;
  }
  
  @include respond-to('lg') {
    display: none;
  }
}

// Mobile menu content
.mobile-menu-content {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 280px;
  max-width: 85vw;
  background-color: var(--color-background);
  box-shadow: var(--shadow-2xl);
  z-index: var(--z-modal);
  
  display: flex;
  flex-direction: column;
  
  transform: translateX(100%);
  transition: transform var(--transition-base);
  
  .mobile-menu-overlay.active & {
    transform: translateX(0);
  }
  
  .mobile-menu-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4);
    border-bottom: 1px solid var(--color-neutral-200);
    
    .mobile-menu-title {
      font-size: var(--text-lg);
      font-weight: var(--font-semibold);
      color: var(--color-text-primary);
    }
    
    .mobile-menu-close {
      width: 36px;
      height: 36px;
      border-radius: var(--radius-lg);
      background-color: var(--color-neutral-100);
      border: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all var(--transition-fast);
      
      &:hover {
        background-color: var(--color-neutral-200);
      }
      
      &:focus {
        outline: 2px solid var(--color-primary-500);
        outline-offset: 2px;
      }
    }
  }
  
  .mobile-menu-nav {
    flex: 1;
    padding: var(--space-4) 0;
    overflow-y: auto;
    
    .mobile-nav-list {
      list-style: none;
      margin: 0;
      padding: 0;
    }
    
    .mobile-nav-link {
      display: flex;
      align-items: center;
      gap: var(--space-3);
      padding: var(--space-4) var(--space-4);
      color: var(--color-text-primary);
      text-decoration: none;
      font-weight: var(--font-medium);
      transition: all var(--transition-fast);
      
      &:hover {
        background-color: var(--color-primary-50);
        color: var(--color-primary-700);
      }
      
      &:focus {
        outline: 2px solid var(--color-primary-500);
        outline-offset: -2px;
      }
      
      &.active {
        background-color: var(--color-primary-100);
        color: var(--color-primary-700);
        border-right: 3px solid var(--color-primary-600);
      }
      
      .nav-icon {
        width: 20px;
        height: 20px;
        opacity: 0.7;
      }
    }
  }
  
  .mobile-menu-footer {
    padding: var(--space-4);
    border-top: 1px solid var(--color-neutral-200);
    
    .mobile-weather {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      margin-bottom: var(--space-3);
      font-size: var(--text-sm);
      color: var(--color-text-secondary);
      
      .weather-icon {
        width: 18px;
        height: 18px;
      }
      
      .weather-temp {
        font-weight: var(--font-medium);
        color: var(--color-text-primary);
      }
    }
  }
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .header,
  .mobile-menu-content,
  .mobile-menu-overlay,
  .language-dropdown,
  .hamburger span {
    transition: none;
  }
}

// Print styles
@media print {
  .header {
    position: static;
    box-shadow: none;
    border-bottom: 1px solid #000;
  }
  
  .mobile-menu-button,
  .mobile-menu-overlay {
    display: none;
  }
}
