// =============================================================================
// ANIMATIONS UTILITIES
// Modern CSS animations and transitions
// =============================================================================

// Animation initial states
.animate-initial {
  opacity: 0;
  
  &.animate-fade-in {
    opacity: 0;
  }
  
  &.animate-slide-up {
    opacity: 0;
    transform: translateY(30px);
  }
  
  &.animate-slide-down {
    opacity: 0;
    transform: translateY(-30px);
  }
  
  &.animate-slide-left {
    opacity: 0;
    transform: translateX(30px);
  }
  
  &.animate-slide-right {
    opacity: 0;
    transform: translateX(-30px);
  }
  
  &.animate-scale-in {
    opacity: 0;
    transform: scale(0.9);
  }
  
  &.animate-rotate-in {
    opacity: 0;
    transform: rotate(-5deg) scale(0.95);
  }
}

// Animation final states
.animate-in {
  opacity: 1;
  transform: none;
  transition: all var(--transition-base);
  
  &.animate-fade-in {
    opacity: 1;
  }
  
  &.animate-slide-up,
  &.animate-slide-down,
  &.animate-slide-left,
  &.animate-slide-right {
    opacity: 1;
    transform: translateX(0) translateY(0);
  }
  
  &.animate-scale-in {
    opacity: 1;
    transform: scale(1);
  }
  
  &.animate-rotate-in {
    opacity: 1;
    transform: rotate(0) scale(1);
  }
}

// Stagger animation delays
.animate-stagger {
  &:nth-child(1) { animation-delay: 0ms; }
  &:nth-child(2) { animation-delay: 150ms; }
  &:nth-child(3) { animation-delay: 300ms; }
  &:nth-child(4) { animation-delay: 450ms; }
  &:nth-child(5) { animation-delay: 600ms; }
  &:nth-child(6) { animation-delay: 750ms; }
}

// Hover animations
.hover-lift {
  transition: transform var(--transition-base);
  
  &:hover {
    transform: translateY(-4px);
  }
}

.hover-scale {
  transition: transform var(--transition-base);
  
  &:hover {
    transform: scale(1.05);
  }
}

.hover-rotate {
  transition: transform var(--transition-base);
  
  &:hover {
    transform: rotate(2deg);
  }
}

// Loading animations
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-neutral-300);
  border-radius: 50%;
  border-top-color: var(--color-primary-600);
  animation: spin 1s ease-in-out infinite;
}

.loading-dots {
  display: inline-block;
  
  &::after {
    content: '';
    animation: dots 1.5s steps(4, end) infinite;
  }
}

.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

// Keyframe animations
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes dots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideLeft {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideRight {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes rotateIn {
  from {
    opacity: 0;
    transform: rotate(-5deg) scale(0.95);
  }
  to {
    opacity: 1;
    transform: rotate(0) scale(1);
  }
}

// Utility classes for direct animation
.animate-fade-in-direct {
  animation: fadeIn var(--transition-base) ease-out;
}

.animate-slide-up-direct {
  animation: slideUp var(--transition-base) ease-out;
}

.animate-slide-down-direct {
  animation: slideDown var(--transition-base) ease-out;
}

.animate-slide-left-direct {
  animation: slideLeft var(--transition-base) ease-out;
}

.animate-slide-right-direct {
  animation: slideRight var(--transition-base) ease-out;
}

.animate-scale-in-direct {
  animation: scaleIn var(--transition-base) ease-out;
}

.animate-rotate-in-direct {
  animation: rotateIn var(--transition-base) ease-out;
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .animate-initial,
  .animate-in,
  .hover-lift,
  .hover-scale,
  .hover-rotate,
  .loading-spinner,
  .loading-pulse {
    animation: none !important;
    transition: none !important;
  }
  
  .animate-in {
    opacity: 1;
    transform: none;
  }
}
