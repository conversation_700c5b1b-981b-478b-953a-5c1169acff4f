#!/usr/bin/env node

// =============================================================================
// VALIDATION SCRIPT - GolfinThaï Modern
// Comprehensive validation of the modern architecture
// =============================================================================

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

// Configuration
const config = {
  verbose: process.argv.includes('--verbose'),
  fix: process.argv.includes('--fix')
};

// Validation results
const results = {
  passed: 0,
  failed: 0,
  warnings: 0,
  errors: []
};

// Logger
class Logger {
  static info(message) {
    console.log(`ℹ️  ${message}`);
  }
  
  static success(message) {
    console.log(`✅ ${message}`);
    results.passed++;
  }
  
  static warning(message) {
    console.log(`⚠️  ${message}`);
    results.warnings++;
  }
  
  static error(message) {
    console.log(`❌ ${message}`);
    results.failed++;
    results.errors.push(message);
  }
  
  static verbose(message) {
    if (config.verbose) {
      console.log(`🔍 ${message}`);
    }
  }
}

// File utilities
class FileUtils {
  static async exists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }
  
  static async readFile(filePath) {
    try {
      return await fs.readFile(filePath, 'utf8');
    } catch {
      return null;
    }
  }
  
  static async getFileSize(filePath) {
    try {
      const stats = await fs.stat(filePath);
      return stats.size;
    } catch {
      return 0;
    }
  }
  
  static async isDirectory(filePath) {
    try {
      const stats = await fs.stat(filePath);
      return stats.isDirectory();
    } catch {
      return false;
    }
  }
}

// Validation tasks
class ValidationTasks {
  
  // Validate file structure
  static async validateFileStructure() {
    Logger.info('Validating file structure...');
    
    const requiredFiles = [
      'src/js/main.js',
      'src/js/core/App.js',
      'src/js/utils/Logger.js',
      'src/js/utils/EventEmitter.js',
      'src/styles/main.scss',
      'src/styles/abstracts/_variables.scss',
      'src/styles/abstracts/_mixins.scss',
      'package.json',
      'vite.config.js',
      'index-modern.html'
    ];
    
    const requiredDirectories = [
      'src/js/core',
      'src/js/components',
      'src/js/utils',
      'src/styles/abstracts',
      'src/styles/base',
      'src/styles/components',
      'src/styles/layout'
    ];
    
    // Check required files
    for (const file of requiredFiles) {
      const filePath = path.join(rootDir, file);
      if (await FileUtils.exists(filePath)) {
        Logger.verbose(`Found required file: ${file}`);
      } else {
        Logger.error(`Missing required file: ${file}`);
      }
    }
    
    // Check required directories
    for (const dir of requiredDirectories) {
      const dirPath = path.join(rootDir, dir);
      if (await FileUtils.isDirectory(dirPath)) {
        Logger.verbose(`Found required directory: ${dir}`);
      } else {
        Logger.error(`Missing required directory: ${dir}`);
      }
    }
    
    Logger.success('File structure validation completed');
  }
  
  // Validate package.json
  static async validatePackageJson() {
    Logger.info('Validating package.json...');
    
    const packagePath = path.join(rootDir, 'package.json');
    const content = await FileUtils.readFile(packagePath);
    
    if (!content) {
      Logger.error('package.json not found');
      return;
    }
    
    try {
      const packageJson = JSON.parse(content);
      
      // Check required fields
      const requiredFields = ['name', 'version', 'scripts', 'devDependencies'];
      for (const field of requiredFields) {
        if (packageJson[field]) {
          Logger.verbose(`Found required field: ${field}`);
        } else {
          Logger.error(`Missing required field in package.json: ${field}`);
        }
      }
      
      // Check required scripts
      const requiredScripts = ['dev', 'build', 'preview'];
      for (const script of requiredScripts) {
        if (packageJson.scripts && packageJson.scripts[script]) {
          Logger.verbose(`Found required script: ${script}`);
        } else {
          Logger.error(`Missing required script in package.json: ${script}`);
        }
      }
      
      // Check version
      if (packageJson.version === '2.0.0') {
        Logger.success('Package version is correct (2.0.0)');
      } else {
        Logger.warning(`Package version should be 2.0.0, found: ${packageJson.version}`);
      }
      
    } catch (error) {
      Logger.error(`Invalid JSON in package.json: ${error.message}`);
    }
  }
  
  // Validate HTML structure
  static async validateHTML() {
    Logger.info('Validating HTML structure...');
    
    const htmlPath = path.join(rootDir, 'index-modern.html');
    const content = await FileUtils.readFile(htmlPath);
    
    if (!content) {
      Logger.error('index-modern.html not found');
      return;
    }
    
    // Check for required elements
    const requiredElements = [
      '<meta name="viewport"',
      '<meta name="description"',
      'id="mobile-menu-btn"',
      'id="mobile-menu-overlay"',
      'class="header"',
      'class="carousel"'
    ];
    
    for (const element of requiredElements) {
      if (content.includes(element)) {
        Logger.verbose(`Found required element: ${element}`);
      } else {
        Logger.error(`Missing required element in HTML: ${element}`);
      }
    }
    
    // Check for modern features
    const modernFeatures = [
      'type="module"',
      'rel="preconnect"',
      'loading="lazy"',
      'aria-label'
    ];
    
    for (const feature of modernFeatures) {
      if (content.includes(feature)) {
        Logger.verbose(`Found modern feature: ${feature}`);
      } else {
        Logger.warning(`Missing modern feature in HTML: ${feature}`);
      }
    }
    
    Logger.success('HTML structure validation completed');
  }
  
  // Validate CSS structure
  static async validateCSS() {
    Logger.info('Validating CSS structure...');
    
    const mainScssPath = path.join(rootDir, 'src/styles/main.scss');
    const content = await FileUtils.readFile(mainScssPath);
    
    if (!content) {
      Logger.error('main.scss not found');
      return;
    }
    
    // Check for required imports
    const requiredImports = [
      '@import \'abstracts/variables\'',
      '@import \'abstracts/mixins\'',
      '@import \'base/reset\'',
      '@import \'base/typography\'',
      '@import \'components/buttons\'',
      '@import \'layout/header\''
    ];
    
    for (const importStatement of requiredImports) {
      if (content.includes(importStatement)) {
        Logger.verbose(`Found required import: ${importStatement}`);
      } else {
        Logger.error(`Missing required import in main.scss: ${importStatement}`);
      }
    }
    
    // Check variables file
    const variablesPath = path.join(rootDir, 'src/styles/abstracts/_variables.scss');
    const variablesContent = await FileUtils.readFile(variablesPath);
    
    if (variablesContent) {
      const requiredVariables = [
        '--color-primary-600',
        '--font-primary',
        '--space-4',
        '--radius-lg',
        '--transition-base'
      ];
      
      for (const variable of requiredVariables) {
        if (variablesContent.includes(variable)) {
          Logger.verbose(`Found required CSS variable: ${variable}`);
        } else {
          Logger.error(`Missing required CSS variable: ${variable}`);
        }
      }
    }
    
    Logger.success('CSS structure validation completed');
  }
  
  // Validate JavaScript structure
  static async validateJavaScript() {
    Logger.info('Validating JavaScript structure...');
    
    const mainJsPath = path.join(rootDir, 'src/js/main.js');
    const content = await FileUtils.readFile(mainJsPath);
    
    if (!content) {
      Logger.error('main.js not found');
      return;
    }
    
    // Check for ES6+ features
    const es6Features = [
      'import {',
      'export class',
      'const ',
      'async ',
      'await ',
      '=>'
    ];
    
    for (const feature of es6Features) {
      if (content.includes(feature)) {
        Logger.verbose(`Found ES6+ feature: ${feature}`);
      } else {
        Logger.warning(`Missing ES6+ feature in main.js: ${feature}`);
      }
    }
    
    // Check App.js
    const appJsPath = path.join(rootDir, 'src/js/core/App.js');
    const appContent = await FileUtils.readFile(appJsPath);
    
    if (appContent) {
      const requiredMethods = [
        'constructor(',
        'async init(',
        'getManager(',
        'cleanup('
      ];
      
      for (const method of requiredMethods) {
        if (appContent.includes(method)) {
          Logger.verbose(`Found required method in App.js: ${method}`);
        } else {
          Logger.error(`Missing required method in App.js: ${method}`);
        }
      }
    }
    
    Logger.success('JavaScript structure validation completed');
  }
  
  // Validate configuration files
  static async validateConfiguration() {
    Logger.info('Validating configuration files...');
    
    const configFiles = [
      'vite.config.js',
      'postcss.config.js',
      '.eslintrc.js',
      '.stylelintrc.js',
      'vitest.config.js'
    ];
    
    for (const configFile of configFiles) {
      const filePath = path.join(rootDir, configFile);
      if (await FileUtils.exists(filePath)) {
        Logger.verbose(`Found configuration file: ${configFile}`);
      } else {
        Logger.warning(`Missing configuration file: ${configFile}`);
      }
    }
    
    Logger.success('Configuration validation completed');
  }
  
  // Validate performance requirements
  static async validatePerformance() {
    Logger.info('Validating performance requirements...');
    
    // Check if dist directory exists (after build)
    const distPath = path.join(rootDir, 'dist');
    if (await FileUtils.isDirectory(distPath)) {
      Logger.verbose('Found dist directory');
      
      // Check bundle sizes (approximate)
      const cssFiles = ['dist/assets/index.css', 'dist/assets/main.css'];
      const jsFiles = ['dist/assets/index.js', 'dist/assets/main.js'];
      
      let totalCSSSize = 0;
      let totalJSSize = 0;
      
      for (const cssFile of cssFiles) {
        const size = await FileUtils.getFileSize(path.join(rootDir, cssFile));
        totalCSSSize += size;
      }
      
      for (const jsFile of jsFiles) {
        const size = await FileUtils.getFileSize(path.join(rootDir, jsFile));
        totalJSSize += size;
      }
      
      // Performance targets
      const maxCSSSize = 100 * 1024; // 100KB
      const maxJSSize = 500 * 1024;  // 500KB
      
      if (totalCSSSize > 0) {
        if (totalCSSSize < maxCSSSize) {
          Logger.success(`CSS bundle size OK: ${Math.round(totalCSSSize / 1024)}KB`);
        } else {
          Logger.warning(`CSS bundle size too large: ${Math.round(totalCSSSize / 1024)}KB`);
        }
      }
      
      if (totalJSSize > 0) {
        if (totalJSSize < maxJSSize) {
          Logger.success(`JS bundle size OK: ${Math.round(totalJSSize / 1024)}KB`);
        } else {
          Logger.warning(`JS bundle size too large: ${Math.round(totalJSSize / 1024)}KB`);
        }
      }
      
    } else {
      Logger.warning('Dist directory not found - run npm run build first');
    }
  }
  
  // Generate validation report
  static async generateReport() {
    Logger.info('Generating validation report...');
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        passed: results.passed,
        failed: results.failed,
        warnings: results.warnings,
        total: results.passed + results.failed + results.warnings
      },
      errors: results.errors,
      recommendations: []
    };
    
    // Add recommendations based on results
    if (results.failed > 0) {
      report.recommendations.push('Fix all failed validations before proceeding');
    }
    
    if (results.warnings > 0) {
      report.recommendations.push('Review warnings and consider fixing them');
    }
    
    if (results.failed === 0 && results.warnings === 0) {
      report.recommendations.push('All validations passed! Ready for production');
    }
    
    const reportPath = path.join(rootDir, 'VALIDATION-REPORT.json');
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
    
    Logger.success(`Validation report generated: ${reportPath}`);
    return report;
  }
}

// Main validation function
async function validate() {
  Logger.info('🔍 Starting GolfinThaï Modern validation...');
  
  try {
    await ValidationTasks.validateFileStructure();
    await ValidationTasks.validatePackageJson();
    await ValidationTasks.validateHTML();
    await ValidationTasks.validateCSS();
    await ValidationTasks.validateJavaScript();
    await ValidationTasks.validateConfiguration();
    await ValidationTasks.validatePerformance();
    
    const report = await ValidationTasks.generateReport();
    
    // Summary
    console.log('\n📊 VALIDATION SUMMARY:');
    console.log(`✅ Passed: ${report.summary.passed}`);
    console.log(`❌ Failed: ${report.summary.failed}`);
    console.log(`⚠️  Warnings: ${report.summary.warnings}`);
    
    if (report.summary.failed === 0) {
      Logger.success('🎉 All critical validations passed!');
      process.exit(0);
    } else {
      Logger.error('❌ Some validations failed. Please fix them before proceeding.');
      process.exit(1);
    }
    
  } catch (error) {
    Logger.error(`Validation failed: ${error.message}`);
    process.exit(1);
  }
}

// Run validation
if (import.meta.url === `file://${process.argv[1]}`) {
  validate();
}
