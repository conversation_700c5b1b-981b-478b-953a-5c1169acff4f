// =============================================================================
// MOBILE MENU MANAGER TESTS
// Unit tests for mobile menu functionality
// =============================================================================

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { MobileMenuManager } from '../../src/js/components/MobileMenuManager.js';

// Mock DOM elements
const createMockDOM = () => {
  document.body.innerHTML = `
    <button id="mobile-menu-btn" aria-expanded="false">Menu</button>
    <button id="mobile-menu-close">Close</button>
    <div id="mobile-menu-overlay" class="mobile-menu-overlay">
      <div id="mobile-menu-content">
        <a href="#home">Home</a>
        <a href="#about">About</a>
        <a href="#contact">Contact</a>
      </div>
    </div>
  `;
};

// Mock App
const mockApp = {
  on: vi.fn(),
  emit: vi.fn(),
  getManager: vi.fn()
};

describe('MobileMenuManager', () => {
  let mobileMenuManager;

  beforeEach(() => {
    createMockDOM();
    mobileMenuManager = new MobileMenuManager(mockApp);
  });

  afterEach(() => {
    document.body.innerHTML = '';
    vi.clearAllMocks();
  });

  describe('Initialization', () => {
    it('should initialize with correct default state', () => {
      expect(mobileMenuManager.isOpen).toBe(false);
      expect(mobileMenuManager.isAnimating).toBe(false);
    });

    it('should find required DOM elements', async () => {
      await mobileMenuManager.init();
      
      expect(mobileMenuManager.menuButton).toBeTruthy();
      expect(mobileMenuManager.menuOverlay).toBeTruthy();
      expect(mobileMenuManager.menuLinks).toHaveLength(3);
    });

    it('should setup accessibility attributes', async () => {
      await mobileMenuManager.init();
      
      const menuButton = document.getElementById('mobile-menu-btn');
      expect(menuButton.getAttribute('aria-expanded')).toBe('false');
      expect(menuButton.getAttribute('aria-controls')).toBe('mobile-menu-overlay');
    });
  });

  describe('Menu Operations', () => {
    beforeEach(async () => {
      await mobileMenuManager.init();
    });

    it('should open menu correctly', async () => {
      await mobileMenuManager.openMenu();
      
      expect(mobileMenuManager.isOpen).toBe(true);
      expect(mobileMenuManager.menuOverlay.classList.contains('active')).toBe(true);
      expect(mobileMenuManager.menuButton.getAttribute('aria-expanded')).toBe('true');
      expect(mockApp.emit).toHaveBeenCalledWith('mobileMenu:opened');
    });

    it('should close menu correctly', async () => {
      // First open the menu
      await mobileMenuManager.openMenu();
      
      // Then close it
      await mobileMenuManager.closeMenu();
      
      expect(mobileMenuManager.isOpen).toBe(false);
      expect(mobileMenuManager.menuOverlay.classList.contains('active')).toBe(false);
      expect(mobileMenuManager.menuButton.getAttribute('aria-expanded')).toBe('false');
      expect(mockApp.emit).toHaveBeenCalledWith('mobileMenu:closed');
    });

    it('should prevent body scroll when menu is open', async () => {
      await mobileMenuManager.openMenu();
      
      expect(document.body.style.overflow).toBe('hidden');
    });

    it('should restore body scroll when menu is closed', async () => {
      await mobileMenuManager.openMenu();
      await mobileMenuManager.closeMenu();
      
      expect(document.body.style.overflow).toBe('');
    });
  });

  describe('Event Handling', () => {
    beforeEach(async () => {
      await mobileMenuManager.init();
    });

    it('should toggle menu on button click', async () => {
      const menuButton = document.getElementById('mobile-menu-btn');
      
      // Simulate click
      menuButton.click();
      await new Promise(resolve => setTimeout(resolve, 350)); // Wait for animation
      
      expect(mobileMenuManager.isOpen).toBe(true);
    });

    it('should close menu on close button click', async () => {
      await mobileMenuManager.openMenu();
      
      const closeButton = document.getElementById('mobile-menu-close');
      closeButton.click();
      await new Promise(resolve => setTimeout(resolve, 350)); // Wait for animation
      
      expect(mobileMenuManager.isOpen).toBe(false);
    });

    it('should close menu on escape key', async () => {
      await mobileMenuManager.openMenu();
      
      // Simulate escape key
      const escapeEvent = new KeyboardEvent('keydown', { key: 'Escape' });
      document.dispatchEvent(escapeEvent);
      await new Promise(resolve => setTimeout(resolve, 350)); // Wait for animation
      
      expect(mobileMenuManager.isOpen).toBe(false);
    });

    it('should close menu on link click', async () => {
      await mobileMenuManager.openMenu();
      
      const link = mobileMenuManager.menuLinks[0];
      link.click();
      
      // Wait for the delayed close
      await new Promise(resolve => setTimeout(resolve, 200));
      
      expect(mobileMenuManager.isOpen).toBe(false);
    });
  });

  describe('Responsive Behavior', () => {
    beforeEach(async () => {
      await mobileMenuManager.init();
    });

    it('should close menu on desktop resize', async () => {
      await mobileMenuManager.openMenu();
      
      // Mock window resize to desktop size
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1200
      });
      
      mobileMenuManager.handleResize();
      
      expect(mobileMenuManager.isOpen).toBe(false);
    });

    it('should keep menu state on mobile resize', async () => {
      await mobileMenuManager.openMenu();
      
      // Mock window resize to mobile size
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 600
      });
      
      mobileMenuManager.handleResize();
      
      expect(mobileMenuManager.isOpen).toBe(true);
    });
  });

  describe('Animation States', () => {
    beforeEach(async () => {
      await mobileMenuManager.init();
    });

    it('should prevent multiple operations during animation', async () => {
      // Start opening menu
      const openPromise = mobileMenuManager.openMenu();
      
      // Try to close immediately (should be ignored)
      await mobileMenuManager.closeMenu();
      
      // Wait for open to complete
      await openPromise;
      
      expect(mobileMenuManager.isOpen).toBe(true);
    });

    it('should set animating state correctly', async () => {
      const openPromise = mobileMenuManager.openMenu();
      
      expect(mobileMenuManager.isAnimating).toBe(true);
      
      await openPromise;
      
      expect(mobileMenuManager.isAnimating).toBe(false);
    });
  });

  describe('Cleanup', () => {
    beforeEach(async () => {
      await mobileMenuManager.init();
    });

    it('should remove event listeners on cleanup', () => {
      const removeEventListenerSpy = vi.spyOn(document, 'removeEventListener');
      
      mobileMenuManager.cleanup();
      
      expect(removeEventListenerSpy).toHaveBeenCalledWith('keydown', expect.any(Function));
    });

    it('should restore body scroll on cleanup', async () => {
      await mobileMenuManager.openMenu();
      
      mobileMenuManager.cleanup();
      
      expect(document.body.style.overflow).toBe('');
    });
  });

  describe('Error Handling', () => {
    it('should handle missing DOM elements gracefully', async () => {
      document.body.innerHTML = ''; // Remove all elements
      
      await expect(mobileMenuManager.init()).rejects.toThrow();
    });

    it('should handle operations on uninitialized manager', async () => {
      const uninitializedManager = new MobileMenuManager(mockApp);
      
      // Should not throw errors
      await uninitializedManager.openMenu();
      await uninitializedManager.closeMenu();
      
      expect(uninitializedManager.isOpen).toBe(false);
    });
  });
});
