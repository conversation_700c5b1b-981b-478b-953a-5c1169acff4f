# 🏌️ GolfinThaï - Git Ignore

# Development files
*.log
*.tmp
*~
.DS_Store
Thumbs.db

# Backup files
*-backup-*
*_backup.*
*_original.*

# IDE files
.vscode/
.idea/
*.swp
*.swo

# Node modules (if added later)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs (if build process added)
dist/
build/
.cache/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Temporary files
temp/
tmp/
*.temp

# Archive folders
ARCHIVE/
backup-*/
old/
legacy/

# Disabled files
*.disabled
*.old
*.bak