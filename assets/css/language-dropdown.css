/* ================================
   Language Switcher Dropdown Styles
   ================================ */

/* Desktop Language Switcher */
.language-switcher-desktop {
    position: relative;
}

.lang-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: rgba(0, 87, 75, 0.1);
    border: 1px solid rgba(0, 87, 75, 0.2);
    border-radius: 0.5rem;
    color: var(--luxury-emerald);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.lang-btn:hover {
    background: rgba(0, 87, 75, 0.2);
    border-color: var(--luxury-emerald);
    transform: translateY(-1px);
}

.lang-arrow {
    transition: transform 0.3s ease;
}

.lang-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 0.5rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 87, 75, 0.2);
    border-radius: 0.75rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    min-width: 140px;
    z-index: 1000;
    animation: dropdownFadeIn 0.2s ease-out;
}

.lang-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    padding: 0.75rem 1rem;
    border: none;
    background: transparent;
    color: var(--text-primary);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 0.5rem;
}

.lang-option:hover {
    background: rgba(0, 87, 75, 0.1);
    color: var(--luxury-emerald);
}

.lang-option:first-child {
    border-radius: 0.75rem 0.75rem 0.5rem 0.5rem;
}

.lang-option:last-child {
    border-radius: 0.5rem 0.5rem 0.75rem 0.75rem;
}

/* Mobile Language Switcher */
.language-switcher-mobile {
    position: relative;
}

.lang-btn-mobile {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.4rem 0.6rem;
    background: rgba(0, 87, 75, 0.1);
    border: 1px solid rgba(0, 87, 75, 0.2);
    border-radius: 0.4rem;
    color: var(--luxury-emerald);
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.lang-btn-mobile:hover {
    background: rgba(0, 87, 75, 0.2);
    border-color: var(--luxury-emerald);
}

.lang-dropdown-mobile {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 0.5rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 87, 75, 0.2);
    border-radius: 0.5rem;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    min-width: 100px;
    z-index: 1000;
    animation: dropdownFadeIn 0.2s ease-out;
}

.lang-option-mobile {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    width: 100%;
    padding: 0.6rem 0.8rem;
    border: none;
    background: transparent;
    color: var(--text-primary);
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 0.25rem;
}

.lang-option-mobile:hover {
    background: rgba(0, 87, 75, 0.1);
    color: var(--luxury-emerald);
}

/* Animation */
@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .lang-dropdown,
    .lang-dropdown-mobile {
        background: rgba(13, 27, 26, 0.95);
        border-color: rgba(0, 87, 75, 0.3);
    }
    
    .lang-option,
    .lang-option-mobile {
        color: rgba(255, 255, 255, 0.9);
    }
    
    .lang-option:hover,
    .lang-option-mobile:hover {
        color: #10d9c4;
    }
}

/* Accessibility improvements */
.lang-btn:focus,
.lang-btn-mobile:focus,
.lang-option:focus,
.lang-option-mobile:focus {
    outline: 2px solid var(--luxury-emerald);
    outline-offset: 2px;
}

/* Ensure dropdowns are above other elements */
.language-switcher-desktop,
.language-switcher-mobile {
    z-index: 999;
}
