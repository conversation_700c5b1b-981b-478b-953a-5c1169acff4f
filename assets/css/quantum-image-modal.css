/* ===================================
   🖼️ QUANTUM IMAGE MODAL - APPLE/NETFLIX LEVEL DESIGN
   Perfect image viewing experience - Zero hacks
   =================================== */

/* 🎭 MODAL BASE */
.quantum-image-modal {
    position: fixed;
    inset: 0;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    backdrop-filter: blur(0px);
    background: rgba(0, 0, 0, 0);
}

.quantum-image-modal.active {
    opacity: 1;
    visibility: visible;
    backdrop-filter: blur(20px);
    background: rgba(0, 0, 0, 0.95);
}

/* 🎨 MODAL BACKDROP */
.modal-backdrop {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.1);
    cursor: zoom-out;
}

/* 📦 MODAL CONTAINER - GUARANTEED FUNCTIONALITY */
.modal-container {
    position: relative !important;
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 2rem !important;
    box-sizing: border-box !important;
    z-index: 10000 !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* ❌ CLOSE BUTTON - Apple style */
.modal-close {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    width: 48px;
    height: 48px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    z-index: 10001;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.modal-close:active {
    transform: scale(0.95);
}

/* 🎛️ MODAL CONTROLS - Pro tools */
.modal-controls {
    position: absolute;
    top: 1.5rem;
    left: 1.5rem;
    display: flex;
    gap: 0.5rem;
    z-index: 10001;
}

.control-btn {
    width: 44px;
    height: 44px;
    border: none;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    color: white;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.control-btn:active {
    transform: translateY(0);
}

/* 🖼️ IMAGE CONTAINER - Perfect centering - GUARANTEED VISIBILITY */
.image-container {
    position: relative !important;
    max-width: 100% !important;
    max-height: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    overflow: visible !important; /* CHANGED: Allow image to be fully visible */
    z-index: 10000 !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* 🖼️ MODAL IMAGE - PERFECT SIZING - GUARANTEED VISIBILITY */
.modal-image {
    max-width: 90vw !important;
    max-height: 85vh !important;
    width: auto !important;
    height: auto !important;
    min-width: 300px !important; /* GUARANTEED MINIMUM */
    min-height: 200px !important; /* GUARANTEED MINIMUM */
    border-radius: 12px !important;
    box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(255, 255, 255, 0.1) !important;
    cursor: grab !important;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    transform-origin: center !important;
    will-change: transform !important;
    object-fit: contain !important;
    opacity: 1 !important; /* FORCE VISIBLE */
    visibility: visible !important; /* FORCE VISIBLE */
    display: block !important; /* FORCE DISPLAY */
    z-index: 10001 !important; /* ABOVE EVERYTHING */
}

.modal-image:active {
    cursor: grabbing;
}

/* 🔄 LOADING SPINNER - Elegant */
.loading-spinner {
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.2);
    border-top: 3px solid #A3D1C8;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ℹ️ MODAL INFO - Clean typography */
.modal-info {
    position: absolute;
    bottom: 1.5rem;
    left: 1.5rem;
    right: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(20px);
    padding: 1rem 1.5rem;
    border-radius: 12px;
    color: white;
    z-index: 10001;
}

.image-title {
    font-weight: 500;
    font-size: 1rem;
    opacity: 0.9;
    flex: 1;
}

.zoom-indicator {
    font-family: 'Monaco', monospace;
    font-size: 0.875rem;
    color: #A3D1C8;
    font-weight: 600;
}

/* 📱 MOBILE OPTIMIZATIONS */
@media (max-width: 768px) {
    .modal-container {
        padding: 1rem;
    }
    
    .modal-close {
        top: 1rem;
        right: 1rem;
        width: 44px;
        height: 44px;
        font-size: 1.1rem;
    }
    
    .modal-controls {
        top: 1rem;
        left: 1rem;
        gap: 0.25rem;
    }
    
    .control-btn {
        width: 40px;
        height: 40px;
        font-size: 0.9rem;
    }
    
    .modal-info {
        bottom: 1rem;
        left: 1rem;
        right: 1rem;
        padding: 0.75rem 1rem;
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
    
    .image-title {
        font-size: 0.875rem;
    }
    
    .zoom-indicator {
        font-size: 0.8rem;
    }
}

/* 🎮 INTERACTION STATES */
@media (hover: hover) {
    .modal-image:hover {
        box-shadow: 
            0 35px 60px rgba(0, 0, 0, 0.6),
            0 0 0 1px rgba(255, 255, 255, 0.15);
    }
}

/* 🌐 FULLSCREEN SUPPORT */
.quantum-image-modal:fullscreen {
    background: rgba(0, 0, 0, 1);
}

.quantum-image-modal:fullscreen .modal-container {
    padding: 0;
}

/* 🔒 BODY LOCK */
body.modal-open {
    overflow: hidden;
    touch-action: none;
}

/* ⚡ PERFORMANCE OPTIMIZATIONS */
.quantum-image-modal * {
    backface-visibility: hidden;
    perspective: 1000px;
}

/* 🎯 FOCUS MANAGEMENT */
.quantum-image-modal:focus-within .modal-close,
.quantum-image-modal:focus-within .control-btn {
    outline: 2px solid #A3D1C8;
    outline-offset: 2px;
}

/* ✨ ENTRANCE ANIMATION */
.quantum-image-modal.active .modal-image {
    animation: imageEnter 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes imageEnter {
    0% {
        opacity: 0;
        transform: scale(0.9) translateY(20px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* 🎨 SMOOTH SCROLLBAR (if needed) */
.quantum-image-modal::-webkit-scrollbar {
    display: none;
}

.quantum-image-modal {
    -ms-overflow-style: none;
    scrollbar-width: none;
}/* ===================================
   🚨 QUANTUM IMAGE MODAL - EMERGENCY FIXES
   Fix for stuck modals with black overlay
   =================================== */

/* 🚨 FORCE HIDE STUCK MODALS */
.quantum-image-modal:not(.active) {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
}

/* 🔧 BETTER BACKDROP BEHAVIOR */
.modal-backdrop {
    cursor: pointer !important;
    z-index: 10000 !important;
}

/* 🎯 CLICK DETECTION IMPROVEMENT */
.modal-backdrop::before {
    content: '';
    position: absolute;
    inset: 0;
    z-index: 1;
    background: transparent;
    cursor: pointer;
}

/* 📱 MOBILE BACKDROP FIX */
@media (max-width: 768px) {
    .modal-backdrop {
        touch-action: manipulation;
    }
}

/* 🚨 EMERGENCY RESET */
.modal-emergency-close {
    position: fixed !important;
    top: 1rem !important;
    right: 1rem !important;
    z-index: 10002 !important;
    background: rgba(220, 38, 38, 0.9) !important;
    color: white !important;
    border: none !important;
    border-radius: 50% !important;
    width: 60px !important;
    height: 60px !important;
    font-size: 1.5rem !important;
    cursor: pointer !important;
    display: none;
}

.modal-emergency-close.show {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}