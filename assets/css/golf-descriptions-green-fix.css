/*
 * 🎨 GOLF DESCRIPTIONS GREEN HIGHLIGHT - DEVS PROTOCOL
 * Force green colors on important text in descriptions
 */

/* 🎯 FORCE GREEN COLORS ON STRONG TEXT */
.course-description strong,
.course-description b,
.course-description span strong,
.course-description span b {
    color: #065f46 !important; /* Dark green - matches theme */
    font-weight: 700 !important;
    text-shadow: none !important;
}

/* 🎯 ALTERNATIVE GREEN FOR NUMBERS/SPECIAL TEXT */
.course-description strong:contains("18"),
.course-description strong:contains("27"),
.course-description strong:contains("trous"),
.course-description strong:contains("km"),
.course-description strong:contains("mètres") {
    color: #059669 !important; /* Slightly lighter green */
}

/* 🎯 ENSURE CONTRAST AND READABILITY */
.course-description span[data-translate] strong {
    background: rgba(16, 185, 129, 0.08) !important;
    padding: 0.1em 0.2em !important;
    border-radius: 3px !important;
    transition: all 0.2s ease !important;
}

/* 🎯 HOVER EFFECT ON STRONG TEXT */
.course-card:hover .course-description strong {
    background: rgba(16, 185, 129, 0.15) !important;
    transform: scale(1.02) !important;
}

/* 🎯 MAKE SURE INHERITANCE WORKS */
.course-description * {
    color: inherit;
}

.course-description strong,
.course-description b {
    color: #065f46 !important; /* Override any inheritance */
}

/* 🎯 SPECIFIC TARGETING FOR DYNAMICALLY ADDED CONTENT */
.course-description span[data-translate*="course"] strong {
    color: #065f46 !important;
    font-weight: 700 !important;
    background: rgba(16, 185, 129, 0.1) !important;
    padding: 0.1em 0.2em !important;
    border-radius: 3px !important;
}

/* 🎯 MOBILE SPECIFIC ADJUSTMENTS */
@media (max-width: 768px) {
    .course-description strong {
        font-size: 0.95em !important;
        padding: 0.08em 0.15em !important;
    }
}

/* 🎯 DEBUG HELPER (remove in production) */
.course-description strong::before {
    /* content: "💚"; */
    /* font-size: 0.8em; */
    /* margin-right: 0.2em; */
}

/* 🎯 ENSURE STYLES APPLY AFTER JAVASCRIPT CONVERSION */
.course-description[data-converted="true"] strong {
    color: #065f46 !important;
    font-weight: 700 !important;
    background: rgba(16, 185, 129, 0.1) !important;
    padding: 0.1em 0.2em !important;
    border-radius: 3px !important;
}
