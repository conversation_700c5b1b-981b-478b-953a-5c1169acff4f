/* =============================================
   🔥 HOSTINGER ANTI-COMPRESSION CSS OVERRIDE
   Force high quality rendering on Hostinger
   ============================================= */

/* 🎯 FORCE HIGH QUALITY ON HOSTINGER SERVERS */
.carousel-image img {
    /* OVERRIDE HOSTINGER COMPRESSION */
    image-rendering: auto !important;
    image-rendering: high-quality !important;
    
    /* FORCE ORIGINAL DIMENSIONS */
    min-width: 100% !important;
    min-height: 100% !important;
    max-width: none !important;
    max-height: none !important;
    
    /* PREVENT SERVER-SIDE SCALING */
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    object-position: center center !important;
    
    /* ANTI-COMPRESSION GPU TRICKS */
    transform: translateZ(0) scale(1.001) !important;
    will-change: transform !important;
    backface-visibility: hidden !important;
    
    /* FORCE BROWSER TO RESPECT ORIGINAL */
    filter: contrast(1.01) saturate(1.01) !important;
    
    /* HOSTINGER SPECIFIC FIXES */
    image-orientation: from-image !important;
    color-profile: auto !important;
    
    /* PREVENT ANY OPTIMIZATION */
    transition: none !important;
    animation: none !important;
}

/* 📱 MOBILE SPECIFIC - HOSTINGER OVERRIDE */
@media (max-width: 768px) {
    .carousel-image img {
        /* MOBILE ANTI-COMPRESSION */
        image-rendering: auto !important;
        image-rendering: high-quality !important;
        image-rendering: -webkit-optimize-contrast !important;
        
        /* FORCE QUALITY ON MOBILE */
        -webkit-transform: translateZ(0) scale(1.001) !important;
        transform: translateZ(0) scale(1.001) !important;
        
        /* PREVENT MOBILE OPTIMIZATION */
        -webkit-backface-visibility: hidden !important;
        backface-visibility: hidden !important;
        
        /* FORCE ORIGINAL SIZE */
        width: 100% !important;
        height: 100% !important;
        
        /* HOSTINGER MOBILE SPECIFIC */
        filter: contrast(1.005) saturate(1.005) brightness(1.001) !important;
    }
}

/* 🔥 RETINA DISPLAYS - FORCE QUALITY */
@media (-webkit-min-device-pixel-ratio: 2), 
       (min-device-pixel-ratio: 2), 
       (min-resolution: 192dpi) {
    .carousel-image img {
        /* RETINA ANTI-COMPRESSION */
        image-rendering: auto !important;
        image-rendering: high-quality !important;
        
        /* FORCE RETINA QUALITY */
        -webkit-transform: translateZ(0) scale(1.0001) !important;
        transform: translateZ(0) scale(1.0001) !important;
    }
}

/* =============================================
   🎯 HOSTINGER RESULT: CRYSTAL CLEAR IMAGES!
   ============================================= */