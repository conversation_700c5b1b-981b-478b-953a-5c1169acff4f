/* =============================================
   🚨 QUANTUM EMERGENCY FIX - URGENT REPAIR
   Fix "GolfinThaï" positioning + Ultra-compact weather
   FRÉRO'S IMMEDIATE NEEDS
   ============================================= */

/* 📱 EMERGENCY MOBILE FIXES */
@media (max-width: 1023px) {
    
    /* 🏷️ LOGO CONTAINER - FORCE PROPER LAYOUT */
    .flex.items-center.space-x-3:first-child {
        flex: 0 0 105px !important; /* Slightly reduced for more weather space */
        min-width: 105px !important;
        max-width: 105px !important;
        z-index: 1000 !important; /* HIGHEST PRIORITY */
        position: relative !important;
        
        /* FORCE HORIZONTAL LAYOUT */
        display: flex !important;
        flex-direction: row !important;
        align-items: center !important;
        justify-content: flex-start !important;
        gap: 0.4rem !important; /* Tight but visible gap */
        height: 100% !important;
        overflow: visible !important; /* CRITICAL: Allow text to show */
        padding: 0.5rem 0.2rem !important;
        
        /* FORCE VISIBILITY */
        background: transparent !important;
        border: none !important;
    }
    
    /* 🎯 LOGO CIRCLE - OPTIMIZED SIZE */
    .logo-container-bg {
        width: 36px !important; /* Slightly smaller to give text more room */
        height: 36px !important;
        min-width: 36px !important;
        min-height: 36px !important;
        border-radius: 50% !important;
        background-size: cover !important;
        background-position: center !important;
        flex-shrink: 0 !important;
        margin: 0 !important;
        z-index: 1001 !important;
        position: relative !important;
        
        /* Subtle styling */
        box-shadow: 0 1px 4px rgba(45, 212, 191, 0.1) !important;
        border: 1px solid rgba(163, 209, 200, 0.15) !important;
        transition: all 0.3s ease !important;
    }
    
    /* 🏷️ "GolfinThaï" TEXT CONTAINER - FORCE VISIBILITY */
    .flex.items-center.space-x-3:first-child > div:last-child {
        display: block !important; /* Simple block display */
        flex: 1 !important;
        z-index: 1002 !important; /* HIGHER than logo circle */
        position: relative !important;
        
        /* FORCE TEXT TO SHOW */
        overflow: visible !important; /* CRITICAL: No hidden overflow */
        margin: 0 !important;
        padding: 0 !important;
        width: auto !important;
        max-width: 65px !important; /* Fit in remaining space */
        height: auto !important;
        
        /* Ensure visibility */
        background: transparent !important;
        border: none !important;
        text-align: left !important;
    }
    
    /* 🎯 "GolfinThaï" TITLE - MAXIMUM VISIBILITY */
    .header-logo-gradient {
        font-size: 0.85rem !important; /* Optimized for tight space */
        line-height: 1.1 !important;
        font-weight: 700 !important; /* Strong but not excessive */
        white-space: nowrap !important;
        overflow: visible !important; /* NEVER HIDE */
        text-overflow: clip !important; /* NO ELLIPSIS */
        margin: 0 !important;
        padding: 0 !important;
        width: auto !important;
        max-width: 62px !important; /* Fit in container */
        text-align: left !important;
        z-index: 1003 !important; /* HIGHEST text priority */
        position: relative !important;
        
        /* DARKER COLORS AS REQUESTED */
        background: linear-gradient(135deg, #065f46 0%, #047857 70%, #059669 100%) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
        
        /* Enhanced visibility */
        filter: drop-shadow(0 1px 2px rgba(6, 95, 70, 0.2)) !important;
        transition: all 0.3s ease !important;
        
        /* FORCE DISPLAY */
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
    
    /* 🌡️ WEATHER WIDGET - ULTRA COMPACT as requested */
    .weather-widget-mobile {
        flex: 0 0 70px !important; /* MUCH smaller - FRÉRO'S REQUEST */
        min-width: 70px !important;
        max-width: 75px !important;
        text-align: center !important;
        z-index: 500 !important; /* Lower than logo */
        position: relative !important;
        overflow: visible !important;
        margin: 0 auto !important;
        padding: 0.4rem 0.2rem !important; /* Tight padding */
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.04) 0%, rgba(45, 212, 191, 0.02) 100%) !important;
        border-radius: 8px !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        
        /* Minimal styling */
        box-shadow: 0 1px 3px rgba(45, 212, 191, 0.06) !important;
        border: 1px solid rgba(163, 209, 200, 0.1) !important;
        backdrop-filter: blur(1px) !important;
    }
    
    /* 🌡️ TEMPERATURE - OPTIMIZED FOR 70px WIDTH */
    .weather-temp-mobile {
        font-size: 0.85rem !important; /* Smaller to fit in 70px */
        line-height: 1.1 !important;
        white-space: nowrap !important;
        overflow: visible !important;
        display: block !important;
        font-weight: 600 !important; /* Lighter weight for smaller size */
        color: #047857 !important; /* Dark green */
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
        letter-spacing: 0.2px !important;
        margin: 0 !important;
        padding: 0 1px !important; /* Minimal padding */
        width: auto !important;
        max-width: none !important;
        text-align: center !important;
        z-index: 501 !important;
        position: relative !important;
        
        /* Compact optimization */
        filter: drop-shadow(0 0 3px rgba(4, 120, 87, 0.15)) !important;
    }
    
    /* 🌡️ LOCATION TEXT - ULTRA COMPACT */
    .weather-location-mobile {
        font-size: 0.6rem !important; /* Smaller for 70px widget */
        line-height: 1 !important;
        white-space: nowrap !important;
        overflow: visible !important;
        display: block !important;
        color: #059669 !important;
        opacity: 0.8 !important;
        margin-top: 1px !important;
        font-weight: 500 !important;
        text-align: center !important;
        letter-spacing: 0.1px !important;
        z-index: 501 !important;
        position: relative !important;
    }
    
    /* 🎮 CONTROLS ZONE - MORE SPACE from compact weather */
    .lg\\:hidden.flex.items-center.space-x-3 {
        flex: 0 0 115px !important; /* More space thanks to compact weather */
        min-width: 115px !important;
        max-width: 120px !important;
        gap: 0.5rem !important; /* Comfortable gaps */
        z-index: 600 !important;
        position: relative !important;
        align-items: center !important;
        justify-content: flex-end !important;
        overflow: visible !important;
        height: 100% !important;
        padding: 0.25rem 0 !important;
    }
    
    /* 🌍 LANGUAGE SWITCHER - COMFORTABLE SIZE */
    .language-switcher-mobile {
        flex: 0 0 55px !important;
        min-width: 55px !important;
        max-width: 55px !important;
        margin-left: 0.3rem !important;
        margin-right: 0.3rem !important;
        z-index: 700 !important;
        position: relative !important;
    }
    
    /* 🌍 LANGUAGE BUTTON - COMFORTABLE */
    .language-switcher-mobile .lang-btn-mobile {
        min-width: 55px !important;
        max-width: 55px !important;
        height: 40px !important;
        padding: 6px 10px !important;
        font-size: 0.75rem !important;
        gap: 0.25rem !important;
        border-radius: 7px !important;
        overflow: hidden !important;
        white-space: nowrap !important;
        z-index: 701 !important;
        position: relative !important;
        
        /* Subtle styling */
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.06) 0%, rgba(45, 212, 191, 0.03) 100%) !important;
        border: 1px solid rgba(163, 209, 200, 0.15) !important;
        backdrop-filter: blur(1px) !important;
        transition: all 0.3s ease !important;
    }
    
    /* 🍔 HAMBURGER MENU - COMFORTABLE */
    #mobile-menu-btn {
        flex: 0 0 50px !important;
        min-width: 50px !important;
        max-width: 50px !important;
        height: 50px !important;
        margin: 0 !important;
        padding: 13px !important;
        z-index: 800 !important;
        position: relative !important;
        border-radius: 8px !important;
        transition: all 0.3s ease !important;
        
        /* Subtle styling */
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.06) 0%, rgba(45, 212, 191, 0.03) 100%) !important;
        border: 1px solid rgba(163, 209, 200, 0.15) !important;
        backdrop-filter: blur(1px) !important;
    }
    
    /* 🍔 HAMBURGER ICON */
    #mobile-menu-btn i {
        font-size: 1.1rem !important;
        line-height: 1 !important;
        color: #047857 !important;
        z-index: 801 !important;
        position: relative !important;
    }
}

/* 🚨 EMERGENCY SPACE MATHEMATICS:
   Logo: 105px (horizontal layout, text visible)
   Weather: 70px (ULTRA COMPACT as requested)
   Controls: 115px (comfortable 2 buttons)
   
   Total: 290px for ~320px mobile = 91% efficiency
   Space gained: Weather 80px → 70px = +10px breathing room
   
   CRITICAL FIXES:
   ✅ "GolfinThaï" z-index priority + overflow visible
   ✅ Weather widget MUCH smaller (70px)
   ✅ All elements forced to proper positioning
*/

/* 📱 VERY SMALL SCREENS - PROPORTIONAL */
@media (max-width: 380px) {
    .flex.items-center.space-x-3:first-child {
        flex: 0 0 95px !important;
        max-width: 95px !important;
    }
    
    .logo-container-bg {
        width: 32px !important;
        height: 32px !important;
    }
    
    .header-logo-gradient {
        font-size: 0.8rem !important;
        max-width: 58px !important;
    }
    
    .weather-widget-mobile {
        flex: 0 0 65px !important;
        min-width: 65px !important;
        max-width: 68px !important;
    }
    
    .weather-temp-mobile {
        font-size: 0.8rem !important;
    }
    
    .weather-location-mobile {
        font-size: 0.55rem !important;
    }
    
    .lg\\:hidden.flex.items-center.space-x-3 {
        flex: 0 0 110px !important;
        max-width: 110px !important;
    }
    
    .language-switcher-mobile {
        flex: 0 0 50px !important;
        min-width: 50px !important;
        max-width: 50px !important;
    }
    
    #mobile-menu-btn {
        flex: 0 0 48px !important;
        min-width: 48px !important;
        max-width: 48px !important;
        height: 48px !important;
        padding: 11px !important;
    }
}

/* 🎨 MINIMAL HOVER EFFECTS - NO INTERFERENCE */
@media (max-width: 1023px) {
    
    /* Logo subtle hover */
    .flex.items-center.space-x-3:first-child:hover .header-logo-gradient {
        filter: drop-shadow(0 2px 4px rgba(6, 95, 70, 0.3)) brightness(1.05) !important;
    }
    
    /* Weather minimal hover */
    .weather-widget-mobile:hover {
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.08) 0%, rgba(45, 212, 191, 0.04) 100%) !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 2px 8px rgba(45, 212, 191, 0.08) !important;
    }
    
    /* Button hovers */
    .language-switcher-mobile:hover .lang-btn-mobile {
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.1) 0%, rgba(45, 212, 191, 0.05) 100%) !important;
        transform: scale(1.02) !important;
    }
    
    #mobile-menu-btn:hover {
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.1) 0%, rgba(45, 212, 191, 0.05) 100%) !important;
        transform: scale(1.02) !important;
    }
    
    /* Smooth transitions */
    .flex.items-center.space-x-3:first-child,
    .header-logo-gradient,
    .weather-widget-mobile,
    .weather-temp-mobile,
    .language-switcher-mobile .lang-btn-mobile,
    #mobile-menu-btn {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }
}

/* 🏆 EMERGENCY FIXES APPLIED:
   
   🚨 "GolfinThaï" TEXT ISSUES FIXED:
   ✅ z-index: 1003 (highest priority)
   ✅ overflow: visible (never hidden)
   ✅ position: relative (proper stacking)
   ✅ display: block + visibility: visible (forced)
   
   🚨 WEATHER WIDGET ULTRA-COMPACT:
   ✅ 80px → 70px (MUCH smaller as requested)
   ✅ Font sizes adjusted for smaller space
   ✅ Padding optimized for compact layout
   
   RESULT: "GolfinThaï" ALWAYS VISIBLE + Weather MINIMAL SIZE!
*/