/* =============================================
   📱 QUANTUM MOBILE UX FIXES - APPLE LEVEL
   Perfect mobile experience - Zero amateur pixels
   ============================================= */

/* 🍔 BURGER MENU - PERFECT CENTERING */
.lg\:hidden.flex.items-center.space-x-3 {
    align-items: center !important;
    justify-content: space-between !important;
    min-height: 48px !important;
    width: 100% !important;
}

#mobile-menu-btn {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 48px !important;
    min-height: 48px !important;
    margin: 0 !important;
    padding: 12px !important;
    background: transparent !important;
    border: none !important;
    border-radius: 12px !important;
    transition: all 0.2s ease !important;
    touch-action: manipulation !important;
    -webkit-tap-highlight-color: rgba(163, 209, 200, 0.2) !important;
}

#mobile-menu-btn:hover,
#mobile-menu-btn:focus {
    background: rgba(163, 209, 200, 0.1) !important;
    transform: scale(1.05) !important;
}

#mobile-menu-btn:active {
    transform: scale(0.95) !important;
    background: rgba(163, 209, 200, 0.2) !important;
}

/* 🌍 LANGUAGE SWITCHER MOBILE - NO OVERLAP FIX */
.language-switcher-mobile {
    position: relative !important;
    z-index: 1000 !important;
}

.language-switcher-mobile .lang-btn-mobile {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.25rem !important;
    padding: 8px 12px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(163, 209, 200, 0.3) !important;
    border-radius: 8px !important;
    color: white !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
    touch-action: manipulation !important;
    -webkit-tap-highlight-color: rgba(163, 209, 200, 0.2) !important;
    min-width: 60px !important;
    min-height: 36px !important;
}

.language-switcher-mobile .lang-dropdown-mobile {
    position: absolute !important;
    top: calc(100% + 8px) !important;
    right: 0 !important;
    left: auto !important;
    z-index: 9999 !important;
    background: white !important;
    border: 1px solid rgba(163, 209, 200, 0.3) !important;
    border-radius: 12px !important;
    box-shadow: 
        0 8px 25px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(163, 209, 200, 0.1) !important;
    overflow: hidden !important;
    min-width: 120px !important;
    transform: translateY(-10px) !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.language-switcher-mobile .lang-dropdown-mobile.dropdown-open {
    transform: translateY(0) !important;
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
}

.language-switcher-mobile .lang-option-mobile {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    padding: 12px 16px !important;
    background: transparent !important;
    border: none !important;
    color: #1f2937 !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    text-align: left !important;
    width: 100% !important;
    transition: all 0.2s ease !important;
    cursor: pointer !important;
    touch-action: manipulation !important;
    -webkit-tap-highlight-color: rgba(163, 209, 200, 0.2) !important;
}

.language-switcher-mobile .lang-option-mobile:hover,
.language-switcher-mobile .lang-option-mobile:focus {
    background: rgba(163, 209, 200, 0.1) !important;
}

.language-switcher-mobile .lang-option-mobile:active {
    background: rgba(163, 209, 200, 0.2) !important;
}

.language-switcher-mobile .lang-option-mobile.current {
    background: rgba(163, 209, 200, 0.15) !important;
    border-left: 3px solid #00574B !important;
    font-weight: 600 !important;
}

/* 🎯 Z-INDEX HIERARCHY - LOGO ALWAYS ON TOP */

/* 🏷️ LOGO CONTAINER - HIGHEST PRIORITY */
.logo-container-bg,
.flex.items-center.space-x-3 {
    z-index: 1000 !important;
    position: relative !important;
}

/* 🌡️ WEATHER WIDGET - LOWER PRIORITY */
.weather-widget-mobile,
.weather-widget-mini {
    z-index: 100 !important;
    position: relative !important;
}

/* 🚀 NUCLEAR LOGO SOLUTION: Background Image Method */
.logo-container-bg {
    width: 48px !important;
    height: 48px !important;
    border-radius: 50% !important;
    background-image: url('../images/logo-golfinthai.jpg') !important;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    flex-shrink: 0 !important;
    border: 2px solid #A3D1C8 !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

@media (min-width: 1024px) {
    .logo-container-bg {
        width: 64px !important;
        height: 64px !important;
    }
}

@media (min-width: 1024px) {
    .logo-container-bg {
        width: 64px !important;
        height: 64px !important;
    }
}
@media (max-width: 1023px) {
    .nav-bar .container .flex.justify-between {
        align-items: center !important;
        min-height: 80px !important;
        padding: 0.5rem 0 !important;
    }
    
    /* Logo section optimization */
    .flex.items-center.space-x-3 {
        flex: 1 !important;
        min-width: 0 !important;
    }
    
    /* Weather widget mobile centering */
    .weather-widget-mobile {
        flex-shrink: 0 !important;
        margin: 0 0.25rem !important; /* Reduced margin to avoid overlap */
        max-width: 60px !important; /* Limit width to prevent overflow */
        z-index: 100 !important;
        position: relative !important;
        text-align: center !important;
        overflow: hidden !important; /* Prevent text overflow */
    }
    
    /* Weather widget mobile text optimization */
    .weather-temp-mobile {
        font-size: 0.75rem !important; /* Slightly smaller text */
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }
    
    .weather-location-mobile {
        font-size: 0.7rem !important; /* Even smaller for BKK */
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }
    
    /* Mobile controls section */
    .lg\:hidden.flex.items-center.space-x-3 {
        flex-shrink: 0 !important;
        gap: 0.5rem !important;
    }
}

/* 📱 MOBILE MENU OVERLAY - ENHANCED */
@media (max-width: 768px) {
    .mobile-menu-overlay {
        backdrop-filter: blur(20px) !important;
        -webkit-backdrop-filter: blur(20px) !important;
    }
    
    .mobile-menu-link {
        min-height: 56px !important;
        padding: 16px 24px !important;
        touch-action: manipulation !important;
        -webkit-tap-highlight-color: rgba(163, 209, 200, 0.2) !important;
    }
    
    /* Enhanced close button */
    .mobile-menu-close {
        width: 56px !important;
        height: 56px !important;
        border-radius: 50% !important;
        background: rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(10px) !important;
        -webkit-backdrop-filter: blur(10px) !important;
        transition: all 0.2s ease !important;
    }
    
    .mobile-menu-close:hover,
    .mobile-menu-close:focus {
        background: rgba(255, 255, 255, 0.2) !important;
        transform: scale(1.05) !important;
    }
}

/* 🎨 SMOOTH ANIMATIONS - Apple-style */
@media (prefers-reduced-motion: no-preference) {
    .language-switcher-mobile .lang-dropdown-mobile.dropdown-open {
        animation: mobileDropdownSlide 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
    
    @keyframes mobileDropdownSlide {
        0% {
            opacity: 0;
            transform: translateY(-10px) scale(0.95);
        }
        100% {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }
}

/* ♿ ACCESSIBILITY ENHANCEMENTS */
.language-switcher-mobile .lang-btn-mobile:focus,
#mobile-menu-btn:focus {
    outline: 2px solid #A3D1C8 !important;
    outline-offset: 2px !important;
}

/* 🎯 TOUCH OPTIMIZATION */
@media (hover: none) and (pointer: coarse) {
    #mobile-menu-btn,
    .language-switcher-mobile .lang-btn-mobile,
    .language-switcher-mobile .lang-option-mobile {
        min-height: 44px !important;
        touch-action: manipulation !important;
    }
}

/* 🔧 Z-INDEX MANAGEMENT - Layered perfection */
.nav-bar {
    z-index: 1020 !important;
}

.language-switcher-mobile {
    z-index: 1021 !important;
}

.language-switcher-mobile .lang-dropdown-mobile {
    z-index: 1022 !important;
}

.mobile-menu-overlay {
    z-index: 1030 !important;
}

/* 🌟 VISUAL POLISH - The Apple touch */
.language-switcher-mobile .lang-dropdown-mobile::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 16px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid white;
    filter: drop-shadow(0 -2px 4px rgba(0, 0, 0, 0.1));
}

/* 💡 SMART DARK MODE SUPPORT */
@media (prefers-color-scheme: dark) {
    .language-switcher-mobile .lang-dropdown-mobile {
        background: #1f2937 !important;
        border-color: rgba(163, 209, 200, 0.4) !important;
    }
    
    .language-switcher-mobile .lang-option-mobile {
        color: white !important;
    }
    
    .language-switcher-mobile .lang-dropdown-mobile::before {
        border-bottom-color: #1f2937 !important;
    }
}
