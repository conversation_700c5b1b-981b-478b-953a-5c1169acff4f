/* 
 * =========================================================================
 * GOLFINTHAI - CORRECTIONS FINALES CONSOLIDÉES - VERSION PRODUCTION
 * =========================================================================
 * 
 * Fichier : golfinthai-fixes.css
 * Version : 1.0 Production
 * Auteur : Senior Developer Team
 * Date : 2025
 * 
 * Description : Corrections CSS consolidées pour le site GolfinThaï
 * - Carrousel fonctionnel et performant
 * - Hero title parfaitement positionné
 * - Boutons contact optimisés
 * - Effets gradient animés
 * - Responsive design complet
 * 
 * =========================================================================
 */

/* ===== CARROUSEL - PRODUCTION FINAL ===== */

/* Suppression du voile sombre pour couleurs vives */
.carousel-slide::before {
    display: none !important;
    content: none !important;
}

/* Slides carrousel - Configuration finale */
.carousel-slide {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    opacity: 0 !important;
    transition: opacity 1s ease-in-out !important;
    z-index: 1 !important;
    margin: 0 !important;
    padding: 0 !important;
    transform: none !important;
    display: block !important;
    visibility: visible !important;
    filter: contrast(1.05) saturate(1.1) brightness(1.02) !important;
}

/* Slide active visible */
.carousel-slide.active {
    opacity: 1 !important;
    z-index: 2 !important;
}

/* Container carrousel production */
.hero-carousel {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 1 !important;
    width: 100vw !important;
    height: 100vh !important;
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Amélioration des couleurs carrousel au hover */
.hero-carousel:hover .carousel-slide.active {
    filter: contrast(1.1) saturate(1.15) brightness(1.05) !important;
    transition: filter 0.3s ease !important;
}

/* Hero content au-dessus sans interférence */
.hero-content {
    pointer-events: none !important;
}

.hero-content * {
    pointer-events: auto !important;
}

/* Pagination visible et fonctionnelle */
.carousel-pagination {
    z-index: 10 !important;
    pointer-events: auto !important;
}

.pagination-bullet {
    z-index: 11 !important;
    pointer-events: auto !important;
    cursor: pointer !important;
}

/* Fallbacks pour compatibilité */
.hero-section .carousel-slide.active {
    opacity: 1 !important;
}

.hero-section::before,
.hero-section::after {
    z-index: 0 !important;
}

/* ===== HERO TITLE - ESPACEMENT ET POSITIONNEMENT ===== */

/* Conteneur hero parfaitement centré */
.hero-content {
    position: absolute !important;
    top: 50% !important;                   /* Position test 50% */
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    z-index: 10 !important;
    width: 100% !important;
    text-align: center !important;
}

/* Espacement entre GOLFEZ et AUTREMENT via line-height */
.hero-title {
    font-size: clamp(3rem, 8vw, 6rem) !important;
    font-weight: 900 !important;
    letter-spacing: 0.05em !important;
    line-height: 2.2 !important;             /* Écarte les mots */
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7) !important;
    color: white !important;
    margin: 0 !important;
    padding: 0 !important;
    text-align: center !important;
}

/* BR sans interférence */
.hero-title br {
    display: block !important;
    margin-top: 0 !important;               /* Pas d'interférence */
    margin-bottom: 0 !important;            /* Pas d'interférence */
    height: auto !important;
    padding: 0 !important;
    content: "" !important;
}

/* ===== BOUTON CONTACT - VISIBILITÉ ET ESPACEMENT ===== */

/* Bouton contact visible et professionnel */
.contact-form .btn-primary,
button[type="submit"].btn-primary {
    background: linear-gradient(135deg, #2c5f54 0%, #1a4139 50%, #0d1b1a 100%) !important;
    color: white !important;
    border: 2px solid #4a9b8e !important;
    font-weight: 600 !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5) !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;                  /* Espacement entre icône et texte */
    padding: 0.875rem 2rem !important;
}

/* Espacement spécifique pour l'icône dans le bouton */
.contact-form .btn-primary i,
button[type="submit"].btn-primary i {
    margin-right: 0.5rem !important;        /* Espace après l'icône */
    margin-left: 0 !important;
    display: inline-block !important;
}

/* Si l'icône est en Font Awesome */
.contact-form .btn-primary .fa,
.contact-form .btn-primary .fas,
button[type="submit"].btn-primary .fa,
button[type="submit"].btn-primary .fas {
    margin-right: 0.75rem !important;       /* Plus d'espace pour Font Awesome */
    font-size: 1rem !important;
}

.contact-form .btn-primary:hover,
button[type="submit"].btn-primary:hover {
    background: linear-gradient(135deg, #4a9b8e 0%, #2c5f54 50%, #1a4139 100%) !important;
    border-color: #63d4c1 !important;
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4) !important;
    color: white !important;
}

/* ===== SECTIONS RÉGIONALES - ESPACEMENT ===== */

/* Espacement correct section "Throughout Thailand" */
.regional-features .feature-item {
    margin-bottom: 2rem !important;
}

.regional-features .flex.justify-center {
    margin-top: 3rem !important;
    padding-top: 2rem !important;
    border-top: 1px solid rgba(163, 209, 200, 0.2) !important;
}

.features-grid {
    gap: 2rem !important;
    margin-bottom: 2rem !important;
}

.regional-features .flex.justify-center .feature-item {
    margin-top: 1rem !important;
    padding: 2rem !important;
    background: rgba(26, 44, 42, 0.3) !important;
    border-radius: 1rem !important;
    border: 1px solid rgba(163, 209, 200, 0.2) !important;
}

/* ===== RESPONSIVE ===== */

@media (max-width: 768px) {
    .hero-title {
        line-height: 2.0 !important;        /* Légèrement réduit sur mobile */
    }
    
    .hero-content {
        top: 48% !important;                /* Position mobile ajustée pour 50% desktop */
    }
    
    .contact-form .btn-primary,
    button[type="submit"].btn-primary {
        font-size: 1rem !important;
        padding: 0.875rem 2rem !important;
    }
    
    .regional-features .flex.justify-center {
        margin-top: 2rem !important;
        padding-top: 1.5rem !important;
    }
    
    .carousel-slide {
        width: 100vw !important;
        height: 100vh !important;
        height: 100dvh !important;
    }
}

/* ===== EFFET GRADIENT TEXTE - SPÉCIFICITÉ QUANTUM AI ===== */

/* Sélecteurs hyper-spécifiques pour dominer Tailwind et tous les frameworks */
html body .container .section-title .text-gradient,
html body .services-section .section-title .text-gradient,
html body .destinations-section .section-title .text-gradient,
html body .about-section .section-title .text-gradient,
html body .testimonials-section .section-title .text-gradient,
html body .contact-section .section-title .text-gradient,
html body .section-header .section-title .text-gradient,
html body .text-gradient,
span.text-gradient,
.text-gradient {
    background: linear-gradient(135deg, 
        #4a9b8e 0%, 
        #63d4c1 20%, 
        #2d8b7f 40%,
        #4fcfa6 60%,
        #2c5f54 80%, 
        #63d4c1 100%) !important;
    background-size: 300% 300% !important;
    background-clip: text !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    color: transparent !important;
    animation: quantumGradientRotate 3s linear infinite !important;
    font-weight: 700 !important;
    position: relative !important;
    display: inline-block !important;
    text-shadow: none !important;
    opacity: 1 !important;
    z-index: 10 !important;
    will-change: background !important;
}

/* Animation Quantum ROTATION - Gradient qui TOURNE physiquement */
@keyframes quantumGradientRotate {
    0% { 
        background: linear-gradient(0deg, 
            #4a9b8e 0%, #63d4c1 20%, #2d8b7f 40%, #4fcfa6 60%, #2c5f54 80%, #63d4c1 100%) !important;
    }
    25% { 
        background: linear-gradient(90deg, 
            #4a9b8e 0%, #63d4c1 20%, #2d8b7f 40%, #4fcfa6 60%, #2c5f54 80%, #63d4c1 100%) !important;
    }
    50% { 
        background: linear-gradient(180deg, 
            #4a9b8e 0%, #63d4c1 20%, #2d8b7f 40%, #4fcfa6 60%, #2c5f54 80%, #63d4c1 100%) !important;
    }
    75% { 
        background: linear-gradient(270deg, 
            #4a9b8e 0%, #63d4c1 20%, #2d8b7f 40%, #4fcfa6 60%, #2c5f54 80%, #63d4c1 100%) !important;
    }
    100% { 
        background: linear-gradient(360deg, 
            #4a9b8e 0%, #63d4c1 20%, #2d8b7f 40%, #4fcfa6 60%, #2c5f54 80%, #63d4c1 100%) !important;
    }
}

/* Animation alternative avec couleurs ultra-contrastées */
@keyframes quantumGradientMegaVisible {
    0% { 
        background: linear-gradient(45deg, #ff0000, #00ff00, #0000ff) !important;
        background-size: 400% 400% !important;
        background-position: 0% 0% !important;
    }
    25% { 
        background: linear-gradient(45deg, #00ff00, #0000ff, #ff0000) !important;
        background-size: 400% 400% !important;
        background-position: 100% 0% !important;
    }
    50% { 
        background: linear-gradient(45deg, #0000ff, #ff0000, #00ff00) !important;
        background-size: 400% 400% !important;
        background-position: 100% 100% !important;
    }
    75% { 
        background: linear-gradient(45deg, #ff0000, #00ff00, #0000ff) !important;
        background-size: 400% 400% !important;
        background-position: 0% 100% !important;
    }
    100% { 
        background: linear-gradient(45deg, #00ff00, #0000ff, #ff0000) !important;
        background-size: 400% 400% !important;
        background-position: 0% 0% !important;
    }
}

/* Fallback ultime - hue-rotate pour navigateurs difficiles */
@keyframes quantumHueRotate {
    0% { 
        filter: hue-rotate(0deg) saturate(1.5) brightness(1.2) !important;
    }
    25% { 
        filter: hue-rotate(90deg) saturate(1.8) brightness(1.4) !important;
    }
    50% { 
        filter: hue-rotate(180deg) saturate(2) brightness(1.6) !important;
    }
    75% { 
        filter: hue-rotate(270deg) saturate(1.8) brightness(1.4) !important;
    }
    100% { 
        filter: hue-rotate(360deg) saturate(1.5) brightness(1.2) !important;
    }
}

/* Fallback Quantum pour navigateurs legacy */
@supports not (background-clip: text) {
    html body .text-gradient,
    span.text-gradient,
    .text-gradient {
        color: #4a9b8e !important;
        -webkit-text-fill-color: #4a9b8e !important;
        background: none !important;
        text-shadow: 0 0 20px rgba(74, 155, 142, 0.8), 
                     0 0 40px rgba(99, 212, 193, 0.6),
                     0 0 60px rgba(45, 139, 127, 0.4) !important;
        animation: quantumGlowPulse 2s ease-in-out infinite !important;
    }
    
    @keyframes quantumGlowPulse {
        0%, 100% { 
            text-shadow: 0 0 20px rgba(74, 155, 142, 0.8), 
                         0 0 40px rgba(99, 212, 193, 0.6),
                         0 0 60px rgba(45, 139, 127, 0.4);
        }
        50% { 
            text-shadow: 0 0 30px rgba(74, 155, 142, 1), 
                         0 0 60px rgba(99, 212, 193, 0.8),
                         0 0 90px rgba(45, 139, 127, 0.6);
        }
    }
}

/* Override spécifique anti-Tailwind */
.text-gradient[class*="text-"],
.text-gradient[class*="color-"],
.text-gradient[class*="bg-"] {
    background: linear-gradient(135deg, 
        #4a9b8e 0%, 
        #63d4c1 20%, 
        #2d8b7f 40%,
        #4fcfa6 60%,
        #2c5f54 80%, 
        #63d4c1 100%) !important;
    background-size: 300% 300% !important;
    background-clip: text !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    color: transparent !important;
    animation: quantumGradientMove 3s ease-in-out infinite !important;
}

/* Effet hover Quantum */
.text-gradient:hover {
    animation-duration: 1.5s !important;
    background-size: 400% 400% !important;
    transform: scale(1.02) !important;
    transition: transform 0.3s ease !important;
}

/* ===== FIN FICHIER ===== */
