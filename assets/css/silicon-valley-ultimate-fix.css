/*
 * ❌ SECTION DRAPEAUX DÉSACTIVÉE - REMPLACÉE PAR simple-clean-fix.css
 * Les drapeaux sont maintenant gérés dans simple-clean-fix.css avec un design premium
 * Cette section est conservée uniquement pour les gradients rotatifs
 */

/* ===== 🌈 SECTION GRADIENTS ROTATIFS RESTAURÉS ===== */

/* Sélecteurs ultra-spécifiques pour dominer tous les CSS conflictuels */
html body main section .container .section-header .section-title .text-gradient,
html body main .services-section .container .section-header .section-title .text-gradient,
html body main .destinations-section .container .section-header .section-title .text-gradient,
html body main .about-section .container .section-header .section-title .text-gradient,
html body main .testimonials-section .container .section-header .section-title .text-gradient,
html body main .contact-section .container .section-header .section-title .text-gradient,
html body .section-title .text-gradient,
html body .text-gradient,
span.text-gradient {
    /* Reset pour éliminer les conflits */
    color: transparent !important;
    text-shadow: none !important;
    
    /* Application du gradient rotatif */
    background: linear-gradient(0deg, 
        #4a9b8e 0%, 
        #63d4c1 20%, 
        #2d8b7f 40%, 
        #4fcfa6 60%, 
        #2c5f54 80%, 
        #63d4c1 100%) !important;
    
    background-size: 400% 400% !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    
    /* Animation de rotation */
    animation: quantumGradientRotationUltimate 4s linear infinite !important;
    
    /* Optimisations performance */
    will-change: background-position !important;
    transform: translateZ(0) !important;
    
    /* Style de base */
    font-weight: 700 !important;
    display: inline-block !important;
    position: relative !important;
    z-index: 1 !important;
}

/* Animation de rotation ultra-smooth avec vraie rotation physique */
@keyframes quantumGradientRotationUltimate {
    0% { 
        background: linear-gradient(0deg, 
            #4a9b8e 0%, #63d4c1 20%, #2d8b7f 40%, #4fcfa6 60%, #2c5f54 80%, #63d4c1 100%) !important;
        background-position: 0% 50% !important;
    }
    25% { 
        background: linear-gradient(90deg, 
            #4a9b8e 0%, #63d4c1 20%, #2d8b7f 40%, #4fcfa6 60%, #2c5f54 80%, #63d4c1 100%) !important;
        background-position: 100% 50% !important;
    }
    50% { 
        background: linear-gradient(180deg, 
            #4a9b8e 0%, #63d4c1 20%, #2d8b7f 40%, #4fcfa6 60%, #2c5f54 80%, #63d4c1 100%) !important;
        background-position: 100% 100% !important;
    }
    75% { 
        background: linear-gradient(270deg, 
            #4a9b8e 0%, #63d4c1 20%, #2d8b7f 40%, #4fcfa6 60%, #2c5f54 80%, #63d4c1 100%) !important;
        background-position: 0% 100% !important;
    }
    100% { 
        background: linear-gradient(360deg, 
            #4a9b8e 0%, #63d4c1 20%, #2d8b7f 40%, #4fcfa6 60%, #2c5f54 80%, #63d4c1 100%) !important;
        background-position: 0% 50% !important;
    }
}

/* ===== 🎯 SECTION 3: RESPONSIVE PERFECTION ===== */

/* Mobile adaptations */
@media (max-width: 768px) {
    html body .flag-icon {
        width: 20px !important;
        height: 15px !important;
        margin-right: 6px !important;
    }
}

@media (max-width: 480px) {
    html body .flag-icon {
        width: 18px !important;
        height: 14px !important;
        margin-right: 5px !important;
    }
}

/* ===== 🔧 SECTION 4: OVERRIDES SPÉCIAUX ===== */

/* Override pour éliminer tous les conflits de z-index */
html body .lang-dropdown,
html body .lang-dropdown-mobile {
    z-index: 9999 !important;
}

/* Override pour les modales (garde mes fixes précédents) */
html body .premium-modal-title {
    background: linear-gradient(135deg, var(--color-emerald), var(--color-emerald-light)) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    animation: none !important; /* Pas de rotation pour les titres modaux */
}
