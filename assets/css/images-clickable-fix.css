/* 🚨 URGENT FIX - Images Clickables Bloquées par Overlays */
/* Correction pour permettre aux clics de passer aux images */

.course-overlay {
    pointer-events: none !important;
}

/* Autoriser les clics sur les éléments interactifs dans l'overlay */
.course-overlay .course-location-badge,
.course-overlay .course-expand-btn {
    pointer-events: auto !important;
}

/* Assurer que les images sont bien cliquables */
.course-image {
    pointer-events: auto !important;
    cursor: pointer !important;
}

/* Fix pour intro-image aussi */
.intro-image {
    pointer-events: auto !important;
    cursor: pointer !important;
}

/* Hover overlay - autoriser les clics à passer */
.course-card::before {
    pointer-events: none !important;
}

.course-card:hover .course-overlay {
    pointer-events: none !important;
}

/* Seuls les boutons et badges sont cliquables dans l'overlay */
.course-expand-btn {
    pointer-events: auto !important;
    cursor: pointer !important;
    z-index: 10 !important;
}

.course-location-badge {
    pointer-events: auto !important;
    z-index: 10 !important;
}

/* 📝 Notes de Debug */
/*
PROBLÈME IDENTIFIÉ:
- Les overlays (.course-overlay) ont position: absolute couvrant toute l'image
- Ils bloquent les événements onclick="openSimpleModal(...)" des images
- Même avec opacity: 0, les overlays interceptent les clics

SOLUTION:
- pointer-events: none sur les overlays
- pointer-events: auto sur les éléments interactifs spécifiques
- Les images restent cliquables pour ouvrir les modales
*/