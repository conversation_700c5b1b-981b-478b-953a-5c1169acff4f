/* =============================================
   🌍 LANGUAGE SWITCHER MOBILE OPTIMIZATION
   CLEAN SOLUTION: Letters only on mobile (FR/EN)
   Flag + Letters on desktop
   ============================================= */

/* 📱 MOBILE: Letters only - Clean & Readable */
@media (max-width: 1023px) {
    
    /* 🌍 HIDE FLAG ON MOBILE - Keep letters only */
    .language-switcher-mobile .lang-btn-mobile .flag-icon {
        display: none !important;
    }
    
    /* 🔤 ENHANCE LETTERS ON MOBILE - Bigger & Centered */
    .language-switcher-mobile .lang-btn-mobile .lang-text-mobile {
        font-size: 0.75rem !important; /* Bigger than 0.6rem */
        font-weight: 700 !important; /* Bolder */
        letter-spacing: 0.5px !important; /* Better spacing */
        text-align: center !important;
        width: 100% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        margin: 0 !important;
        padding: 0 !important;
        
        /* Clear colors for better visibility */
        color: #047857 !important;
        text-shadow: none !important;
        text-transform: uppercase !important;
    }
    
    /* 🌍 MOBILE BUTTON - Optimized for letters only */
    .language-switcher-mobile .lang-btn-mobile {
        min-width: 38px !important;
        max-width: 38px !important;
        height: 32px !important;
        padding: 0 !important; /* Remove padding for better centering */
        gap: 0 !important; /* No gap needed */
        
        /* Better centering */
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        
        /* Clean styling */
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.06) 0%, rgba(45, 212, 191, 0.03) 100%) !important;
        border: 1px solid rgba(163, 209, 200, 0.15) !important;
        border-radius: 6px !important;
        backdrop-filter: blur(2px) !important;
        transition: all 0.3s ease !important;
    }
    
    /* 📱 MOBILE DROPDOWN - Letters only */
    .lang-dropdown-mobile {
        min-width: 45px !important; /* Slightly wider for readability */
        padding: 2px 0 !important;
        border-radius: 6px !important;
        
        /* Better positioning */
        right: 0 !important;
        left: auto !important;
        top: 100% !important;
        margin-top: 2px !important;
    }
    
    /* 📱 DROPDOWN OPTIONS - Clean letters */
    .lang-option-mobile {
        padding: 6px 0 !important; /* Vertical padding only */
        text-align: center !important;
        gap: 0 !important;
        min-height: 28px !important;
        
        /* Hide flags in dropdown too */
    }
    
    .lang-option-mobile .flag-icon {
        display: none !important;
    }
    
    .lang-option-mobile span:not(.flag-icon) {
        font-size: 0.7rem !important;
        font-weight: 600 !important;
        color: #047857 !important;
        text-transform: uppercase !important;
        letter-spacing: 0.3px !important;
    }
    
    /* 🎨 ENHANCED HOVER ON MOBILE */
    .language-switcher-mobile:hover .lang-btn-mobile {
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.12) 0%, rgba(45, 212, 191, 0.06) 100%) !important;
        border-color: rgba(163, 209, 200, 0.3) !important;
        transform: scale(1.05) !important;
    }
    
    .language-switcher-mobile:hover .lang-text-mobile {
        color: #065f46 !important;
        transform: scale(1.1) !important;
    }
    
    .lang-option-mobile:hover {
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.08) 0%, rgba(45, 212, 191, 0.04) 100%) !important;
    }
    
    .lang-option-mobile:hover span:not(.flag-icon) {
        color: #065f46 !important;
        transform: scale(1.05) !important;
    }
}

/* 🖥️ DESKTOP: Keep flag + letters (unchanged) */
@media (min-width: 1024px) {
    /* Desktop styling remains with both flag and text */
    .language-switcher-desktop .lang-btn .flag-icon {
        display: inline-block !important;
    }
    
    .language-switcher-desktop .lang-btn .lang-text {
        display: inline-block !important;
        margin-left: 0.5rem !important;
    }
}

/* 🔥 ULTRA-SMALL SCREENS - Even cleaner */
@media (max-width: 380px) {
    .language-switcher-mobile .lang-btn-mobile {
        min-width: 36px !important;
        max-width: 36px !important;
        height: 30px !important;
    }
    
    .language-switcher-mobile .lang-btn-mobile .lang-text-mobile {
        font-size: 0.68rem !important;
        letter-spacing: 0.3px !important;
    }
    
    .lang-dropdown-mobile {
        min-width: 40px !important;
    }
    
    .lang-option-mobile span:not(.flag-icon) {
        font-size: 0.65rem !important;
    }
}

/* =============================================
   🎯 SOLUTION RESULTS:
   ✅ Mobile: CLEAN letters only (FR/EN) - No more tiny flags
   ✅ Desktop: Keep flag + letters for full experience
   ✅ Better readability on small screens
   ✅ Consistent with your compact design
   ✅ Professional and accessible
   
   FRÉRO'S MOBILE UX: PERFECTION! 🏆
   ============================================= */