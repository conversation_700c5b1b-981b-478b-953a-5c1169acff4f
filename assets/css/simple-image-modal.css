/* =============================================
   🚀 LEGACY SIMPLE IMAGE MODAL - DÉSACTIVÉ
   Remplacé par Ultra Premium Modal V3
   ============================================= */

/* 
   ⚠️ CE CSS EST DÉSACTIVÉ POUR ÉVITER LES CONFLITS
   Le nouveau système Ultra Premium Modal V3 gère tout
   
   Si tu veux réactiver l'ancien système :
   1. Supprime les commentaires /* */
   2. Change le script vers V2 dans index.html
*/

/*
.simple-image-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 99999;
    display: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.simple-modal-image {
    max-width: 90vw !important;
    max-height: 90vh !important;
    width: auto !important;
    height: auto !important;
    min-width: 300px !important;
    min-height: 200px !important;
    object-fit: contain !important;
    border-radius: 12px !important;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5) !important;
    background: white !important;
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    cursor: default !important;
}

.simple-modal-close {
    position: absolute !important;
    top: -50px !important;
    right: 0 !important;
    background: rgba(255, 255, 255, 0.9) !important;
    border: none !important;
    border-radius: 50% !important;
    width: 44px !important;
    height: 44px !important;
    font-size: 24px !important;
    cursor: pointer !important;
    color: #333 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.2s ease !important;
    z-index: 100000 !important;
}

.simple-modal-close:hover {
    background: white !important;
    transform: scale(1.1) !important;
}

@media (max-width: 768px) {
    .simple-modal-image {
        max-width: 95vw !important;
        max-height: 85vh !important;
        min-width: 250px !important;
        min-height: 150px !important;
    }
    
    .simple-modal-close {
        top: -40px !important;
        right: 10px !important;
        width: 40px !important;
        height: 40px !important;
        font-size: 20px !important;
    }
}

@media (max-width: 480px) {
    .simple-modal-image {
        max-width: 98vw !important;
        max-height: 80vh !important;
        min-width: 200px !important;
        min-height: 120px !important;
    }
}

.simple-image-modal {
    transition: opacity 0.3s ease;
}

.simple-modal-close:focus {
    outline: 2px solid #A3D1C8;
    outline-offset: 2px;
}

body:has(.simple-image-modal[style*="flex"]) {
    overflow: hidden;
}

.simple-modal-open {
    overflow: hidden !important;
}

.simple-modal-image[src=""] {
    background: #f0f0f0 url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="20" fill="none" stroke="%23A3D1C8" stroke-width="4" stroke-dasharray="31.416" stroke-dashoffset="31.416"><animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/><animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/></circle></svg>') center center no-repeat !important;
    background-size: 50px 50px !important;
}

.simple-image-modal[style*="flex"] .simple-modal-image {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}
*/

/* ✅ NOUVEAU SYSTÈME ACTIF : Ultra Premium Modal V3 */
