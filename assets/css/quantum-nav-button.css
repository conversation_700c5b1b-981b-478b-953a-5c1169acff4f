/* ===================================
   🎯 QUANTUM NAVIGATION BUTTON - PREMIUM LEVEL
   Perfect integration with GolfinThai design system
   INSTANT STYLES - No loading flash!
   =================================== */

/* ✅ INSTANT PERFECT STYLES - Apply immediately to all contact buttons */
.btn-primary,
a[href="#contact"],
.quantum-contact-btn {
    position: relative !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
    padding: 0.75rem 1.5rem !important;
    font-family: var(--font-sans) !important;
    font-weight: 600 !important;
    font-size: 0.95rem !important;
    text-decoration: none !important;
    border: none !important;
    border-radius: 2rem !important;
    cursor: pointer !important;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    overflow: hidden !important;
    white-space: nowrap !important;
    
    /* 🌿 GRADIENT BACKGROUND - Matching theme */
    background: linear-gradient(135deg, #00574B 0%, #A3D1C8 50%, #00574B 100%) !important;
    background-size: 200% 200% !important;
    color: white !important;
    
    /* 🚀 PERFORMANCE */
    will-change: transform, background-position, box-shadow !important;
    backface-visibility: hidden !important;
    transform: translateZ(0) !important;
    
    /* 🎯 TOUCH TARGET - Mobile first */
    min-height: 44px !important;
    min-width: 140px !important;
    
    /* ✨ INSTANT ANIMATION */
    animation: buttonGradientShift 4s ease-in-out infinite !important;
}

/* ✨ ANIMATED GRADIENT - Matching text-gradient speed */
@keyframes buttonGradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* 🎭 HOVER EFFECTS - Apple level - Apply to all button types */
.btn-primary:hover,
a[href="#contact"]:hover,
.quantum-contact-btn:hover {
    transform: translateY(-2px) scale(1.02) !important;
    box-shadow: 
        0 10px 25px rgba(0, 87, 75, 0.3),
        0 0 0 1px rgba(163, 209, 200, 0.2) !important;
    animation-duration: 2s !important; /* Faster on hover */
}

.btn-primary:active,
a[href="#contact"]:active,
.quantum-contact-btn:active {
    transform: translateY(-1px) scale(0.98) !important;
    transition-duration: 0.1s !important;
}

/* 🌟 SHINE EFFECT - Premium touch */
.quantum-contact-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.2), 
        transparent);
    transition: left 0.6s ease;
}

.quantum-contact-btn:hover::before {
    left: 100%;
}

/* 💫 RIPPLE EFFECT - On click */
.quantum-contact-btn::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    transform: scale(0);
    opacity: 0;
    pointer-events: none;
}

.quantum-contact-btn.ripple::after {
    animation: rippleEffect 0.6s ease-out;
}

@keyframes rippleEffect {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

/* 🏷️ BUTTON ICON - Elegant spacing */
.quantum-contact-btn i {
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.quantum-contact-btn:hover i {
    transform: translateX(2px);
}

/* 📱 MOBILE OPTIMIZATIONS */
@media (max-width: 768px) {
    .quantum-contact-btn {
        padding: 0.7rem 1.25rem;
        font-size: 0.9rem;
        min-width: 120px;
        gap: 0.4rem;
    }
    
    .quantum-contact-btn i {
        font-size: 0.9rem;
    }
}

/* 🌐 DESKTOP ENHANCEMENTS */
@media (min-width: 1024px) {
    .quantum-contact-btn {
        padding: 0.8rem 1.75rem;
        font-size: 1rem;
        min-width: 160px;
    }
    
    /* 🎯 SUBTLE GLOW ON LARGE SCREENS */
    .quantum-contact-btn:hover {
        box-shadow: 
            0 15px 35px rgba(0, 87, 75, 0.4),
            0 0 20px rgba(163, 209, 200, 0.15),
            0 0 0 1px rgba(163, 209, 200, 0.2);
    }
}

/* 🎪 LOADING STATE - For form submission */
.quantum-contact-btn.loading {
    pointer-events: none;
    opacity: 0.8;
}

.quantum-contact-btn.loading::before {
    animation: loadingShimmer 1.5s linear infinite;
}

@keyframes loadingShimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* ♿ ACCESSIBILITY ENHANCEMENTS */
.quantum-contact-btn:focus-visible {
    outline: 2px solid #A3D1C8;
    outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
    .quantum-contact-btn {
        animation: none;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .quantum-contact-btn::before {
        display: none;
    }
    
    .quantum-contact-btn:hover {
        transform: none;
    }
}

/* 🎨 ALTERNATIVE STYLES - Secondary button */
.quantum-contact-btn.secondary {
    background: transparent;
    border: 2px solid transparent;
    background-image: linear-gradient(rgba(13, 27, 26, 0.9), rgba(13, 27, 26, 0.9)),
                      linear-gradient(135deg, #00574B 0%, #A3D1C8 50%, #00574B 100%);
    background-origin: border-box;
    background-clip: content-box, border-box;
    color: #A3D1C8;
    animation: none;
}

.quantum-contact-btn.secondary:hover {
    background-image: linear-gradient(rgba(13, 27, 26, 0.7), rgba(13, 27, 26, 0.7)),
                      linear-gradient(135deg, #A3D1C8 0%, #00574B 50%, #A3D1C8 100%);
    color: white;
}

/* 🌟 SUCCESS STATE - After form submission */
.quantum-contact-btn.success {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    animation: successPulse 2s ease-in-out;
}

@keyframes successPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}