/* =============================================
   🚨 EMERGENCY IMAGE MODAL VISIBILITY FIX
   Bulletproof solution - Override everything
   ============================================= */

/* 🚨 NUCLEAR OPTION - FORCE IMAGE VISIBILITY */
.quantum-image-modal .modal-image,
.course-modal .modal-image,
.modal-image {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    position: relative !important;
    z-index: 10001 !important;
    background: transparent !important;
    border: 2px solid #A3D1C8 !important; /* DEBUG: Visible border */
    min-width: 400px !important;
    min-height: 300px !important;
    max-width: 90vw !important;
    max-height: 80vh !important;
    object-fit: contain !important;
    margin: auto !important;
}

/* 🚨 FORCE CONTAINER VISIBILITY */
.quantum-image-modal .image-container,
.course-modal .image-container,
.modal-container .image-container {
    opacity: 1 !important;
    visibility: visible !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: rgba(255, 255, 255, 0.1) !important; /* DEBUG: Visible background */
    border: 1px solid rgba(163, 209, 200, 0.5) !important; /* DEBUG: Visible border */
    padding: 20px !important;
    width: 100% !important;
    height: 100% !important;
    z-index: 10000 !important;
}

/* 🚨 FORCE MODAL CONTAINER VISIBILITY */
.quantum-image-modal .modal-container,
.course-modal .modal-container {
    opacity: 1 !important;
    visibility: visible !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: rgba(0, 0, 0, 0.1) !important; /* DEBUG: Slightly visible background */
    width: 100% !important;
    height: 100% !important;
    z-index: 9999 !important;
    position: relative !important;
}

/* 🚨 FORCE MODAL BACKDROP */
.quantum-image-modal,
.course-modal {
    background: rgba(0, 0, 0, 0.9) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
}

/* 🚨 REMOVE ANY HIDDEN CLASSES */
.modal-image.hidden,
.image-container.hidden,
.modal-container.hidden {
    display: flex !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* 🚨 DEBUG INFO - Shows image loading status */
.modal-image::after {
    content: 'Image loaded: ' attr(src) !important;
    position: absolute !important;
    bottom: -30px !important;
    left: 0 !important;
    color: #A3D1C8 !important;
    font-size: 12px !important;
    font-family: monospace !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 300px !important;
    z-index: 10002 !important;
}

/* 🚨 LOADING SPINNER OVERRIDE */
.loading-spinner {
    background: rgba(163, 209, 200, 0.2) !important;
    color: white !important;
    z-index: 10000 !important;
}

.loading-spinner::before {
    content: 'Chargement image...' !important;
    color: white !important;
    font-size: 16px !important;
    margin-bottom: 10px !important;
}

/* 📱 MOBILE EMERGENCY FIXES */
@media (max-width: 768px) {
    .modal-image {
        min-width: 300px !important;
        min-height: 200px !important;
        max-width: 95vw !important;
        max-height: 70vh !important;
    }
    
    .image-container {
        padding: 10px !important;
    }
}

/* 🚨 ACCESSIBILITY EMERGENCY */
.modal-image:focus {
    outline: 3px solid #A3D1C8 !important;
    outline-offset: 2px !important;
}

/* 🚨 ANTI-TRANSFORM CONFLICTS */
.modal-image {
    transform: none !important;
    transition: none !important;
}

.modal-image:hover {
    transform: none !important;
}
