/* 🎯 LOGO FIX FINAL - SENIOR DEV SOLUTION CORRECTED */
/* Desktop: cover (fills circle) | Mobile: contain (shows full logo) */

.logo-container-bg {
    /* Desktop default - COVER (fills the circle perfectly) */
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    
    /* Perfect circle */
    border-radius: 50% !important;
    overflow: hidden !important;
    
    /* Consistent sizing */
    width: 64px !important;
    height: 64px !important;
    min-width: 64px !important;
    min-height: 64px !important;
    
    /* Positioning */
    flex-shrink: 0 !important;
    display: block !important;
}

/* Mobile-specific - CONTAIN (shows full logo without cropping) */
@media screen and (max-width: 1023px) {
    .logo-container-bg {
        background-size: contain !important;
        background-color: rgba(255, 255, 255, 0.15) !important;
    }
}

/* High-DPI displays */
@media screen and (-webkit-min-device-pixel-ratio: 2) {
    .logo-container-bg {
        image-rendering: -webkit-optimize-contrast !important;
        image-rendering: crisp-edges !important;
    }
}
