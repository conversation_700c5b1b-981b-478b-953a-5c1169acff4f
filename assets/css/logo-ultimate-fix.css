/* 🎯 LOGO FINAL CSS - ÉCRASE TOUT LE BORDEL */
/* Solution propre qui marche pour tous les visiteurs */

/* Desktop par défaut - Logo rempli le cercle */
.logo-container-bg {
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    width: 64px !important;
    height: 64px !important;
    border-radius: 50% !important;
    overflow: hidden !important;
    flex-shrink: 0 !important;
    display: block !important;
}

/* Mobile - Logo complet visible */
@media screen and (max-width: 1023px) {
    .logo-container-bg {
        background-size: contain !important;
        background-color: rgba(255, 255, 255, 0.2) !important;
    }
}

/* Détection tactile = mobile */
@media (hover: none) and (pointer: coarse) {
    .logo-container-bg {
        background-size: contain !important;
        background-color: rgba(255, 255, 255, 0.2) !important;
    }
}

/* Force pour tous les scripts JS */
[style*="background-size"] .logo-container-bg,
.logo-container-bg[style*="background-size"] {
    background-size: cover !important;
}

@media screen and (max-width: 1023px) {
    [style*="background-size"] .logo-container-bg,
    .logo-container-bg[style*="background-size"] {
        background-size: contain !important;
        background-color: rgba(255, 255, 255, 0.2) !important;
    }
}

/* Spécificité maximale */
html body .logo-container-bg {
    background-size: cover !important;
}

@media screen and (max-width: 1023px) {
    html body .logo-container-bg {
        background-size: contain !important;
        background-color: rgba(255, 255, 255, 0.2) !important;
    }
}
