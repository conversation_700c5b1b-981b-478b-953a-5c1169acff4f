/* =============================================
   🎯 QUANTUM RIGHT-SIDE COMPACT - FINAL PERFECT
   Weather + Language NEAR HAMBURGER (right side)
   MAXIMUM space for "GolfinThaï" in center
   ============================================= */

/* 📱 MOBILE HEADER RIGHT-SIDE PERFECT LAYOUT */
@media (max-width: 1023px) {
    
    /* 🎯 MAIN HEADER CONTAINER - Perfect distribution */
    .nav-bar .container .flex.justify-between {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        gap: 0 !important;
        padding: 0.5rem 0 !important;
        position: relative !important;
        height: auto !important;
        min-height: 60px !important;
    }
    
    /* 🏷️ LOGO ZONE - MAXIMUM CENTRAL SPACE */
    .flex.items-center.space-x-3:first-child {
        flex: 0 0 140px !important; /* HUGE SPACE thanks to right-side grouping! */
        min-width: 140px !important;
        max-width: 140px !important;
        z-index: 1000 !important;
        position: relative !important;
        
        /* HORIZONTAL LAYOUT - MAXIMUM SPACE */
        display: flex !important;
        flex-direction: row !important;
        align-items: center !important;
        justify-content: flex-start !important;
        gap: 0.6rem !important; /* Generous gap with more space */
        height: 100% !important;
        overflow: visible !important;
        padding: 0.5rem 0.4rem !important;
        
        /* MAXIMUM VISIBILITY */
        background: transparent !important;
        border: none !important;
    }
    
    /* 🎯 LOGO CIRCLE - EVEN BIGGER with more space */
    .logo-container-bg {
        width: 44px !important; /* EVEN BIGGER LOGO! */
        height: 44px !important;
        min-width: 44px !important;
        min-height: 44px !important;
        border-radius: 50% !important;
        background-size: cover !important;
        background-position: center !important;
        flex-shrink: 0 !important;
        margin: 0 !important;
        z-index: 1001 !important;
        position: relative !important;
        
        /* Premium styling */
        box-shadow: 0 2px 8px rgba(45, 212, 191, 0.15) !important;
        border: 2px solid rgba(163, 209, 200, 0.25) !important;
        transition: all 0.3s ease !important;
    }
    
    /* 🏷️ "GolfinThaï" TEXT CONTAINER - HUGE SPACE */
    .flex.items-center.space-x-3:first-child > div:last-child {
        display: block !important;
        flex: 1 !important;
        z-index: 1002 !important;
        position: relative !important;
        
        /* HUGE TEXT SPACE */
        overflow: visible !important;
        margin: 0 !important;
        padding: 0 !important;
        width: auto !important;
        max-width: 90px !important; /* HUGE space available! */
        height: auto !important;
        
        /* Perfect visibility */
        background: transparent !important;
        border: none !important;
        text-align: left !important;
    }
    
    /* 🎯 "GolfinThaï" TITLE - MAXIMUM SIZE & VISIBILITY */
    .header-logo-gradient {
        font-size: 1rem !important; /* EVEN BIGGER font! */
        line-height: 1.1 !important;
        font-weight: 800 !important;
        white-space: nowrap !important;
        overflow: visible !important;
        text-overflow: clip !important;
        margin: 0 !important;
        padding: 0 !important;
        width: auto !important;
        max-width: 88px !important; /* Full huge space */
        text-align: left !important;
        z-index: 1003 !important;
        position: relative !important;
        
        /* DARK COLORS as requested */
        background: linear-gradient(135deg, #065f46 0%, #047857 70%, #059669 100%) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
        
        /* Enhanced visibility */
        filter: drop-shadow(0 1px 3px rgba(6, 95, 70, 0.3)) !important;
        transition: all 0.3s ease !important;
        
        /* FORCE DISPLAY */
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
    
    /* 🌡️ WEATHER WIDGET - 50PX PERFECT + RIGHT-SIDE GROUPED */
    .weather-widget-mobile {
        flex: 0 0 50px !important; /* Keep perfect 50px */
        min-width: 50px !important;
        max-width: 55px !important;
        text-align: center !important;
        z-index: 500 !important;
        position: relative !important;
        overflow: visible !important;
        
        /* RIGHT-SIDE POSITIONING - NEAR HAMBURGER */
        margin: 0 0.2rem 0 auto !important; /* Minimal gap, pushed right */
        padding: 0.3rem 0.1rem !important;
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.03) 0%, rgba(45, 212, 191, 0.02) 100%) !important;
        border-radius: 6px !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        
        /* Minimal styling for 50px */
        box-shadow: 0 1px 3px rgba(45, 212, 191, 0.04) !important;
        border: 1px solid rgba(163, 209, 200, 0.08) !important;
        backdrop-filter: blur(1px) !important;
    }
    
    /* 🌡️ TEMPERATURE - OPTIMIZED FOR 50px */
    .weather-temp-mobile {
        font-size: 0.75rem !important;
        line-height: 1.1 !important;
        white-space: nowrap !important;
        overflow: visible !important;
        display: block !important;
        font-weight: 600 !important;
        color: #047857 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.04) !important;
        letter-spacing: 0.1px !important;
        margin: 0 !important;
        padding: 0 !important;
        width: auto !important;
        max-width: none !important;
        text-align: center !important;
        z-index: 501 !important;
        position: relative !important;
        
        /* Compact optimization */
        filter: drop-shadow(0 0 2px rgba(4, 120, 87, 0.1)) !important;
    }
    
    /* 🌡️ LOCATION TEXT - MINIMAL for 50px */
    .weather-location-mobile {
        font-size: 0.55rem !important;
        line-height: 1 !important;
        white-space: nowrap !important;
        overflow: visible !important;
        display: block !important;
        color: #059669 !important;
        opacity: 0.75 !important;
        margin-top: 1px !important;
        font-weight: 500 !important;
        text-align: center !important;
        letter-spacing: 0.05px !important;
        z-index: 501 !important;
        position: relative !important;
    }
    
    /* 🎮 CONTROLS ZONE - RIGHT-SIDE GROUPED, COMPACT */
    .lg\\:hidden.flex.items-center.space-x-3 {
        flex: 0 0 120px !important; /* Right-side group space */
        min-width: 120px !important;
        max-width: 125px !important;
        gap: 0.25rem !important; /* TIGHT gaps - elements close together */
        z-index: 600 !important;
        position: relative !important;
        align-items: center !important;
        justify-content: flex-end !important; /* All pushed to right edge */
        overflow: visible !important;
        height: 100% !important;
        
        /* RIGHT-SIDE GROUPING - ALL NEAR HAMBURGER */
        padding: 0.25rem 0.2rem !important; /* Minimal padding */
        margin: 0 !important; /* No extra margin */
    }
    
    /* 🌍 LANGUAGE SWITCHER - GROUPED WITH WEATHER NEAR HAMBURGER */
    .language-switcher-mobile {
        flex: 0 0 48px !important; /* Compact for grouping */
        min-width: 48px !important;
        max-width: 48px !important;
        
        /* TIGHT POSITIONING - NEAR HAMBURGER */
        margin-left: 0.15rem !important; /* Close to weather widget */
        margin-right: 0.2rem !important; /* Close to hamburger */
        z-index: 700 !important;
        position: relative !important;
    }
    
    /* 🌍 LANGUAGE BUTTON - COMPACT FOR RIGHT-SIDE GROUP */
    .language-switcher-mobile .lang-btn-mobile {
        min-width: 48px !important;
        max-width: 48px !important;
        height: 36px !important; /* Compact height */
        padding: 5px 7px !important; /* Compact padding */
        font-size: 0.7rem !important; /* Smaller font */
        gap: 0.2rem !important;
        border-radius: 5px !important;
        overflow: hidden !important;
        white-space: nowrap !important;
        z-index: 701 !important;
        position: relative !important;
        
        /* Subtle styling */
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.04) 0%, rgba(45, 212, 191, 0.02) 100%) !important;
        border: 1px solid rgba(163, 209, 200, 0.12) !important;
        backdrop-filter: blur(1px) !important;
        transition: all 0.3s ease !important;
    }
    
    /* 🍔 HAMBURGER MENU - RIGHT EDGE ANCHOR */
    #mobile-menu-btn {
        flex: 0 0 48px !important;
        min-width: 48px !important;
        max-width: 48px !important;
        height: 48px !important;
        margin: 0 !important;
        padding: 12px !important;
        z-index: 800 !important;
        position: relative !important;
        border-radius: 7px !important;
        transition: all 0.3s ease !important;
        
        /* RIGHT EDGE ANCHOR */
        margin-left: 0.1rem !important; /* Very close to language switcher */
        
        /* Subtle styling */
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.04) 0%, rgba(45, 212, 191, 0.02) 100%) !important;
        border: 1px solid rgba(163, 209, 200, 0.12) !important;
        backdrop-filter: blur(1px) !important;
    }
    
    /* 🍔 HAMBURGER ICON */
    #mobile-menu-btn i {
        font-size: 1.05rem !important;
        line-height: 1 !important;
        color: #047857 !important;
        z-index: 801 !important;
        position: relative !important;
    }
}

/* 🔥 RIGHT-SIDE GROUPING SPACE MATHEMATICS:
   Logo: 140px (HUGE central space!)
   Weather: 50px (perfect compact)
   Language: 48px (compact)
   Hamburger: 48px (standard)
   Gap management: Weather+Language+Hamburger grouped tightly = ~150px total right side
   
   Total: 140px (logo) + 150px (right group) = 290px for ~320px mobile
   Central space gained: +20px MORE for "GolfinThaï"!
   
   RESULT: "GolfinThaï" gets MAXIMUM central visibility! 🎉
*/

/* 📱 VERY SMALL SCREENS - PROPORTIONAL SCALING */
@media (max-width: 380px) {
    .flex.items-center.space-x-3:first-child {
        flex: 0 0 125px !important;
        max-width: 125px !important;
    }
    
    .logo-container-bg {
        width: 40px !important;
        height: 40px !important;
    }
    
    .header-logo-gradient {
        font-size: 0.95rem !important;
        max-width: 78px !important;
    }
    
    .weather-widget-mobile {
        flex: 0 0 45px !important;
        min-width: 45px !important;
        max-width: 48px !important;
    }
    
    .weather-temp-mobile {
        font-size: 0.7rem !important;
    }
    
    .weather-location-mobile {
        font-size: 0.5rem !important;
    }
    
    .lg\\:hidden.flex.items-center.space-x-3 {
        flex: 0 0 115px !important;
        max-width: 115px !important;
    }
    
    .language-switcher-mobile {
        flex: 0 0 44px !important;
        min-width: 44px !important;
        max-width: 44px !important;
    }
    
    #mobile-menu-btn {
        flex: 0 0 45px !important;
        min-width: 45px !important;
        max-width: 45px !important;
        height: 45px !important;
        padding: 10px !important;
    }
}

/* 🎨 ENHANCED HOVER EFFECTS - PREMIUM INTERACTIONS */
@media (max-width: 1023px) {
    
    /* Logo hover - Enhanced prominence with more space */
    .flex.items-center.space-x-3:first-child:hover .header-logo-gradient {
        filter: drop-shadow(0 2px 8px rgba(6, 95, 70, 0.4)) brightness(1.1) !important;
        transform: scale(1.03) !important;
    }
    
    .flex.items-center.space-x-3:first-child:hover .logo-container-bg {
        transform: scale(1.08) !important;
        box-shadow: 0 4px 16px rgba(45, 212, 191, 0.25) !important;
    }
    
    /* Weather widget - Minimal hover for right-side group */
    .weather-widget-mobile:hover {
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.06) 0%, rgba(45, 212, 191, 0.03) 100%) !important;
        transform: translateY(-1px) scale(1.02) !important;
        box-shadow: 0 2px 6px rgba(45, 212, 191, 0.06) !important;
    }
    
    /* Temperature subtle glow */
    .weather-widget-mobile:hover .weather-temp-mobile {
        color: #065f46 !important;
        filter: drop-shadow(0 0 4px rgba(6, 95, 70, 0.2)) !important;
    }
    
    /* Right-side group hover effects */
    .language-switcher-mobile:hover .lang-btn-mobile {
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.08) 0%, rgba(45, 212, 191, 0.04) 100%) !important;
        border-color: rgba(163, 209, 200, 0.2) !important;
        transform: scale(1.02) !important;
    }
    
    #mobile-menu-btn:hover {
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.08) 0%, rgba(45, 212, 191, 0.04) 100%) !important;
        border-color: rgba(163, 209, 200, 0.2) !important;
        transform: scale(1.02) !important;
    }
    
    #mobile-menu-btn:hover i {
        color: #065f46 !important;
    }
    
    /* Smooth transitions */
    .flex.items-center.space-x-3:first-child,
    .logo-container-bg,
    .weather-widget-mobile,
    .header-logo-gradient,
    .weather-temp-mobile,
    .language-switcher-mobile .lang-btn-mobile,
    #mobile-menu-btn,
    #mobile-menu-btn i {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }
}

/* 🧪 LOGO CIRCLE BACKGROUND - Enhanced for even bigger logo */
@media (max-width: 1023px) {
    .logo-container-bg {
        background-image: url('../images/logo.png') !important;
        background-size: cover !important;
        background-position: center !important;
        background-repeat: no-repeat !important;
        
        /* Enhanced fallback with darker colors */
        background-color: #059669 !important;
        border: 2px solid #047857 !important;
    }
    
    /* Fallback content for even bigger logo */
    .logo-container-bg::after {
        content: 'GT' !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 100% !important;
        height: 100% !important;
        font-size: 1rem !important; /* Bigger text for 44px logo */
        font-weight: 900 !important;
        color: white !important;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5) !important;
        letter-spacing: 1px !important;
    }
}

/* 🏆 RIGHT-SIDE GROUPING PERFECTION ACHIEVED:
   
   ✅ Weather widget: 50px (perfect as requested)
   ✅ Weather + Language: Grouped near hamburger (right side)
   ✅ Logo space: 140px (MAXIMUM central space!)
   ✅ Logo circle: 44px (even BIGGER!)
   ✅ Font size: 1rem (BIGGEST possible!)
   ✅ Right-side compact grouping achieved
   ✅ "GolfinThaï" MAXIMUM central prominence!
   
   RESULT: Perfect right-side grouping + huge logo space! 🎯
*/