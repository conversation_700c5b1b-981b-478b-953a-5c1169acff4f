/*
 * Layout CSS pour GolfinThaï
 * Mise en page générale et structure
 */

/* Classes de conteneur */
/* ✅ CONSOLIDÉ : .container unifié dans critical.css avec responsive */
/* Note: Tailwind .container disponible aussi (max-width responsive jusqu'à 1536px) */

/* Grilles responsive */
.grid {
    display: grid;
    gap: 1rem;
}

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }

@media (min-width: 768px) {
    .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

/* Flexbox utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

/* Espacements */
.space-x-3 > * + * { margin-left: 0.75rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-y-8 > * + * { margin-top: 2rem; }

/* Marges */
.mx-auto { margin-left: auto; margin-right: auto; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-12 { margin-bottom: 3rem; }
.mb-16 { margin-bottom: 4rem; }

/* Padding */
.p-4 { padding: 1rem; }
.p-8 { padding: 2rem; }
.p-12 { padding: 3rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }

/* Sections principales */
.section {
    padding: 4rem 0;
    position: relative;
}

.section-header {
    margin-bottom: 3rem;
}

.section-title {
    font-family: var(--font-display);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--color-white);
    margin-bottom: 1rem;
}

@media (min-width: 1024px) {
    .section-title {
        font-size: 3.5rem;
    }
}
