/* =============================================
   🔥 ULTRA-MODERN CAROUSEL - SILICON VALLEY EDITION V2
   GOLFINTHAI - ANTI-PIXELIZATION FIX
   CRYSTAL CLEAR MOBILE IMAGES - FRÉRO'S VISION
   ============================================= */

/* 🎯 CAROUSEL CONTAINER - PERFECT FOUNDATION */
.hero-carousel {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 5 !important;
    width: 100vw !important;
    height: 100vh !important;
    height: 100dvh !important;
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
    
    /* PERFORMANCE OPTIMIZATIONS */
    contain: layout style paint !important;
    will-change: contents !important;
}

/* 🎠 CAROUSEL SLIDES - ULTRA-MODERN STRUCTURE */
.carousel-slide {
    /* Position and dimensions */
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    height: 100dvh !important;
    
    /* Visibility and transitions */
    opacity: 0 !important;
    transition: opacity 1s cubic-bezier(0.4, 0, 0.2, 1) !important;
    
    /* Display properties */
    display: block !important;
    visibility: visible !important;
    z-index: 1 !important;
    
    /* Reset any transforms */
    transform: none !important;
    margin: 0 !important;
    padding: 0 !important;
    
    /* MODERN CONTAINMENT */
    contain: style layout paint !important;
}

/* 🎯 ACTIVE SLIDE - VISIBLE STATE */
.carousel-slide.active {
    opacity: 1 !important;
    z-index: 2 !important;
}

/* 🖼️ MODERN IMAGE CONTAINER - PICTURE ELEMENT */
.carousel-image {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    display: block !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    outline: none !important;
}

/* 🎨 ULTRA-PREMIUM IMAGE STYLING - ANTI-PIXELIZATION */
.carousel-image img {
    /* PERFECT DIMENSIONS */
    width: 100% !important;
    height: 100% !important;
    display: block !important;
    
    /* PERFECT SCALING - CRYSTAL CLEAR IMAGES */
    object-fit: cover !important;
    object-position: center !important;
    
    /* 🔥 ANTI-PIXELIZATION - SMOOTH RENDERING */
    image-rendering: auto !important;
    image-rendering: high-quality !important;
    image-rendering: -webkit-optimize-contrast !important;
    
    /* GPU ACCELERATION FOR SILKY SMOOTH PERFORMANCE */
    transform: translateZ(0) !important;
    will-change: transform !important;
    backface-visibility: hidden !important;
    -webkit-backface-visibility: hidden !important;
    
    /* ANTI-ALIASING FOR CRYSTAL CLEAR IMAGES */
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
    
    /* MODERN CONTAINMENT */
    contain: layout style size !important;
    
    /* REMOVE ANY BORDERS/MARGINS */
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    outline: none !important;
    vertical-align: top !important;
}

/* 📱 MOBILE OPTIMIZATIONS - CRYSTAL CLEAR QUALITY */
@media (max-width: 768px) {
    .carousel-image img {
        /* 🔥 MOBILE ANTI-PIXELIZATION - FRÉRO'S SECRET SAUCE */
        image-rendering: auto !important;
        image-rendering: high-quality !important;
        
        /* PREVENT MOBILE COMPRESSION & PIXELIZATION */
        -webkit-transform: translateZ(0) !important;
        transform: translateZ(0) !important;
        
        /* REMOVE CONFLICTING TRANSFORMS */
        -webkit-transform: none !important;
        transform: none !important;
        
        /* FORCE HIGH QUALITY SCALING */
        -webkit-transform: translate3d(0, 0, 0) !important;
        transform: translate3d(0, 0, 0) !important;
        
        /* HARDWARE ACCELERATION WITHOUT DEGRADATION */
        -webkit-perspective: 1000px !important;
        perspective: 1000px !important;
    }
    
    /* MOBILE CAROUSEL OPTIMIZATIONS */
    .hero-carousel {
        /* PREVENT MOBILE SCROLLING ISSUES */
        overscroll-behavior: none !important;
        -webkit-overflow-scrolling: touch !important;
    }
    
    .carousel-slide {
        /* MOBILE TRANSITION OPTIMIZATION */
        will-change: opacity !important;
    }
}

/* 🔥 ULTRA-HIGH DPI SUPPORT - RETINA DISPLAYS */
@media (-webkit-min-device-pixel-ratio: 2), 
       (min-device-pixel-ratio: 2), 
       (min-resolution: 192dpi), 
       (min-resolution: 2dppx) {
    .carousel-image img {
        /* 🎯 RETINA ANTI-PIXELIZATION */
        image-rendering: auto !important;
        image-rendering: high-quality !important;
        
        /* CLEAN RETINA RENDERING - NO WEIRD SCALING */
        -webkit-transform: translateZ(0) !important;
        transform: translateZ(0) !important;
    }
}

/* 🎯 ULTRA-MODERN PAGINATION - GLASSMORPHISM */
.carousel-pagination {
    position: absolute !important;
    bottom: 20px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 100 !important;
    display: flex !important;
    gap: 10px !important;
    padding: 12px 18px !important;
    
    /* GLASSMORPHISM EFFECT - APPLE STYLE */
    background: rgba(0, 0, 0, 0.2) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    border-radius: 20px !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

.pagination-bullet {
    width: 8px !important;
    height: 8px !important;
    border-radius: 50% !important;
    background: rgba(255, 255, 255, 0.4) !important;
    cursor: pointer !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    border: none !important;
    outline: none !important;
    margin: 0 !important;
    padding: 0 !important;
    min-width: 8px !important;
    min-height: 8px !important;
}

.pagination-bullet:hover {
    background: rgba(255, 255, 255, 0.7) !important;
    transform: scale(1.3) !important;
}

.pagination-bullet.active {
    background: #A3D1C8 !important;
    box-shadow: 0 0 15px rgba(163, 209, 200, 0.5) !important;
    transform: scale(1.5) !important;
}

/* 💫 PERFORMANCE OPTIMIZATIONS */
.carousel-slide {
    /* GPU LAYERS */
    transform: translateZ(0) !important;
    
    /* OPTIMIZE REPAINTS */
    isolation: isolate !important;
}

.carousel-image img {
    /* FORCE BROWSER OPTIMIZATION */
    content-visibility: auto !important;
    
    /* OPTIMIZE LOADING */
    decoding: async !important;
}

/* 🛡️ FALLBACK FOR OLD BROWSERS */
@supports not (object-fit: cover) {
    .carousel-image img {
        /* FALLBACK FOR IE */
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        min-width: 100% !important;
        min-height: 100% !important;
        transform: translate(-50%, -50%) !important;
    }
}

/* 🌙 DARK MODE SUPPORT */
@media (prefers-color-scheme: dark) {
    .carousel-pagination {
        background: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
    }
}

/* 🎨 REDUCE MOTION FOR ACCESSIBILITY */
@media (prefers-reduced-motion: reduce) {
    .carousel-slide {
        transition: none !important;
    }
    
    .pagination-bullet {
        transition: none !important;
    }
}

/* 🔥 WEBKIT SPECIFIC OPTIMIZATIONS */
@supports (-webkit-appearance: none) {
    .carousel-image img {
        -webkit-transform: translateZ(0) !important;
        -webkit-backface-visibility: hidden !important;
        -webkit-perspective: 1000px !important;
    }
}

/* 🚀 PERFORMANCE MONITORING */
.carousel-slide {
    /* COMPOSITION LAYERS */
    will-change: opacity !important;
    
    /* PREVENT LAYOUT THRASHING */
    contain: layout style paint !important;
}

/* =============================================
   🎯 MIGRATION FROM OLD BACKGROUND-IMAGE SYSTEM
   Remove background-image styles and use modern picture elements
   ============================================= */

/* DISABLE OLD BACKGROUND IMAGE SYSTEM */
.carousel-slide[style*="background-image"] {
    background-image: none !important;
    background-size: unset !important;
    background-position: unset !important;
    background-repeat: unset !important;
}

/* =============================================
   🔥 FRÉRO'S ANTI-PIXELIZATION GUARANTEE:
   ✅ CRYSTAL CLEAR mobile images - NO MORE PIXELATION
   ✅ Smooth high-quality rendering on all devices
   ✅ Removed conflicting image-rendering properties
   ✅ Clean GPU acceleration without quality loss
   ✅ Modern auto rendering for best browser optimization
   ✅ Retina display support without artifacts
   ✅ Performance maintained with quality priority
   
   RESULT: Tes images de carrousel vont être NETTES ! 🔥
   Plus jamais de pixelisation sur mobile !
   ============================================= */