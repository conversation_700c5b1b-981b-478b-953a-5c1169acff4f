/*
 * Styles pour sections spécifiques - GolfinThaï
 */

/* ===== SECTIONS LAYOUT ===== */
.intro-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, rgba(26, 44, 42, 0.9), rgba(13, 27, 26, 0.95));
}

.services-section {
    padding: 5rem 0;
    background: var(--color-dark);
}

.destinations-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, var(--color-black), var(--color-dark));
}

.about-section {
    padding: 5rem 0;
    background: var(--color-dark);
}

.testimonials-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, var(--color-dark), var(--color-black));
}

.contact-section {
    padding: 5rem 0;
    background: var(--color-dark);
}

.footer-section {
    padding: 3rem 0 2rem;
    background: var(--color-black);
    border-top: 1px solid rgba(163, 209, 200, 0.2);
}

/* ===== CTA SECTION ===== */
.cta-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, var(--color-emerald-dark), var(--color-emerald));
}

.cta-description {
    font-size: 1.125rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== CONTENT SPECIFIC ===== */
.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.contact-icon {
    width: 3rem;
    height: 3rem;
    background: var(--color-emerald);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.contact-details h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color-white);
    margin-bottom: 0.25rem;
}

.contact-link {
    color: var(--color-emerald-light);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.contact-link:hover {
    color: var(--color-emerald);
}

.contact-form-container {
    width: 100%;
}

/* ===== FOOTER STYLES ===== */
.footer-title {
    font-family: var(--font-display);
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--color-emerald);
    margin-bottom: 1rem;
}

.footer-description {
    color: var(--color-emerald-light);
    margin-bottom: 2rem;
    font-size: 1rem;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.social-link {
    width: 3rem;
    height: 3rem;
    background: var(--color-emerald);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--color-emerald-light);
    transform: translateY(-2px);
}

.footer-copyright {
    color: rgba(163, 209, 200, 0.8);
    font-size: 0.875rem;
}

/* ===== COURSE MODAL ===== */
.course-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--color-dark);
    border-radius: 1rem;
    max-width: 800px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    border: 1px solid rgba(163, 209, 200, 0.2);
}

.modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    color: var(--color-emerald-light);
    font-size: 1.5rem;
    cursor: pointer;
    z-index: 10;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: rgba(163, 209, 200, 0.1);
    color: var(--color-emerald);
}

.modal-header {
    position: relative;
}

.modal-image {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: 1rem 1rem 0 0;
}

.modal-body {
    padding: 2rem;
}

.modal-title {
    font-family: var(--font-display);
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--color-white);
    margin-bottom: 0.5rem;
}

.modal-location {
    color: var(--color-emerald);
    font-weight: 600;
    margin-bottom: 1rem;
}

.modal-description {
    color: var(--color-emerald-light);
    font-size: 1.125rem;
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.modal-features {
    color: var(--color-emerald-light);
    font-size: 1rem;
}

.modal-features strong {
    color: var(--color-white);
}
