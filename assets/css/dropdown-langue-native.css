/* ================================
   DROPDOWN LANGUE - Classes CSS Natives  
   Remplacement des 5 hacks styles inline translation-simple.js
   ================================ */

/* ===== ÉTATS DROPDOWN ===== */

/* Dropdown fermé par défaut - Remplace dropdown.style.display = 'none' */
.lang-dropdown,
.lang-dropdown-mobile {
    display: none;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

/* Dropdown ouvert - Remplace dropdown.style.display = 'block' */
.lang-dropdown.dropdown-open,
.lang-dropdown-mobile.dropdown-open {
    display: block !important;
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* ===== ÉTATS FLÈCHE ===== */

/* Flèche normale (fermé) - Remplace arrow.style.transform = 'rotate(0deg)' */
.lang-arrow {
    transform: rotate(0deg);
    transition: transform 0.3s ease;
}

/* Flèche rotée (ouvert) - Remplace arrow.style.transform = 'rotate(180deg)' */
.lang-arrow.arrow-rotated {
    transform: rotate(180deg);
}

/* ===== ANIMATION SMOOTH ===== */

/* Animation d'entrée du dropdown */
.lang-dropdown.dropdown-open,
.lang-dropdown-mobile.dropdown-open {
    animation: dropdownSlideIn 0.3s ease-out;
}

@keyframes dropdownSlideIn {
    0% {
        opacity: 0;
        transform: translateY(-10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== ÉTATS BOUTONS ===== */

/* Bouton langue actif (dropdown ouvert) */
.lang-btn[aria-expanded="true"],
.lang-btn-mobile[aria-expanded="true"] {
    background-color: rgba(37, 211, 102, 0.1);
    border-color: var(--color-emerald);
}

/* Option langue courante dans dropdown */
.lang-option.current,
.lang-option-mobile.current {
    background-color: rgba(37, 211, 102, 0.2);
    border-left: 3px solid var(--color-emerald);
    font-weight: 600;
}

/* ===== RESPONSIVE SAFEGUARD ===== */

/* Auto-fermeture sur mobile landscape */
@media (max-width: 768px) and (orientation: landscape) {
    .lang-dropdown.dropdown-open,
    .lang-dropdown-mobile.dropdown-open {
        max-height: 150px;
        overflow-y: auto;
    }
}

/* ===== ACCESSIBILITÉ ===== */

/* Focus visible sur les options */
.lang-option:focus,
.lang-option-mobile:focus {
    outline: 2px solid var(--color-emerald);
    outline-offset: 2px;
}

/* Indication visuelle pour lecteurs d'écran */
.lang-dropdown[aria-hidden="false"],
.lang-dropdown-mobile[aria-hidden="false"] {
    /* Visible pour assistive technologies */
}
