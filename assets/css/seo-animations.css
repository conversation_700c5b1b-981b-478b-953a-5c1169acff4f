/* SEO OPTIMIZED ANIMATIONS */

@keyframes moveGrad {
    0% { background-position: 0% 0%; }
    100% { background-position: 200% 0%; }
}

.text-gradient {
    background: linear-gradient(90deg, #00574B 0%, #A3D1C8 50%, #00574B 100%) !important;
    background-size: 200% 100% !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    animation: moveGrad 2s linear infinite !important;
}

.header-logo-gradient {
    background: linear-gradient(90deg, #00574B 0%, #A3D1C8 50%, #00574B 100%) !important;
    background-size: 200% 100% !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    animation: moveGrad 3s linear infinite !important;
}