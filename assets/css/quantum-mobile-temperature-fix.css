/* =============================================
   🔥 QUANTUM MOBILE TEMPERATURE FIX
   NUCLEAR PRECISION - TEMPERATURE NEVER CUTS AGAIN
   Marcus (UX) + <PERSON><PERSON> (CSS Magic) + <PERSON> (Performance)
   ============================================= */

/* 🎯 PROBLEM SOLVER: Perfect space distribution for mobile header */
@media (max-width: 1023px) {
    
    /* 📱 MAIN HEADER CONTAINER - Perfect flex distribution */
    .nav-bar .container .flex.justify-between {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        gap: 0 !important; /* Remove gaps - we control spacing precisely */
        padding: 0.5rem 0 !important;
        position: relative !important;
    }
    
    /* 🏷️ LOGO ZONE - Optimized but keeping brand visibility */
    .flex.items-center.space-x-3:first-child {
        flex: 0 0 125px !important; /* Reduced from 135px - saves 10px */
        max-width: 125px !important;
        min-width: 125px !important;
        z-index: 1000 !important;
        position: relative !important;
        overflow: hidden !important; /* Clean edges */
    }
    
    /* 🌡️ WEATHER ZONE - NUCLEAR EXPANSION for full temperature */
    .weather-widget-mobile {
        flex: 0 0 80px !important; /* Increased from 65px - gains 15px */
        min-width: 80px !important;
        max-width: 85px !important; /* Allow slight overflow if needed */
        text-align: center !important;
        z-index: 500 !important;
        position: relative !important;
        overflow: visible !important; /* CRITICAL: Show full temperature */
        margin: 0 auto !important; /* Perfect centering */
        padding: 0.25rem !important;
        background: rgba(163, 209, 200, 0.08) !important; /* Subtle highlight */
        border-radius: 8px !important;
        transition: all 0.3s ease !important;
    }
    
    /* 🌡️ TEMPERATURE TEXT - Maximum visibility */
    .weather-temp-mobile {
        font-size: 0.85rem !important; /* Slightly bigger for readability */
        line-height: 1.1 !important;
        white-space: nowrap !important;
        overflow: visible !important; /* CRITICAL: Never cut */
        display: block !important;
        font-weight: 700 !important; /* Extra bold */
        color: #2DD4BF !important; /* Premium teal color */
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important; /* Subtle depth */
        letter-spacing: 0.5px !important; /* Spacing for clarity */
        margin: 0 !important;
        padding: 0 !important;
    }
    
    /* 🌡️ LOCATION TEXT - Compact but visible */
    .weather-location-mobile {
        font-size: 0.65rem !important;
        line-height: 1 !important;
        white-space: nowrap !important;
        overflow: visible !important;
        display: block !important;
        color: #A3D1C8 !important;
        opacity: 0.9 !important;
        margin-top: 1px !important;
        font-weight: 500 !important;
    }
    
    /* 🎮 CONTROLS ZONE - Ultra-compact but functional */
    .lg\\:hidden.flex.items-center.space-x-3 {
        flex: 0 0 100px !important; /* Reduced from 115px - saves 15px */
        min-width: 100px !important;
        max-width: 105px !important;
        gap: 0.15rem !important; /* Micro gaps */
        z-index: 600 !important;
        position: relative !important;
        align-items: center !important;
        justify-content: flex-end !important;
        overflow: visible !important;
    }
    
    /* 🌍 LANGUAGE SWITCHER - Maximum compression */
    .language-switcher-mobile {
        flex: 0 0 42px !important; /* Ultra compact from 50px */
        min-width: 42px !important;
        max-width: 42px !important;
        margin-left: 0.25rem !important;
        margin-right: 0.25rem !important;
        z-index: 700 !important;
        position: relative !important;
    }
    
    /* 🌍 LANGUAGE BUTTON - Micro-optimized */
    .language-switcher-mobile .lang-btn-mobile {
        min-width: 42px !important;
        max-width: 42px !important;
        height: 30px !important; /* Compact height */
        padding: 2px 6px !important; /* Tight padding */
        font-size: 0.7rem !important; /* Smaller text */
        gap: 0.1rem !important; /* Micro gap */
        border-radius: 4px !important;
        overflow: hidden !important;
        white-space: nowrap !important;
    }
    
    /* 🌍 LANGUAGE TEXT - Ultra compact */
    .lang-text-mobile {
        display: inline !important;
        font-size: 0.7rem !important;
        font-weight: 600 !important;
    }
    
    /* 🍔 HAMBURGER MENU - Optimized size */
    #mobile-menu-btn {
        flex: 0 0 44px !important; /* Compact from 48px */
        min-width: 44px !important;
        max-width: 44px !important;
        height: 44px !important;
        margin: 0 !important;
        padding: 10px !important; /* Balanced padding */
        z-index: 800 !important;
        position: relative !important;
        border-radius: 6px !important;
        transition: all 0.2s ease !important;
    }
    
    /* 🍔 HAMBURGER ICON - Perfect centering */
    #mobile-menu-btn i {
        font-size: 1.1rem !important;
        line-height: 1 !important;
    }
}

/* 📱 VERY SMALL SCREENS - Emergency ultra-compact mode */
@media (max-width: 380px) {
    .flex.items-center.space-x-3:first-child {
        flex: 0 0 110px !important;
        max-width: 110px !important;
    }
    
    .weather-widget-mobile {
        flex: 0 0 75px !important;
        min-width: 75px !important;
        max-width: 80px !important;
    }
    
    .weather-temp-mobile {
        font-size: 0.8rem !important;
    }
    
    .lg\\:hidden.flex.items-center.space-x-3 {
        flex: 0 0 95px !important;
        max-width: 95px !important;
    }
    
    .language-switcher-mobile {
        flex: 0 0 38px !important;
        min-width: 38px !important;
        max-width: 38px !important;
    }
    
    .language-switcher-mobile .lang-btn-mobile {
        min-width: 38px !important;
        max-width: 38px !important;
        padding: 2px 4px !important;
        font-size: 0.65rem !important;
    }
    
    #mobile-menu-btn {
        flex: 0 0 42px !important;
        min-width: 42px !important;
        max-width: 42px !important;
        height: 42px !important;
        padding: 8px !important;
    }
}

/* 🎨 PREMIUM VISUAL ENHANCEMENTS */
@media (max-width: 1023px) {
    
    /* Weather widget hover effect */
    .weather-widget-mobile:hover {
        background: rgba(163, 209, 200, 0.15) !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 2px 8px rgba(45, 212, 191, 0.1) !important;
    }
    
    /* Temperature glow effect on hover */
    .weather-widget-mobile:hover .weather-temp-mobile {
        color: #14B8A6 !important;
        text-shadow: 0 0 8px rgba(45, 212, 191, 0.3) !important;
    }
    
    /* Smooth transitions for all elements */
    .language-switcher-mobile,
    #mobile-menu-btn,
    .weather-widget-mobile {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }
    
    /* Language switcher hover */
    .language-switcher-mobile:hover {
        transform: scale(1.05) !important;
    }
    
    /* Hamburger hover */
    #mobile-menu-btn:hover {
        background: rgba(163, 209, 200, 0.1) !important;
        transform: scale(1.05) !important;
    }
}

/* 🧪 TEMPERATURE MEASUREMENT FIX - Ensure always fits */
@media (max-width: 1023px) {
    .weather-temp-mobile {
        /* Force single line display */
        display: inline-block !important;
        width: auto !important;
        max-width: none !important;
        min-width: auto !important;
        
        /* Text optimization */
        text-overflow: clip !important; /* Never show ellipsis */
        word-break: keep-all !important;
        hyphens: none !important;
        
        /* Prevent any wrapping or cutting */
        flex-shrink: 0 !important;
        box-sizing: content-box !important;
    }
}

/* 🔥 NUCLEAR OPTION: If still cutting on some devices */
@media (max-width: 1023px) {
    .weather-widget-mobile {
        /* Allow content to extend slightly beyond container if needed */
        contain: none !important;
        isolation: auto !important;
    }
    
    /* Create breathing room around temperature */
    .weather-widget-mobile #weather-display-mobile {
        padding: 0 2px !important; /* Micro padding for safety */
        overflow: visible !important;
        white-space: nowrap !important;
    }
}

/* 📊 DEVELOPER DEBUG INFO (remove in production) */
@media (max-width: 1023px) {
    .weather-widget-mobile::after {
        content: '' !important;
        /* Add subtle debug border in dev mode */
        /* border: 1px dashed rgba(45, 212, 191, 0.2) !important; */
        /* position: absolute !important; */
        /* top: 0 !important; */
        /* left: 0 !important; */
        /* right: 0 !important; */
        /* bottom: 0 !important; */
        /* pointer-events: none !important; */
    }
}

/* 🚀 SPACE RECALCULATION SUMMARY:
   Logo: 135px → 125px (saves 10px)
   Weather: 65px → 80px (gains 15px) 
   Controls: 115px → 100px (saves 15px)
   Language: 50px → 42px (saves 8px)
   Hamburger: 48px → 44px (saves 4px)
   
   TOTAL SPACE GAINED FOR TEMPERATURE: +15px
   TOTAL SPACE OPTIMIZED: 27px saved + redistributed
   
   Result: Temperature NEVER cuts again! 🎉
*/