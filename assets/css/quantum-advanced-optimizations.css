/* =============================================
   ⚡ QUANTUM ADVANCED OPTIMIZATIONS
   Apple/Netflix level secret sauce
   ============================================= */

/* 🌟 APPLE-STYLE BACKDROP BLUR PROGRESSION */
.quantum-image-modal {
    backdrop-filter: blur(0px) brightness(1) !important;
    -webkit-backdrop-filter: blur(0px) brightness(1) !important;
    transition: 
        backdrop-filter 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
        background 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    background: rgba(0, 0, 0, 0) !important;
}

.quantum-image-modal.active {
    backdrop-filter: blur(25px) brightness(0.8) !important;
    -webkit-backdrop-filter: blur(25px) brightness(0.8) !important;
    background: rgba(0, 0, 0, 0.95) !important;
}

/* 🎬 PROGRESSIVE IMAGE LOADING - FIXED */
.modal-image {
    opacity: 1 !important; /* FIX: Image visible par défaut */
    transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

/* REMOVED: Problematic .loaded class that was hiding images */

/* 🚀 PERFORMANCE OPTIMIZATIONS */
.quantum-image-modal * {
    backface-visibility: hidden !important;
    perspective: 1000px !important;
    transform-style: preserve-3d !important;
}

/* 📱 MOBILE GESTURE IMPROVEMENTS */
@media (max-width: 768px) {
    .modal-image {
        touch-action: pan-x pan-y !important;
        user-select: none !important;
        -webkit-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
    }
}

/* 🎯 SMART BUTTON HOVER PRELOADING */
.btn-primary,
a[href="#contact"],
.quantum-contact-btn {
    position: relative !important;
}

.btn-primary::after,
a[href="#contact"]::after,
.quantum-contact-btn::after {
    content: '' !important;
    position: absolute !important;
    inset: 0 !important;
    background: linear-gradient(135deg, #A3D1C8 0%, #00574B 50%, #A3D1C8 100%) !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
    z-index: -1 !important;
    border-radius: inherit !important;
}

.btn-primary:hover::after,
a[href="#contact"]:hover::after,
.quantum-contact-btn:hover::after {
    opacity: 1 !important;
}

/* 🌊 MICRO-ANIMATIONS */
@keyframes subtlePulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

.btn-primary:focus,
a[href="#contact"]:focus,
.quantum-contact-btn:focus {
    animation: subtlePulse 2s ease-in-out infinite !important;
}

/* 🔧 SYSTEM PERFORMANCE MONITORING */
.performance-monitor {
    position: fixed;
    top: 10px;
    left: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: #00ff00;
    padding: 5px 10px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 12px;
    z-index: 999999;
    display: none;
}

.performance-monitor.show {
    display: block;
}

/* 🎨 LOADING STATES ENHANCEMENT */
.loading-spinner {
    background: radial-gradient(circle, rgba(163, 209, 200, 0.1) 0%, transparent 70%) !important;
}

.spinner {
    border-top-color: #A3D1C8 !important;
    filter: drop-shadow(0 0 10px rgba(163, 209, 200, 0.5)) !important;
}

/* 🌟 ENHANCED VISUAL FEEDBACK */
.modal-close:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    transform: scale(1.1) !important;
}

.modal-close:active {
    transform: scale(0.9) !important;
}

/* 🎯 ACCESSIBILITY IMPROVEMENTS */
@media (prefers-reduced-motion: reduce) {
    .quantum-image-modal,
    .modal-image,
    .btn-primary,
    a[href="#contact"],
    .quantum-contact-btn {
        animation: none !important;
        transition: none !important;
    }
}

/* 📊 SMART CACHING INDICATORS */
.preload-indicator {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 8px;
    height: 8px;
    background: #00ff00;
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.preload-indicator.loaded {
    opacity: 0.7;
}

/* 🚀 SCROLL PERFORMANCE */
.modal-open body {
    overscroll-behavior: none !important;
    -webkit-overflow-scrolling: touch !important;
}

/* 🎬 CINEMATIC ENTRANCE */
.quantum-image-modal.active .modal-container {
    animation: cinematicEntrance 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

@keyframes cinematicEntrance {
    0% {
        opacity: 0;
        transform: scale(0.8) translateY(50px);
        filter: blur(10px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
        filter: blur(0px);
    }
}

/* 💫 BUTTON QUANTUM EFFECTS */
.btn-primary,
a[href="#contact"],
.quantum-contact-btn {
    box-shadow: 
        0 2px 8px rgba(0, 87, 75, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.btn-primary:hover,
a[href="#contact"]:hover,
.quantum-contact-btn:hover {
    box-shadow: 
        0 8px 25px rgba(0, 87, 75, 0.3),
        0 0 20px rgba(163, 209, 200, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

/* 🔥 QUANTUM GLOW EFFECTS */
@keyframes quantumGlow {
    0%, 100% { 
        box-shadow: 
            0 2px 8px rgba(0, 87, 75, 0.2),
            0 0 0 0 rgba(163, 209, 200, 0);
    }
    50% { 
        box-shadow: 
            0 8px 25px rgba(0, 87, 75, 0.4),
            0 0 20px 5px rgba(163, 209, 200, 0.3);
    }
}

.btn-primary:focus,
a[href="#contact"]:focus,
.quantum-contact-btn:focus {
    animation: quantumGlow 2s ease-in-out infinite !important;
}
