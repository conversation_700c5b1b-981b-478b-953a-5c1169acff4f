/* ===============================================
   MOBILE UNIFIED CLEAN CSS - GolfinThaï
   Styles unifiés pour menu mobile et modales
   Version nettoyée sans conflits
   =============================================== */

/* ===== MENU MOBILE ===== */

/* Bouton hamburger - Touch-friendly */
#mobile-menu-btn {
    min-width: 48px;
    min-height: 48px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: rgba(34, 197, 94, 0.2);
    user-select: none;
    -webkit-user-select: none;
    cursor: pointer;
    z-index: 9999;
}

/* Menu overlay - État fermé par défaut */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.95);
    z-index: 9998;
    display: none;
    opacity: 0;
    visibility: hidden;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

/* Menu ouvert */
.mobile-menu-overlay.mobile-menu-open {
    display: flex !important;
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateX(0) !important;
}

/* Liens du menu */
.mobile-menu-link {
    color: white;
    font-size: 1.5rem;
    font-weight: 500;
    text-decoration: none;
    padding: 12px 24px;
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    touch-action: manipulation;
    -webkit-tap-highlight-color: rgba(34, 197, 94, 0.2);
    user-select: none;
    -webkit-user-select: none;
    transition: color 0.2s ease;
}

.mobile-menu-link:hover,
.mobile-menu-link:focus {
    color: #22c55e;
}

/* Bouton fermer */
.mobile-menu-close {
    position: absolute;
    top: 20px;
    right: 20px;
    background: transparent;
    border: none;
    color: white;
    font-size: 2rem;
    width: 48px;
    height: 48px;
    cursor: pointer;
    touch-action: manipulation;
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0.2);
}

/* Body lock pour menu */
.body-menu-locked {
    overflow: hidden !important;
    position: fixed !important;
    width: 100% !important;
    left: 0;
}

/* ===== MODALES ===== */

/* Modal overlay */
.course-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10000;
    display: none;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

/* Modal ouverte */
.course-modal.modal-open {
    display: flex !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Modal fermée */
.course-modal.modal-closed {
    opacity: 0;
    visibility: hidden;
}

/* Modal cachée */
.course-modal.modal-hidden {
    display: none !important;
}

/* Contenu modal */
.modal-content {
    background: white;
    border-radius: 16px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

/* Image modal */
.modal-image {
    width: 100%;
    height: auto;
    max-height: 60vh;
    object-fit: cover;
}

/* Corps modal */
.modal-body {
    padding: 20px;
    max-height: 30vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

/* Bouton fermer modal */
.modal-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    border: none;
    color: white;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    font-size: 1.25rem;
    cursor: pointer;
    z-index: 10;
    touch-action: manipulation;
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0.2);
}

/* Body lock pour modal */
.body-modal-locked {
    overflow: hidden !important;
    position: fixed !important;
    width: 100% !important;
    left: 0;
}

/* ===== ANIMATIONS ===== */

@keyframes modalSlideIn {
    from {
        transform: translateY(50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* ===== RESPONSIVE ===== */

/* Desktop - Cacher menu mobile */
@media (min-width: 1024px) {
    .mobile-menu-overlay {
        display: none !important;
        opacity: 0 !important;
        visibility: hidden !important;
        transform: translateX(100%) !important;
    }
    
    .body-menu-locked {
        overflow: auto !important;
        position: static !important;
        width: auto !important;
    }
}

/* Mobile - Optimisations */
@media (max-width: 768px) {
    .modal-content {
        max-width: calc(100vw - 20px);
        max-height: 90vh;
    }
    
    .modal-image {
        height: 200px;
    }
    
    .modal-body {
        max-height: 50vh;
    }
}

/* ===== ACCESSIBILITÉ ===== */

/* Focus visible */
#mobile-menu-btn:focus,
.mobile-menu-link:focus,
.modal-close:focus {
    outline: 2px solid #22c55e;
    outline-offset: 2px;
}

/* Réduction mouvement */
@media (prefers-reduced-motion: reduce) {
    .mobile-menu-overlay,
    .course-modal,
    .modal-content {
        transition: none;
        animation: none;
    }
}

/* ===== SÉCURITÉ ANTI-CONFLITS ===== */

/* Forcer les styles importants - OVERRIDE main.css */
.mobile-menu-overlay.mobile-menu-open {
    display: flex !important;
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateX(0) !important;
    /* Override main.css */
    background: rgba(0, 0, 0, 0.95) !important;
    backdrop-filter: blur(20px) !important;
}

.course-modal.modal-open {
    display: flex !important;
    opacity: 1 !important;
    visibility: visible !important;
    /* Override main.css */
    background: rgba(0, 0, 0, 0.8) !important;
}

/* Reset des transformations parasites */
.mobile-menu-overlay:not(.mobile-menu-open) {
    transform: translateX(100%) !important;
    display: none !important;
}

.course-modal:not(.modal-open) {
    opacity: 0 !important;
    visibility: hidden !important;
    display: none !important;
}

/* ===== OVERRIDE SPÉCIFIQUES MAIN.CSS ===== */

/* Forcer notre gestion du menu mobile */
.mobile-menu-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    z-index: 9998 !important;
    transition: all 0.3s ease !important;
}

/* Forcer notre gestion des modales */
.course-modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    z-index: 10000 !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.3s ease !important;
}

/* Assurer que les body locks fonctionnent */
.body-menu-locked,
.body-modal-locked {
    overflow: hidden !important;
    position: fixed !important;
    width: 100% !important;
    left: 0 !important;
    top: 0 !important;
}
