/* ===================================
   🎭 GolfinThaï - Animations Propres
   Remplace le chaos quantum/nuclear
   =================================== */

/* 🎯 Keyframes Essentielles */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-6px);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* 🌈 Gradient Animations */
@keyframes gradientSlide {
    0% {
        background-position: 0% 0%;
    }
    100% {
        background-position: 300% 0%;
    }
}

@keyframes gradientPulse {
    0%, 100% {
        background-size: 200% 200%;
        background-position: 0% 0%;
    }
    50% {
        background-size: 250% 250%;
        background-position: 100% 100%;
    }
}

/* 🎨 Text Gradient Animation */
.text-gradient {
    background: linear-gradient(
        135deg,
        var(--color-emerald) 0%,
        var(--color-emerald-light) 25%,
        var(--color-emerald) 50%,
        var(--color-emerald-dark) 75%,
        var(--color-emerald) 100%
    );
    background-size: 300% 100%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientSlide 8s linear infinite;
}

.header-logo-gradient {
    background: linear-gradient(
        90deg,
        var(--color-emerald) 0%,
        var(--color-emerald-light) 25%,
        var(--color-emerald) 50%,
        var(--color-emerald-light) 75%,
        var(--color-emerald) 100%
    );
    background-size: 200% 100%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientSlide 8s linear infinite;
}

/* 🎪 Reveal Animations */
.reveal-animation {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.reveal-animation.visible {
    opacity: 1;
    transform: translateY(0);
}

/* 🎯 Utility Animation Classes */
.animate-fadeIn {
    animation: fadeIn 0.5s ease-out;
}

.animate-fadeInUp {
    animation: fadeInUp 0.8s ease-out;
}

.animate-slideInRight {
    animation: slideInRight 0.6s ease-out;
}

.animate-slideInLeft {
    animation: slideInLeft 0.6s ease-out;
}

.animate-scaleIn {
    animation: scaleIn 0.4s ease-out;
}

.animate-bounce {
    animation: bounce 2s infinite;
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

/* 🎭 Hover Animations */
.hover-lift {
    transition: transform var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-4px);
}

.hover-scale {
    transition: transform var(--transition-normal);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: all var(--transition-normal);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(0, 87, 75, 0.4);
}

/* 🌊 Loading States */
.loading-skeleton {
    background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0.1) 25%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.1) 75%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 87, 75, 0.2);
    border-top: 4px solid var(--color-emerald);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 🎪 Staggered Animations */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }
.stagger-6 { animation-delay: 0.6s; }

/* 🎯 Page Transitions */
.page-enter {
    opacity: 0;
    transform: translateY(20px);
}

.page-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.6s ease-out;
}

.page-exit {
    opacity: 1;
    transform: translateY(0);
}

.page-exit-active {
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.4s ease-in;
}

/* 🎨 Modal Animations */
.modal-backdrop-enter {
    opacity: 0;
}

.modal-backdrop-enter-active {
    opacity: 1;
    transition: opacity 0.3s ease-out;
}

.modal-content-enter {
    opacity: 0;
    transform: scale(0.9) translateY(50px);
}

.modal-content-enter-active {
    opacity: 1;
    transform: scale(1) translateY(0);
    transition: all 0.3s ease-out;
}

/* 🎪 Carousel Animations */
.carousel-slide-enter {
    opacity: 0;
    transform: translateX(100%);
}

.carousel-slide-enter-active {
    opacity: 1;
    transform: translateX(0);
    transition: all 0.8s ease-out;
}

.carousel-slide-exit {
    opacity: 1;
    transform: translateX(0);
}

.carousel-slide-exit-active {
    opacity: 0;
    transform: translateX(-100%);
    transition: all 0.8s ease-out;
}

/* 🎯 Arrow Rotation */
.arrow-rotated {
    transform: rotate(180deg);
    transition: transform var(--transition-normal);
}

/* 📱 Mobile Optimizations */
@media (max-width: 768px) {
    .reveal-animation {
        transform: translateY(20px);
    }
    
    .hover-lift:hover {
        transform: translateY(-2px);
    }
    
    .hover-scale:hover {
        transform: scale(1.02);
    }
}

/* ♿ Accessibility - Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .text-gradient,
    .header-logo-gradient {
        animation: none;
        background: var(--color-emerald);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .reveal-animation {
        opacity: 1;
        transform: none;
    }
    
    .animate-bounce,
    .animate-pulse,
    .animate-spin,
    .animate-float {
        animation: none;
    }
}

/* 🎭 Performance Optimizations */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}