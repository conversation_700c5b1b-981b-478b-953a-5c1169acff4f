/* ===================================
   🧹 GolfinThaï - Reset CSS Moderne
   Remplace le chaos Tailwind/Custom
   =================================== */

/* 🎯 Reset Moderne Optimisé */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-sans);
    font-size: var(--text-base);
    line-height: 1.6;
    color: var(--color-white);
    background-color: var(--color-black);
    min-height: 100vh;
    overflow-x: hidden;
}

/* 🔗 Liens */
a {
    color: inherit;
    text-decoration: none;
    transition: color var(--transition-normal);
}

a:hover,
a:focus {
    color: var(--color-emerald-light);
}

/* 🖼️ Images */
img,
picture,
video,
canvas,
svg {
    display: block;
    max-width: 100%;
    height: auto;
}

/* 📝 Formulaires */
input,
button,
textarea,
select {
    font: inherit;
    color: inherit;
    background: transparent;
    border: none;
    outline: none;
}

button {
    cursor: pointer;
    background: transparent;
    border: none;
    padding: 0;
}

/* 📋 Listes */
ul,
ol {
    list-style: none;
}

/* 🎯 Focus Visible */
:focus-visible {
    outline: 2px solid var(--color-emerald);
    outline-offset: 2px;
}

/* 📱 Touch Targets */
button,
[role="button"],
input[type="submit"],
input[type="button"],
input[type="reset"] {
    min-height: 44px;
    min-width: 44px;
}

/* 🌐 Accessibilité */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--color-emerald);
    color: var(--color-black);
    padding: 8px;
    text-decoration: none;
    border-radius: var(--radius-md);
    z-index: var(--z-max);
    transition: top var(--transition-normal);
}

.skip-link:focus {
    top: 6px;
}

/* 🎭 Animations Réduites */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* 📱 Mobile Optimizations */
@media (max-width: 768px) {
    html {
        font-size: 14px;
    }
    
    body {
        -webkit-overflow-scrolling: touch;
        overscroll-behavior: contain;
    }
}

/* 🖨️ Print Styles */
@media print {
    *,
    *::before,
    *::after {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    a,
    a:visited {
        text-decoration: underline;
    }
    
    img {
        max-width: 100% !important;
        page-break-inside: avoid;
    }
    
    h1,
    h2,
    h3 {
        page-break-after: avoid;
    }
}