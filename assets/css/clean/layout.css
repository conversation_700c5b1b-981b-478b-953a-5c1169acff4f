/* ===================================
   📐 GolfinThaï - Layout System Propre
   Remplace le chaos container/grid
   =================================== */

/* 📦 Container System */
.container {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 var(--space-md);
}

@media (min-width: 640px) {
    .container {
        padding: 0 var(--space-lg);
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 var(--space-xl);
    }
}

/* 🏗️ Grid System */
.grid {
    display: grid;
    gap: var(--space-lg);
}

.grid-1 { grid-template-columns: 1fr; }
.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }

/* 📱 Responsive Grid */
.grid-responsive {
    grid-template-columns: 1fr;
}

@media (min-width: 768px) {
    .grid-responsive {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .grid-responsive {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* 🔄 Flexbox Utilities */
.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.flex-wrap {
    flex-wrap: wrap;
}

.items-center {
    align-items: center;
}

.items-start {
    align-items: flex-start;
}

.items-end {
    align-items: flex-end;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.justify-around {
    justify-content: space-around;
}

.justify-end {
    justify-content: flex-end;
}

/* 📏 Spacing Utilities */
.gap-xs { gap: var(--space-xs); }
.gap-sm { gap: var(--space-sm); }
.gap-md { gap: var(--space-md); }
.gap-lg { gap: var(--space-lg); }
.gap-xl { gap: var(--space-xl); }

/* 📐 Width & Height */
.w-full { width: 100%; }
.w-auto { width: auto; }
.h-full { height: 100%; }
.h-auto { height: auto; }
.h-screen { height: 100vh; }
.h-screen { height: 100dvh; } /* Modern viewport */

/* 🎯 Position */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* 📱 Display */
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }
.hidden { display: none; }

/* 📱 Responsive Display */
.hidden-mobile {
    display: none;
}

@media (min-width: 768px) {
    .hidden-mobile {
        display: block;
    }
    
    .visible-mobile {
        display: none;
    }
}

.hidden-desktop {
    display: block;
}

@media (min-width: 1024px) {
    .hidden-desktop {
        display: none;
    }
    
    .visible-desktop {
        display: block;
    }
}

/* 🎨 Text Alignment */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

/* 🌊 Overflow */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-auto { overflow-y: auto; }

/* 🎭 Transform */
.transform { transform: translateZ(0); } /* Force GPU acceleration */

/* 📱 Mobile-First Responsive Helpers */
@media (max-width: 767px) {
    .mobile-only {
        display: block;
    }
    
    .desktop-only {
        display: none;
    }
    
    .container {
        padding: 0 var(--space-sm);
    }
    
    .grid-2,
    .grid-3,
    .grid-4 {
        grid-template-columns: 1fr;
    }
}

@media (min-width: 768px) {
    .mobile-only {
        display: none;
    }
    
    .desktop-only {
        display: block;
    }
}

/* 🎯 Aspect Ratios */
.aspect-square {
    aspect-ratio: 1 / 1;
}

.aspect-video {
    aspect-ratio: 16 / 9;
}

.aspect-photo {
    aspect-ratio: 4 / 3;
}

/* 🌟 Section Layout */
.section {
    padding: var(--space-4xl) 0;
}

@media (max-width: 768px) {
    .section {
        padding: var(--space-3xl) 0;
    }
}

/* 🎪 Hero Layout */
.hero {
    min-height: 100vh;
    min-height: 100dvh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

/* 🃏 Card Layout */
.card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-2xl);
    padding: var(--space-xl);
    transition: all var(--transition-normal);
}

.card:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(0, 87, 75, 0.3);
    transform: translateY(-4px);
    box-shadow: var(--shadow-emerald);
}

/* 🧠 Header Layout */
.nav-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: var(--z-navbar);
    transition: background-color var(--transition-normal), box-shadow var(--transition-normal);
    padding: var(--space-md) 0;
}

.nav-bar.scrolled {
    background-color: rgba(13, 27, 26, 0.8);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-lg);
}

.logo-wrapper {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.logo-image {
    height: 44px;
    width: 44px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--color-emerald);
    transition: transform var(--transition-normal);
}

.logo-wrapper:hover .logo-image {
    transform: scale(1.1) rotate(10deg);
}

.logo-text {
    font-family: var(--font-display);
    font-size: var(--text-2xl);
    font-weight: 600;
    color: var(--color-white);
}

/* 🦶 Footer Layout */
.footer {
    padding: var(--space-3xl) 0;
    background-color: var(--color-dark);
    border-top: 1px solid rgba(0, 87, 75, 0.2);
}

/* 🎯 Skip Link */
.skip-link {
    position: absolute;
    top: -100px;
    left: 0;
    background: var(--color-emerald);
    color: var(--color-black);
    padding: var(--space-md);
    z-index: 99999;
    transition: top 0.3s;
}

.skip-link:focus {
    top: 0;
}

/* 📱 Responsive Layout Adjustments */
@media (max-width: 1279px) {
    .logo-text {
        display: none;
    }
}

@media (max-width: 767px) {
    .nav-bar {
        padding: var(--space-sm) 0;
    }
    
    .logo-image {
        height: 40px;
        width: 40px;
    }
}
