/* ===================================
   🔧 GolfinThaï - Utilities CSS
   Classes utilitaires pour développement rapide
   =================================== */

/* 🎨 Colors */
.text-emerald { color: var(--color-emerald); }
.text-emerald-light { color: var(--color-emerald-light); }
.text-white { color: var(--color-white); }
.text-black { color: var(--color-black); }

.bg-emerald { background-color: var(--color-emerald); }
.bg-emerald-light { background-color: var(--color-emerald-light); }
.bg-white { background-color: var(--color-white); }
.bg-black { background-color: var(--color-black); }
.bg-dark { background-color: var(--color-dark); }

/* 🌈 Gradients */
.bg-gradient-emerald { background: var(--gradient-emerald); }
.bg-gradient-dark { background: var(--gradient-dark); }
.bg-gradient-glass { background: var(--gradient-glass); }

/* 📏 Spacing */
.m-0 { margin: 0; }
.m-auto { margin: auto; }
.mx-auto { margin-left: auto; margin-right: auto; }
.my-auto { margin-top: auto; margin-bottom: auto; }

.p-0 { padding: 0; }
.px-0 { padding-left: 0; padding-right: 0; }
.py-0 { padding-top: 0; padding-bottom: 0; }

.m-xs { margin: var(--space-xs); }
.m-sm { margin: var(--space-sm); }
.m-md { margin: var(--space-md); }
.m-lg { margin: var(--space-lg); }
.m-xl { margin: var(--space-xl); }

.p-xs { padding: var(--space-xs); }
.p-sm { padding: var(--space-sm); }
.p-md { padding: var(--space-md); }
.p-lg { padding: var(--space-lg); }
.p-xl { padding: var(--space-xl); }

/* 📐 Sizing */
.w-0 { width: 0; }
.w-full { width: 100%; }
.w-screen { width: 100vw; }
.w-fit { width: fit-content; }

.h-0 { height: 0; }
.h-full { height: 100%; }
.h-screen { height: 100vh; }
.h-dvh { height: 100dvh; }
.h-fit { height: fit-content; }

.min-h-0 { min-height: 0; }
.min-h-full { min-height: 100%; }
.min-h-screen { min-height: 100vh; }
.min-h-dvh { min-height: 100dvh; }

.max-w-xs { max-width: 20rem; }
.max-w-sm { max-width: 24rem; }
.max-w-md { max-width: 28rem; }
.max-w-lg { max-width: 32rem; }
.max-w-xl { max-width: 36rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-6xl { max-width: 72rem; }
.max-w-full { max-width: 100%; }
.max-w-none { max-width: none; }

/* 🎯 Position */
.static { position: static; }
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.top-0 { top: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }

/* 🎭 Transform */
.transform { transform: translateZ(0); }
.scale-95 { transform: scale(0.95); }
.scale-100 { transform: scale(1); }
.scale-105 { transform: scale(1.05); }
.scale-110 { transform: scale(1.1); }

.rotate-0 { transform: rotate(0deg); }
.rotate-90 { transform: rotate(90deg); }
.rotate-180 { transform: rotate(180deg); }

.translate-x-0 { transform: translateX(0); }
.translate-y-0 { transform: translateY(0); }
.-translate-y-1 { transform: translateY(-0.25rem); }
.-translate-y-2 { transform: translateY(-0.5rem); }

/* 🌫️ Effects */
.opacity-0 { opacity: 0; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }
.shadow-emerald { box-shadow: var(--shadow-emerald); }
.shadow-none { box-shadow: none; }

/* 🔄 Transitions */
.transition-none { transition: none; }
.transition-all { transition: all var(--transition-normal); }
.transition-colors { transition: color var(--transition-normal), background-color var(--transition-normal), border-color var(--transition-normal); }
.transition-opacity { transition: opacity var(--transition-normal); }
.transition-transform { transition: transform var(--transition-normal); }

.duration-75 { transition-duration: 75ms; }
.duration-100 { transition-duration: 100ms; }
.duration-150 { transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.duration-500 { transition-duration: 500ms; }

.ease-linear { transition-timing-function: linear; }
.ease-in { transition-timing-function: cubic-bezier(0.4, 0, 1, 1); }
.ease-out { transition-timing-function: cubic-bezier(0, 0, 0.2, 1); }
.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

/* 📱 Z-Index */
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }
.z-auto { z-index: auto; }

/* 🎨 Border Radius */
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-3xl { border-radius: var(--radius-3xl); }
.rounded-full { border-radius: var(--radius-full); }

/* 🖼️ Object Fit */
.object-contain { object-fit: contain; }
.object-cover { object-fit: cover; }
.object-fill { object-fit: fill; }
.object-none { object-fit: none; }
.object-scale-down { object-fit: scale-down; }

.object-bottom { object-position: bottom; }
.object-center { object-position: center; }
.object-left { object-position: left; }
.object-right { object-position: right; }
.object-top { object-position: top; }

/* 🎯 Cursor */
.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-wait { cursor: wait; }
.cursor-text { cursor: text; }
.cursor-move { cursor: move; }
.cursor-not-allowed { cursor: not-allowed; }

/* 👆 User Select */
.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }
.select-auto { user-select: auto; }

/* 🎪 Backdrop Filter */
.backdrop-blur-none { backdrop-filter: none; }
.backdrop-blur-sm { backdrop-filter: blur(4px); }
.backdrop-blur { backdrop-filter: blur(8px); }
.backdrop-blur-md { backdrop-filter: blur(12px); }
.backdrop-blur-lg { backdrop-filter: blur(16px); }
.backdrop-blur-xl { backdrop-filter: blur(24px); }
.backdrop-blur-2xl { backdrop-filter: blur(40px); }

/* 🎨 Typography */
.font-thin { font-weight: 100; }
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }
.text-4xl { font-size: var(--text-4xl); }
.text-5xl { font-size: var(--text-5xl); }

.leading-none { line-height: 1; }
.leading-tight { line-height: 1.25; }
.leading-snug { line-height: 1.375; }
.leading-normal { line-height: 1.5; }
.leading-relaxed { line-height: 1.625; }
.leading-loose { line-height: 2; }

.tracking-tighter { letter-spacing: -0.05em; }
.tracking-tight { letter-spacing: -0.025em; }
.tracking-normal { letter-spacing: 0; }
.tracking-wide { letter-spacing: 0.025em; }
.tracking-wider { letter-spacing: 0.05em; }
.tracking-widest { letter-spacing: 0.1em; }

.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }
.normal-case { text-transform: none; }

.italic { font-style: italic; }
.not-italic { font-style: normal; }

/* 🎯 Hover States */
.hover\:scale-105:hover { transform: scale(1.05); }
.hover\:scale-110:hover { transform: scale(1.1); }
.hover\:-translate-y-1:hover { transform: translateY(-0.25rem); }
.hover\:-translate-y-2:hover { transform: translateY(-0.5rem); }
.hover\:shadow-lg:hover { box-shadow: var(--shadow-lg); }
.hover\:shadow-xl:hover { box-shadow: var(--shadow-xl); }
.hover\:shadow-emerald:hover { box-shadow: var(--shadow-emerald); }

/* 🎯 Focus States */
.focus\:outline-none:focus { outline: none; }
.focus\:ring-2:focus { box-shadow: 0 0 0 2px var(--color-emerald); }
.focus\:ring-emerald:focus { box-shadow: 0 0 0 2px var(--color-emerald); }

/* 📱 Responsive Utilities */
@media (min-width: 640px) {
    .sm\:block { display: block; }
    .sm\:hidden { display: none; }
    .sm\:flex { display: flex; }
    .sm\:grid { display: grid; }
    .sm\:text-lg { font-size: var(--text-lg); }
    .sm\:text-xl { font-size: var(--text-xl); }
    .sm\:text-2xl { font-size: var(--text-2xl); }
}

@media (min-width: 768px) {
    .md\:block { display: block; }
    .md\:hidden { display: none; }
    .md\:flex { display: flex; }
    .md\:grid { display: grid; }
    .md\:text-xl { font-size: var(--text-xl); }
    .md\:text-2xl { font-size: var(--text-2xl); }
    .md\:text-3xl { font-size: var(--text-3xl); }
}

@media (min-width: 1024px) {
    .lg\:block { display: block; }
    .lg\:hidden { display: none; }
    .lg\:flex { display: flex; }
    .lg\:grid { display: grid; }
    .lg\:text-2xl { font-size: var(--text-2xl); }
    .lg\:text-3xl { font-size: var(--text-3xl); }
    .lg\:text-4xl { font-size: var(--text-4xl); }
}

/* 📱 Mobile/Desktop Visibility */
.hidden-mobile {
    display: block;
}

.visible-mobile {
    display: none;
}

@media (max-width: 1279px) {
    .hidden-mobile {
        display: none;
    }
    
    .visible-mobile {
        display: block;
    }
}

/* 🔄 Animation Delays */
.stagger-1 {
    animation-delay: 0.1s;
}

.stagger-2 {
    animation-delay: 0.2s;
}

.stagger-3 {
    animation-delay: 0.3s;
}



/* 📱 Screen Reader Only */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 🎯 Margin/Padding Helpers */
.mr-sm { margin-right: var(--space-sm); }
.mr-md { margin-right: var(--space-md); }
.mr-lg { margin-right: var(--space-lg); }

.mb-sm { margin-bottom: var(--space-sm); }
.mb-md { margin-bottom: var(--space-md); }
.mb-lg { margin-bottom: var(--space-lg); }

/* 🎯 Gap Utilities */
.gap-xs { gap: var(--space-xs); }
.gap-sm { gap: var(--space-sm); }
.gap-md { gap: var(--space-md); }
.gap-lg { gap: var(--space-lg); }
.gap-xl { gap: var(--space-xl); }

/* 🎯 Grid Responsive */
.grid-responsive {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-xl);
}

@media (min-width: 1024px) {
    .grid-responsive {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 🎯 Flexbox */
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.flex-row { flex-direction: row; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-center { align-items: center; }
.items-baseline { align-items: baseline; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-initial { flex: 0 1 auto; }
.flex-none { flex: none; }

.flex-grow { flex-grow: 1; }
.flex-grow-0 { flex-grow: 0; }
.flex-shrink { flex-shrink: 1; }
.flex-shrink-0 { flex-shrink: 0; }

/* 🎯 Text Alignment */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

/* 🎯 Display */
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.hidden { display: none; }
.grid { display: grid; }

/* 🎯 Overflow */
.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }

/* 🎯 Height Specific */
.h-80 { height: 20rem; }
.h-96 { height: 24rem; }

/* 🎯 Margin Top */
.mt-8 { margin-top: 2rem; }

/* 🎯 Print Utilities */
@media print {
    .print\:hidden { display: none !important; }
    .print\:block { display: block !important; }
}