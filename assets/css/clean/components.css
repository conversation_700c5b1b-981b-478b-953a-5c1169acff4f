/* ===================================
   🧩 GolfinThaï - Styles des Composants
   =================================== */

/* 🎨 Boutons */
.btn {
    display: inline-block;
    padding: var(--space-md) var(--space-xl);
    border-radius: var(--radius-3xl);
    font-weight: 600;
    text-decoration: none;
    transition: all var(--transition-normal);
    cursor: pointer;
    border: none;
    text-align: center;
}

.btn-primary {
    background: var(--gradient-emerald);
    color: var(--color-black);
}

.btn-primary:hover,
.btn-primary:focus {
    transform: translateY(-2px);
    box-shadow: var(--shadow-emerald);
}

/* 🔗 Liens de Navigation */
.nav-link {
    position: relative;
    color: var(--color-white);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-normal);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--color-emerald);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform var(--transition-normal);
}

.nav-link:hover,
.nav-link:focus,
.nav-link.active {
    color: var(--color-emerald-light);
}

.nav-link:hover::after,
.nav-link:focus::after,
.nav-link.active::after {
    transform: scaleX(1);
    transform-origin: left;
}

/* 📱 Navigation Mobile */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(13, 27, 26, 0.95);
    backdrop-filter: blur(10px);
    z-index: var(--z-overlay);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity var(--transition-normal), visibility var(--transition-normal);
}

.mobile-menu-overlay.open {
    opacity: 1;
    visibility: visible;
}

.mobile-menu-link {
    font-family: var(--font-display);
    font-size: var(--text-3xl);
    color: var(--color-white);
    text-decoration: none;
    padding: var(--space-md) 0;
    transition: color var(--transition-normal);
}

.mobile-menu-link:hover {
    color: var(--color-emerald);
}

.mobile-menu-close {
    position: absolute;
    top: var(--space-xl);
    right: var(--space-xl);
    background: none;
    border: none;
    color: var(--color-white);
    font-size: var(--text-3xl);
    cursor: pointer;
}

/* 🌐 Sélecteur de Langue */
.language-switcher-desktop, .language-switcher-mobile {
    position: relative;
}

.lang-btn {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    background: none;
    border: none;
    color: var(--color-white);
    cursor: pointer;
    font-family: var(--font-sans);
    font-weight: 500;
}

.lang-dropdown, .lang-dropdown-mobile {
    position: absolute;
    top: calc(100% + var(--space-md));
    right: 0;
    background: var(--color-dark);
    border: 1px solid rgba(0, 87, 75, 0.3);
    border-radius: var(--radius-lg);
    padding: var(--space-sm);
    z-index: var(--z-dropdown);
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all var(--transition-normal);
}

.lang-dropdown.dropdown-open, .lang-dropdown-mobile.dropdown-open {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.lang-option, .lang-option-mobile {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    background: none;
    border: none;
    color: var(--color-white);
    padding: var(--space-sm) var(--space-md);
    width: 100%;
    text-align: left;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: background-color var(--transition-normal);
}

.lang-option:hover, .lang-option-mobile:hover {
    background-color: rgba(0, 87, 75, 0.2);
}

.lang-option.current, .lang-option-mobile.current {
    background-color: rgba(0, 87, 75, 0.4);
    font-weight: 600;
}

/* 🎠 Carrousel */
.hero-carousel {
    position: absolute;
    inset: 0;
    z-index: var(--z-background);
}

.carousel-slide {
    position: absolute;
    inset: 0;
    opacity: 0;
    transition: opacity 1.5s ease-in-out;
}

.carousel-slide.active {
    opacity: 1;
}

.carousel-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.carousel-pagination {
    position: absolute;
    bottom: var(--space-xl);
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: var(--space-md);
    z-index: var(--z-base);
}

.pagination-bullet {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.pagination-bullet.active {
    background: var(--color-white);
    transform: scale(1.2);
}

/* 🏌️ Carte de Parcours */
.course-card {
    background: var(--color-dark-light);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    transition: all var(--transition-normal);
    display: flex;
    flex-direction: column;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.course-image-container {
    position: relative;
    aspect-ratio: 16/9;
}

.course-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.course-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(to top, rgba(13, 27, 26, 0.7) 0%, transparent 50%);
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    padding: var(--space-md);
}

.course-location-badge {
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 600;
    color: var(--color-black);
}

.badge-teal { background-color: var(--color-teal); }
.badge-gray { background-color: var(--color-gray); }
.badge-green { background-color: var(--color-green); }
.badge-orange { background-color: var(--color-orange); }
.badge-red { background-color: var(--color-red); }
.badge-blue { background-color: var(--color-blue); }

.course-expand-btn {
    width: 2.5rem;
    height: 2.5rem;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    color: var(--color-white);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.course-expand-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.course-content {
    padding: var(--space-lg);
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.course-title {
    font-family: var(--font-display);
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--color-emerald-light);
    margin-bottom: var(--space-sm);
}

.course-description {
    color: rgba(252, 252, 252, 0.8);
    line-height: 1.6;
    margin-bottom: var(--space-md);
    flex-grow: 1;
}

.course-meta {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    color: rgba(252, 252, 252, 0.6);
    font-size: var(--text-sm);
}

/* 💬 Carte de Témoignage */
.testimonial-card {
    background: var(--color-dark-light);
    border-radius: var(--radius-2xl);
    padding: var(--space-xl);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.testimonial-quote {
    position: relative;
    padding-left: var(--space-xl);
    margin-bottom: var(--space-lg);
}

.quote-icon {
    position: absolute;
    top: 0;
    left: 0;
    font-size: var(--text-2xl);
    color: var(--color-emerald);
    opacity: 0.5;
}

.testimonial-text {
    color: rgba(252, 252, 252, 0.9);
    line-height: 1.7;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.author-name {
    font-weight: 600;
    color: var(--color-white);
}

.author-rating {
    color: #FFD700; /* Or var(--color-gold) */
}

/*
 * 🖼️ MODAL SYSTEM STYLES
 */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(13, 27, 26, 0.9);
    backdrop-filter: blur(10px);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.modal-backdrop.visible {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    position: relative;
    background: #0D1B1A;
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(0, 87, 75, 0.5);
    opacity: 0;
    transform: scale(0.95) translateY(10px);
    transition: transform 0.3s ease, opacity 0.3s ease;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-backdrop.visible .modal-container {
    opacity: 1;
    transform: scale(1) translateY(0);
}

.modal-close {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    color: white;
    font-size: 20px;
    cursor: pointer;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease, transform 0.2s ease;
}

.modal-close:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

/* Course Modal Specifics */
.course-modal {
    flex-direction: row;
    max-width: 75vw;
    width: 1200px;
}

.course-modal-image {
    flex: 1.3;
    background-color: #000;
}

.course-modal-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.course-modal-details {
    flex: 1;
    padding: 40px;
    overflow-y: auto;
    color: rgba(252, 252, 252, 0.9);
}

.course-modal-details h3 {
    color: #00A896;
    font-size: 2.5rem;
    margin: 0 0 10px;
    font-family: 'Playfair Display', serif;
}

.course-modal-details .location {
    font-style: italic;
    color: #999;
    margin-bottom: 25px;
    font-size: 1.1rem;
}

.course-modal-details .description {
    line-height: 1.8;
    margin-bottom: 30px;
    font-size: 1rem;
}

.course-modal-details h4 {
    color: #00A896;
    font-size: 1.6rem;
    margin-bottom: 20px;
    font-family: 'Playfair Display', serif;
    border-top: 1px solid rgba(0, 87, 75, 0.5);
    padding-top: 20px;
}

.course-modal-details ul {
    list-style: none;
    padding-left: 0;
    font-size: 1rem;
}

.course-modal-details ul li {
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
}

.course-modal-details ul li i {
    color: #00A896;
    margin-right: 15px;
}

/* Image Modal Specifics */
.image-modal {
    max-width: 90vw;
    max-height: 90vh;
}

.image-modal img {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
}

.image-modal p {
    padding: 20px;
    text-align: center;
    color: rgba(252, 252, 252, 0.9);
    line-height: 1.6;
    background-color: rgba(0,0,0,0.3);
}

@media (max-width: 992px) {
    .course-modal {
        flex-direction: column;
        max-width: 85vw;
        width: auto;
    }
    .course-modal-image {
        max-height: 40vh;
    }
}
