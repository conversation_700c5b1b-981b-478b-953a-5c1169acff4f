/* ===================================
   🧩 GolfinThaï - Components Propres
   Remplace le chaos des 50+ fichiers CSS
   =================================== */

/* 🔘 Button System */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-sm) var(--space-lg);
    border: none;
    border-radius: var(--radius-xl);
    font-family: var(--font-sans);
    font-weight: 500;
    font-size: var(--text-sm);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    outline: none;
    min-height: 40px;
    -webkit-tap-highlight-color: transparent;
    letter-spacing: 0.025em;
}

.btn:focus-visible {
    outline: 2px solid var(--color-emerald);
    outline-offset: 2px;
}

.btn-primary {
    background: var(--gradient-emerald);
    color: var(--color-black);
    box-shadow: var(--shadow-lg);
}

.btn-primary:hover,
.btn-primary:focus {
    transform: translateY(-2px);
    box-shadow: var(--shadow-emerald);
}

.btn-secondary {
    background: transparent;
    color: var(--color-emerald);
    border: 2px solid var(--color-emerald);
}

.btn-secondary:hover,
.btn-secondary:focus {
    background: var(--color-emerald);
    color: var(--color-black);
}

/* 🧭 Navigation */
.nav-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-fixed);
    background: rgba(13, 27, 26, 0.9);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 87, 75, 0.2);
    transition: all var(--transition-normal);
}

.nav-bar.scrolled {
    background: rgba(13, 27, 26, 0.95);
    box-shadow: var(--shadow-lg);
}

.nav-link {
    color: var(--color-white);
    font-weight: 500;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
    text-decoration: none;
}

.nav-link:hover,
.nav-link:focus {
    color: var(--color-emerald-light);
    background: rgba(0, 87, 75, 0.1);
}

/* 🏷️ Logo */
.logo-wrapper {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.logo-image {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(163, 209, 200, 0.3);
    transition: all var(--transition-normal);
}

.logo-image:hover {
    border-color: var(--color-emerald);
    transform: scale(1.05);
}

.logo-text {
    font-family: var(--font-display);
    font-size: var(--text-xl);
    font-weight: 700;
    background: linear-gradient(
        90deg,
        var(--color-emerald) 0%,
        var(--color-emerald-light) 25%,
        var(--color-emerald) 50%,
        var(--color-emerald-light) 75%,
        var(--color-emerald) 100%
    );
    background-size: 200% 100%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientSlide 8s linear infinite;
}

/* 🌍 Language Switcher */
.language-switcher-desktop,
.language-switcher-mobile {
    position: relative;
}

.flag-icon {
    font-size: 16px;
    margin-right: 8px;
}

.lang-btn {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-md);
    background: rgba(163, 209, 200, 0.1);
    border: 1px solid rgba(163, 209, 200, 0.3);
    border-radius: var(--radius-lg);
    color: var(--color-white);
    cursor: pointer;
    transition: all var(--transition-normal);
    min-height: 44px;
}

.lang-btn:hover,
.lang-btn:focus {
    background: rgba(163, 209, 200, 0.2);
    border-color: rgba(163, 209, 200, 0.5);
}

.lang-dropdown {
    position: absolute;
    top: calc(100% + var(--space-sm));
    right: 0;
    background: rgba(13, 27, 26, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 87, 75, 0.3);
    border-radius: var(--radius-xl);
    padding: var(--space-sm);
    min-width: 160px;
    box-shadow: var(--shadow-xl);
    z-index: var(--z-dropdown);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
}

.lang-dropdown.dropdown-open {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.lang-dropdown-mobile {
    position: absolute;
    top: calc(100% + var(--space-sm));
    right: 0;
    background: rgba(13, 27, 26, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 87, 75, 0.3);
    border-radius: var(--radius-xl);
    padding: var(--space-sm);
    min-width: 120px;
    box-shadow: var(--shadow-xl);
    z-index: var(--z-dropdown);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
}

.lang-dropdown-mobile.dropdown-open {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.lang-option,
.lang-option-mobile {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-md);
    color: var(--color-white);
    cursor: pointer;
    transition: all var(--transition-normal);
    width: 100%;
    text-align: left;
    background: transparent;
    border: none;
}

.lang-option:hover,
.lang-option:focus,
.lang-option-mobile:hover,
.lang-option-mobile:focus {
    background: rgba(0, 87, 75, 0.2);
    color: var(--color-emerald-light);
}

.lang-option.current,
.lang-option-mobile.current {
    background: rgba(0, 87, 75, 0.3);
    color: var(--color-emerald);
}

/* 🍔 Mobile Menu */
.mobile-menu-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    background: rgba(163, 209, 200, 0.1);
    border: 1px solid rgba(163, 209, 200, 0.3);
    border-radius: var(--radius-lg);
    color: var(--color-emerald);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.mobile-menu-btn:hover,
.mobile-menu-btn:focus {
    background: rgba(163, 209, 200, 0.2);
    transform: scale(1.05);
}

.mobile-menu-overlay {
    position: fixed;
    inset: 0;
    background: rgba(13, 27, 26, 0.98);
    backdrop-filter: blur(20px);
    z-index: var(--z-modal);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-100%);
    transition: all var(--transition-slow);
}

.mobile-menu-overlay.open {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.mobile-menu-link {
    font-family: var(--font-display);
    font-size: var(--text-3xl);
    color: var(--color-white);
    text-decoration: none;
    margin: var(--space-lg) 0;
    padding: var(--space-md) var(--space-xl);
    border-radius: var(--radius-xl);
    transition: all var(--transition-normal);
}

.mobile-menu-link:hover,
.mobile-menu-link:focus {
    color: var(--color-emerald-light);
    background: rgba(0, 87, 75, 0.1);
}

.mobile-menu-close {
    position: absolute;
    top: var(--space-xl);
    right: var(--space-xl);
    width: 44px;
    height: 44px;
    background: transparent;
    border: none;
    color: var(--color-emerald);
    font-size: var(--text-2xl);
    cursor: pointer;
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
}

.mobile-menu-close:hover,
.mobile-menu-close:focus {
    background: rgba(0, 87, 75, 0.1);
    transform: scale(1.1);
}



/* 🎠 Carousel */
.hero-carousel {
    position: absolute;
    inset: 0;
    overflow: hidden;
}

.carousel-slide {
    position: absolute;
    inset: 0;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.carousel-slide.active {
    opacity: 1;
}

.carousel-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.carousel-pagination {
    position: absolute;
    bottom: var(--space-xl);
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: var(--space-sm);
    z-index: var(--z-base);
}

.carousel-pagination .pagination-bullet {
    width: 4px !important;
    height: 4px !important;
    min-width: 4px;
    min-height: 4px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all var(--transition-normal);
    border: none;
    outline: none;
    box-sizing: border-box;
}

.carousel-pagination .pagination-bullet.active,
.carousel-pagination .pagination-bullet:hover {
    background: var(--color-emerald);
    transform: scale(1.2);
}

/* 🏌️ Course Cards */
.course-card {
    background: rgba(13, 27, 26, 0.7);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 87, 75, 0.3);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.course-card:hover,
.course-card:focus {
    transform: translateY(-8px);
    box-shadow: var(--shadow-emerald);
    border-color: rgba(0, 87, 75, 0.6);
}

.course-image-container {
    position: relative;
    aspect-ratio: 16/9;
    overflow: hidden;
}

.course-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
    cursor: pointer;
    z-index: 2;
    position: relative;
}

.course-card:hover .course-image {
    transform: scale(1.1);
}

.course-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(
        to top,
        rgba(13, 27, 26, 0.8) 0%,
        transparent 50%
    );
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: var(--space-md);
    pointer-events: none;
    z-index: 1;
}

.course-location-badge {
    padding: var(--space-xs) var(--space-md);
    border-radius: var(--radius-3xl);
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--color-white);
    background: var(--color-emerald);
}

.course-content {
    padding: var(--space-lg);
}

.course-title {
    font-family: var(--font-display);
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--color-emerald);
    margin-bottom: var(--space-sm);
}

.course-description {
    color: rgba(252, 252, 252, 0.8);
    font-size: var(--text-sm);
    line-height: 1.6;
    margin-bottom: var(--space-md);
}

.course-meta {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.course-features {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    font-size: var(--text-xs);
    color: var(--color-emerald-light);
    font-weight: 500;
}

.course-expand-btn {
    background: rgba(0, 87, 75, 0.8);
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-white);
    cursor: pointer;
    transition: all var(--transition-normal);
    pointer-events: auto;
    z-index: 3;
}

.course-expand-btn:hover {
    background: var(--color-emerald);
    transform: scale(1.1);
}

/* Badge Colors */
.badge-teal { background: #14b8a6; }
.badge-gray { background: #6b7280; }
.badge-green { background: #10b981; }
.badge-orange { background: #f59e0b; }
.badge-red { background: #ef4444; }
.badge-blue { background: #3b82f6; }

/* Clickable Images */
.clickable-image {
    cursor: pointer;
    transition: transform var(--transition-normal);
}

.clickable-image:hover {
    transform: scale(1.02);
}

/* 💬 Testimonials */
.testimonial-card {
    background: rgba(13, 27, 26, 0.7);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 87, 75, 0.3);
    border-radius: var(--radius-2xl);
    padding: var(--space-xl);
    transition: all var(--transition-normal);
}

.testimonial-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-emerald);
    border-color: rgba(0, 87, 75, 0.5);
}

.testimonial-text {
    color: rgba(252, 252, 252, 0.9);
    line-height: 1.7;
    margin-bottom: var(--space-lg);
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.author-name {
    font-family: var(--font-display);
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--color-emerald);
}

.author-rating {
    display: flex;
    gap: var(--space-xs);
    color: var(--color-emerald);
}

/* 🎯 Text Gradient */
.text-gradient {
    background: linear-gradient(
        135deg,
        var(--color-emerald) 0%,
        var(--color-emerald-light) 25%,
        var(--color-emerald) 50%,
        var(--color-emerald-dark) 75%,
        var(--color-emerald) 100%
    );
    background-size: 300% 100%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientSlide 8s linear infinite;
}



/* 🔍 Skip Link */




/* 🎨 Quote Icon */
.quote-icon {
    color: var(--color-emerald);
    font-size: var(--text-2xl);
    margin-bottom: var(--space-md);
    opacity: 0.7;
}

/* 🎨 Additional Styles */
.intro-paragraph {
    font-size: var(--text-lg);
    color: rgba(252, 252, 252, 0.9);
    margin-bottom: var(--space-lg);
    line-height: 1.7;
}

.detail-item {
    margin-bottom: var(--space-4xl);
}

.detail-header {
    margin-bottom: var(--space-xl);
}

.detail-icon {
    width: 4rem;
    height: 4rem;
    background: rgba(0, 87, 75, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-emerald);
    font-size: var(--text-2xl);
    margin-bottom: var(--space-lg);
}

.detail-title {
    font-family: var(--font-display);
    font-size: var(--text-2xl);
    font-weight: 600;
    color: var(--color-emerald);
    margin-bottom: var(--space-lg);
}

.detail-paragraph {
    font-size: var(--text-lg);
    color: rgba(252, 252, 252, 0.8);
    margin-bottom: var(--space-lg);
    line-height: 1.7;
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    padding: var(--space-lg);
    color: var(--color-white);
}

.image-title {
    font-family: var(--font-display);
    font-size: var(--text-xl);
    font-weight: 600;
    margin-bottom: var(--space-sm);
}

.image-description {
    font-size: var(--text-sm);
    opacity: 0.9;
}

.founder-name {
    font-family: var(--font-display);
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--color-emerald);
    margin-bottom: var(--space-sm);
}

.founder-title {
    font-size: var(--text-base);
    color: rgba(252, 252, 252, 0.8);
    margin-bottom: var(--space-md);
}

.founder-rating {
    display: flex;
    justify-content: center;
    gap: var(--space-xs);
    color: var(--color-emerald);
}

.founder-description {
    font-size: var(--text-lg);
    color: rgba(252, 252, 252, 0.9);
    margin-bottom: var(--space-lg);
    line-height: 1.7;
}

.highlight-name {
    color: var(--color-emerald);
    font-weight: 600;
}

.founder-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.9));
    padding: var(--space-lg);
}

.courses-header {
    text-align: center;
    margin-bottom: var(--space-4xl);
}

.courses-title {
    font-family: var(--font-display);
    font-size: var(--text-3xl);
    font-weight: 600;
    color: var(--color-emerald);
}

.features-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-xl);
    margin-top: var(--space-4xl);
}

@media (min-width: 768px) {
    .features-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.feature-item {
    text-align: center;
    background: rgba(0, 87, 75, 0.15);
    border: 1px solid rgba(0, 87, 75, 0.3);
    border-radius: var(--radius-2xl);
    padding: var(--space-xl);
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
}

.feature-item:hover {
    background: rgba(0, 87, 75, 0.25);
    border-color: rgba(0, 87, 75, 0.5);
    transform: translateY(-4px);
    box-shadow: var(--shadow-emerald);
}

.feature-icon {
    width: 4rem;
    height: 4rem;
    background: rgba(0, 87, 75, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-emerald);
    font-size: var(--text-2xl);
    margin: 0 auto var(--space-lg);
}

.feature-title {
    font-family: var(--font-display);
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--color-emerald);
    margin-bottom: var(--space-md);
}

.feature-description {
    color: rgba(252, 252, 252, 0.8);
    line-height: 1.6;
}

/* 📱 Mobile Optimizations */
@media (max-width: 768px) {
    .nav-bar {
        padding: var(--space-sm) 0;
    }
    
    .logo-text {
        font-size: var(--text-lg);
    }
    
    .weather-widget {
        padding: var(--space-xs) var(--space-sm);
    }
    
    .course-card,
    .testimonial-card {
        margin: 0 var(--space-sm);
    }
    
    .mobile-menu-link {
        font-size: var(--text-2xl);
    }
}