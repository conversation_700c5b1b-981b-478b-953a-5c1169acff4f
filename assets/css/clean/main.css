/* ===================================
   🚀 GolfinThaï - CSS Principal Propre
   Architecture Silicon Valley
   Mobile-First Responsive Design
   =================================== */

/* 📦 Imports dans l'ordre logique */
@import url('./variables.css');
@import url('./reset.css');
@import url('./layout.css');
@import url('./components.css');
@import url('./animations.css');
@import url('./utilities.css');

/* 🎯 Styles Spécifiques GolfinThaï */

/* 🌤️ Weather Widget */
.weather-widget {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-md);
    background: rgba(0, 87, 75, 0.1);
    border-radius: var(--radius-xl);
    border: 1px solid rgba(0, 87, 75, 0.2);
    transition: all var(--transition-normal);
}

.weather-widget:hover {
    background: rgba(0, 87, 75, 0.15);
    border-color: rgba(0, 87, 75, 0.3);
}

.weather-icon {
    color: var(--color-emerald);
    font-size: var(--text-lg);
}

.weather-temp {
    font-weight: 600;
    color: var(--color-white);
    font-size: var(--text-base);
}

.weather-location {
    font-size: var(--text-sm);
    color: rgba(252, 252, 252, 0.7);
    font-weight: 500;
}

/* Mobile Weather Widget */
@media (max-width: 1279px) {
    .weather-widget {
        padding: var(--space-xs) var(--space-sm);
        min-width: 60px;
    }
    
    .weather-temp {
        font-size: var(--text-sm);
    }
    
    .weather-location {
        font-size: var(--text-xs);
    }
    
    .weather-icon {
        font-size: var(--text-base);
    }
}

/* Responsive Visibility */
.hidden-mobile {
    display: flex;
}

.visible-mobile {
    display: none;
}

@media (max-width: 1279px) {
    .hidden-mobile {
        display: none;
    }
    
    .visible-mobile {
        display: flex;
    }
}

/* 🏠 Hero Section */
.hero-section {
    position: relative;
    min-height: 100vh;
    min-height: 100dvh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
        180deg,
        rgba(13, 27, 26, 0.4) 0%,
        rgba(13, 27, 26, 0.6) 40%,
        rgba(13, 27, 26, 0.8) 100%
    );
    z-index: var(--z-base);
}

.hero-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: calc(var(--z-base) + 1);
    text-align: center;
    width: 100%;
    max-width: 100vw;
    padding: var(--space-4xl) var(--space-md);
}

.hero-title {
    font-family: 'Marcellus SC', var(--font-display);
    font-size: clamp(3rem, 10vw, 8rem);
    font-weight: 500;
    line-height: 1.1;
    color: rgba(255, 255, 255, 0.45);
    text-shadow: 2px 4px 20px rgba(0, 0, 0, 0.7);
    animation: fadeInUp 1s ease-out;
}

/* 📝 Typography */
.section-title {
    font-family: var(--font-display);
    font-size: clamp(2rem, 6vw, 4rem);
    font-weight: 700;
    margin-bottom: var(--space-lg);
    text-align: center;
}

.section-description {
    font-size: clamp(1rem, 2.5vw, 1.25rem);
    color: rgba(252, 252, 252, 0.8);
    max-width: 64rem;
    margin: 0 auto var(--space-2xl);
    line-height: 1.7;
    text-align: center;
}

.intro-text {
    font-size: clamp(1rem, 2.5vw, 1.25rem);
    color: rgba(252, 252, 252, 0.9);
    margin-bottom: var(--space-lg);
    line-height: 1.7;
}

.highlight-text {
    font-size: clamp(1.125rem, 3vw, 1.5rem);
    font-weight: 600;
    color: var(--color-emerald-light);
    line-height: 1.6;
}

/* 🏌️ Services Section */
.services-section {
    background-color: var(--color-black);
}

.services-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-xl);
}

@media (min-width: 768px) {
    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}

@media (min-width: 1024px) {
    .services-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--space-2xl);
    }
}

.service-card {
    text-align: center;
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease-out forwards;
}

.service-card:nth-child(2) { animation-delay: 0.1s; }
.service-card:nth-child(3) { animation-delay: 0.2s; }

.service-image-container {
    position: relative;
    margin-bottom: var(--space-lg);
    overflow: hidden;
    border-radius: var(--radius-xl);
    aspect-ratio: 4/3;
}

.service-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
    cursor: pointer;
}

.service-card:hover .service-image {
    transform: scale(1.1);
}

.service-title {
    font-family: var(--font-display);
    font-size: clamp(1.25rem, 3vw, 1.5rem);
    font-weight: 600;
    color: var(--color-emerald);
    margin-bottom: var(--space-md);
}

.service-description {
    color: rgba(252, 252, 252, 0.8);
    line-height: 1.6;
}

/* 🌍 Destinations Section */
.destinations-section {
    background-color: var(--color-dark);
}

.destinations-intro {
    margin-bottom: var(--space-4xl);
}

.intro-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-2xl);
    align-items: center;
    max-width: 80rem;
    margin: 0 auto;
}

@media (min-width: 1024px) {
    .intro-grid {
        grid-template-columns: 1fr 1fr;
    }
}

.intro-subtitle {
    font-family: var(--font-display);
    font-size: clamp(1.5rem, 4vw, 2rem);
    font-weight: 600;
    color: var(--color-emerald);
    margin-bottom: var(--space-lg);
}

.intro-image-container {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-2xl);
    aspect-ratio: 16/10;
}

.intro-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
    cursor: pointer;
}

.intro-image-container:hover .intro-image {
    transform: scale(1.05);
}

/* 🏌️ Golf Courses */
.courses-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-xl);
}

@media (min-width: 768px) {
    .courses-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
}

@media (min-width: 1280px) {
    .courses-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* 🏆 About Section */
.about-section {
    background-color: var(--color-black);
}

.founder-image-container {
    position: relative;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: 0 0 0 4px rgba(0, 87, 75, 0.3);
}

.founder-image {
    width: 100%;
    height: 20rem;
    object-fit: cover;
    object-position: center 20%;
    transition: transform var(--transition-slow);
    cursor: pointer;
}

@media (min-width: 640px) {
    .founder-image {
        height: 26rem;
        object-position: center 15%;
    }
}

@media (min-width: 1024px) {
    .founder-image {
        height: 32rem;
        object-position: center 20%;
    }
}

.founder-image-container:hover .founder-image {
    transform: scale(1.05);
}

.founder-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-md);
    margin-top: var(--space-lg);
}

@media (min-width: 640px) {
    .founder-stats {
        grid-template-columns: repeat(3, 1fr);
    }
}

.stat-item {
    text-align: center;
    background: rgba(0, 87, 75, 0.1);
    border-radius: var(--radius-xl);
    padding: var(--space-md);
}

.stat-value {
    font-family: var(--font-display);
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--color-emerald);
    margin-bottom: var(--space-xs);
}

.stat-label {
    font-size: var(--text-xs);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: rgba(252, 252, 252, 0.8);
}

/* 💬 Testimonials Section */
.testimonials-section {
    background-color: var(--color-dark);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-xl);
}

@media (min-width: 768px) {
    .testimonials-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
}

@media (min-width: 1280px) {
    .testimonials-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.testimonial-cta {
    background: rgba(0, 87, 75, 0.1);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.cta-icon {
    width: 4rem;
    height: 4rem;
    background: rgba(0, 87, 75, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-emerald);
    font-size: var(--text-2xl);
    margin-bottom: var(--space-lg);
}

.cta-title {
    font-family: var(--font-display);
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--color-emerald);
    margin-bottom: var(--space-md);
}

.cta-description {
    color: rgba(252, 252, 252, 0.8);
    line-height: 1.6;
    margin-bottom: var(--space-lg);
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-md) var(--space-xl);
    background: var(--gradient-emerald);
    color: var(--color-black);
    text-decoration: none;
    border-radius: var(--radius-3xl);
    font-weight: 600;
    transition: all var(--transition-normal);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-emerald);
}

/* 📞 Contact Section */
.contact-section {
    background-color: var(--color-dark);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: var(--space-xl);
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: var(--space-md);
}

.contact-icon {
    width: 3rem;
    height: 3rem;
    background: rgba(0, 87, 75, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-emerald);
    font-size: var(--text-lg);
    flex-shrink: 0;
}

.contact-label {
    font-family: var(--font-display);
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--color-white);
    margin-bottom: var(--space-sm);
}

.contact-link {
    color: rgba(252, 252, 252, 0.8);
    font-size: var(--text-lg);
    text-decoration: none;
    transition: color var(--transition-normal);
}

.contact-link:hover,
.contact-link:focus {
    color: var(--color-emerald);
    text-decoration: underline;
}

/* 📝 Forms */
.contact-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.form-group-row {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-lg);
}

@media (min-width: 640px) {
    .form-group-row {
        grid-template-columns: repeat(2, 1fr);
    }
}

.form-input {
    width: 100%;
    padding: var(--space-md) var(--space-lg);
    background: rgba(13, 27, 26, 0.5);
    border: 1px solid rgba(0, 87, 75, 0.2);
    border-radius: var(--radius-xl);
    color: var(--color-white);
    font-family: var(--font-sans);
    font-size: var(--text-base);
    transition: all var(--transition-normal);
    outline: none;
}

.form-input::placeholder {
    color: rgba(252, 252, 252, 0.5);
}

.form-input:focus {
    border-color: var(--color-emerald);
    background: rgba(13, 27, 26, 0.7);
    box-shadow: 0 0 0 3px rgba(0, 87, 75, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 8rem;
}

/* 🦶 Footer */
.footer-section {
    padding: var(--space-2xl) 0;
    background-color: var(--color-dark);
    border-top: 1px solid rgba(0, 87, 75, 0.2);
}

.footer-title {
    font-family: var(--font-display);
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--color-emerald);
    margin-bottom: var(--space-md);
}

.footer-description {
    color: rgba(252, 252, 252, 0.7);
    margin-bottom: var(--space-lg);
    line-height: 1.6;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.social-link {
    width: 3rem;
    height: 3rem;
    background: rgba(0, 87, 75, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-emerald);
    font-size: var(--text-xl);
    text-decoration: none;
    transition: all var(--transition-normal);
}

.social-link:hover,
.social-link:focus {
    background: var(--color-emerald);
    color: var(--color-black);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.footer-copyright {
    color: rgba(252, 252, 252, 0.5);
    font-size: var(--text-sm);
    text-align: center;
}

.footer-siret {
    font-size: var(--text-sm);
    color: rgba(255, 255, 255, 0.7);
    margin-top: var(--space-sm);
    text-align: center;
}

/* 💬 WhatsApp Float */
.whatsapp-float {
    position: fixed;
    bottom: var(--space-xl);
    right: var(--space-xl);
    width: 4rem;
    height: 4rem;
    background: #25d366;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-white);
    font-size: var(--text-2xl);
    text-decoration: none;
    box-shadow: var(--shadow-xl);
    transition: all var(--transition-normal);
    z-index: var(--z-fixed);
    animation: bounce 2s infinite;
}

.whatsapp-float:hover,
.whatsapp-float:focus {
    background: #128c7e;
    color: var(--color-white);
    transform: scale(1.1);
    animation: none;
}

/* 🎯 Glass Effects */
.glass-card {
    background: rgba(13, 27, 26, 0.7);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 87, 75, 0.3);
    border-radius: var(--radius-2xl);
}

/* 📱 Mobile Optimizations */
@media (max-width: 768px) {
    .hero-content {
        padding: var(--space-3xl) var(--space-md);
    }
    
    .section {
        padding: var(--space-3xl) 0;
    }
    
    .whatsapp-float {
        bottom: var(--space-lg);
        right: var(--space-lg);
        width: 3.5rem;
        height: 3.5rem;
        font-size: var(--text-xl);
    }
}

/* 🖨️ Print Styles */
@media print {
    .nav-bar,
    .mobile-menu-overlay,
    .carousel-pagination,
    .whatsapp-float,
    button {
        display: none !important;
    }
    
    .hero-section {
        min-height: auto;
        padding: var(--space-xl) 0;
    }
    
    .section {
        page-break-inside: avoid;
        padding: var(--space-lg) 0;
    }
}