/* ===================================
   🎯 GolfinThaï - Variables CSS Propres
   Architecture Silicon Valley
   =================================== */

:root {
    /* 🎨 Couleurs Principales */
    --color-emerald: #00574B;
    --color-emerald-light: #A3D1C8;
    --color-emerald-dark: #2d8b7f;
    --color-white: #fcfcfc;
    --color-black: #0d1b1a;
    --color-dark: #1a2c2a;
    
    /* 🌈 Couleurs Système */
    --color-success: #22c55e;
    --color-warning: #f59e0b;
    --color-error: #ef4444;
    --color-info: #3b82f6;
    
    /* 📏 Espacements */
    --space-xs: 0.25rem;    /* 4px */
    --space-sm: 0.5rem;     /* 8px */
    --space-md: 1rem;       /* 16px */
    --space-lg: 1.5rem;     /* 24px */
    --space-xl: 2rem;       /* 32px */
    --space-2xl: 3rem;      /* 48px */
    --space-3xl: 4rem;      /* 64px */
    --space-4xl: 6rem;      /* 96px */
    
    /* 🔤 Typographie */
    --font-sans: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-display: 'Playfair Display', 'Georgia', serif;
    --font-mono: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
    
    /* 📐 Tailles de Police */
    --text-xs: 0.75rem;     /* 12px */
    --text-sm: 0.875rem;    /* 14px */
    --text-base: 1rem;      /* 16px */
    --text-lg: 1.125rem;    /* 18px */
    --text-xl: 1.25rem;     /* 20px */
    --text-2xl: 1.5rem;     /* 24px */
    --text-3xl: 1.875rem;   /* 30px */
    --text-4xl: 2.25rem;    /* 36px */
    --text-5xl: 3rem;       /* 48px */
    
    /* 🔄 Transitions */
    --transition-fast: 150ms ease;
    --transition-normal: 300ms ease;
    --transition-slow: 500ms ease;
    
    /* 📦 Border Radius */
    --radius-sm: 0.25rem;   /* 4px */
    --radius-md: 0.5rem;    /* 8px */
    --radius-lg: 0.75rem;   /* 12px */
    --radius-xl: 1rem;      /* 16px */
    --radius-2xl: 1.5rem;   /* 24px */
    --radius-3xl: 2rem;     /* 32px */
    --radius-full: 9999px;
    
    /* 🌫️ Ombres */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-emerald: 0 10px 30px rgba(0, 87, 75, 0.3);
    
    /* 📱 Z-Index Scale */
    --z-base: 1;
    --z-dropdown: 100;
    --z-sticky: 200;
    --z-fixed: 300;
    --z-modal-backdrop: 400;
    --z-modal: 500;
    --z-popover: 600;
    --z-tooltip: 700;
    --z-toast: 800;
    --z-max: 999;
    
    /* 🖥️ Breakpoints (pour référence) */
    --bp-sm: 640px;
    --bp-md: 768px;
    --bp-lg: 1024px;
    --bp-xl: 1280px;
    --bp-2xl: 1536px;
    
    /* 🎭 Animations */
    --animation-bounce: bounce 1s infinite;
    --animation-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    --animation-spin: spin 1s linear infinite;
    
    /* 🌐 Gradients */
    --gradient-emerald: linear-gradient(135deg, var(--color-emerald) 0%, var(--color-emerald-light) 100%);
    --gradient-dark: linear-gradient(135deg, var(--color-black) 0%, var(--color-dark) 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
}

/* 🌙 Mode Sombre (si nécessaire plus tard) */
@media (prefers-color-scheme: dark) {
    :root {
        --color-white: #fcfcfc;
        --color-black: #0d1b1a;
    }
}

/* 📱 Variables Responsive */
@media (max-width: 768px) {
    :root {
        --space-4xl: 4rem; /* Réduction pour mobile */
        --text-5xl: 2.5rem; /* Titres plus petits sur mobile */
    }
}