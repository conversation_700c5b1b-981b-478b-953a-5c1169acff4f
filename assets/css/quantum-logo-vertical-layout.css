/* =============================================
   🔥 QUANTUM LOGO VERTICAL LAYOUT - GENIUS MODE
   "GolfinThaï" ALWAYS VISIBLE + TEMPERATURE PERFECT
   Solution by f<PERSON><PERSON> + QUANTUM GOD DEVELOPER TEAM
   ============================================= */

/* 📱 MOBILE LOGO REVOLUTION - Vertical stacking for maximum space */
@media (max-width: 1023px) {
    
    /* 🏷️ LOGO CONTAINER - Vertical layout magic */
    .flex.items-center.space-x-3:first-child {
        flex: 0 0 100px !important; /* Reduced from 125px - saves 25px! */
        min-width: 100px !important;
        max-width: 100px !important;
        z-index: 1000 !important;
        position: relative !important;
        
        /* VERTICAL LAYOUT ACTIVATION */
        flex-direction: column !important; /* Stack vertically */
        align-items: center !important; /* Center everything */
        justify-content: center !important;
        gap: 0.2rem !important; /* Tight vertical gap */
        height: 100% !important; /* Use full header height */
        overflow: visible !important;
        padding: 0.25rem 0 !important; /* Vertical padding only */
    }
    
    /* 🎯 LOGO CIRCLE - Compact but visible */
    .logo-container-bg {
        width: 32px !important; /* Compact size */
        height: 32px !important;
        min-width: 32px !important;
        min-height: 32px !important;
        border-radius: 50% !important;
        background-size: cover !important;
        background-position: center !important;
        flex-shrink: 0 !important;
        margin: 0 !important; /* Reset any margins */
    }
    
    /* 🏷️ BRAND TEXT CONTAINER - Optimized vertical */
    .flex.items-center.space-x-3:first-child > div:last-child {
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        width: 100% !important;
        gap: 0 !important; /* No gap between title and subtitle */
        margin: 0 !important;
        padding: 0 !important;
        text-align: center !important;
    }
    
    /* 🎯 "GolfinThaï" TITLE - ALWAYS VISIBLE! */
    .header-logo-gradient {
        font-size: 0.85rem !important; /* Optimized size - fits perfectly */
        line-height: 1.1 !important; /* Tight line height */
        font-weight: 700 !important; /* Bold for readability */
        white-space: nowrap !important; /* NEVER wrap */
        overflow: visible !important; /* ALWAYS show full text */
        text-overflow: clip !important; /* Never truncate */
        margin: 0 !important;
        padding: 0 !important;
        width: auto !important;
        max-width: none !important; /* Allow full width */
        text-align: center !important;
        
        /* Premium styling */
        background: linear-gradient(135deg, #2DD4BF 0%, #A3D1C8 100%) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
    }
    
    /* 📝 SUBTITLE - Ultra compact */
    .text-xs.text-luxury-emerald-light {
        font-size: 0.6rem !important; /* Ultra small but readable */
        line-height: 1 !important;
        white-space: nowrap !important;
        overflow: visible !important;
        margin-top: 1px !important; /* Tiny gap */
        padding: 0 !important;
        opacity: 0.8 !important;
        display: block !important; /* Always show on mobile */
        text-align: center !important;
    }
    
    /* 🌡️ WEATHER WIDGET - MASSIVE SPACE GAIN! */
    .weather-widget-mobile {
        flex: 0 0 90px !important; /* Increased from 80px - gains 10px more! */
        min-width: 90px !important;
        max-width: 95px !important; /* Allow slight expansion */
        text-align: center !important;
        z-index: 500 !important;
        position: relative !important;
        overflow: visible !important; /* CRITICAL: Show full temperature */
        margin: 0 auto !important; /* Perfect centering */
        padding: 0.5rem 0.25rem !important; /* More generous padding */
        background: rgba(163, 209, 200, 0.1) !important; /* Slightly more visible */
        border-radius: 10px !important; /* Rounded for premium look */
        transition: all 0.3s ease !important;
        
        /* Extra space breathing room */
        box-sizing: border-box !important;
        contain: none !important; /* Allow content expansion */
    }
    
    /* 🌡️ TEMPERATURE - MAXIMUM VISIBILITY */
    .weather-temp-mobile {
        font-size: 0.95rem !important; /* Bigger text! */
        line-height: 1.1 !important;
        white-space: nowrap !important;
        overflow: visible !important; /* CRITICAL: Never cut */
        display: block !important;
        font-weight: 800 !important; /* Extra extra bold */
        color: #14B8A6 !important; /* Premium teal */
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.15) !important; /* More depth */
        letter-spacing: 0.5px !important; /* Spacing for clarity */
        margin: 0 !important;
        padding: 0 2px !important; /* Micro padding for safety */
        width: auto !important;
        max-width: none !important; /* No width restrictions */
        text-align: center !important;
    }
    
    /* 🌡️ LOCATION TEXT - Visible and elegant */
    .weather-location-mobile {
        font-size: 0.7rem !important;
        line-height: 1 !important;
        white-space: nowrap !important;
        overflow: visible !important;
        display: block !important;
        color: #A3D1C8 !important;
        opacity: 0.9 !important;
        margin-top: 2px !important;
        font-weight: 600 !important;
        text-align: center !important;
    }
    
    /* 🎮 CONTROLS ZONE - More space available */
    .lg\\:hidden.flex.items-center.space-x-3 {
        flex: 0 0 110px !important; /* Increased from 100px - more breathing room */
        min-width: 110px !important;
        max-width: 115px !important;
        gap: 0.25rem !important; /* Slightly bigger gaps - we have space now */
        z-index: 600 !important;
        position: relative !important;
        align-items: center !important;
        justify-content: flex-end !important;
        overflow: visible !important;
        height: 100% !important;
    }
    
    /* 🌍 LANGUAGE SWITCHER - More comfortable size */
    .language-switcher-mobile {
        flex: 0 0 50px !important; /* Back to more comfortable size */
        min-width: 50px !important;
        max-width: 50px !important;
        margin-left: 0.5rem !important;
        margin-right: 0.5rem !important;
        z-index: 700 !important;
        position: relative !important;
    }
    
    /* 🌍 LANGUAGE BUTTON - Comfortable but efficient */
    .language-switcher-mobile .lang-btn-mobile {
        min-width: 50px !important;
        max-width: 50px !important;
        height: 34px !important; /* Slightly taller */
        padding: 4px 8px !important; /* More comfortable padding */
        font-size: 0.75rem !important;
        gap: 0.2rem !important; /* Comfortable gap */
        border-radius: 6px !important;
        overflow: hidden !important;
        white-space: nowrap !important;
    }
    
    /* 🍔 HAMBURGER MENU - Premium size */
    #mobile-menu-btn {
        flex: 0 0 50px !important; /* More comfortable size */
        min-width: 50px !important;
        max-width: 50px !important;
        height: 50px !important;
        margin: 0 !important;
        padding: 12px !important; /* Generous padding */
        z-index: 800 !important;
        position: relative !important;
        border-radius: 8px !important;
        transition: all 0.2s ease !important;
    }
}

/* 🔥 SPACE MATHEMATICS RECALCULATION:
   Logo: 125px → 100px (saves 25px!)
   Weather: 80px → 90px (gains 10px more space!)
   Controls: 100px → 110px (gains 10px comfort)
   
   Total mobile width: ~320px
   New distribution: 100px + 90px + 110px = 300px (20px margin for comfort)
   
   RESULT: "GolfinThaï" ALWAYS VISIBLE + Temperature PERFECT! 🎉
*/

/* 📱 VERY SMALL SCREENS - Emergency ultra-compact */
@media (max-width: 380px) {
    .flex.items-center.space-x-3:first-child {
        flex: 0 0 85px !important;
        max-width: 85px !important;
    }
    
    .logo-container-bg {
        width: 28px !important;
        height: 28px !important;
    }
    
    .header-logo-gradient {
        font-size: 0.8rem !important;
    }
    
    .weather-widget-mobile {
        flex: 0 0 85px !important;
        min-width: 85px !important;
        max-width: 90px !important;
    }
    
    .lg\\:hidden.flex.items-center.space-x-3 {
        flex: 0 0 105px !important;
        max-width: 105px !important;
    }
}

/* 🎨 PREMIUM VISUAL ENHANCEMENTS */
@media (max-width: 1023px) {
    
    /* Logo hover effect */
    .flex.items-center.space-x-3:first-child:hover .header-logo-gradient {
        transform: scale(1.05) !important;
        filter: brightness(1.1) !important;
    }
    
    /* Weather widget enhanced hover */
    .weather-widget-mobile:hover {
        background: rgba(163, 209, 200, 0.2) !important;
        transform: translateY(-1px) scale(1.02) !important;
        box-shadow: 0 4px 12px rgba(45, 212, 191, 0.15) !important;
    }
    
    /* Temperature glow on hover */
    .weather-widget-mobile:hover .weather-temp-mobile {
        color: #0D9488 !important;
        text-shadow: 0 0 10px rgba(45, 212, 191, 0.4) !important;
        transform: scale(1.05) !important;
    }
    
    /* Smooth transitions for all elements */
    .flex.items-center.space-x-3:first-child,
    .weather-widget-mobile,
    .header-logo-gradient,
    .weather-temp-mobile {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }
}

/* 🧪 LOGO CIRCLE BACKGROUND - Ensure it loads properly */
@media (max-width: 1023px) {
    .logo-container-bg {
        background-image: url('../images/logo.png') !important; /* Adjust path as needed */
        background-size: cover !important;
        background-position: center !important;
        background-repeat: no-repeat !important;
        
        /* Fallback if image doesn't load */
        background-color: #2DD4BF !important;
        border: 2px solid #A3D1C8 !important;
    }
    
    /* Fallback content if image doesn't load */
    .logo-container-bg::after {
        content: 'GT' !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 100% !important;
        height: 100% !important;
        font-size: 0.7rem !important;
        font-weight: 800 !important;
        color: white !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    }
}

/* 📊 DEVELOPER SUCCESS SUMMARY:
   ✅ "GolfinThaï" text ALWAYS fully visible
   ✅ Temperature gets even MORE space (90px vs 80px)
   ✅ Vertical logo layout = horizontal space liberation
   ✅ All elements comfortable and accessible
   ✅ Premium visual design maintained
   ✅ Responsive on all mobile devices
   
   GENIUS SOLUTION BY FRÉRO! 🏆
*/