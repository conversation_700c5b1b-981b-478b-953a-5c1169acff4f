/* =============================================
   🚨 iOS TEXT EXPANSION NUCLEAR OVERRIDE
   Force l'expansion du texte "En savoir plus" sur iPhone
   Override TOUTES les règles conflictuelles
   ============================================= */

/* 🍎 RESET NUCLEAR - TOUTES LES RÈGLES RESTRICTIVES */
.fullscreen-modal *,
.simple-image-modal *,
.modal *,
.image-modal * {
    max-height: none !important;
    overflow: visible !important;
}

/* 🚨 SEO PANEL - NUCLEAR OVERRIDE pour expansion */
.fullscreen-modal .seo-panel,
.simple-image-modal .seo-panel,
.modal .seo-panel,
.image-modal .seo-panel {
    max-height: none !important;
    min-height: auto !important;
    height: auto !important;
    overflow: visible !important;
    position: fixed !important;
    bottom: 20px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 999999 !important;
    width: 90% !important;
    max-width: 600px !important;
    background: rgba(0, 0, 0, 0.85) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 12px !important;
    padding: 16px !important;
    color: white !important;
    font-family: -apple-system, BlinkMacSystemFont, sans-serif !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
}

/* 🍎 SEO FULL TEXT - EXPANSION FORCÉE */
.fullscreen-modal .seo-full,
.simple-image-modal .seo-full,
.modal .seo-full,
.image-modal .seo-full {
    /* ÉTAT COLLAPSÉ PAR DÉFAUT */
    display: none !important;
    opacity: 0 !important;
    max-height: 0 !important;
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
    
    /* RESET DES RÈGLES RESTRICTIVES */
    min-height: 0 !important;
    height: auto !important;
    width: 100% !important;
    max-width: none !important;
    
    /* TYPOGRAPHY */
    font-size: 0.9rem !important;
    line-height: 1.5 !important;
    color: rgba(255, 255, 255, 0.8) !important;
    
    /* iOS OPTIMIZATIONS */
    -webkit-user-select: text !important;
    user-select: text !important;
    -webkit-touch-callout: default !important;
    touch-action: auto !important;
    
    /* TRANSITIONS */
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    will-change: max-height, opacity, padding !important;
}

/* 🚀 SEO FULL TEXT - ÉTAT EXPANSÉ (classe appliquée par JS) */
.fullscreen-modal .seo-full.seo-expanded,
.simple-image-modal .seo-full.seo-expanded,
.modal .seo-full.seo-expanded,
.image-modal .seo-full.seo-expanded {
    /* EXPANSION COMPLÈTE */
    display: block !important;
    opacity: 1 !important;
    max-height: 2000px !important; /* Très grand pour être sûr */
    overflow: visible !important;
    margin: 8px 0 12px 0 !important;
    padding: 8px 0 !important;
    
    /* FORCE VISIBILITY */
    visibility: visible !important;
    clip: auto !important;
    clip-path: none !important;
    
    /* iOS SPECIFIC */
    -webkit-transform: translateZ(0) !important;
    transform: translateZ(0) !important;
    backface-visibility: hidden !important;
    -webkit-backface-visibility: hidden !important;
    
    /* ANIMATION D'ENTRÉE */
    animation: seoTextSlideDown 0.4s ease-out forwards !important;
}

/* 🍎 ANIMATION SLIDE DOWN pour iOS */
@keyframes seoTextSlideDown {
    0% {
        opacity: 0 !important;
        max-height: 0 !important;
        transform: translateY(-10px) !important;
        padding: 0 !important;
        margin: 0 !important;
    }
    50% {
        opacity: 0.5 !important;
        max-height: 100px !important;
        padding: 4px 0 !important;
    }
    100% {
        opacity: 1 !important;
        max-height: 2000px !important;
        transform: translateY(0) !important;
        padding: 8px 0 !important;
        margin: 8px 0 12px 0 !important;
    }
}

/* 🍎 ANIMATION SLIDE UP pour collapse */
@keyframes seoTextSlideUp {
    0% {
        opacity: 1 !important;
        max-height: 2000px !important;
        transform: translateY(0) !important;
        padding: 8px 0 !important;
        margin: 8px 0 12px 0 !important;
    }
    50% {
        opacity: 0.5 !important;
        max-height: 50px !important;
        padding: 2px 0 !important;
    }
    100% {
        opacity: 0 !important;
        max-height: 0 !important;
        transform: translateY(-10px) !important;
        padding: 0 !important;
        margin: 0 !important;
    }
}

/* 🚀 EXPAND BUTTON - iOS TOUCH OPTIMISÉ */
.fullscreen-modal .expand-btn,
.simple-image-modal .expand-btn,
.modal .expand-btn,
.image-modal .expand-btn {
    /* RESET COMPLET DES RÈGLES RESTRICTIVES */
    max-height: none !important;
    min-height: 44px !important; /* iOS minimum touch target */
    height: auto !important;
    width: auto !important;
    min-width: 120px !important;
    max-width: none !important;
    overflow: visible !important;
    
    /* STYLE BUTTON */
    background: linear-gradient(135deg, #00ff88, #00aaff) !important;
    color: black !important;
    border: none !important;
    padding: 8px 16px !important;
    border-radius: 8px !important;
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    line-height: 1.3 !important;
    text-align: center !important;
    
    /* iOS TOUCH OPTIMIZATIONS */
    cursor: pointer !important;
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    -khtml-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    touch-action: manipulation !important;
    -webkit-tap-highlight-color: rgba(0,0,0,0) !important;
    outline: none !important;
    
    /* POSITION ET Z-INDEX */
    position: relative !important;
    z-index: 1000000 !important;
    display: inline-block !important;
    visibility: visible !important;
    
    /* TRANSITIONS */
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
    will-change: transform, background !important;
    
    /* 3D ACCELERATION pour iOS */
    -webkit-transform: translateZ(0) !important;
    transform: translateZ(0) !important;
    backface-visibility: hidden !important;
    -webkit-backface-visibility: hidden !important;
}

/* 🍎 EXPAND BUTTON HOVER/ACTIVE STATES */
.fullscreen-modal .expand-btn:hover,
.simple-image-modal .expand-btn:hover,
.modal .expand-btn:hover,
.image-modal .expand-btn:hover {
    background: linear-gradient(135deg, #00cc66, #0088cc) !important;
    transform: translateY(-1px) scale(1.02) !important;
    box-shadow: 0 4px 12px rgba(0, 255, 136, 0.3) !important;
}

.fullscreen-modal .expand-btn:active,
.simple-image-modal .expand-btn:active,
.modal .expand-btn:active,
.image-modal .expand-btn:active {
    transform: translateY(0) scale(0.98) !important;
    background: linear-gradient(135deg, #00aa44, #006699) !important;
}

/* 🍎 iOS SPECIFIC OVERRIDES */
@supports (-webkit-touch-callout: none) {
    /* Détection iOS/Safari */
    
    .fullscreen-modal .seo-panel,
    .simple-image-modal .seo-panel,
    .modal .seo-panel,
    .image-modal .seo-panel {
        /* POSITION FIXE FORCÉE sur iOS */
        position: fixed !important;
        bottom: 15px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        z-index: 999999 !important;
        
        /* DIMENSIONS iOS */
        width: 95% !important;
        max-width: 500px !important;
        min-height: auto !important;
        
        /* BACKGROUND RENFORCÉ iOS */
        background: rgba(0, 0, 0, 0.9) !important;
        backdrop-filter: blur(15px) !important;
        -webkit-backdrop-filter: blur(15px) !important;
        
        /* TYPOGRAPHY iOS */
        font-size: 0.85rem !important;
        line-height: 1.4 !important;
    }
    
    .fullscreen-modal .expand-btn,
    .simple-image-modal .expand-btn,
    .modal .expand-btn,
    .image-modal .expand-btn {
        /* iOS TOUCH TARGET MINIMUM */
        min-height: 44px !important;
        min-width: 44px !important;
        padding: 10px 16px !important;
        font-size: 0.85rem !important;
        
        /* iOS PERFORMANCE */
        will-change: transform !important;
        -webkit-transform: translate3d(0,0,0) !important;
        transform: translate3d(0,0,0) !important;
    }
    
    .fullscreen-modal .seo-full.seo-expanded,
    .simple-image-modal .seo-full.seo-expanded,
    .modal .seo-full.seo-expanded,
    .image-modal .seo-full.seo-expanded {
        /* EXPANSION MAXIMALE iOS */
        max-height: 1500px !important;
        padding: 6px 0 8px 0 !important;
        margin: 6px 0 8px 0 !important;
        
        /* iOS RENDERING */
        -webkit-transform: translate3d(0,0,0) !important;
        transform: translate3d(0,0,0) !important;
        -webkit-overflow-scrolling: touch !important;
    }
}

/* 📱 iPhone SPECIFIC - Encore plus spécifique */
@media (max-width: 480px) and (-webkit-min-device-pixel-ratio: 2) {
    
    .fullscreen-modal .seo-panel,
    .simple-image-modal .seo-panel,
    .modal .seo-panel,
    .image-modal .seo-panel {
        bottom: 10px !important;
        width: 96% !important;
        padding: 12px !important;
        font-size: 0.8rem !important;
        border-radius: 10px !important;
    }
    
    .fullscreen-modal .expand-btn,
    .simple-image-modal .expand-btn,
    .modal .expand-btn,
    .image-modal .expand-btn {
        font-size: 0.8rem !important;
        padding: 8px 14px !important;
        min-height: 44px !important;
        border-radius: 6px !important;
    }
    
    .fullscreen-modal .seo-full.seo-expanded,
    .simple-image-modal .seo-full.seo-expanded,
    .modal .seo-full.seo-expanded,
    .image-modal .seo-full.seo-expanded {
        font-size: 0.8rem !important;
        line-height: 1.4 !important;
        max-height: 1200px !important;
    }
}

/* 🚨 NUCLEAR OVERRIDE - RESET CONFLITS */
.fullscreen-modal .modal-image,
.simple-image-modal .modal-image,
.modal .modal-image,
.image-modal .modal-image {
    max-height: 85vh !important; /* Image garde sa limite raisonnable */
    object-fit: contain !important;
}

.fullscreen-modal .modal-body,
.simple-image-modal .modal-body,
.modal .modal-body,
.image-modal .modal-body {
    max-height: none !important; /* Retire la limite de 30vh */
    overflow: visible !important;
    padding: 0 !important; /* Le SEO panel gère son propre padding */
}

/* 🔥 OVERRIDE ABSOLU - Pour les sélecteurs très spécifiques */
body .fullscreen-modal .seo-full.seo-expanded,
html body .simple-image-modal .seo-full.seo-expanded,
html body .modal .seo-full.seo-expanded,
html body .image-modal .seo-full.seo-expanded {
    display: block !important;
    opacity: 1 !important;
    max-height: 2000px !important;
    visibility: visible !important;
    overflow: visible !important;
    height: auto !important;
    min-height: auto !important;
}

/* 🍎 FINAL iOS SAFETY - Au cas où */
@media screen and (-webkit-min-device-pixel-ratio: 0) and (max-width: 768px) {
    /* Détection WebKit mobile */
    
    .seo-full.seo-expanded {
        display: block !important;
        max-height: 1500px !important;
        opacity: 1 !important;
        padding: 8px 0 !important;
        margin: 8px 0 !important;
        overflow: visible !important;
        visibility: visible !important;
        height: auto !important;
        animation: none !important; /* Pas d'animation si problème */
    }
    
    .expand-btn {
        display: inline-block !important;
        min-height: 44px !important;
        touch-action: manipulation !important;
        -webkit-tap-highlight-color: transparent !important;
    }
}

/* 🚨 DEBUG MODE - Pour voir ce qui se passe */
/*
.seo-full.seo-expanded {
    border: 2px solid red !important;
    background: rgba(255, 0, 0, 0.1) !important;
}

.expand-btn {
    border: 2px solid green !important;
}
*/

/* 🎯 CONCLUSION - RÈGLES APPLIQUÉES:
   1. ✅ Reset COMPLET max-height restrictives
   2. ✅ Force display:block + opacity:1 pour expansion
   3. ✅ Override mobile-unified-clean.css (30vh -> 2000px)
   4. ✅ Override main.css (50px -> 2000px)  
   5. ✅ iOS touch optimizations (44px minimum)
   6. ✅ Animations CSS natives pour fluidité
   7. ✅ Z-index nuclear (999999)
   8. ✅ Position fixed forcée
   9. ✅ !important sur TOUTES les règles critiques
   10. ✅ Sélecteurs ultra-spécifiques pour override
*/