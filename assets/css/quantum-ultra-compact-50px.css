/* =============================================
   🎯 QUANTUM ULTRA-COMPACT FINAL - 50PX WEATHER
   Maximum space for "GolfinThaï" + Weather 50px + Right shift
   FRÉRO'S PERFECT VISION
   ============================================= */

/* 📱 MOBILE HEADER ULTRA-COMPACT PERFECTION */
@media (max-width: 1023px) {
    
    /* 🎯 MAIN HEADER CONTAINER - Perfect distribution */
    .nav-bar .container .flex.justify-between {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        gap: 0 !important;
        padding: 0.5rem 0 !important;
        position: relative !important;
        height: auto !important;
        min-height: 60px !important;
    }
    
    /* 🏷️ LOGO ZONE - MAXIMUM SPACE for "GolfinThaï" */
    .flex.items-center.space-x-3:first-child {
        flex: 0 0 120px !important; /* MORE SPACE thanks to 50px weather! */
        min-width: 120px !important;
        max-width: 120px !important;
        z-index: 1000 !important;
        position: relative !important;
        
        /* HORIZONTAL LAYOUT - CLASSIC */
        display: flex !important;
        flex-direction: row !important;
        align-items: center !important;
        justify-content: flex-start !important;
        gap: 0.5rem !important; /* Comfortable gap */
        height: 100% !important;
        overflow: visible !important;
        padding: 0.5rem 0.3rem !important;
        
        /* MAXIMUM VISIBILITY */
        background: transparent !important;
        border: none !important;
    }
    
    /* 🎯 LOGO CIRCLE - BIGGER thanks to extra space */
    .logo-container-bg {
        width: 42px !important; /* BIGGER LOGO! */
        height: 42px !important;
        min-width: 42px !important;
        min-height: 42px !important;
        border-radius: 50% !important;
        background-size: cover !important;
        background-position: center !important;
        flex-shrink: 0 !important;
        margin: 0 !important;
        z-index: 1001 !important;
        position: relative !important;
        
        /* Premium but subtle styling */
        box-shadow: 0 2px 6px rgba(45, 212, 191, 0.12) !important;
        border: 2px solid rgba(163, 209, 200, 0.2) !important;
        transition: all 0.3s ease !important;
    }
    
    /* 🏷️ "GolfinThaï" TEXT CONTAINER - MAXIMUM SPACE */
    .flex.items-center.space-x-3:first-child > div:last-child {
        display: block !important;
        flex: 1 !important;
        z-index: 1002 !important;
        position: relative !important;
        
        /* MAXIMUM TEXT SPACE */
        overflow: visible !important;
        margin: 0 !important;
        padding: 0 !important;
        width: auto !important;
        max-width: 72px !important; /* More space available! */
        height: auto !important;
        
        /* Perfect visibility */
        background: transparent !important;
        border: none !important;
        text-align: left !important;
    }
    
    /* 🎯 "GolfinThaï" TITLE - PERFECT VISIBILITY */
    .header-logo-gradient {
        font-size: 0.95rem !important; /* BIGGER font thanks to more space! */
        line-height: 1.1 !important;
        font-weight: 800 !important;
        white-space: nowrap !important;
        overflow: visible !important;
        text-overflow: clip !important;
        margin: 0 !important;
        padding: 0 !important;
        width: auto !important;
        max-width: 70px !important; /* Full space available */
        text-align: left !important;
        z-index: 1003 !important;
        position: relative !important;
        
        /* DARK COLORS as requested */
        background: linear-gradient(135deg, #065f46 0%, #047857 70%, #059669 100%) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
        
        /* Enhanced visibility */
        filter: drop-shadow(0 1px 3px rgba(6, 95, 70, 0.25)) !important;
        transition: all 0.3s ease !important;
        
        /* FORCE DISPLAY */
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
    
    /* 🌡️ WEATHER WIDGET - ULTRA COMPACT 50PX + RIGHT SHIFT */
    .weather-widget-mobile {
        flex: 0 0 50px !important; /* ULTRA COMPACT 50px! */
        min-width: 50px !important;
        max-width: 55px !important;
        text-align: center !important;
        z-index: 500 !important;
        position: relative !important;
        overflow: visible !important;
        margin: 0 0.5rem 0 auto !important; /* RIGHT SHIFT with auto margin */
        padding: 0.3rem 0.1rem !important; /* Ultra tight padding */
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.03) 0%, rgba(45, 212, 191, 0.02) 100%) !important;
        border-radius: 6px !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        
        /* Minimal styling for 50px */
        box-shadow: 0 1px 3px rgba(45, 212, 191, 0.04) !important;
        border: 1px solid rgba(163, 209, 200, 0.08) !important;
        backdrop-filter: blur(1px) !important;
    }
    
    /* 🌡️ TEMPERATURE - OPTIMIZED FOR 50px WIDTH */
    .weather-temp-mobile {
        font-size: 0.75rem !important; /* Smaller for 50px space */
        line-height: 1.1 !important;
        white-space: nowrap !important;
        overflow: visible !important;
        display: block !important;
        font-weight: 600 !important;
        color: #047857 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.04) !important;
        letter-spacing: 0.1px !important;
        margin: 0 !important;
        padding: 0 !important;
        width: auto !important;
        max-width: none !important;
        text-align: center !important;
        z-index: 501 !important;
        position: relative !important;
        
        /* Compact optimization */
        filter: drop-shadow(0 0 2px rgba(4, 120, 87, 0.1)) !important;
    }
    
    /* 🌡️ LOCATION TEXT - MINIMAL for 50px */
    .weather-location-mobile {
        font-size: 0.55rem !important; /* Ultra small for 50px */
        line-height: 1 !important;
        white-space: nowrap !important;
        overflow: visible !important;
        display: block !important;
        color: #059669 !important;
        opacity: 0.75 !important;
        margin-top: 1px !important;
        font-weight: 500 !important;
        text-align: center !important;
        letter-spacing: 0.05px !important;
        z-index: 501 !important;
        position: relative !important;
    }
    
    /* 🎮 CONTROLS ZONE - SHIFTED RIGHT with language */
    .lg\\:hidden.flex.items-center.space-x-3 {
        flex: 0 0 110px !important; /* Comfortable for 2 buttons */
        min-width: 110px !important;
        max-width: 115px !important;
        gap: 0.4rem !important;
        z-index: 600 !important;
        position: relative !important;
        align-items: center !important;
        justify-content: flex-end !important;
        overflow: visible !important;
        height: 100% !important;
        padding: 0.25rem 0.5rem 0.25rem 0 !important; /* RIGHT SHIFT with right padding */
        margin-left: 0.3rem !important; /* Additional right shift */
    }
    
    /* 🌍 LANGUAGE SWITCHER - SHIFTED RIGHT */
    .language-switcher-mobile {
        flex: 0 0 52px !important;
        min-width: 52px !important;
        max-width: 52px !important;
        margin-left: 0.2rem !important; /* Closer to weather */
        margin-right: 0.4rem !important; /* More space from edge */
        z-index: 700 !important;
        position: relative !important;
    }
    
    /* 🌍 LANGUAGE BUTTON - SHIFTED RIGHT */
    .language-switcher-mobile .lang-btn-mobile {
        min-width: 52px !important;
        max-width: 52px !important;
        height: 38px !important;
        padding: 6px 8px !important;
        font-size: 0.75rem !important;
        gap: 0.25rem !important;
        border-radius: 6px !important;
        overflow: hidden !important;
        white-space: nowrap !important;
        z-index: 701 !important;
        position: relative !important;
        
        /* Subtle styling */
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.04) 0%, rgba(45, 212, 191, 0.02) 100%) !important;
        border: 1px solid rgba(163, 209, 200, 0.12) !important;
        backdrop-filter: blur(1px) !important;
        transition: all 0.3s ease !important;
    }
    
    /* 🍔 HAMBURGER MENU - EDGE POSITION */
    #mobile-menu-btn {
        flex: 0 0 48px !important;
        min-width: 48px !important;
        max-width: 48px !important;
        height: 48px !important;
        margin: 0 !important;
        padding: 12px !important;
        z-index: 800 !important;
        position: relative !important;
        border-radius: 7px !important;
        transition: all 0.3s ease !important;
        
        /* Subtle styling */
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.04) 0%, rgba(45, 212, 191, 0.02) 100%) !important;
        border: 1px solid rgba(163, 209, 200, 0.12) !important;
        backdrop-filter: blur(1px) !important;
    }
    
    /* 🍔 HAMBURGER ICON */
    #mobile-menu-btn i {
        font-size: 1.05rem !important;
        line-height: 1 !important;
        color: #047857 !important;
        z-index: 801 !important;
        position: relative !important;
    }
}

/* 🔥 ULTRA-COMPACT SPACE MATHEMATICS:
   Logo: 120px (MAXIMUM space for "GolfinThaï"!)
   Weather: 50px (ULTRA COMPACT as requested)
   Controls: 110px (comfortable 2 buttons, shifted right)
   
   Total: 280px for ~320px mobile = 88% efficiency
   Space gained: Weather 70px → 50px = +20px for logo!
   
   RESULT: "GolfinThaï" gets MAXIMUM VISIBILITY! 🎉
*/

/* 📱 VERY SMALL SCREENS - PROPORTIONAL SCALING */
@media (max-width: 380px) {
    .flex.items-center.space-x-3:first-child {
        flex: 0 0 110px !important;
        max-width: 110px !important;
    }
    
    .logo-container-bg {
        width: 38px !important;
        height: 38px !important;
    }
    
    .header-logo-gradient {
        font-size: 0.9rem !important;
        max-width: 65px !important;
    }
    
    .weather-widget-mobile {
        flex: 0 0 45px !important;
        min-width: 45px !important;
        max-width: 48px !important;
    }
    
    .weather-temp-mobile {
        font-size: 0.7rem !important;
    }
    
    .weather-location-mobile {
        font-size: 0.5rem !important;
    }
    
    .lg\\:hidden.flex.items-center.space-x-3 {
        flex: 0 0 105px !important;
        max-width: 105px !important;
    }
    
    .language-switcher-mobile {
        flex: 0 0 48px !important;
        min-width: 48px !important;
        max-width: 48px !important;
    }
    
    #mobile-menu-btn {
        flex: 0 0 45px !important;
        min-width: 45px !important;
        max-width: 45px !important;
        height: 45px !important;
        padding: 10px !important;
    }
}

/* 🎨 REFINED HOVER EFFECTS - SUBTLE INTERACTIONS */
@media (max-width: 1023px) {
    
    /* Logo hover - Enhanced prominence */
    .flex.items-center.space-x-3:first-child:hover .header-logo-gradient {
        filter: drop-shadow(0 2px 6px rgba(6, 95, 70, 0.35)) brightness(1.08) !important;
        transform: scale(1.02) !important;
    }
    
    .flex.items-center.space-x-3:first-child:hover .logo-container-bg {
        transform: scale(1.05) !important;
        box-shadow: 0 3px 12px rgba(45, 212, 191, 0.2) !important;
    }
    
    /* Weather widget - Minimal hover for 50px */
    .weather-widget-mobile:hover {
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.06) 0%, rgba(45, 212, 191, 0.03) 100%) !important;
        transform: translateY(-1px) scale(1.02) !important;
        box-shadow: 0 2px 6px rgba(45, 212, 191, 0.06) !important;
    }
    
    /* Temperature subtle glow */
    .weather-widget-mobile:hover .weather-temp-mobile {
        color: #065f46 !important;
        filter: drop-shadow(0 0 4px rgba(6, 95, 70, 0.2)) !important;
    }
    
    /* Button hovers - Professional feedback */
    .language-switcher-mobile:hover .lang-btn-mobile {
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.08) 0%, rgba(45, 212, 191, 0.04) 100%) !important;
        border-color: rgba(163, 209, 200, 0.2) !important;
        transform: scale(1.02) !important;
    }
    
    #mobile-menu-btn:hover {
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.08) 0%, rgba(45, 212, 191, 0.04) 100%) !important;
        border-color: rgba(163, 209, 200, 0.2) !important;
        transform: scale(1.02) !important;
    }
    
    #mobile-menu-btn:hover i {
        color: #065f46 !important;
    }
    
    /* Smooth transitions */
    .flex.items-center.space-x-3:first-child,
    .logo-container-bg,
    .weather-widget-mobile,
    .header-logo-gradient,
    .weather-temp-mobile,
    .language-switcher-mobile .lang-btn-mobile,
    #mobile-menu-btn,
    #mobile-menu-btn i {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }
}

/* 🧪 LOGO CIRCLE BACKGROUND - Enhanced for bigger logo */
@media (max-width: 1023px) {
    .logo-container-bg {
        background-image: url('../images/logo.png') !important;
        background-size: cover !important;
        background-position: center !important;
        background-repeat: no-repeat !important;
        
        /* Enhanced fallback with darker colors */
        background-color: #059669 !important;
        border: 2px solid #047857 !important;
    }
    
    /* Fallback content for bigger logo */
    .logo-container-bg::after {
        content: 'GT' !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 100% !important;
        height: 100% !important;
        font-size: 0.95rem !important; /* Bigger text for 42px logo */
        font-weight: 900 !important;
        color: white !important;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5) !important;
        letter-spacing: 1px !important;
    }
}

/* 🏆 ULTRA-COMPACT 50PX ACHIEVEMENT:
   
   ✅ Weather widget: 70px → 50px (ULTRA COMPACT!)
   ✅ Logo space: 105px → 120px (+15px MORE for "GolfinThaï"!)
   ✅ Logo circle: 36px → 42px (BIGGER LOGO!)
   ✅ Font size: 0.85rem → 0.95rem (BIGGER TEXT!)
   ✅ Weather + Language shifted RIGHT (more logo space)
   ✅ "GolfinThaï" MAXIMUM VISIBILITY achieved!
   
   RESULT: Exactly what fréro wanted! 🎯
*/