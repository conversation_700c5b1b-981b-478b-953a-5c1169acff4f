/* 
 * 🍔 MOBILE MENU SPACING FIX - DEVS PROTOCOL
 * Fix espacement uniforme menu hamburger
 */

/* Reset et uniformisation espacement menu mobile */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    z-index: 9999;
    display: none;
    align-items: center;
    justify-content: center;
}

.mobile-menu-overlay .flex {
    height: 100vh !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0 !important; /* Reset gap */
}

/* Force espacement uniforme */
.mobile-menu-link {
    font-family: var(--font-display);
    font-size: 2rem !important;
    color: var(--color-white) !important;
    transition: color var(--transition-normal);
    
    /* ESPACEMENT UNIFORME FORCÉ */
    margin: 0 !important; /* Reset margin */
    padding: 1.5rem 2rem !important; /* Espacement uniforme */
    
    display: block !important;
    width: auto !important;
    text-align: center !important;
    text-decoration: none !important;
    
    /* Assurer espacement visuel */
    border-radius: 0.5rem;
    min-height: 60px;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.mobile-menu-link:hover,
.mobile-menu-link:focus {
    color: var(--color-emerald-light) !important;
    outline: none !important;
    background: rgba(0, 255, 136, 0.1) !important;
    border-radius: 1rem !important;
    transform: scale(1.05);
}

/* Force le bon espacement avec nth-child */
.mobile-menu-link:nth-child(1) { margin-bottom: 1.5rem !important; } /* Accueil */
.mobile-menu-link:nth-child(2) { margin-bottom: 1.5rem !important; } /* Destinations */
.mobile-menu-link:nth-child(3) { margin-bottom: 1.5rem !important; } /* À Propos */
.mobile-menu-link:nth-child(4) { margin-bottom: 1.5rem !important; } /* Témoignages */
.mobile-menu-link:nth-child(5) { margin-bottom: 0 !important; }     /* Contact (dernier) */

/* Media query pour mobile spécifiquement */
@media (max-width: 768px) {
    .mobile-menu-link {
        font-size: 1.8rem !important;
        padding: 1.2rem 1.5rem !important;
        min-height: 55px !important;
    }
    
    .mobile-menu-link:nth-child(1),
    .mobile-menu-link:nth-child(2),
    .mobile-menu-link:nth-child(3),
    .mobile-menu-link:nth-child(4) {
        margin-bottom: 1.2rem !important;
    }
}

/* Debug - pour voir l'espacement (à retirer en prod) */
/*
.mobile-menu-link {
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}
*/
