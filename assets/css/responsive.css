/*
 * Responsive CSS pour GolfinThaï
 * Media queries et adaptations mobile/tablette/desktop
 */

/* ===== BREAKPOINTS ===== */
/* Mobile: 320px - 767px */
/* Tablet: 768px - 1023px */
/* Desktop: 1024px+ */

/* ===== MOBILE FIRST ===== */
/* Styles de base pour mobile déjà définis dans les autres fichiers */

/* ===== TABLET (768px et plus) ===== */
@media (min-width: 768px) {
    /* Navigation */
    .nav-bar {
        padding: 0;
    }
    
    /* Sections */
    .section {
        padding: 6rem 0;
    }
    
    /* Grilles */
    .services-grid,
    .courses-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
    
    /* Typography */
    .section-title {
        font-size: 3rem;
    }
    
    .intro-text {
        font-size: 1.125rem;
    }
}

/* ===== DESKTOP (1024px et plus) ===== */
@media (min-width: 1024px) {
    /* Navigation */
    .nav-bar {
        height: 6rem;
    }
    
    /* Menu mobile caché */
    .mobile-menu-overlay {
        display: none;
    }
    
    /* Sections */
    .section {
        padding: 8rem 0;
    }
    
    /* Grilles */
    .courses-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2.5rem;
    }
    
    .testimonials-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
    
    /* Typography */
    .section-title {
        font-size: 4rem;
    }
    
    /* About section layout */
    .about-content {
        max-width: 1200px;
        margin: 0 auto;
    }
}
