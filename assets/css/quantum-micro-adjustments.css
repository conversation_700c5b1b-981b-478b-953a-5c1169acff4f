/* =============================================
   🎯 QUANTUM MICRO-ADJUSTMENTS - PRECISION LEVEL
   Fix "Voyages" text overflow + Weather widget size
   Micro-surgery by f<PERSON><PERSON> + QUANTUM TEAM
   ============================================= */

/* 📱 MOBILE SUBTITLE MICRO-ADJUSTMENT */
@media (max-width: 1023px) {
    
    /* 📝 SUBTITLE "Voyages Golfiques Thaïlande" - Perfect positioning */
    .text-xs.text-luxury-emerald-light {
        font-size: 0.6rem !important; 
        line-height: 1 !important;
        white-space: nowrap !important;
        overflow: visible !important;
        margin-top: 1px !important; 
        padding: 0 2px 0 4px !important; /* MICRO SHIFT: +2px right padding */
        opacity: 0.8 !important;
        display: block !important; 
        text-align: center !important;
        
        /* PRECISION POSITIONING - No more "V" cut-off */
        position: relative !important;
        left: 2px !important; /* MICRO SHIFT: +2px to the right */
        max-width: 96px !important; /* Slight containment */
        margin-left: auto !important;
        margin-right: auto !important;
        
        /* Enhanced readability */
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
        letter-spacing: 0.2px !important; /* Micro letter spacing */
    }
    
    /* 🌡️ WEATHER WIDGET - Slightly more compact (per fréro's feedback) */
    .weather-widget-mobile {
        flex: 0 0 85px !important; /* Reduced from 90px - still bigger than original 80px */
        min-width: 85px !important;
        max-width: 88px !important; /* Slightly more compact */
        text-align: center !important;
        z-index: 500 !important;
        position: relative !important;
        overflow: visible !important; 
        margin: 0 auto !important; 
        padding: 0.4rem 0.2rem !important; /* Slightly more compact padding */
        background: rgba(163, 209, 200, 0.08) !important; /* Subtle background */
        border-radius: 8px !important; 
        transition: all 0.3s ease !important;
        
        /* Optimized for better proportion */
        box-sizing: border-box !important;
        contain: none !important; 
    }
    
    /* 🌡️ TEMPERATURE TEXT - Maintain perfect readability */
    .weather-temp-mobile {
        font-size: 0.9rem !important; /* Maintain good size */
        line-height: 1.1 !important;
        white-space: nowrap !important;
        overflow: visible !important; 
        display: block !important;
        font-weight: 700 !important; /* Strong but not excessive */
        color: #14B8A6 !important; 
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.12) !important; 
        letter-spacing: 0.3px !important; /* Slightly reduced spacing */
        margin: 0 !important;
        padding: 0 1px !important; /* Micro padding for safety */
        width: auto !important;
        max-width: none !important; 
        text-align: center !important;
    }
    
    /* 🌡️ LOCATION TEXT - Proportional adjustment */
    .weather-location-mobile {
        font-size: 0.65rem !important; /* Slightly smaller to match */
        line-height: 1 !important;
        white-space: nowrap !important;
        overflow: visible !important;
        display: block !important;
        color: #A3D1C8 !important;
        opacity: 0.85 !important; /* Slightly more subtle */
        margin-top: 1px !important;
        font-weight: 500 !important; /* Lighter weight */
        text-align: center !important;
    }
}

/* 📱 VERY SMALL SCREENS - Emergency precision adjustments */
@media (max-width: 380px) {
    
    /* Subtitle even more precise positioning */
    .text-xs.text-luxury-emerald-light {
        font-size: 0.55rem !important; /* Slightly smaller on tiny screens */
        left: 3px !important; /* Bit more shift on small screens */
        max-width: 90px !important; /* Tighter containment */
        padding: 0 1px 0 3px !important; /* Adjusted micro-padding */
    }
    
    /* Weather widget ultra-compact but functional */
    .weather-widget-mobile {
        flex: 0 0 80px !important; /* Back to compact on tiny screens */
        min-width: 80px !important;
        max-width: 82px !important;
        padding: 0.3rem 0.15rem !important;
    }
    
    .weather-temp-mobile {
        font-size: 0.85rem !important; /* Proportional reduction */
        letter-spacing: 0.2px !important;
    }
    
    .weather-location-mobile {
        font-size: 0.6rem !important;
    }
}

/* 🎨 ENHANCED VISUAL REFINEMENTS */
@media (max-width: 1023px) {
    
    /* Logo container subtle refinement */
    .flex.items-center.space-x-3:first-child {
        flex: 0 0 100px !important; /* Maintain width */
        min-width: 100px !important;
        max-width: 100px !important;
        /* Perfect centering with micro-adjustments */
        padding: 0.25rem 0 !important;
        align-items: center !important;
        justify-content: center !important;
    }
    
    /* Weather widget hover refinement */
    .weather-widget-mobile:hover {
        background: rgba(163, 209, 200, 0.15) !important;
        transform: translateY(-1px) scale(1.02) !important;
        box-shadow: 0 3px 10px rgba(45, 212, 191, 0.12) !important; /* Subtle shadow */
    }
    
    /* Temperature premium glow on hover */
    .weather-widget-mobile:hover .weather-temp-mobile {
        color: #0D9488 !important;
        text-shadow: 0 0 8px rgba(45, 212, 191, 0.3) !important;
        transform: scale(1.03) !important; /* Subtle scale */
    }
    
    /* Subtitle hover effect */
    .text-xs.text-luxury-emerald-light:hover {
        opacity: 1 !important;
        transform: scale(1.02) !important;
        color: #2DD4BF !important;
    }
}

/* 🔍 PRECISION DEBUG - Visual validation */
@media (max-width: 1023px) {
    
    /* Uncomment for visual debugging */
    /*
    .text-xs.text-luxury-emerald-light {
        border: 1px dashed rgba(255, 165, 0, 0.5) !important;
        background: rgba(255, 165, 0, 0.1) !important;
    }
    
    .weather-widget-mobile {
        border: 1px dashed rgba(78, 205, 196, 0.5) !important;
        background: rgba(78, 205, 196, 0.1) !important;
    }
    */
}

/* 📊 MICRO-ADJUSTMENT SUMMARY:
   
   SUBTITLE FIXES:
   - +2px right shift (left: 2px)
   - +2px right padding  
   - Letter spacing for clarity
   - Max-width containment
   
   WEATHER WIDGET REFINEMENT:
   - 90px → 85px (5px reduction)
   - Maintains perfect temperature visibility
   - Proportional padding adjustments
   - Enhanced hover effects
   
   RESULT: 
   ✅ "Voyages Golfiques Thaïlande" fully visible
   ✅ Weather widget optimized size
   ✅ Professional micro-interactions
   ✅ Perfect mobile layout balance
*/