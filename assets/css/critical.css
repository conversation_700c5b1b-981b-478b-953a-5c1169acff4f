/* 
 * CSS Critique pour GolfinThaï
 * Styles essentiels pour le rendu above-the-fold
 */

/* Variables CSS */
:root {
    /* Couleurs principales */
    --color-emerald: #00574B;
    --color-emerald-light: #A3D1C8;
    --color-emerald-dark: #003D34;
    --color-black: #0D1B1A;
    --color-dark: #1A2C2A;
    --color-white: #FCFCFC;
    --color-cream: #F0F5F4;
    --font-display: 'Playfair Display', serif;
    --font-sans: 'Poppins', sans-serif;
    
    /* Couleurs complémentaires */
    --color-gray: #707D7C;
    
    /* Spacing Scale */
    --space-xs: 0.25rem;    /* 4px */
    --space-sm: 0.5rem;     /* 8px */
    --space-md: 1rem;       /* 16px */
    --space-lg: 1.5rem;     /* 24px */
    --space-xl: 2rem;       /* 32px */
    --space-2xl: 3rem;      /* 48px */
    --space-3xl: 4rem;      /* 64px */
    --space-4xl: 6rem;      /* 96px */
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-3xl: 2rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-emerald: 0 0 30px rgba(0, 87, 75, 0.3);
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Z-index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* Reset spécifique (Tailwind gère le reset global) */
html { 
    scroll-behavior: smooth; /* ← PERMET LA NAVIGATION PAR ANCRES */
    text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
}

body {
    font-family: var(--font-sans);
    font-size: 1rem;
    font-weight: 400;
    background-color: var(--color-black);
    color: var(--color-white);
    line-height: 1.6;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Section Hero - critique pour le first paint */
.hero-section {
    min-height: 100vh;
    min-height: 100dvh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Carrousel Hero - VERSION SIMPLE QUI MARCHE */
.hero-carousel {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 5;
    width: 100vw;
    height: 100vh;
    height: 100dvh;
    overflow: hidden;
    margin: 0;
    padding: 0;
}

/* Slides du carrousel simple */
.carousel-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    height: 100dvh;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    margin: 0;
    padding: 0;
}

.carousel-slide.active {
    opacity: 1;
}

.carousel-slide::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
        180deg,
        rgba(13, 27, 26, 0.4) 0%,
        rgba(13, 27, 26, 0.6) 40%,
        rgba(13, 27, 26, 0.8) 100%
    );
    z-index: 1;
}

/* ===== PAGINATION CARROUSEL - OPTIMISÉ RESPONSIVE ===== */
.carousel-pagination {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    display: flex;
    gap: 6px;
    padding: 6px 10px;
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* PAGINATION CARROUSEL - Taille optimisée pour visibilité + cliquabilité */
span.pagination-bullet[role="button"] {
    width: 5px !important;
    height: 5px !important;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    outline: none;
    margin: 0;
    padding: 0;
    min-width: 5px !important;
    min-height: 5px !important;
    /* Zone de clic agrandie pour mobile */
    position: relative;
}

span.pagination-bullet[role="button"]:hover {
    background: rgba(255, 255, 255, 0.7);
    transform: scale(1.6);
}

span.pagination-bullet[role="button"].active {
    background: var(--color-emerald-light);
    box-shadow: 0 0 8px rgba(163, 209, 200, 0.5);
    transform: scale(1.8);
}

/* Zone de clic agrandie invisible pour meilleure UX */
span.pagination-bullet[role="button"]::before {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    border-radius: 50%;
    /* Zone de clic invisible mais cliquable */
}

/* Mobile - taille optimisée pour touch */
@media (max-width: 768px) {
    .carousel-pagination {
        bottom: 15px;
        gap: 8px;
        padding: 8px 12px;
    }
    
    span.pagination-bullet[role="button"] {
        width: 6px !important;
        height: 6px !important;
        min-width: 6px !important;
        min-height: 6px !important;
    }
    
    /* Zone de clic encore plus grande sur mobile */
    span.pagination-bullet[role="button"]::before {
        top: -12px;
        left: -12px;
        right: -12px;
        bottom: -12px;
    }
}

/* Navigation - visible immédiatement */
.nav-bar {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1020;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    background: rgb(13, 27, 26);
    border-bottom: 1px solid var(--color-emerald-dark);
}

/* Loading skeleton pour UX */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Optimisation touch target pour mobile */
button, a, input, [tabindex] {
    min-height: 44px;
    min-width: 44px;
}

/* Gestion du focus pour l'accessibilité */
:focus-visible {
    outline: 2px solid var(--color-emerald);
    outline-offset: 2px;
}

/* Skip link pour l'accessibilité */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--color-emerald);
    color: white;
    padding: 8px;
    text-decoration: none;
    z-index: 10000;
    transition: top 0.3s;
}

.skip-link:focus {
    top: 6px;
}

/* Styles de base pour éviter FOUC */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 640px) {
    .container { padding: 0 1.5rem; }
}

@media (min-width: 1024px) {
    .container { padding: 0 2rem; }
}

/* Classes utilitaires critiques */
.text-center { text-align: center; }
.hidden { display: none; }
.sr-only { 
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
