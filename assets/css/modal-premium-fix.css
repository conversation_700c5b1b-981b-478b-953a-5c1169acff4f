/* 
 * 🔥 SILICON VALLEY MODAL & DRAPEAUX FIX
 * Transformation ultra-premium des modales et sélecteur de langue
 */

/* 🎯 1. MODAL TITLE ELEGANT GRADIENT - Cohérent avec le site */
.premium-modal-title {
    font-size: 32px !important;
    font-weight: 700 !important;
    /* Dégradé élégant cohérent avec les couleurs du site */
    background: linear-gradient(135deg, var(--color-emerald), var(--color-emerald-light)) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    margin-bottom: 10px !important;
    line-height: 1.2 !important;
    text-shadow: none !important;
    /* Animation subtile */
    animation: titleGlow 3s ease-in-out infinite alternate;
}

/* Animation subtile pour le titre */
@keyframes titleGlow {
    0% { filter: brightness(1); }
    100% { filter: brightness(1.1); }
}

/* 🎯 2. BOUTON DÉCOUVRIR ULTRA CLASSE */
.ultra-premium-toggle-btn {
    display: block !important;
    margin: 30px auto 20px !important;
    padding: 18px 36px !important;
    
    /* Dégradé élégant cohérent avec le site */
    background: linear-gradient(135deg, var(--color-emerald), var(--color-emerald-light)) !important;
    border: 2px solid transparent !important;
    border-radius: 50px !important;
    
    /* Texte élégant */
    color: var(--color-white) !important;
    font-weight: 600 !important;
    font-size: 16px !important;
    letter-spacing: 0.5px !important;
    text-transform: uppercase !important;
    
    cursor: pointer !important;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    
    /* Ombre élégante */
    box-shadow: 
        0 8px 25px rgba(0, 87, 75, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset !important;
    
    min-height: 56px !important;
    min-width: 220px !important;
    
    /* Optimisations mobile */
    -webkit-tap-highlight-color: transparent !important;
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    touch-action: manipulation !important;
    
    /* Effet de profondeur */
    position: relative !important;
    overflow: hidden !important;
}

/* Effet de survol élégant */
.ultra-premium-toggle-btn:hover {
    transform: translateY(-3px) !important;
    box-shadow: 
        0 12px 35px rgba(0, 87, 75, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.2) inset !important;
    
    /* Ajout d'un léger glow */
    filter: brightness(1.1) !important;
}

/* Effet de clic */
.ultra-premium-toggle-btn:active {
    transform: translateY(-1px) scale(0.98) !important;
    transition: all 0.1s ease !important;
}

/* Effet shimmer subtil */
.ultra-premium-toggle-btn::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(
        90deg, 
        transparent, 
        rgba(255, 255, 255, 0.2), 
        transparent
    ) !important;
    transition: left 0.8s ease !important;
}

.ultra-premium-toggle-btn:hover::before {
    left: 100% !important;
}

/* 🎯 3. DRAPEAUX CSS ÉLÉGANTS - Fini les émojis moches ! */

/* Container pour les drapeaux */
.flag-icon {
    display: inline-block !important;
    width: 24px !important;
    height: 18px !important;
    border-radius: 3px !important;
    position: relative !important;
    overflow: hidden !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
    vertical-align: middle !important;
    margin-right: 8px !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Drapeau français élégant */
.flag-icon:before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
}

/* Drapeau Français - Style CSS pur */
.lang-option[data-lang="fr"] .flag-icon,
.lang-option-mobile[data-lang="fr"] .flag-icon,
.lang-btn .flag-icon,
.lang-btn-mobile .flag-icon {
    background: linear-gradient(
        to right,
        #002654 0% 33.33%,
        #ffffff 33.33% 66.66%,
        #ce1126 66.66% 100%
    ) !important;
}

/* Drapeau Anglais - Style CSS pur */
.lang-option[data-lang="en"] .flag-icon,
.lang-option-mobile[data-lang="en"] .flag-icon {
    background: #012169 !important;
}

.lang-option[data-lang="en"] .flag-icon:after {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: 
        /* Croix de Saint-André */
        linear-gradient(45deg, transparent 40%, #ffffff 40% 46%, #ffffff 54% 60%, transparent 60%),
        linear-gradient(-45deg, transparent 40%, #ffffff 40% 46%, #ffffff 54% 60%, transparent 60%),
        /* Croix de Saint-Georges */
        linear-gradient(to right, transparent 42%, #ce1126 42% 46%, #ffffff 46% 54%, #ce1126 54% 58%, transparent 58%),
        linear-gradient(to bottom, transparent 42%, #ce1126 42% 46%, #ffffff 46% 54%, #ce1126 54% 58%, transparent 58%);
}

/* Animations pour les drapeaux */
.flag-icon {
    transition: all 0.3s ease !important;
}

.lang-option:hover .flag-icon,
.lang-option-mobile:hover .flag-icon,
.lang-btn:hover .flag-icon,
.lang-btn-mobile:hover .flag-icon {
    transform: scale(1.1) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4) !important;
}

/* Style pour les boutons de langue */
.lang-btn, .lang-btn-mobile {
    transition: all 0.3s ease !important;
}

.lang-btn:hover, .lang-btn-mobile:hover {
    background: rgba(0, 87, 75, 0.1) !important;
    border-color: var(--color-emerald-light) !important;
}

/* Dropdown amélioré */
.lang-dropdown, .lang-dropdown-mobile {
    background: rgba(26, 44, 42, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(163, 209, 200, 0.2) !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3) !important;
    overflow: hidden !important;
}

.lang-option, .lang-option-mobile {
    transition: all 0.2s ease !important;
    padding: 12px 16px !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
}

.lang-option:hover, .lang-option-mobile:hover {
    background: var(--color-emerald) !important;
    color: var(--color-white) !important;
}

.lang-option:last-child, .lang-option-mobile:last-child {
    border-bottom: none !important;
}

/* 🎯 4. MODAL SUBTITLE COHÉRENT */
.premium-modal-category {
    display: inline-block !important;
    padding: 8px 20px !important;
    background: rgba(0, 87, 75, 0.1) !important;
    border: 1px solid rgba(163, 209, 200, 0.3) !important;
    border-radius: 50px !important;
    color: var(--color-emerald-light) !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    margin-bottom: 15px !important;
}

/* 🎯 5. ÉLÉMENTS MODAUX COHÉRENTS */
.premium-legend h3 {
    font-size: 24px !important;
    color: var(--color-emerald-light) !important;
    margin-bottom: 15px !important;
    font-weight: 600 !important;
}

.detail-row strong {
    color: var(--color-emerald-light) !important;
    margin-right: 10px !important;
    min-width: 120px !important;
}

.highlight-item {
    background: rgba(0, 87, 75, 0.1) !important;
    border-left: 3px solid var(--color-emerald-light) !important;
    padding: 12px 16px !important;
    border-radius: 8px !important;
    color: #f0f0f0 !important;
    font-size: 14px !important;
    font-weight: 500 !important;
}

.premium-tag {
    padding: 6px 14px !important;
    background: linear-gradient(135deg, rgba(0, 87, 75, 0.2), rgba(163, 209, 200, 0.2)) !important;
    border: 1px solid rgba(163, 209, 200, 0.4) !important;
    border-radius: 20px !important;
    color: var(--color-emerald-light) !important;
    font-size: 12px !important;
    font-weight: 500 !important;
}

/* 🔥 RESPONSIVE PERFECTION */
@media (max-width: 768px) {
    .ultra-premium-toggle-btn {
        padding: 16px 28px !important;
        font-size: 15px !important;
        min-width: 200px !important;
    }
    
    .premium-modal-title {
        font-size: 28px !important;
    }
    
    .flag-icon {
        width: 20px !important;
        height: 15px !important;
    }
}

@media (max-width: 480px) {
    .ultra-premium-toggle-btn {
        padding: 14px 24px !important;
        font-size: 14px !important;
        min-width: 180px !important;
    }
    
    .premium-modal-title {
        font-size: 24px !important;
    }
}
