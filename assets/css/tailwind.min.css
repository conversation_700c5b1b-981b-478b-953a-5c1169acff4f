/*! tailwindcss v3.4.0 | MIT License | https://tailwindcss.com */
*,::after,::before{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}::after,::before{--tw-content:''}html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",<PERSON><PERSON>,"Helvetica Neue",<PERSON><PERSON>,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace,SFMono-Regular,"Consolas","Liberation Mono","Menlo",monospace;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}[type=button],[type=reset],[type=submit],button{-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]{display:none}*,::after,::before{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: }

/* Container */
.container{width:100%;margin-left:auto;margin-right:auto;padding-left:1rem;padding-right:1rem}@media (min-width: 640px){.container{max-width:640px}}@media (min-width: 768px){.container{max-width:768px}}@media (min-width: 1024px){.container{max-width:1024px}}@media (min-width: 1280px){.container{max-width:1280px}}@media (min-width: 1536px){.container{max-width:1536px}}

/* Spacing */
.mx-auto{margin-left:auto;margin-right:auto}
.mb-2{margin-bottom:0.5rem}
.mb-4{margin-bottom:1rem}
.mb-6{margin-bottom:1.5rem}
.mb-8{margin-bottom:2rem}
.mb-12{margin-bottom:3rem}
.mb-16{margin-bottom:4rem}
.mt-4{margin-top:1rem}
.mt-10{margin-top:2.5rem}

/* Padding */
.p-4{padding:1rem}
.p-6{padding:1.5rem}
.p-8{padding:2rem}
.px-4{padding-left:1rem;padding-right:1rem}
.px-6{padding-left:1.5rem;padding-right:1.5rem}
.px-8{padding-left:2rem;padding-right:2rem}
.py-3{padding-top:0.75rem;padding-bottom:0.75rem}
.py-4{padding-top:1rem;padding-bottom:1rem}
.py-5{padding-top:1.25rem;padding-bottom:1.25rem}
.py-12{padding-top:3rem;padding-bottom:3rem}
.py-16{padding-top:4rem;padding-bottom:4rem}

/* Display */
.block{display:block}
.inline-block{display:inline-block}
.inline-flex{display:inline-flex}
.flex{display:flex}
.grid{display:grid}
.hidden{display:none}

/* Flexbox */
.flex-col{flex-direction:column}
.items-center{align-items:center}
.items-start{align-items:flex-start}
.justify-center{justify-content:center}
.justify-between{justify-content:space-between}
.space-x-2>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-right:calc(0.5rem * var(--tw-space-x-reverse));margin-left:calc(0.5rem * calc(1 - var(--tw-space-x-reverse)))}
.space-x-3>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-right:calc(0.75rem * var(--tw-space-x-reverse));margin-left:calc(0.75rem * calc(1 - var(--tw-space-x-reverse)))}
.space-x-6>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-right:calc(1.5rem * var(--tw-space-x-reverse));margin-left:calc(1.5rem * calc(1 - var(--tw-space-x-reverse)))}
.space-x-8>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-right:calc(2rem * var(--tw-space-x-reverse));margin-left:calc(2rem * calc(1 - var(--tw-space-x-reverse)))}
.space-y-8>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-top:calc(2rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(2rem * var(--tw-space-y-reverse))}

/* Grid */
.grid-cols-1{grid-template-columns:repeat(1, minmax(0, 1fr))}
.grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}
.grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr))}
.gap-6{gap:1.5rem}
.gap-8{gap:2rem}
.gap-12{gap:3rem}

/* Sizing */
.h-12{height:3rem}
.h-16{height:4rem}
.h-20{height:5rem}
.h-24{height:6rem}
.h-48{height:12rem}
.h-64{height:16rem}
.h-96{height:24rem}
.h-full{height:100%}
.min-h-screen{min-height:100vh}
.w-12{width:3rem}
.w-16{width:4rem}
.w-full{width:100%}
.max-w-4xl{max-width:56rem}
.max-w-5xl{max-width:64rem}

/* Position */
.fixed{position:fixed}
.absolute{position:absolute}
.relative{position:relative}
.inset-0{inset:0px}
.bottom-4{bottom:1rem}
.bottom-6{bottom:1.5rem}
.bottom-8{bottom:2rem}
.left-4{left:1rem}
.left-6{left:1.5rem}
.right-4{right:1rem}
.right-6{right:1.5rem}
.right-8{right:2rem}
.top-0{top:0px}
.top-4{top:1rem}
.top-8{top:2rem}
.z-10{z-index:10}
.z-40{z-index:40}
.z-50{z-index:50}

/* Overflow */
.overflow-hidden{overflow:hidden}
.overflow-x-hidden{overflow-x:hidden}
.overflow-y-auto{overflow-y:auto}

/* Border Radius */
.rounded-full{border-radius:9999px}
.rounded-xl{border-radius:0.75rem}
.rounded-2xl{border-radius:1rem}
.rounded-3xl{border-radius:1.5rem}

/* Background */
.bg-luxury-black{background-color:#0D1B1A}
.bg-luxury-dark{background-color:#1A2C2A}
.bg-green-500{background-color:#10b981}

/* Text */
.text-center{text-align:center}
.text-xs{font-size:0.75rem;line-height:1rem}
.text-sm{font-size:0.875rem;line-height:1.25rem}
.text-lg{font-size:1.125rem;line-height:1.75rem}
.text-xl{font-size:1.25rem;line-height:1.75rem}
.text-2xl{font-size:1.5rem;line-height:2rem}
.text-3xl{font-size:1.875rem;line-height:2.25rem}
.font-bold{font-weight:700}
.font-medium{font-weight:500}
.font-semibold{font-weight:600}
.leading-relaxed{line-height:1.625}
.leading-tight{line-height:1.25}

/* Colors */
.text-luxury-white{color:#FCFCFC}
.text-luxury-emerald{color:#00574B}
.text-luxury-emerald-light{color:#A3D1C8}
.text-white{color:#fff}

/* Transforms */
.transform{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
.translate-x-full{--tw-translate-x:100%}

/* Transitions */
.transition-all{transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}
.transition-colors{transition-property:color, background-color, border-color, text-decoration-color, fill, stroke;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}
.transition-transform{transition-property:transform;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}
.duration-300{transition-duration:300ms}
.duration-500{transition-duration:500ms}

/* Effects */
.shadow-lg{--tw-shadow:0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}
.shadow-xl{--tw-shadow:0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}

/* Object Fit */
.object-cover{object-fit:cover}
.object-center{object-position:center}

/* Hover states */
.hover\:scale-105:hover{--tw-scale-x:1.05;--tw-scale-y:1.05;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
.hover\:scale-110:hover{--tw-scale-x:1.1;--tw-scale-y:1.1;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
.hover\:text-luxury-emerald:hover{color:#00574B}

/* Responsive Design */
@media (min-width: 640px) {
    .sm\:block{display:block}
    .sm\:px-6{padding-left:1.5rem;padding-right:1.5rem}
    .sm\:text-xl{font-size:1.25rem;line-height:1.75rem}
    .sm\:text-2xl{font-size:1.5rem;line-height:2rem}
    .sm\:text-4xl{font-size:2.25rem;line-height:2.5rem}
    .sm\:text-6xl{font-size:3.75rem;line-height:1}
}

@media (min-width: 768px) {
    .md\:grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}
    .md\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr))}
    .md\:text-7xl{font-size:4.5rem;line-height:1}
}

@media (min-width: 1024px) {
    .lg\:h-16{height:4rem}
    .lg\:h-24{height:6rem}
    .lg\:flex{display:flex}
    .lg\:hidden{display:none}
    .lg\:w-16{width:4rem}
    .lg\:px-8{padding-left:2rem;padding-right:2rem}
    .lg\:py-24{padding-top:6rem;padding-bottom:6rem}
    .lg\:text-2xl{font-size:1.5rem;line-height:2rem}
    .lg\:text-5xl{font-size:3rem;line-height:1}
    .lg\:text-8xl{font-size:6rem;line-height:1}
    .lg\:gap-12{gap:3rem}
    .lg\:mb-16{margin-bottom:4rem}
}

@media (min-width: 1280px) {
    .xl\:block{display:block}
    .xl\:hidden{display:none}
    .xl\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr))}
    .xl\:text-6xl{font-size:3.75rem;line-height:1}
    .xl\:text-9xl{font-size:8rem;line-height:1}
}