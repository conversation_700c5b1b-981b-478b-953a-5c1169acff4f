/* 📱 MOBILE FLAGS FIX - PERFECT POSITIONING */

@media (max-width: 768px) {
    /* Language switcher mobile container */
    .language-switcher-mobile {
        position: relative !important;
        margin-right: 8px !important;
        min-width: 60px !important;
    }
    
    /* Mobile language button */
    .lang-btn-mobile {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 6px 10px !important;
        background: rgba(163, 209, 200, 0.1) !important;
        border: 1px solid rgba(163, 209, 200, 0.3) !important;
        border-radius: 8px !important;
        min-width: 55px !important;
        height: 36px !important;
    }
    
    /* Mobile dropdown */
    .lang-dropdown-mobile {
        position: absolute !important;
        top: 100% !important;
        right: 0 !important;
        left: auto !important;
        margin-top: 4px !important;
        background: rgba(13, 27, 26, 0.95) !important;
        border: 1px solid rgba(163, 209, 200, 0.3) !important;
        border-radius: 8px !important;
        backdrop-filter: blur(20px) !important;
        z-index: 1000 !important;
        min-width: 120px !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
    }
    
    /* Mobile language options */
    .lang-option-mobile {
        display: flex !important;
        align-items: center !important;
        justify-content: flex-start !important;
        padding: 8px 12px !important;
        width: 100% !important;
        background: transparent !important;
        border: none !important;
        color: #fff !important;
        font-size: 14px !important;
        transition: background 0.2s ease !important;
    }
    
    .lang-option-mobile:hover {
        background: rgba(163, 209, 200, 0.2) !important;
    }
    
    /* Mobile controls container adjustment */
    .lg\\:hidden {
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        margin-right: 4px !important;
    }
    
    /* Mobile menu button adjustment */
    #mobile-menu-btn {
        min-width: 44px !important;
        min-height: 44px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 8px !important;
    }
    
    /* Header container mobile adjustment */
    .container.mx-auto.px-4.sm\\:px-6.lg\\:px-8 {
        padding-left: 12px !important;
        padding-right: 12px !important;
    }
    
    /* Header flex container */
    .flex.justify-between.items-center.h-20.lg\\:h-24 {
        height: 64px !important;
        gap: 8px !important;
    }
    
    /* Logo section mobile */
    .flex.items-center.space-x-3 {
        flex-shrink: 0 !important;
        min-width: 0 !important;
    }
    
    /* Weather widget mobile positioning */
    .weather-widget-mobile {
        margin-right: 4px !important;
        flex-shrink: 0 !important;
    }
}

/* Extra small screens */
@media (max-width: 480px) {
    .container.mx-auto.px-4.sm\\:px-6.lg\\:px-8 {
        padding-left: 8px !important;
        padding-right: 8px !important;
    }
    
    .language-switcher-mobile {
        min-width: 50px !important;
    }
    
    .lang-btn-mobile {
        min-width: 48px !important;
        height: 32px !important;
        padding: 4px 8px !important;
    }
    
    .lang-dropdown-mobile {
        min-width: 100px !important;
    }
}