/* ================================
   CARROUSEL - Classes CSS Natives  
   Remplacement des 12 hacks styles inline carousel-mobile-fixed.js
   ================================ */

/* ===== ÉTATS SLIDES ===== */

/* Slide visible - Remplace slide.style.opacity = '1' */
.slide-visible {
    opacity: 1 !important;
    z-index: 2;
}

/* Slide caché - Remplace slide.style.opacity = '0' */
.slide-hidden {
    opacity: 0 !important;
    z-index: 1;
}

/* Transition slide - Remplace transitions inline */
.slide-transition {
    transition: opacity 1s ease-in-out !important;
}

/* ===== ÉTATS BULLETS PAGINATION ===== */

/* Bullet normal - État par défaut */
.bullet-normal {
    transform: scale(1);
    transition: transform 0.2s ease;
}

/* Bullet pressé - Remplace bullet.style.transform = 'scale(1.05)' */
.bullet-pressed {
    transform: scale(1.05) !important;
    transition: transform 0.15s ease !important;
}

/* ===== ÉTATS TRANSITION CARROUSEL ===== */

/* Carrousel en cours de transition */
.carousel-transitioning .slide-transition {
    transition-duration: 1s;
}

/* Pause animation au hover */
.carousel-paused .slide-transition {
    animation-play-state: paused;
}

/* ===== RESPONSIVE OPTIMISATIONS ===== */

/* Mobile : transitions plus rapides */
@media (max-width: 768px) {
    .slide-transition {
        transition-duration: 0.8s !important;
    }
    
    .bullet-pressed {
        transform: scale(1.08) !important;
    }
}

/* ===== ACCESSIBILITÉ ===== */

/* Respect préférences motion réduite */
@media (prefers-reduced-motion: reduce) {
    .slide-transition,
    .bullet-normal,
    .bullet-pressed {
        transition: none !important;
        animation: none !important;
    }
}