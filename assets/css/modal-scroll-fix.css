/* 
 * MODAL SCROLL FIX - GolfinThaï
 * Correction définitive du problème de scroll lors fermeture modal
 */

/* Force scroll instantané pendant les opérations de modal */
.modal-scroll-fix,
.modal-scroll-fix * {
    scroll-behavior: auto !important;
    -webkit-scroll-behavior: auto !important;
}

/* Éviter les transitions de scroll involontaires */
.modal-closing {
    scroll-behavior: auto !important;
}

/* S'assurer que la modal n'interfère pas avec le scroll */
.course-modal {
    scroll-behavior: auto !important;
}

.course-modal * {
    scroll-behavior: auto !important;
}

/* Override pour body pendant modal */
body.modal-scroll-disabled,
body.modal-scroll-disabled * {
    scroll-behavior: auto !important;
    transition: none !important;
}

/* Force auto scroll sur html pendant les opérations modales */
html.modal-operation {
    scroll-behavior: auto !important;
    transition: none !important;
}

/* Réactive le smooth scroll après modal */
html.modal-operation-complete {
    scroll-behavior: smooth;
    transition: all 0.3s ease;
}
