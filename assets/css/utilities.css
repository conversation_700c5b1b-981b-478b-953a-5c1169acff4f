/*
 * CSS Complémentaire pour GolfinThaï
 * Styles pour les badges, animations et éléments spécialisés
 */

/* ===== BADGES DES PARCOURS ===== */
.course-location-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
    position: absolute;
    top: 1rem;
    left: 1rem;
    z-index: 10;
}

.badge-teal { background-color: #14b8a6; }
.badge-gray { background-color: #6b7280; }
.badge-green { background-color: #16a34a; }
.badge-orange { background-color: #ea580c; }
.badge-red { background-color: #dc2626; }
.badge-blue { background-color: #2563eb; }

/* ===== GRILLES SPÉCIALISÉES ===== */
.services-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-top: 3rem;
}

.courses-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-top: 2rem;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-top: 2rem;
}

@media (min-width: 768px) {
    .services-grid { grid-template-columns: repeat(3, 1fr); }
    .courses-grid { grid-template-columns: repeat(2, 1fr); }
    .testimonials-grid { grid-template-columns: repeat(2, 1fr); }
}

@media (min-width: 1024px) {
    .courses-grid { grid-template-columns: repeat(3, 1fr); }
}

/* ===== ANIMATIONS DE RÉVÉLATION ===== */
.reveal-animation {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.reveal-animation.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* ===== COURSE OVERLAY ===== */
.course-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.3), transparent, rgba(0,0,0,0.7));
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    padding: 1rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.course-card:hover .course-overlay {
    opacity: 1;
}

.course-expand-btn {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-emerald);
    cursor: pointer;
    transition: all 0.3s ease;
}

.course-expand-btn:hover {
    background: white;
    transform: scale(1.1);
}

/* ===== WIDGETS MÉTÉO ===== */
.weather-widget-mini, .weather-widget-mobile {
    padding: 0.5rem;
    background: rgba(26, 44, 42, 0.8);
    border-radius: 0.5rem;
    border: 1px solid rgba(163, 209, 200, 0.2);
}

.weather-temp, .weather-temp-mobile {
    font-weight: 600;
    color: var(--color-emerald-light);
}

.weather-location, .weather-location-mobile {
    font-size: 0.75rem;
    color: var(--color-emerald-light);
    opacity: 0.8;
}

/* ===== MENU MOBILE ===== */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(13, 27, 26, 0.95);
    backdrop-filter: blur(10px);
    z-index: 9999;
    display: none;
}

.mobile-menu-link {
    color: var(--color-emerald-light);
    text-decoration: none;
    font-size: 1.5rem;
    font-weight: 500;
    transition: color 0.3s ease;
}

.mobile-menu-link:hover {
    color: var(--color-emerald);
}

.mobile-menu-close {
    color: var(--color-emerald-light);
    background: none;
    border: none;
    font-size: 2rem;
    cursor: pointer;
}

/* ===== BOUTON WHATSAPP FLOTTANT ===== */
.whatsapp-float {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: #25d366;
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    text-decoration: none;
    box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
    z-index: 1000;
    transition: all 0.3s ease;
}

.whatsapp-float:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.6);
}

/* ===== CLASSES UTILITAIRES MANQUANTES ===== */
.text-luxury-emerald { color: var(--color-emerald); }
.text-luxury-emerald-light { color: var(--color-emerald-light); }
.ring-2 { box-shadow: 0 0 0 2px currentColor; }
.ring-luxury-emerald { --tw-ring-color: var(--color-emerald); }
.font-display { font-family: var(--font-display); }

/* ===== WEATHER WIDGET ===== */
.weather-icon {
    display: inline-block;
    font-size: 1rem;
}

.weather-icon.text-sm {
    font-size: 0.875rem;
}

.weather-temp, .weather-temp-mobile {
    font-weight: 600;
    color: var(--color-emerald-light);
    font-size: 0.875rem;
}

.weather-location, .weather-location-mobile {
    font-size: 0.75rem;
    color: var(--color-emerald-light);
    opacity: 0.8;
}

/* ===== NAVIGATION ACTIVE STATE ===== */
.nav-link.active {
    color: var(--color-emerald);
    font-weight: 600;
}

/* ===== FORM UTILITIES ===== */
.form-group-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

/* ===== ERROR STATES ===== */
.form-input.error {
    border-color: #dc2626;
    background-color: rgba(239, 68, 68, 0.1);
}

.field-error {
    color: #dc2626;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    display: block;
}

.form-success {
    background-color: rgba(34, 197, 94, 0.1);
    border: 1px solid #22c55e;
    color: #15803d;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.form-error {
    background-color: rgba(239, 68, 68, 0.1);
    border: 1px solid #dc2626;
    color: #dc2626;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

/* ===== BUTTON LOADING STATE ===== */
.btn.loading {
    opacity: 0.7;
    cursor: not-allowed;
}

/* ===== INTRO TEXT STYLES ===== */
.intro-text {
    font-size: 1.125rem;
    line-height: 1.7;
    margin-bottom: 1.5rem;
    color: var(--color-emerald-light);
}

.intro-paragraph {
    font-size: 1.125rem;
    line-height: 1.7;
    color: var(--color-emerald-light);
}

.intro-subtitle {
    font-family: var(--font-display);
    font-size: 1.875rem;
    font-weight: 600;
    color: var(--color-white);
    margin-bottom: 1rem;
}

.highlight-text {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--color-emerald);
    text-align: center;
    padding: 1.5rem;
    background: rgba(163, 209, 200, 0.1);
    border-radius: 1rem;
    border: 1px solid rgba(163, 209, 200, 0.2);
}

.intro-highlight {
    margin-top: 2rem;
}

/* ===== SECTION STYLES ===== */
.section-description {
    font-size: 1.125rem;
    line-height: 1.7;
    color: var(--color-emerald-light);
    max-width: 800px;
    margin: 0 auto;
}

/* ===== CLASSES UTILITAIRES ===== */
.text-gradient {
    background: linear-gradient(135deg, var(--color-emerald), var(--color-emerald-light));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

.w-full { width: 100%; }
.h-full { height: 100%; }
.object-cover { object-fit: cover; }
.rounded-3xl { border-radius: 1.5rem; }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

/* ===== RESPONSIVE TWEAKS ===== */
@media (max-width: 768px) {
    .whatsapp-float {
        bottom: 1rem;
        right: 1rem;
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
}

/* ===== TOUCH FEEDBACK CLASSES ===== */
.touch-feedback-pressed {
    transform: scale(0.98);
    transition: transform 0.1s ease-out;
}

.touch-feedback-released {
    transform: scale(1);
    transition: transform 0.1s ease-out;
}