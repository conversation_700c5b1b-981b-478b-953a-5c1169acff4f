/* ===================================
   GolfinThaï - Main Stylesheet
   Mobile-First Responsive Design
   SEO & Performance Optimized
   =================================== */

/* CSS Custom Properties (Variables) */
/* ✅ CONSOLIDÉ : Variables migrent vers critical.css pour éviter duplication */

/* Base Styles spécifiques (Reset géré par Tailwind) */
/* ✅ CONSOLIDÉ : Body styles migrent vers critical.css pour éviter duplication */

html {
    font-size: 16px;
    -ms-text-size-adjust: 100%;
}
/* ===================================
   Accessibility & Screen Readers
   =================================== */
/* ✅ CONSOLIDÉ : Classes accessibilité migrent vers critical.css */

/* ===================================
   Typography Scale
   =================================== */

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-display);
    font-weight: 700;
    line-height: 1.2;
    color: var(--color-white);
    margin-bottom: var(--space-md);
}

h1 { font-size: clamp(2.5rem, 8vw, 6rem); }
h2 { font-size: clamp(2rem, 6vw, 4rem); }
h3 { font-size: clamp(1.5rem, 4vw, 2.5rem); }
h4 { font-size: clamp(1.25rem, 3vw, 1.75rem); }
h5 { font-size: clamp(1.125rem, 2.5vw, 1.5rem); }
h6 { font-size: clamp(1rem, 2vw, 1.25rem); }

p {
    margin-bottom: var(--space-md);
    color: rgba(252, 252, 252, 0.9);
}

a {
    color: var(--color-emerald-light);
    text-decoration: none;
    transition: color var(--transition-normal);
}

a:hover,
a:focus {
    color: var(--color-emerald);
    outline: 2px solid var(--color-emerald);
    outline-offset: 2px;
}

/* ===================================
   Layout & Grid System
   =================================== */

/* .container supprimé - on utilise .container-custom défini dans layout.css */

@media (min-width: 1024px) {
    .container { padding: 0 var(--space-xl); }
}

/* ===================================
   Button System
   =================================== */

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-md) var(--space-xl);
    border: none;
    border-radius: var(--radius-3xl);
    font-family: var(--font-sans);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    outline: none;
}

.btn:focus {
    outline: 2px solid var(--color-emerald);
    outline-offset: 2px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--color-emerald) 0%, var(--color-emerald-light) 50%, var(--color-emerald) 100%);
    background-size: 200% 200%;
    color: var(--color-black);
    box-shadow: var(--shadow-lg);
}

.btn-primary:hover,
.btn-primary:focus {
    background-position: 100% 0;
    transform: translateY(-2px);
    box-shadow: var(--shadow-emerald);
    color: var(--color-black);
}

/* ===================================
   Navigation Styles
   =================================== */

/* ===================================
   Navigation Styles - Glass Effect
   =================================== */

/* .nav-bar base styles définis dans critical.css */
.nav-bar {
    height: 80px;
    display: flex;
    align-items: center;
}

.nav-bar.scrolled {
    background: rgba(13, 27, 26, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 87, 75, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Glass utility classes for other elements */
.glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-dark {
    background: rgba(13, 27, 26, 0.7);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 87, 75, 0.3);
}

.glass-emerald {
    background: rgba(0, 87, 75, 0.1);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(0, 87, 75, 0.3);
}

/* ===================================
   Weather Widget Styles
   =================================== */

.weather-widget-mini {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border-radius: 12px;
    padding: 8px 16px;
    border: 1px solid rgba(0, 87, 75, 0.2);
    transition: all 0.3s ease;
    cursor: pointer;
}

.weather-widget-mini:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(0, 87, 75, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 87, 75, 0.2);
}

.weather-widget-mobile {
    background: rgba(0, 87, 75, 0.1);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 6px 12px;
    border: 1px solid rgba(0, 87, 75, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
}

.weather-widget-mobile:hover {
    background: rgba(0, 87, 75, 0.2);
    border-color: rgba(0, 87, 75, 0.5);
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 87, 75, 0.3);
}

/* Weather animations */
.weather-icon { 
    animation: weather-float 3s ease-in-out infinite; 
}

@keyframes weather-float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-3px); }
}

.weather-temp { 
    font-weight: 600; 
    color: var(--color-white); 
}

.weather-temp-mobile { 
    font-weight: 700; 
    color: var(--color-emerald); 
    font-size: 0.9rem; 
}

.weather-location { 
    color: var(--color-emerald-light); 
    font-weight: 500; 
}

.weather-location-mobile { 
    color: var(--color-white); 
    font-weight: 400; 
    font-size: 0.75rem; 
    opacity: 0.9; 
}

.nav-link {
    color: var(--color-white);
    font-weight: 500;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
}

.nav-link:hover,
.nav-link:focus {
    color: var(--color-emerald-light);
    background: rgba(0, 87, 75, 0.1);
    outline: none;
}

.mobile-menu-overlay {
    position: fixed;
    inset: 0;
    background: rgba(13, 27, 26, 0.98);
    backdrop-filter: blur(20px);
    z-index: var(--z-modal-backdrop);
    transform: translateX(100%);
    transition: transform var(--transition-slow);
    display: none; /* Caché par défaut */
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

/* ✅ SUPPRIMÉE : Ancienne classe .mobile-menu-overlay.open remplacée par mobile-menu-native.css */

.mobile-menu-link {
    font-family: var(--font-display);
    font-size: 2rem;
    color: var(--color-white);
    transition: color var(--transition-normal);
    margin: 1rem 0;
    padding: 1rem 2rem;
    display: block;
    width: 100%;
    text-align: center;
}

.mobile-menu-link:hover,
.mobile-menu-link:focus {
    color: var(--color-emerald-light);
    outline: none;
    background: rgba(0, 87, 75, 0.1);
    border-radius: 1rem;
}

.mobile-menu-close {
    position: absolute;
    top: var(--space-xl);
    right: var(--space-xl);
    background: none;
    border: none;
    color: var(--color-emerald);
    font-size: 2rem;
    cursor: pointer;
    padding: var(--space-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
}

.mobile-menu-close:hover,
.mobile-menu-close:focus {
    background: rgba(0, 87, 75, 0.1);
    transform: scale(1.1);
    outline: 2px solid var(--color-emerald);
    outline-offset: 2px;
}
/* ===================================
   Hero Section Styles
   =================================== */

.hero-section {
    position: relative;
    min-height: 100vh;
    min-height: 100dvh;
    max-height: 100vh;
    max-height: 100dvh;
    height: 100vh;
    height: 100dvh;
    width: 100vw;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin: 0;
    padding: 0;
}

.hero-section::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
        180deg,
        rgba(13, 27, 26, 0.4) 0%,
        rgba(13, 27, 26, 0.6) 40%,
        rgba(13, 27, 26, 0.8) 100%
    );
    z-index: 2;
}












.hero-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 30;
    text-align: center;
    width: 100%;
    color: var(--color-white);
    max-width: 100vw; /* Éviter le débordement horizontal */
    padding: var(--space-4xl) var(--space-md);
    /* Ensure content is above all overlays and perfectly centered */
    box-sizing: border-box; /* Important pour le padding */
}

.hero-title {
    font-family: var(--font-display);
    font-size: clamp(3rem, 10vw, 8rem);
    font-weight: 500;
    line-height: 1.1;
    color: rgba(255, 255, 255, 0.65);
    text-shadow: 2px 4px 20px rgba(0, 0, 0, 0.7);
    animation: fadeInUp 1s ease-out;
}

/* 🎯 RÈGLE ULTRA-SPÉCIFIQUE POUR FORCER LA TRANSPARENCE + MARCELLUS SC */
.hero-section .hero-content .hero-title,
#home .hero-content .hero-title {
    font-family: 'Marcellus SC', serif !important;
    color: rgba(255, 255, 255, 0.45) !important;
    font-weight: 500 !important;
}

/* ===================================
   Section Layouts
   =================================== */

.section {
    padding: var(--space-4xl) 0;
}

@media (min-width: 1024px) {
    .section {
        padding: 6rem 0;
    }
}



.section-title {
    font-family: var(--font-display);
    font-size: clamp(2rem, 6vw, 4rem);
    font-weight: 700;
    margin-bottom: var(--space-lg);
}

.section-description {
    font-size: clamp(1rem, 2.5vw, 1.25rem);
    color: rgba(252, 252, 252, 0.8);
    max-width: 64rem;
    margin: 0 auto;
    line-height: 1.7;
}

.text-gradient {
    background: linear-gradient(
        135deg,
        var(--color-emerald) 0%,
        var(--color-emerald-light) 25%,
        var(--color-emerald) 50%,
        var(--color-emerald-dark) 75%,
        var(--color-emerald) 100%
    );
    background-size: 200% auto;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    /* Animation gérée par quantum-fixes.css - ne pas dupliquer ici */
}

/* ===================================
   Card Components
   =================================== */

.card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-2xl);
    padding: var(--space-xl);
    transition: all var(--transition-normal);
}

.card:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(0, 87, 75, 0.3);
    transform: translateY(-4px);
    box-shadow: var(--shadow-emerald);
}

.glass-card {
    background: rgba(13, 27, 26, 0.7);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 87, 75, 0.3);
    border-radius: var(--radius-2xl);
}

/* ===================================
   Grid Systems
   =================================== */

.grid-1 { display: grid; grid-template-columns: 1fr; gap: var(--space-xl); }
.grid-2 { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: var(--space-xl); }
.grid-3 { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--space-xl); }

@media (min-width: 768px) {
    .grid-2 { grid-template-columns: repeat(2, 1fr); }
    .grid-3 { grid-template-columns: repeat(3, 1fr); }
}

/* ===================================
   Introduction Section
   =================================== */

.intro-section {
    padding: var(--space-4xl) 0;
    background-color: var(--color-dark);
}

.intro-text {
    font-size: clamp(1rem, 2.5vw, 1.25rem);
    color: rgba(252, 252, 252, 0.9);
    margin-bottom: var(--space-lg);
    line-height: 1.7;
}

.intro-highlight {
    margin-top: var(--space-2xl);
}

.highlight-text {
    font-size: clamp(1.125rem, 3vw, 1.5rem);
    font-weight: 600;
    color: var(--color-emerald-light);
    line-height: 1.6;
}

/* ===================================
   Services Section
   =================================== */

.services-section {
    padding: var(--space-4xl) 0;
    background-color: var(--color-black);
}

.services-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-xl);
}

@media (min-width: 768px) {
    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}

@media (min-width: 1024px) {
    .services-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--space-2xl);
    }
}

.service-card {
    text-align: center;
    opacity: 0;
    transform: translateY(30px);
    animation: revealCard 0.8s ease-out forwards;
}

.service-card:nth-child(2) { animation-delay: 0.1s; }
.service-card:nth-child(3) { animation-delay: 0.2s; }

.service-image-container {
    position: relative;
    margin-bottom: var(--space-lg);
    overflow: hidden;
    border-radius: var(--radius-xl);
    aspect-ratio: 4/3;
}

.service-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.service-card:hover .service-image {
    transform: scale(1.1);
}

.service-image-container::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
        to top,
        rgba(13, 27, 26, 0.8) 0%,
        transparent 100%
    );
}

.service-title {
    font-family: var(--font-display);
    font-size: clamp(1.25rem, 3vw, 1.5rem);
    font-weight: 600;
    color: var(--color-emerald);
    margin-bottom: var(--space-md);
}

.service-description {
    color: rgba(252, 252, 252, 0.8);
    line-height: 1.6;
}
/* ===================================
   Destinations Section
   =================================== */

.destinations-section {
    padding: var(--space-4xl) 0;
    background-color: var(--color-dark);
}

.destinations-intro {
    margin-bottom: var(--space-4xl);
}

.intro-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-2xl);
    align-items: center;
    max-width: 80rem;
    margin: 0 auto;
}

@media (min-width: 1024px) {
    .intro-grid {
        grid-template-columns: 1fr 1fr;
    }
}

.intro-subtitle {
    font-family: var(--font-display);
    font-size: clamp(1.5rem, 4vw, 2rem);
    font-weight: 600;
    color: var(--color-emerald);
    margin-bottom: var(--space-lg);
}

.intro-paragraphs p {
    margin-bottom: var(--space-lg);
    color: rgba(252, 252, 252, 0.9);
    line-height: 1.7;
}

.intro-image-container {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-2xl);
    aspect-ratio: 16/10;
}

.intro-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.intro-image-container:hover .intro-image {
    transform: scale(1.05);
}

/* ===================================
   Golf Courses Grid
   =================================== */

.golf-courses {
    margin-bottom: var(--space-4xl);
}

.courses-header {
    text-align: center;
    margin-bottom: var(--space-2xl);
}

.courses-title {
    font-family: var(--font-display);
    font-size: clamp(1.5rem, 4vw, 2rem);
    font-weight: 600;
    color: var(--color-emerald);
}

.courses-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-xl);
}

@media (min-width: 768px) {
    .courses-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
}

@media (min-width: 1280px) {
    .courses-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.course-card {
    background: rgba(13, 27, 26, 0.7);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 87, 75, 0.3);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    cursor: pointer;
    transition: all var(--transition-normal);
    opacity: 0;
    transform: translateY(30px);
    animation: revealCard 0.8s ease-out forwards;
    position: relative;
}

.course-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
        rgba(0, 87, 75, 0.1) 0%, 
        transparent 50%, 
        rgba(163, 209, 200, 0.05) 100%);
    opacity: 0;
    transition: opacity var(--transition-normal);
    pointer-events: none;
    z-index: 1;
}

.course-card:hover::before {
    opacity: 1;
}

.course-card:focus {
    outline: 3px solid var(--color-emerald);
    outline-offset: 2px;
}

.course-card:hover,
.course-card:focus {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 
        var(--shadow-emerald),
        0 0 0 1px rgba(0, 87, 75, 0.2);
    border-color: rgba(0, 87, 75, 0.6);
}

.course-card:nth-child(1) { animation-delay: 0s; }
.course-card:nth-child(2) { animation-delay: 0.1s; }
.course-card:nth-child(3) { animation-delay: 0.2s; }
.course-card:nth-child(4) { animation-delay: 0.3s; }
.course-card:nth-child(5) { animation-delay: 0.4s; }
.course-card:nth-child(6) { animation-delay: 0.5s; }

.course-image-container {
    position: relative;
    overflow: hidden;
    aspect-ratio: 16/9;
}

.course-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.course-card:hover .course-image {
    transform: scale(1.1);
}

.course-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(
        to top,
        rgba(13, 27, 26, 0.8) 0%,
        transparent 50%
    );
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: var(--space-md);
}

.course-location-badge {
    padding: var(--space-xs) var(--space-md);
    border-radius: var(--radius-3xl);
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--color-white);
}

.badge-teal { background-color: #0d9488; }
.badge-gray { background-color: #6b7280; }
.badge-green { background-color: #059669; }
.badge-orange { background-color: #ea580c; }
.badge-red { background-color: #dc2626; }
.badge-blue { background-color: #2563eb; }

.course-expand-btn {
    background: rgba(13, 27, 26, 0.8);
    border: none;
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-emerald);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.course-expand-btn:hover {
    background: var(--color-emerald);
    color: var(--color-black);
    transform: scale(1.1);
}

.course-content {
    padding: var(--space-lg);
    position: relative;
}

/* BANDEAU "CLIQUER POUR DÉCOUVRIR" DÉSACTIVÉ */
/*
.course-content::after {
    content: '👆 Cliquez pour découvrir';
    position: absolute;
    bottom: var(--space-sm);
    right: var(--space-sm);
    background: var(--color-emerald);
    color: var(--color-black);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-3xl);
    font-size: 0.75rem;
    font-weight: 600;
    opacity: 0;
    transform: translateY(10px);
    transition: all var(--transition-normal);
    pointer-events: none;
    white-space: nowrap;
}

.course-card:hover .course-content::after {
    opacity: 1;
    transform: translateY(0);
}
*/

@media (max-width: 768px) {
    .course-content::after {
        content: '👆 Touchez pour voir plus';
        font-size: 0.7rem;
        padding: 2px var(--space-xs);
    }
}

.course-title {
    font-family: var(--font-display);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--color-emerald);
    margin-bottom: var(--space-sm);
}

.course-description {
    color: rgba(252, 252, 252, 0.8);
    font-size: 0.875rem;
    line-height: 1.6;
    margin-bottom: var(--space-md);
}

.course-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.course-features {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--color-emerald-light);
}

.course-expand-text {
    font-size: 0.75rem;
    color: var(--color-emerald);
}

/* ===================================
   Regional Features
   =================================== */

.regional-features {
    margin-top: var(--space-3xl);
}

.features-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-xl);
}

@media (min-width: 768px) {
    .features-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.feature-item {
    text-align: center;
    opacity: 0;
    transform: translateY(30px);
    animation: revealCard 0.8s ease-out forwards;
}

.feature-item:nth-child(2) { animation-delay: 0.1s; }
.feature-item:nth-child(3) { animation-delay: 0.2s; }

.feature-icon {
    width: 4rem;
    height: 4rem;
    margin: 0 auto var(--space-md);
    background: rgba(0, 87, 75, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-emerald);
    font-size: 1.5rem;
}

.feature-title {
    font-family: var(--font-display);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--color-emerald);
    margin-bottom: var(--space-sm);
}

.feature-description {
    font-size: 0.875rem;
    color: rgba(252, 252, 252, 0.8);
    line-height: 1.6;
}
/* ===================================
   Weather Widget Styles
   =================================== */

.weather-widget-mini {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border-radius: var(--radius-xl);
    padding: var(--space-sm) var(--space-md);
    border: 1px solid rgba(0, 87, 75, 0.2);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.weather-widget-mini:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(0, 87, 75, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 87, 75, 0.2);
}

.weather-widget-mobile {
    background: rgba(0, 87, 75, 0.1);
    backdrop-filter: blur(15px);
    border-radius: var(--radius-xl);
    padding: var(--space-xs) var(--space-md);
    border: 1px solid rgba(0, 87, 75, 0.3);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.weather-widget-mobile:hover {
    background: rgba(0, 87, 75, 0.2);
    border-color: rgba(0, 87, 75, 0.5);
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 87, 75, 0.3);
}

.weather-icon {
    animation: float 3s ease-in-out infinite;
}

.weather-temp {
    font-weight: 600;
    color: var(--color-white);
}

.weather-temp-mobile {
    font-weight: 700;
    color: var(--color-emerald);
    font-size: 0.9rem;
}

.weather-location {
    color: var(--color-emerald-light);
    font-weight: 500;
}

.weather-location-mobile {
    color: var(--color-white);
    font-weight: 400;
    font-size: 0.75rem;
    opacity: 0.9;
}

/* ===================================
   Animations & Keyframes
   =================================== */

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes revealCard {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-3px);
    }
}

/* 🔥 QUANTUM ANIMATIONS NOW IN quantum-fixes.css */
/* This ensures better performance and no conflicts */
        transform: translateY(-3px);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Intersection Observer Animations */
.reveal-animation {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.reveal-animation.visible {
    opacity: 1;
    transform: translateY(0);
}



/* ===================================
   Loading States
   =================================== */

.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* ===================================
   Scrollbar Styles
   =================================== */

::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: var(--color-dark);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, var(--color-emerald), var(--color-emerald-dark));
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, var(--color-emerald-light), var(--color-emerald));
}

/* Firefox */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--color-emerald) var(--color-dark);
}

/* ===================================
   Utility Classes
   =================================== */

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }

.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.mb-0 { margin-bottom: 0; }
.mb-sm { margin-bottom: var(--space-sm); }
.mb-md { margin-bottom: var(--space-md); }
.mb-lg { margin-bottom: var(--space-lg); }
.mb-xl { margin-bottom: var(--space-xl); }

.mt-0 { margin-top: 0; }
.mt-sm { margin-top: var(--space-sm); }
.mt-md { margin-top: var(--space-md); }
.mt-lg { margin-top: var(--space-lg); }
.mt-xl { margin-top: var(--space-xl); }

.p-0 { padding: 0; }
.p-sm { padding: var(--space-sm); }
.p-md { padding: var(--space-md); }
.p-lg { padding: var(--space-lg); }
.p-xl { padding: var(--space-xl); }

/* ===================================
   Responsive Utilities
   =================================== */

.hidden-mobile {
    display: none;
}

@media (min-width: 768px) {
    .hidden-mobile {
        display: block;
    }
    
    .visible-mobile {
        display: none;
    }
}

.hidden-desktop {
    display: block;
}

@media (min-width: 1024px) {
    .hidden-desktop {
        display: none;
    }
    
    .visible-desktop {
        display: block;
    }
}

/* ===================================
   Print Styles
   =================================== */

@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    a, a:visited {
        text-decoration: underline;
    }
    
    .nav-bar,
    .mobile-menu-overlay,
    .carousel-pagination,
    button {
        display: none !important;
    }
    
    .hero-section {
        min-height: auto;
        padding: 2rem 0;
    }
    
    .section {
        page-break-inside: avoid;
        padding: 1rem 0;
    }
}
/* ===================================
   About Section Styles
   =================================== */

.about-section {
    padding: var(--space-4xl) 0;
    background-color: var(--color-black);
}

.about-intro {
    margin-bottom: var(--space-4xl);
}

.intro-paragraph {
    font-size: clamp(1rem, 2.5vw, 1.25rem);
    color: rgba(252, 252, 252, 0.9);
    margin-bottom: var(--space-lg);
    line-height: 1.7;
}

.about-details {
    margin-bottom: var(--space-4xl);
}

.detail-item {
    opacity: 0;
    transform: translateY(30px);
    animation: revealCard 0.8s ease-out forwards;
}

.detail-header {
    margin-bottom: var(--space-lg);
}

.detail-icon {
    width: 3rem;
    height: 3rem;
    background: rgba(0, 87, 75, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-emerald);
    font-size: 1.25rem;
}

.detail-title {
    font-family: var(--font-display);
    font-size: clamp(1.25rem, 3vw, 1.75rem);
    font-weight: 600;
    color: var(--color-emerald);
    margin-bottom: 0;
}

.detail-content {
    color: rgba(252, 252, 252, 0.9);
}

.detail-paragraph {
    margin-bottom: var(--space-lg);
    line-height: 1.7;
}

.about-images {
    margin-bottom: var(--space-4xl);
}

.image-item {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-2xl);
    aspect-ratio: 4/3;
}

.about-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.image-item:hover .about-image {
    transform: scale(1.1);
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(
        to top,
        rgba(13, 27, 26, 0.9) 0%,
        rgba(13, 27, 26, 0.6) 50%,
        transparent 100%
    );
    padding: var(--space-lg);
    color: var(--color-white);
}

.image-title {
    font-family: var(--font-display);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--color-emerald);
    margin-bottom: var(--space-sm);
}

.image-description {
    font-size: 0.875rem;
    color: rgba(252, 252, 252, 0.8);
    margin-bottom: 0;
}

/* Founder Section */
.founder-section {
    margin-top: var(--space-4xl);
}

.founder-image-container {
    position: relative;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: 0 0 0 4px rgba(0, 87, 75, 0.3);
}

.founder-image {
    width: 100%;
    height: 20rem;
    object-fit: cover;
    object-position: center 20%; /* Cadre sur le visage plutôt que le centre */
    transition: transform var(--transition-slow);
}

@media (min-width: 640px) {
    .founder-image {
        height: 26rem; /* Un peu plus grand sur tablette */
        object-position: center 15%; /* Ajustement pour tablette */
    }
}

@media (min-width: 1024px) {
    .founder-image {
        height: 32rem; /* Plus grand cadre sur desktop (était 28rem) */
        object-position: center 20%; /* Cadrage légèrement ajusté */
    }
    
    /* Style de secours pour desktop */
    .founder-image-container.center-face .founder-image {
        height: 36rem; /* Encore plus grand avec la classe de secours (était 30rem) */
        object-position: center 15%; /* Garde un bon cadrage du visage */
    }
}

/* Écrans très larges (1280px+) */
@media (min-width: 1280px) {
    .founder-image {
        height: 38rem; /* Cadre encore plus grand pour les gros écrans (était 32rem) */
        object-position: center 18%; /* Cadrage optimal */
    }
    
    .founder-image-container.center-face .founder-image {
        height: 42rem; /* Maximum pour voir plus du personnage (était 34rem) */
        object-position: center 12%; /* Garde le visage centré */
    }
}

/* Alternative: Portrait mode pour Sébastien */
.founder-image.portrait-mode {
    object-position: center 30%; /* Pour les photos portrait où le visage est en haut */
    object-fit: cover;
}

.founder-image.landscape-mode {
    object-position: center center; /* Pour les photos paysage */
    object-fit: cover;
}

/* Style de secours si l'image ne s'affiche pas bien */
.founder-image-container.center-face .founder-image {
    object-position: center 10%; /* Remonte encore plus vers le visage */
    height: 28rem; /* Augmenté de 24rem à 28rem */
}

@media (max-width: 639px) {
    .founder-image-container.center-face .founder-image {
        height: 22rem; /* Plus grand sur mobile pour mieux voir */
        object-position: center 15%;
    }
}
    transform: scale(1.05);
}

.founder-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(
        to top,
        rgba(13, 27, 26, 0.9) 0%,
        rgba(13, 27, 26, 0.4) 80%,
        transparent 100%
    );
    padding: var(--space-md);
}

.founder-name {
    font-family: var(--font-display);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--color-white);
    margin-bottom: var(--space-xs);
}

.founder-title {
    color: var(--color-emerald-light);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: var(--space-sm);
}

.founder-rating {
    display: flex;
    justify-content: center;
    gap: var(--space-xs);
    color: var(--color-emerald);
    font-size: 0.75rem;
}

.founder-description {
    font-size: clamp(1rem, 2.5vw, 1.125rem);
    color: rgba(252, 252, 252, 0.9);
    line-height: 1.7;
}

.highlight-name {
    color: var(--color-emerald);
    font-weight: 600;
}

.founder-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-md);
    margin-top: var(--space-lg);
}

@media (min-width: 640px) {
    .founder-stats {
        grid-template-columns: repeat(3, 1fr);
    }
}

.stat-item {
    text-align: center;
    background: rgba(0, 87, 75, 0.1);
    border-radius: var(--radius-xl);
    padding: var(--space-md);
}

.stat-value {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-emerald);
    margin-bottom: var(--space-xs);
}

.stat-label {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: rgba(252, 252, 252, 0.8);
    margin-bottom: 0;
}

/* ===================================
   Testimonials Section Styles
   =================================== */

.testimonials-section {
    padding: var(--space-4xl) 0;
    background-color: var(--color-dark);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-xl);
}

@media (min-width: 768px) {
    .testimonials-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
}

@media (min-width: 1280px) {
    .testimonials-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.testimonial-card {
    background: rgba(13, 27, 26, 0.7);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 87, 75, 0.3);
    border-radius: var(--radius-2xl);
    padding: var(--space-xl);
    transition: all var(--transition-normal);
    opacity: 0;
    transform: translateY(30px);
    animation: revealCard 0.8s ease-out forwards;
}

.testimonial-card:nth-child(2) { animation-delay: 0.1s; }
.testimonial-card:nth-child(3) { animation-delay: 0.2s; }
.testimonial-card:nth-child(4) { animation-delay: 0.3s; }
.testimonial-card:nth-child(5) { animation-delay: 0.4s; }

.testimonial-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-emerald);
    border-color: rgba(0, 87, 75, 0.5);
}

.testimonial-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--space-lg);
}

.testimonial-avatar {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--color-emerald), var(--color-emerald-dark));
    color: var(--color-white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.25rem;
    margin-right: var(--space-md);
}

.testimonial-info {
    flex: 1;
}

.testimonial-name {
    font-family: var(--font-display);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--color-emerald);
    margin-bottom: var(--space-xs);
}

.testimonial-rating {
    display: flex;
    gap: var(--space-xs);
    color: var(--color-emerald);
    font-size: 0.875rem;
    margin-bottom: var(--space-xs);
}

.testimonial-date {
    font-size: 0.875rem;
    color: rgba(252, 252, 252, 0.6);
    margin-bottom: 0;
}

.testimonial-content {
    border: none;
    margin: 0;
}

.testimonial-text {
    color: rgba(252, 252, 252, 0.9);
    line-height: 1.7;
}

.testimonial-text.italic {
    font-style: italic;
    color: rgba(252, 252, 252, 0.8);
}

/* Testimonial CTA */
.testimonial-cta {
    background: rgba(0, 87, 75, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 87, 75, 0.3);
    border-radius: var(--radius-2xl);
    padding: var(--space-xl);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    transition: all var(--transition-normal);
    opacity: 0;
    transform: translateY(30px);
    animation: revealCard 0.8s ease-out forwards;
    animation-delay: 0.5s;
}

.testimonial-cta:hover {
    background: rgba(0, 87, 75, 0.15);
    border-color: rgba(0, 87, 75, 0.5);
    transform: translateY(-4px);
}

.cta-content {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.cta-icon {
    width: 4rem;
    height: 4rem;
    background: rgba(0, 87, 75, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-emerald);
    font-size: 1.5rem;
    margin-bottom: var(--space-lg);
}

.cta-title {
    font-family: var(--font-display);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--color-emerald);
    margin-bottom: var(--space-md);
}

.cta-description {
    color: rgba(252, 252, 252, 0.8);
    line-height: 1.6;
    margin-bottom: var(--space-lg);
}

/* Testimonials Stats */
.testimonials-stats {
    margin-top: var(--space-4xl);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-xl);
}

@media (min-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}
/* ===================================
   CTA Section Styles
   =================================== */

.cta-section {
    padding: var(--space-4xl) 0;
    background-color: var(--color-black);
}

.cta-description {
    font-size: clamp(1rem, 2.5vw, 1.25rem);
    color: rgba(252, 252, 252, 0.7);
    max-width: 48rem;
    margin: 0 auto var(--space-2xl);
    line-height: 1.7;
}

/* ===================================
   Contact Section Styles
   =================================== */

.contact-section {
    padding: var(--space-4xl) 0;
    background-color: var(--color-dark);
}

.contact-content {
    margin-top: var(--space-2xl);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: var(--space-xl);
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: var(--space-md);
}

.contact-icon {
    width: 3rem;
    height: 3rem;
    background: rgba(0, 87, 75, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-emerald);
    font-size: 1.125rem;
    flex-shrink: 0;
}

.contact-details {
    flex: 1;
}

.contact-label {
    font-family: var(--font-display);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--color-white);
    margin-bottom: var(--space-sm);
}

.contact-link {
    color: rgba(252, 252, 252, 0.8);
    font-size: 1.125rem;
    text-decoration: none;
    transition: color var(--transition-normal);
}

.contact-link:hover,
.contact-link:focus {
    color: var(--color-emerald);
    text-decoration: underline;
}

/* ===================================
   Form Styles
   =================================== */

.contact-form-container {
    opacity: 0;
    transform: translateY(30px);
    animation: revealCard 0.8s ease-out forwards;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.form-group-row {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-lg);
}

@media (min-width: 640px) {
    .form-group-row {
        grid-template-columns: repeat(2, 1fr);
    }
}

.form-group {
    position: relative;
}

.form-input {
    width: 100%;
    padding: var(--space-md) var(--space-lg);
    background: rgba(13, 27, 26, 0.5);
    border: 1px solid rgba(0, 87, 75, 0.2);
    border-radius: var(--radius-xl);
    color: var(--color-white);
    font-family: var(--font-sans);
    font-size: 1rem;
    transition: all var(--transition-normal);
    outline: none;
}

.form-input::placeholder {
    color: rgba(252, 252, 252, 0.5);
}

.form-input:focus {
    border-color: var(--color-emerald);
    background: rgba(13, 27, 26, 0.7);
    box-shadow: 0 0 0 3px rgba(0, 87, 75, 0.1);
}

.form-input:invalid:not(:placeholder-shown) {
    border-color: #dc2626;
}

.form-input:valid:not(:placeholder-shown) {
    border-color: var(--color-emerald);
}

.form-textarea {
    resize: vertical;
    min-height: 8rem;
    font-family: var(--font-sans);
}

.field-error {
    color: #dc2626;
    font-size: 0.875rem;
    margin-top: var(--space-xs);
    display: block;
}

/* ===================================
   Footer Styles
   =================================== */

.footer-section {
    padding: var(--space-2xl) 0;
    background-color: var(--color-dark);
    border-top: 1px solid rgba(0, 87, 75, 0.2);
}

.footer-content {
    text-align: center;
}

.footer-title {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-emerald);
    margin-bottom: var(--space-md);
}

.footer-description {
    color: rgba(252, 252, 252, 0.7);
    margin-bottom: var(--space-lg);
    line-height: 1.6;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.social-link {
    width: 3rem;
    height: 3rem;
    background: rgba(0, 87, 75, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-emerald);
    font-size: 1.25rem;
    text-decoration: none;
    transition: all var(--transition-normal);
}

.social-link:hover,
.social-link:focus {
    background: var(--color-emerald);
    color: var(--color-black);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.footer-copyright {
    color: rgba(252, 252, 252, 0.5);
    font-size: 0.875rem;
    margin-bottom: 0;
}

/* ===================================
   WhatsApp Floating Button
   =================================== */

.whatsapp-float {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 4rem;
    height: 4rem;
    background: #25d366;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-white);
    font-size: 1.5rem;
    text-decoration: none;
    box-shadow: var(--shadow-xl);
    transition: all var(--transition-normal);
    z-index: var(--z-fixed);
    animation: bounce 2s infinite;
}

.whatsapp-float:hover,
.whatsapp-float:focus {
    background: #128c7e;
    color: var(--color-white);
    transform: scale(1.1);
    animation: none;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* ===================================
   Course Modal - Luxury Mobile-First Design
   =================================== */

.course-modal {
    position: fixed;
    inset: 0;
    z-index: var(--z-modal);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-sm);
    opacity: 0;
    visibility: hidden;
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    backdrop-filter: blur(0px);
    /* Mobile-first touch optimization */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
}

.course-modal[aria-hidden="false"] {
    opacity: 1;
    visibility: visible;
    backdrop-filter: blur(25px);
}

.modal-backdrop {
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, 
        rgba(13, 27, 26, 0.97) 0%, 
        rgba(0, 87, 75, 0.88) 25%,
        rgba(0, 61, 52, 0.92) 50%,
        rgba(0, 87, 75, 0.88) 75%, 
        rgba(13, 27, 26, 0.97) 100%);
    backdrop-filter: blur(25px);
    animation: backdropFadeIn 0.5s ease-out;
}

@keyframes backdropFadeIn {
    from { 
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to { 
        opacity: 1;
        backdrop-filter: blur(25px);
    }
}

.modal-content {
    position: relative;
    background: linear-gradient(145deg, 
        rgba(26, 44, 42, 0.98) 0%, 
        rgba(13, 27, 26, 0.99) 50%,
        rgba(26, 44, 42, 0.98) 100%);
    border-radius: var(--radius-2xl);
    max-width: 95vw;
    width: 100%;
    max-height: 95vh;
    overflow: hidden;
    border: 2px solid rgba(163, 209, 200, 0.3);
    box-shadow: 
        0 32px 64px -12px rgba(0, 0, 0, 0.6),
        0 0 0 1px rgba(163, 209, 200, 0.15),
        inset 0 1px 0 rgba(163, 209, 200, 0.15),
        0 0 50px rgba(0, 87, 75, 0.2);
    transform: scale(0.85) translateY(50px);
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    /* Mobile-first optimizations */
    touch-action: pan-y;
    overscroll-behavior: contain;
    display: flex;
    flex-direction: column;
}

.course-modal[aria-hidden="false"] .modal-content {
    transform: scale(1) translateY(0);
}

/* Improved mobile responsiveness - Mobile First */
@media (max-width: 639px) {
    .course-modal {
        padding: var(--space-xs);
        align-items: flex-start;
        padding-top: 2vh;
    }
    
    .modal-content {
        max-width: 98vw;
        max-height: 96vh;
        border-radius: var(--radius-xl);
        margin-top: auto;
        margin-bottom: auto;
    }
}

@media (min-width: 640px) {
    .course-modal {
        padding: var(--space-md);
    }
    
    .modal-content {
        max-width: 90vw;
        max-height: 90vh;
    }
}

@media (min-width: 768px) {
    .modal-content {
        max-width: 80vw;
        max-height: 85vh;
    }
}

@media (min-width: 1024px) {
    .modal-content {
        max-width: 70vw;
        max-height: 85vh;
    }
}

@media (min-width: 1280px) {
    .modal-content {
        max-width: 64rem;
        max-height: 80vh;
    }
}

.modal-close {
    position: absolute;
    top: var(--space-md);
    right: var(--space-md);
    z-index: 20;
    width: 3.5rem;
    height: 3.5rem;
    background: linear-gradient(135deg, 
        rgba(13, 27, 26, 0.95) 0%, 
        rgba(0, 87, 75, 0.3) 100%);
    border: 2px solid rgba(163, 209, 200, 0.4);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-emerald-light);
    cursor: pointer;
    font-size: 1.4rem;
    transition: all var(--transition-normal);
    backdrop-filter: blur(15px);
    box-shadow: 
        0 8px 25px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(163, 209, 200, 0.1);
    /* Mobile-first touch optimization */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

/* Mobile-first sizing */
@media (max-width: 639px) {
    .modal-close {
        top: var(--space-sm);
        right: var(--space-sm);
        width: 3rem;
        height: 3rem;
        font-size: 1.2rem;
    }
}

.modal-close:hover,
.modal-close:focus,
.modal-close:active {
    background: linear-gradient(135deg, 
        var(--color-emerald) 0%, 
        var(--color-emerald-light) 100%);
    color: var(--color-black);
    transform: scale(1.15) rotate(90deg);
    border-color: var(--color-emerald-light);
    box-shadow: 
        0 12px 35px rgba(0, 87, 75, 0.5),
        0 0 0 4px rgba(163, 209, 200, 0.3),
        0 0 50px rgba(0, 87, 75, 0.3);
    outline: none;
}

.modal-header {
    position: relative;
    height: 40vh;
    min-height: 220px;
    max-height: 320px;
    overflow: hidden;
    flex-shrink: 0;
}

@media (max-width: 639px) {
    .modal-header {
        height: 35vh;
        min-height: 200px;
        max-height: 280px;
    }
}

@media (min-width: 640px) {
    .modal-header {
        height: 38vh;
        max-height: 350px;
    }
}

@media (min-width: 1024px) {
    .modal-header {
        height: 35vh;
        max-height: 400px;
    }
}

/* DEBUG: Modal Image Visibility */
.modal-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s ease;
}

.modal-header:hover .modal-image {
    transform: scale(1.05);
}

.modal-header::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
        to top,
        rgba(13, 27, 26, 0.9) 0%,
        rgba(13, 27, 26, 0.4) 40%,
        transparent 70%
    );
    pointer-events: none;
}

.modal-info {
    position: absolute;
    bottom: var(--space-lg);
    left: var(--space-lg);
    right: var(--space-lg);
    z-index: 2;
}

@media (max-width: 639px) {
    .modal-info {
        bottom: var(--space-md);
        left: var(--space-md);
        right: var(--space-md);
    }
}
}

.modal-location {
    background: linear-gradient(135deg, var(--color-emerald) 0%, var(--color-emerald-light) 100%);
    color: var(--color-black);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-3xl);
    font-size: 0.875rem;
    font-weight: 700;
    margin-bottom: var(--space-sm);
    display: inline-block;
    box-shadow: 0 4px 15px rgba(0, 87, 75, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.modal-title {
    font-family: var(--font-display);
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--color-white);
    margin-bottom: var(--space-sm);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    line-height: 1.2;
}

@media (min-width: 640px) {
    .modal-title {
        font-size: 2rem;
    }
}

@media (min-width: 1024px) {
    .modal-title {
        font-size: 2.25rem;
    }
}

.modal-subtitle {
    color: var(--color-emerald-light);
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 0;
    opacity: 0.9;
}

@media (min-width: 640px) {
    .modal-subtitle {
        font-size: 1.125rem;
    }
}

.modal-body {
    padding: var(--space-lg);
    max-height: 50vh;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--color-emerald) transparent;
}

.modal-body::-webkit-scrollbar {
    width: 6px;
}

.modal-body::-webkit-scrollbar-track {
    background: rgba(0, 87, 75, 0.1);
    border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb {
    background: var(--color-emerald);
    border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: var(--color-emerald-light);
}

@media (min-width: 640px) {
    .modal-body {
        padding: var(--space-xl);
    }
}

/* Course Details in Modal */
.course-details {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.course-features-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-lg);
}

@media (min-width: 768px) {
    .course-features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

.feature-group {
    background: rgba(0, 87, 75, 0.1);
    border: 1px solid rgba(0, 87, 75, 0.2);
    border-radius: var(--radius-xl);
    padding: var(--space-lg);
    transition: all var(--transition-normal);
}

.feature-group:hover {
    background: rgba(0, 87, 75, 0.15);
    border-color: rgba(0, 87, 75, 0.3);
    transform: translateY(-2px);
}

.feature-group h4 {
    font-family: var(--font-display);
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color-emerald);
    margin-bottom: var(--space-md);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

@media (min-width: 640px) {
    .feature-group h4 {
        font-size: 1.25rem;
    }
}

.feature-group h4::before {
    content: '';
    width: 4px;
    height: 4px;
    background: var(--color-emerald);
    border-radius: 50%;
}

.feature-group ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.feature-group li {
    display: flex;
    align-items: center;
    color: rgba(252, 252, 252, 0.9);
    gap: var(--space-sm);
    font-size: 0.9rem;
    line-height: 1.5;
}

.feature-group li i {
    color: var(--color-emerald);
    width: 1rem;
    text-align: center;
    flex-shrink: 0;
}

.course-description-extended {
    background: linear-gradient(135deg, 
        rgba(0, 87, 75, 0.15) 0%, 
        rgba(0, 87, 75, 0.05) 100%);
    border: 1px solid rgba(0, 87, 75, 0.2);
    border-radius: var(--radius-xl);
    padding: var(--space-xl);
    margin-top: var(--space-lg);
    position: relative;
    overflow: hidden;
}

.course-description-extended::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, 
        var(--color-emerald) 0%, 
        var(--color-emerald-light) 50%, 
        var(--color-emerald) 100%);
}

.course-description-extended h4 {
    font-family: var(--font-display);
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color-emerald);
    margin-bottom: var(--space-md);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

@media (min-width: 640px) {
    .course-description-extended h4 {
        font-size: 1.25rem;
    }
}

.course-description-extended h4 i {
    font-size: 1rem;
}

.course-description-extended p {
    color: rgba(252, 252, 252, 0.95);
    line-height: 1.7;
    margin-bottom: 0;
    font-size: 0.95rem;
}

@media (min-width: 640px) {
    .course-description-extended p {
        font-size: 1rem;
    }
}
    color: rgba(252, 252, 252, 0.9);
    line-height: 1.7;
    margin-bottom: 0;
}

/* ===================================
   Notification System
   =================================== */

.notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: var(--color-dark);
    border: 1px solid rgba(0, 87, 75, 0.3);
    border-radius: var(--radius-xl);
    padding: var(--space-md) var(--space-lg);
    display: flex;
    align-items: center;
    gap: var(--space-md);
    z-index: var(--z-tooltip);
    box-shadow: var(--shadow-xl);
    max-width: 24rem;
    animation: slideInRight 0.3s ease-out;
}

.notification-success {
    border-color: rgba(34, 197, 94, 0.3);
}

.notification-error {
    border-color: rgba(239, 68, 68, 0.3);
}

.notification-close {
    background: none;
    border: none;
    color: rgba(252, 252, 252, 0.7);
    cursor: pointer;
    padding: var(--space-xs);
    border-radius: var(--radius-sm);
    transition: color var(--transition-normal);
}

.notification-close:hover {
    color: var(--color-white);
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===================================
   Keyboard Navigation Enhancements
   =================================== */

.keyboard-navigation *:focus {
    outline: 2px solid var(--color-emerald) !important;
    outline-offset: 2px !important;
}

/* Focus styles for better accessibility */
.course-card:focus {
    outline: 2px solid var(--color-emerald);
    outline-offset: 2px;
}

.course-card[tabindex="0"] {
    cursor: pointer;
}

/* ===================================
   Performance & Loading States
   =================================== */

.loading-state {
    opacity: 0.7;
    pointer-events: none;
    position: relative;
}

.loading-state::after {
    content: '';
    position: absolute;
    inset: 0;
    background: rgba(13, 27, 26, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: inherit;
}

/* Image lazy loading placeholder */
img[loading="lazy"] {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

img[loading="lazy"]:not([src]) {
    opacity: 0.7;
}

/* ===================================
   High Contrast Mode Support
   =================================== */

@media (prefers-contrast: high) {
    :root {
        --color-emerald: #00ff9f;
        --color-emerald-light: #66ffcc;
        --color-white: #ffffff;
        --color-black: #000000;
    }
    
    .nav-bar,
    .card,
    .glass-card {
        border-width: 2px;
        border-color: var(--color-emerald);
    }
    
    .btn-primary {
        background: var(--color-emerald);
        color: var(--color-black);
        border: 2px solid var(--color-emerald);
    }
}

/* ===================================
   Reduced Motion Support
   =================================== */

@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    

    
    .reveal-animation,
    .course-card,
    .testimonial-card {
        opacity: 1;
        transform: none;
    }
}

/* ===================================
   Swiper Pagination - SECTION SUPPRIMÉE
   =================================== */

/* ANCIEN CODE PAGINATION SUPPRIMÉ POUR ÉVITER LES CONFLITS
   Les styles du carrousel sont maintenant optimisés dans critical.css
   Cette section était obsolète et causait des conflits avec :
   - Styles Swiper non utilisés
   - Règles !important inutiles 
   - Tailles fixes non responsives
*/

/* ===================================
   Footer SIRET Styling
   =================================== */

.footer-siret {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 0.5rem;
    text-align: center;
}
