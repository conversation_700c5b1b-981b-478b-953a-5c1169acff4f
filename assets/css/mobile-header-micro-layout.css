/* =============================================
   🎯 MOBILE HEADER MICRO-LAYOUT SURGERY
   Fix temperature display + language switcher spacing
   QUANTUM DEVELOPER LEVEL - Ultra Precise
   ============================================= */

/* 🌡️ WEATHER WIDGET - MORE SPACE FOR TEMPERATURE */
@media (max-width: 1023px) {
    .weather-widget-mobile {
        flex: 0 0 auto !important;
        min-width: 65px !important; /* Increased from 45px */
        max-width: 70px !important; /* Increased from 50px */
        text-align: center !important;
        z-index: 100 !important;
        position: relative !important;
        overflow: visible !important; /* Changed from hidden to show full temp */
        margin: 0 0.25rem !important;
        padding: 0.1rem !important;
    }
    
    /* Weather widget text - Better sizing for full temperature */
    .weather-temp-mobile {
        font-size: 0.75rem !important; /* Slightly larger for readability */
        line-height: 1.1 !important;
        white-space: nowrap !important;
        overflow: visible !important; /* Show full temperature */
        display: block !important;
        font-weight: 600 !important; /* Bold for better visibility */
        color: #A3D1C8 !important;
    }
    
    .weather-location-mobile {
        font-size: 0.65rem !important;
        line-height: 1 !important;
        white-space: nowrap !important;
        overflow: visible !important;
        display: block !important;
        color: #A3D1C8 !important;
        opacity: 0.8 !important;
    }
}

/* 🌍 LANGUAGE SWITCHER MOBILE - SHIFT RIGHT + COMPACT */
@media (max-width: 1023px) {
    .language-switcher-mobile {
        flex: 0 0 auto !important;
        min-width: 50px !important; /* Reduced from default */
        max-width: 55px !important; /* Compact size */
        margin-left: 0.5rem !important; /* Push more to the right */
        margin-right: 0.25rem !important; /* Small gap before hamburger */
        z-index: 200 !important;
        position: relative !important;
    }
    
    /* Language button - Ultra compact */
    .language-switcher-mobile .lang-btn-mobile {
        min-width: 50px !important;
        max-width: 50px !important;
        height: 32px !important; /* Slightly smaller height */
        padding: 4px 8px !important; /* Compact padding */
        font-size: 0.8rem !important;
        gap: 0.15rem !important; /* Smaller gap between flag and text */
        border-radius: 6px !important; /* Smaller border radius */
    }
    
    /* Language dropdown - Positioned properly */
    .language-switcher-mobile .lang-dropdown-mobile {
        min-width: 100px !important;
        right: 0 !important; /* Align to right edge */
        left: auto !important;
    }
}

/* 🍔 HAMBURGER MENU - KEEP PERFECT POSITION */
@media (max-width: 1023px) {
    #mobile-menu-btn {
        flex: 0 0 auto !important;
        min-width: 48px !important;
        max-width: 48px !important;
        margin: 0 !important; /* No margin - stays in place */
        padding: 12px !important;
        z-index: 300 !important; /* Highest priority */
        position: relative !important;
    }
}

/* 🎯 MOBILE CONTROLS CONTAINER - OPTIMIZED SPACING */
@media (max-width: 1023px) {
    .lg\\:hidden.flex.items-center.space-x-3 {
        flex: 0 0 auto !important;
        min-width: 110px !important; /* Adjusted for new spacing */
        max-width: 115px !important;
        gap: 0.25rem !important; /* Tight gap */
        z-index: 200 !important;
        position: relative !important;
        align-items: center !important;
        justify-content: flex-end !important; /* Align to right */
    }
}

/* 🎯 LOGO CONTAINER - MAINTAIN PERFECT SIZE */
@media (max-width: 1023px) {
    .flex.items-center.space-x-3:first-child {
        flex: 0 0 auto !important;
        min-width: 130px !important; /* Slightly reduced to give more space */
        max-width: 135px !important;
        z-index: 1000 !important;
        position: relative !important;
    }
}

/* 📱 HEADER CONTAINER - OPTIMIZED DISTRIBUTION */
@media (max-width: 1023px) {
    .nav-bar .container .flex.justify-between {
        gap: 0.25rem !important; /* Tight gaps */
        align-items: center !important;
        justify-content: space-between !important;
        padding: 0.5rem 0 !important;
        /* Perfect space distribution: Logo(135px) + Weather(70px) + Controls(115px) = 320px */
    }
}

/* 🔥 VERY SMALL SCREENS - EMERGENCY COMPACT */
@media (max-width: 380px) {
    .weather-widget-mobile {
        min-width: 60px !important;
        max-width: 65px !important;
        font-size: 0.7rem !important;
    }
    
    .language-switcher-mobile {
        min-width: 45px !important;
        max-width: 45px !important;
    }
    
    .language-switcher-mobile .lang-btn-mobile {
        min-width: 45px !important;
        padding: 3px 6px !important;
        font-size: 0.75rem !important;
    }
    
    .flex.items-center.space-x-3:first-child {
        min-width: 120px !important;
        max-width: 120px !important;
    }
}

/* 🧪 TEMPERATURE DISPLAY ENHANCEMENT */
.weather-temp-mobile {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important; /* Better readability */
    letter-spacing: 0.5px !important; /* Slightly spaced for clarity */
}

/* 🎨 VISUAL POLISH - Subtle improvements */
.weather-widget-mobile {
    background: rgba(163, 209, 200, 0.05) !important; /* Very subtle background */
    border-radius: 6px !important;
    transition: all 0.2s ease !important;
}

.weather-widget-mobile:hover {
    background: rgba(163, 209, 200, 0.1) !important;
    transform: translateY(-1px) !important;
}
