/* =============================================
   🧹 SMART TEXT CLEANUP - NO MORE CHAOS
   Remove all "lire la suite" buttons and show full text
   ============================================= */

/* 🚫 HIDE ALL READ MORE BUTTONS */
.read-more-btn,
.read-less-btn,
.smart-read-more,
.smart-read-less,
[class*="read-more"],
[class*="read-less"] {
    display: none !important;
    visibility: hidden !important;
}

/* 📖 SHOW ALL TEXT CONTENT - NO TRUNCATION */
.text-truncated,
.smart-truncated,
[data-smart-managed] {
    max-height: none !important;
    height: auto !important;
    overflow: visible !important;
    text-overflow: unset !important;
    white-space: normal !important;
    -webkit-line-clamp: unset !important;
    line-clamp: unset !important;
}

/* 🔧 REMOVE TRUNCATION CLASSES */
.truncated,
.collapsed,
.smart-collapsed {
    max-height: none !important;
    overflow: visible !important;
}

/* 🧹 CLEANUP SMART TEXT ATTRIBUTES */
[data-smart-managed] {
    /* Reset all smart text management */
}

/* 📱 MOBILE TEXT - FULL VISIBILITY */
@media (max-width: 768px) {
    p, .text-content, .description, .intro-text {
        display: block !important;
        max-height: none !important;
        overflow: visible !important;
    }
}

/* 🎯 FORCE SHOW ALL PARAGRAPHS */
p:not(:first-child),
.hidden-paragraph,
.truncated-content {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    max-height: none !important;
}

/* 🧹 REMOVE FADE EFFECTS */
.fade-out,
.text-fade,
.gradient-mask {
    background: none !important;
    mask: none !important;
    -webkit-mask: none !important;
}

/* ✅ ENSURE ALL TEXT IS READABLE */
.intro-section p,
.about-section p,
.service-description,
.testimonial-text,
.feature-description {
    display: block !important;
    max-height: none !important;
    overflow: visible !important;
    text-overflow: unset !important;
    white-space: normal !important;
}
