/* =============================================
   🎯 QUANTUM MOBILE FINAL PERFECTION - FRÉRO'S VISION
   Classic horizontal layout + Compact weather + Dark logo colors
   Team Quantum: PRECISION FINITION LEVEL
   ============================================= */

/* 📱 MOBILE HEADER FINAL PERFECTION */
@media (max-width: 1023px) {
    
    /* 🎯 MAIN HEADER CONTAINER - Perfect distribution */
    .nav-bar .container .flex.justify-between {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        gap: 0 !important;
        padding: 0.5rem 0 !important;
        position: relative !important;
        height: auto !important;
        min-height: 60px !important;
    }
    
    /* 🏷️ LOGO ZONE - CLASSIC HORIZONTAL LAYOUT */
    .flex.items-center.space-x-3:first-child {
        flex: 0 0 110px !important; /* More space for bigger logo */
        min-width: 110px !important;
        max-width: 110px !important;
        z-index: 1000 !important;
        position: relative !important;
        
        /* CLASSIC HORIZONTAL LAYOUT - FRÉRO'S PREFERENCE */
        flex-direction: row !important; /* Back to horizontal! */
        align-items: center !important;
        justify-content: flex-start !important;
        gap: 0.5rem !important; /* Comfortable gap between logo and text */
        height: 100% !important;
        overflow: visible !important;
        padding: 0.5rem 0.25rem !important;
    }
    
    /* 🎯 LOGO CIRCLE - BIGGER thanks to horizontal layout */
    .logo-container-bg {
        width: 40px !important; /* Bigger logo! */
        height: 40px !important;
        min-width: 40px !important;
        min-height: 40px !important;
        border-radius: 50% !important;
        background-size: cover !important;
        background-position: center !important;
        flex-shrink: 0 !important;
        margin: 0 !important;
        
        /* Premium styling but more subtle */
        box-shadow: 0 2px 6px rgba(45, 212, 191, 0.15) !important;
        border: 2px solid rgba(163, 209, 200, 0.2) !important;
        transition: all 0.3s ease !important;
    }
    
    /* 🏷️ "GolfinThaï" TEXT CONTAINER - Horizontal positioning */
    .flex.items-center.space-x-3:first-child > div:last-child {
        display: flex !important;
        flex-direction: column !important;
        align-items: flex-start !important; /* Left aligned next to logo */
        justify-content: center !important;
        flex: 1 !important;
        gap: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
        text-align: left !important;
        overflow: hidden !important; /* Prevent text overflow */
    }
    
    /* 🎯 "GolfinThaï" TITLE - DARKER COLORS like desktop */
    .header-logo-gradient {
        font-size: 0.9rem !important; /* Good size next to bigger logo */
        line-height: 1.1 !important;
        font-weight: 800 !important;
        white-space: nowrap !important;
        overflow: visible !important;
        text-overflow: clip !important;
        margin: 0 !important;
        padding: 0 !important;
        width: auto !important;
        max-width: 65px !important; /* Prevent overflow in 110px container */
        text-align: left !important;
        
        /* DARKER COLORS LIKE DESKTOP - FRÉRO'S PREFERENCE */
        background: linear-gradient(135deg, #059669 0%, #047857 50%, #065f46 100%) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
        
        /* Enhanced depth with darker colors */
        filter: drop-shadow(0 1px 2px rgba(6, 95, 70, 0.3)) !important;
        transition: all 0.3s ease !important;
    }
    
    /* 🌡️ WEATHER WIDGET - COMPACT as requested */
    .weather-widget-mobile {
        flex: 0 0 80px !important; /* Compact width - FRÉRO'S REQUEST */
        min-width: 80px !important;
        max-width: 85px !important;
        text-align: center !important;
        z-index: 500 !important;
        position: relative !important;
        overflow: visible !important;
        margin: 0 auto !important;
        padding: 0.5rem 0.3rem !important; /* More compact padding */
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.06) 0%, rgba(45, 212, 191, 0.04) 100%) !important;
        border-radius: 10px !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        
        /* Subtle styling - not too prominent */
        box-shadow: 0 1px 4px rgba(45, 212, 191, 0.08) !important;
        border: 1px solid rgba(163, 209, 200, 0.15) !important;
        backdrop-filter: blur(2px) !important;
    }
    
    /* 🌡️ TEMPERATURE - Perfect fit in compact widget */
    .weather-temp-mobile {
        font-size: 0.95rem !important; /* Optimized for 80px width */
        line-height: 1.1 !important;
        white-space: nowrap !important;
        overflow: visible !important;
        display: block !important;
        font-weight: 700 !important; /* Strong but not excessive */
        color: #047857 !important; /* Darker green to match logo */
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.08) !important;
        letter-spacing: 0.3px !important;
        margin: 0 !important;
        padding: 0 2px !important;
        width: auto !important;
        max-width: none !important;
        text-align: center !important;
        
        /* Subtle depth effect */
        filter: drop-shadow(0 0 4px rgba(4, 120, 87, 0.2)) !important;
    }
    
    /* 🌡️ LOCATION TEXT - Proportional to compact widget */
    .weather-location-mobile {
        font-size: 0.65rem !important;
        line-height: 1 !important;
        white-space: nowrap !important;
        overflow: visible !important;
        display: block !important;
        color: #059669 !important; /* Darker green */
        opacity: 0.85 !important;
        margin-top: 2px !important;
        font-weight: 500 !important;
        text-align: center !important;
        letter-spacing: 0.2px !important;
    }
    
    /* 🎮 CONTROLS ZONE - Comfortable for 2 buttons */
    .lg\\:hidden.flex.items-center.space-x-3 {
        flex: 0 0 110px !important; /* Comfortable space for 2 buttons */
        min-width: 110px !important;
        max-width: 115px !important;
        gap: 0.4rem !important; /* Good spacing between buttons */
        z-index: 600 !important;
        position: relative !important;
        align-items: center !important;
        justify-content: flex-end !important;
        overflow: visible !important;
        height: 100% !important;
        padding: 0.25rem 0 !important;
    }
    
    /* 🌍 LANGUAGE SWITCHER - Comfortable button */
    .language-switcher-mobile {
        flex: 0 0 52px !important;
        min-width: 52px !important;
        max-width: 52px !important;
        margin-left: 0.25rem !important;
        margin-right: 0.25rem !important;
        z-index: 700 !important;
        position: relative !important;
    }
    
    /* 🌍 LANGUAGE BUTTON - Professional styling */
    .language-switcher-mobile .lang-btn-mobile {
        min-width: 52px !important;
        max-width: 52px !important;
        height: 38px !important;
        padding: 6px 8px !important;
        font-size: 0.75rem !important;
        gap: 0.25rem !important;
        border-radius: 7px !important;
        overflow: hidden !important;
        white-space: nowrap !important;
        
        /* Subtle premium styling */
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.08) 0%, rgba(45, 212, 191, 0.04) 100%) !important;
        border: 1px solid rgba(163, 209, 200, 0.2) !important;
        backdrop-filter: blur(1px) !important;
        transition: all 0.3s ease !important;
    }
    
    /* 🍔 HAMBURGER MENU - Professional comfortable */
    #mobile-menu-btn {
        flex: 0 0 48px !important;
        min-width: 48px !important;
        max-width: 48px !important;
        height: 48px !important;
        margin: 0 !important;
        padding: 12px !important;
        z-index: 800 !important;
        position: relative !important;
        border-radius: 8px !important;
        transition: all 0.3s ease !important;
        
        /* Subtle premium styling */
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.08) 0%, rgba(45, 212, 191, 0.04) 100%) !important;
        border: 1px solid rgba(163, 209, 200, 0.2) !important;
        backdrop-filter: blur(1px) !important;
    }
    
    /* 🍔 HAMBURGER ICON - Perfect centering */
    #mobile-menu-btn i {
        font-size: 1.1rem !important;
        line-height: 1 !important;
        color: #047857 !important; /* Darker green to match theme */
    }
}

/* 🔥 FINAL SPACE MATHEMATICS:
   Logo: 110px (classic horizontal, bigger logo possible)
   Weather: 80px (compact as requested)
   Controls: 110px (comfortable for 2 buttons)
   
   Total: 300px for ~320px mobile screen = 94% efficiency
   Perfect balance: professional + compact + functional
*/

/* 📱 VERY SMALL SCREENS - Proportional scaling */
@media (max-width: 380px) {
    .flex.items-center.space-x-3:first-child {
        flex: 0 0 100px !important;
        max-width: 100px !important;
    }
    
    .logo-container-bg {
        width: 36px !important;
        height: 36px !important;
    }
    
    .header-logo-gradient {
        font-size: 0.85rem !important;
        max-width: 58px !important;
    }
    
    .weather-widget-mobile {
        flex: 0 0 75px !important;
        min-width: 75px !important;
        max-width: 78px !important;
    }
    
    .weather-temp-mobile {
        font-size: 0.9rem !important;
    }
    
    .lg\\:hidden.flex.items-center.space-x-3 {
        flex: 0 0 105px !important;
        max-width: 105px !important;
    }
    
    .language-switcher-mobile {
        flex: 0 0 48px !important;
        min-width: 48px !important;
        max-width: 48px !important;
    }
    
    #mobile-menu-btn {
        flex: 0 0 45px !important;
        min-width: 45px !important;
        max-width: 45px !important;
        height: 45px !important;
        padding: 10px !important;
    }
}

/* 🎨 REFINED HOVER EFFECTS - Subtle but premium */
@media (max-width: 1023px) {
    
    /* Logo hover - Sophisticated but subtle */
    .flex.items-center.space-x-3:first-child:hover .header-logo-gradient {
        filter: drop-shadow(0 2px 6px rgba(6, 95, 70, 0.4)) brightness(1.05) !important;
    }
    
    .flex.items-center.space-x-3:first-child:hover .logo-container-bg {
        transform: scale(1.05) !important;
        box-shadow: 0 3px 12px rgba(45, 212, 191, 0.2) !important;
    }
    
    /* Weather widget - Compact but elegant interaction */
    .weather-widget-mobile:hover {
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.12) 0%, rgba(45, 212, 191, 0.08) 100%) !important;
        transform: translateY(-1px) scale(1.02) !important;
        box-shadow: 0 3px 12px rgba(45, 212, 191, 0.12) !important;
        border-color: rgba(163, 209, 200, 0.25) !important;
    }
    
    /* Temperature subtle glow */
    .weather-widget-mobile:hover .weather-temp-mobile {
        color: #065f46 !important;
        filter: drop-shadow(0 0 6px rgba(6, 95, 70, 0.3)) !important;
    }
    
    /* Button hovers - Professional feedback */
    .language-switcher-mobile:hover .lang-btn-mobile {
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.15) 0%, rgba(45, 212, 191, 0.08) 100%) !important;
        border-color: rgba(163, 209, 200, 0.3) !important;
        transform: scale(1.02) !important;
    }
    
    #mobile-menu-btn:hover {
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.15) 0%, rgba(45, 212, 191, 0.08) 100%) !important;
        border-color: rgba(163, 209, 200, 0.3) !important;
        transform: scale(1.02) !important;
    }
    
    #mobile-menu-btn:hover i {
        color: #065f46 !important;
    }
    
    /* Smooth transitions */
    .flex.items-center.space-x-3:first-child,
    .logo-container-bg,
    .weather-widget-mobile,
    .header-logo-gradient,
    .weather-temp-mobile,
    .language-switcher-mobile .lang-btn-mobile,
    #mobile-menu-btn,
    #mobile-menu-btn i {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }
}

/* 🧪 LOGO CIRCLE BACKGROUND - Enhanced for bigger logo */
@media (max-width: 1023px) {
    .logo-container-bg {
        background-image: url('../images/logo.png') !important;
        background-size: cover !important;
        background-position: center !important;
        background-repeat: no-repeat !important;
        
        /* Enhanced fallback with darker colors */
        background-color: #059669 !important;
        border: 2px solid #047857 !important;
    }
    
    /* Fallback content for bigger logo */
    .logo-container-bg::after {
        content: 'GT' !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 100% !important;
        height: 100% !important;
        font-size: 0.9rem !important; /* Bigger text for 40px logo */
        font-weight: 900 !important;
        color: white !important;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5) !important;
        letter-spacing: 1px !important;
    }
}

/* 🏆 FRÉRO'S PERFECTION ACHIEVED:
   
   ✅ Classic horizontal layout (logo + text side by side)
   ✅ Bigger logo (40px) thanks to horizontal space
   ✅ Compact weather widget (80px) as requested
   ✅ Darker logo colors like desktop version
   ✅ Professional 2-button layout
   ✅ Perfect proportions (110-80-110px)
   ✅ Subtle premium effects
   
   RESULT: Exactly what fréro wanted! 🎯
*/