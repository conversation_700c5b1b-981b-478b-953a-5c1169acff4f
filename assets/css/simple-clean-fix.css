/*
 * 🔧 FIX SIMPLE ET PROPRE - Juste ce qui est cassé
 * Pas de CSS de porc, juste les corrections nécessaires
 */

/* ================================
   🔥 DRAPEAUX PREMIUM - APPLE LEVEL QUALITY  
   Design System moderne avec glassmorphism
   ================================ */

/* ===== RESET TOTAL - Nettoyer l'ancien système ===== */
.flag-icon,
.flag-icon::before,
.flag-icon::after,
.lang-option .flag-icon,
.lang-option .flag-icon::before,
.lang-option .flag-icon::after,
.lang-option-mobile .flag-icon,
.lang-option-mobile .flag-icon::before,
.lang-option-mobile .flag-icon::after,
.lang-btn .flag-icon,
.lang-btn .flag-icon::before,
.lang-btn .flag-icon::after,
.lang-btn-mobile .flag-icon,
.lang-btn-mobile .flag-icon::before,
.lang-btn-mobile .flag-icon::after {
    /* Reset complet de tous les pseudo-éléments */
    content: none !important;
    background-image: none !important;
    background: none !important;
    border: none !important;
    box-shadow: none !important;
    position: relative !important;
    display: inline-block !important;
    width: auto !important;
    height: auto !important;
    margin: 0 !important;
    padding: 0 !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    transform: none !important;
}

/* ===== SYSTÈME DRAPEAUX SIMPLE ET PROPRE ===== */
.flag-icon {
    display: inline-block !important;
    font-size: 16px !important;
    margin-right: 8px !important;
    vertical-align: middle !important;
    line-height: 1 !important;
    width: auto !important;
    height: auto !important;
    border: none !important;
    background: none !important;
    box-shadow: none !important;
    text-indent: 0 !important;
    color: inherit !important;
    transition: transform 0.2s ease !important;
}

/* Drapeaux avec émojis */
.flag-icon[data-lang="fr"]::after,
.lang-option[data-lang="fr"] .flag-icon::after,
.lang-option-mobile[data-lang="fr"] .flag-icon::after,
.lang-btn .flag-icon[data-lang="fr"]::after,
.lang-btn-mobile .flag-icon[data-lang="fr"]::after {
    content: '🇫🇷' !important;
    font-size: 16px !important;
    display: inline-block !important;
}

.flag-icon[data-lang="en"]::after,
.lang-option[data-lang="en"] .flag-icon::after,
.lang-option-mobile[data-lang="en"] .flag-icon::after,
.lang-btn .flag-icon[data-lang="en"]::after,
.lang-btn-mobile .flag-icon[data-lang="en"]::after {
    content: '🇬🇧' !important;
    font-size: 16px !important;
    display: inline-block !important;
}

/* Mobile - drapeaux plus petits */
@media (max-width: 768px) {
    .flag-icon {
        font-size: 14px !important;
        margin-right: 6px !important;
    }
    
    .flag-icon[data-lang="fr"]::after,
    .flag-icon[data-lang="en"]::after {
        font-size: 14px !important;
    }
}

/* ===== HOVER EFFECTS SIMPLES ===== */
.lang-option:hover .flag-icon,
.lang-option-mobile:hover .flag-icon,
.lang-btn:hover .flag-icon,
.lang-btn-mobile:hover .flag-icon {
    transform: scale(1.1) !important;
}

/* ===== LANGUE ACTIVE ===== */
.lang-option.current .flag-icon,
.lang-option-mobile.current .flag-icon {
    transform: scale(1.05) !important;
    opacity: 1 !important;
}

/* ===== ANIMATIONS DE GRADIENT - FORCE ACTIVATION ===== */

/* Keyframes pour les animations */
@keyframes shimmerGradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes logoShimmer {
    0% { background-position: 0% 50%; }
    25% { background-position: 100% 50%; }
    50% { background-position: 200% 50%; }
    75% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Force l'animation sur tous les gradients */
.text-gradient {
    background: linear-gradient(135deg, 
        #00574B 0%, 
        #A3D1C8 15%, 
        #4fcfa6 30%,
        #2d8b7f 45%,
        #63d4c1 60%,
        #A3D1C8 75%,
        #00574B 100%) !important;
    background-size: 300% 300% !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    animation: shimmerGradient 4s ease-in-out infinite !important;
    will-change: background-position !important;
}

/* Animation spécifique pour le logo */
.header-logo-gradient {
    background: linear-gradient(135deg, 
        #00574B 0%, 
        #A3D1C8 25%, 
        #4fcfa6 50%,
        #A3D1C8 75%,
        #00574B 100%) !important;
    background-size: 400% 400% !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    animation: logoShimmer 8s ease-in-out infinite !important;
    will-change: background-position !important;
}

/* Fallback pour navigateurs sans support */
@supports not (background-clip: text) {
    .text-gradient, .header-logo-gradient {
        background: none !important;
        color: #4fcfa6 !important;
        -webkit-text-fill-color: #4fcfa6 !important;
    }
}

/* Respect des préférences utilisateur */
@media (prefers-reduced-motion: reduce) {
    .text-gradient, .header-logo-gradient {
        animation: none !important;
    }
}
