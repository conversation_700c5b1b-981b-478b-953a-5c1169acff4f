/* =============================================
   🚨 MOBILE LOGO NUCLEAR FIX - FORCE IMAGE DISPLAY
   Override ALL mobile CSS - Force logo image on mobile
   ============================================= */

/* 🔥 MOBILE ONLY - NUCLEAR OVERRIDE */
@media (max-width: 1023px) {
    .logo-container-bg {
        /* NUCLEAR IMAGE FORCE */
        background-image: url('../images/logo-golfinthai.jpg') !important;
        background-size: cover !important;
        background-position: center !important;
        background-repeat: no-repeat !important;
        
        /* REMOVE GREEN BACKGROUND IMMEDIATELY */
        background-color: transparent !important;
        
        /* FORCE DIMENSIONS */
        width: 44px !important;
        height: 44px !important;
        min-width: 44px !important;
        min-height: 44px !important;
        max-width: 44px !important;
        max-height: 44px !important;
        
        /* PERFECT CIRCLE */
        border-radius: 50% !important;
        
        /* DISPLAY PROPERTIES */
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        
        /* POSITIONING */
        position: relative !important;
        z-index: 1001 !important;
        flex-shrink: 0 !important;
        
        /* SPACING */
        margin: 0 !important;
        padding: 0 !important;
        
        /* STYLING */
        border: 2px solid rgba(6, 95, 70, 0.3) !important;
        box-shadow: 0 2px 8px rgba(45, 212, 191, 0.15) !important;
        
        /* NO PSEUDO ELEMENTS */
        content: none !important;
    }
    
    /* KILL ALL PSEUDO ELEMENTS */
    .logo-container-bg::before,
    .logo-container-bg::after {
        display: none !important;
        content: none !important;
        background: none !important;
    }
    
    /* FORCE OVERRIDE ANY OTHER MOBILE CSS */
    .logo-container-bg {
        background: url('../images/logo-golfinthai.jpg') center center / cover no-repeat !important;
    }
}

/* 🎯 DESKTOP SAFETY - Don't break desktop */
@media (min-width: 1024px) {
    .logo-container-bg {
        background-image: url('../images/logo-golfinthai.jpg') !important;
        background-size: cover !important;
        background-position: center !important;
        background-repeat: no-repeat !important;
        width: 44px !important;
        height: 44px !important;
        border-radius: 50% !important;
        background-color: transparent !important;
    }
}

/* 🚨 CRITICAL: Ensure image loads on mobile */
@media (max-width: 1023px) {
    .logo-container-bg {
        /* Multiple path attempts for mobile */
        background-image: 
            url('../images/logo-golfinthai.jpg'),
            url('./assets/images/logo-golfinthai.jpg'),
            url('assets/images/logo-golfinthai.jpg') !important;
    }
}

/* 🔥 MOBILE LOGO NUCLEAR SUCCESS:
   ✅ GREEN background REMOVED
   ✅ Image paths MULTIPLE attempts
   ✅ ALL properties FORCED with !important
   ✅ Pseudo elements KILLED
   ✅ Desktop PROTECTED
   
   RESULT: Logo image MUST appear on mobile! 🎯
*/