/* 🚨 URGENT FIX - Service Images Not Clickable */
/* Ensure service section images are fully clickable */

.service-image {
    pointer-events: auto !important;
    cursor: pointer !important;
    z-index: 10 !important;
    position: relative !important;
}

.service-image-container {
    pointer-events: auto !important;
    position: relative !important;
}

.service-card {
    pointer-events: auto !important;
}

/* Force remove any overlays on service cards */
.service-card::before,
.service-card::after {
    pointer-events: none !important;
}

.service-image-container::before,
.service-image-container::after {
    pointer-events: none !important;
}

/* Ensure no CSS is blocking clicks */
.services-grid .service-image {
    pointer-events: auto !important;
    cursor: pointer !important;
    position: relative !important;
    z-index: 100 !important;
}

/* Debug helper - add visual indication */
.service-image:hover {
    opacity: 0.8 !important;
    transform: scale(1.02) !important;
    transition: all 0.2s ease !important;
}

/* Ensure service content doesn't block image */
.service-content {
    pointer-events: auto !important;
    z-index: 1 !important;
}

/* 📝 Debug Notes */
/*
IMAGES QUI DOIVENT ÊTRE CLIQUABLES:
1. acceuil1.jpg - onclick configuré ✅
2. acceuil2.jpg - onclick configuré ✅  
3. RedMountainGolfClubPhuket.jpeg - onclick configuré ✅

Si ce CSS ne résout pas le problème, c'est un cache navigateur.
Solution: Cmd + Shift + R dans Safari
*/