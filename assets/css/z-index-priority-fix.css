/* =============================================
   🎯 Z-INDEX PRIORITY FIX - LOGO ALWAYS ON TOP
   Fix weather widget BKK overlapping logo
   ============================================= */

/* 🎯 Z-INDEX HIERARCHY CONTROL */

/* 🏷️ LOGO - HIGHEST PRIORITY (1000) */
.logo-container-bg,
.flex.items-center.space-x-3:first-child {
    z-index: 1000 !important;
    position: relative !important;
}

/* 🌡️ WEATHER WIDGET - LOWER PRIORITY (100) */
.weather-widget-mobile,
.weather-widget-mini,
#weather-display,
#weather-display-mobile {
    z-index: 100 !important;
    position: relative !important;
}

/* 🌍 LANGUAGE SWITCHER - MEDIUM PRIORITY (200) */
.language-switcher-mobile,
.language-switcher-desktop {
    z-index: 200 !important;
    position: relative !important;
}

/* 📱 MOBILE HEADER OPTIMIZATION - NO OVERLAP */
@media (max-width: 1023px) {
    /* Header container with better spacing */
    .nav-bar .container .flex.justify-between {
        gap: 0.5rem !important;
        align-items: center !important;
        justify-content: space-between !important;
        padding: 0.5rem 0 !important;
    }
    
    /* Logo section - Fixed width to prevent squeeze */
    .flex.items-center.space-x-3:first-child {
        flex: 0 0 auto !important;
        min-width: 140px !important;
        max-width: 140px !important;
        z-index: 1000 !important;
        position: relative !important;
    }
    
    /* Weather widget - Constrained size */
    .weather-widget-mobile {
        flex: 0 0 auto !important;
        max-width: 50px !important;
        min-width: 45px !important;
        text-align: center !important;
        z-index: 100 !important;
        position: relative !important;
        overflow: hidden !important;
        margin: 0 !important;
    }
    
    /* Weather widget text - Compact display */
    .weather-temp-mobile {
        font-size: 0.7rem !important;
        line-height: 1 !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        display: block !important;
    }
    
    .weather-location-mobile {
        font-size: 0.65rem !important;
        line-height: 1 !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        display: block !important;
        color: #A3D1C8 !important;
    }
    
    /* Mobile controls - Fixed width */
    .lg\\:hidden.flex.items-center.space-x-3 {
        flex: 0 0 auto !important;
        min-width: 100px !important;
        gap: 0.25rem !important;
        z-index: 200 !important;
        position: relative !important;
    }
}

/* 🎯 FORCE NO OVERLAP - Nuclear option */
.weather-widget-mobile * {
    max-width: 50px !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}

/* 🚨 EMERGENCY FIX - Hide weather widget if it causes problems */
.weather-overlap-fix .weather-widget-mobile {
    display: none !important;
}

/* 📱 VERY SMALL SCREENS - Ultra compact */
@media (max-width: 380px) {
    .weather-widget-mobile {
        max-width: 40px !important;
        font-size: 0.6rem !important;
    }
    
    .weather-temp-mobile,
    .weather-location-mobile {
        font-size: 0.6rem !important;
    }
    
    .flex.items-center.space-x-3:first-child {
        min-width: 120px !important;
        max-width: 120px !important;
    }
}

/* 🔧 DEBUG HELPER - Add visual borders to check spacing */
.debug-spacing .logo-container-bg {
    border: 2px solid red !important;
}

.debug-spacing .weather-widget-mobile {
    border: 2px solid blue !important;
    background: rgba(0, 0, 255, 0.1) !important;
}

.debug-spacing .lg\\:hidden.flex.items-center.space-x-3 {
    border: 2px solid green !important;
    background: rgba(0, 255, 0, 0.1) !important;
}
