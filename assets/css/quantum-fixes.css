/* ===================================
   🔥 GOLFINTHAI CLEAN FIXES - EMERGENCY VERSION
   WORKING GRADIENTS + NO PARTICULES
   =================================== */

/* ✅ ANIMATIONS KEYFRAMES - FIXED */
@keyframes shimmer {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes headerLogoShimmer {
    0% { background-position: 0% 50%; }
    25% { background-position: 100% 50%; }
    50% { background-position: 200% 50%; }
    75% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* ✅ TEXT GRADIENTS - WORKING VERSION */
.text-gradient {
    background: linear-gradient(135deg, 
        #00574B 0%, 
        #A3D1C8 15%, 
        #4fcfa6 30%,
        #2d8b7f 45%,
        #63d4c1 60%,
        #A3D1C8 75%,
        #00574B 100%) !important;
    background-size: 300% 300% !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    animation: shimmer 4s ease-in-out infinite !important;
    will-change: background-position !important;
}

/* ✅ HEADER LOGO - SLOW ANIMATION */
.header-logo-gradient {
    background: linear-gradient(135deg, 
        #00574B 0%, 
        #A3D1C8 25%, 
        #4fcfa6 50%,
        #A3D1C8 75%,
        #00574B 100%) !important;
    background-size: 400% 400% !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    animation: headerLogoShimmer 8s ease-in-out infinite !important;
    will-change: background-position !important;
}

/* ✅ MOBILE TYPOGRAPHY FIXES */
.hero-title {
    font-size: clamp(2.2rem, 8vw, 8rem) !important;
    max-width: 95vw !important;
    overflow-wrap: break-word !important;
}

.hero-content {
    max-width: 100vw !important;
    padding: 2rem 1rem !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
}

@media (max-width: 480px) {
    .hero-title {
        font-size: clamp(1.8rem, 7vw, 3.5rem) !important;
        max-width: 90vw !important;
    }
}

/* ✅ SMART TEXT MANAGER STYLES */
.smart-text-container {
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
}

.smart-text-container.truncated {
    max-height: 4.5em;
    overflow: hidden;
}

.smart-text-container.truncated::after {
    content: '';
    position: absolute;
    bottom: 0; left: 0; right: 0; height: 1.5em;
    background: linear-gradient(transparent, #0D1B1A);
    pointer-events: none;
}

.smart-read-more {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.75rem;
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, #00574B, #A3D1C8);
    color: white;
    border: none;
    border-radius: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 44px;
}

.smart-read-more:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 87, 75, 0.3);
}

@media (max-width: 768px) {
    .smart-text-container.truncated {
        max-height: 3.5em;
    }
    .smart-read-more {
        width: 100%;
        justify-content: center;
    }
}

/* ✅ FALLBACKS */
@supports not (background-clip: text) {
    .text-gradient, .header-logo-gradient {
        background: none !important;
        color: #4fcfa6 !important;
        -webkit-text-fill-color: #4fcfa6 !important;
    }
}

/* ✅ ACCESSIBILITY */
@media (prefers-reduced-motion: reduce) {
    .text-gradient, .header-logo-gradient {
        animation: none !important;
    }
}/* 📱 MOBILE MENU BUTTON - PERFECT CENTER */
#mobile-menu-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    min-height: 44px;
    padding: 0.75rem;
    text-align: center;
    vertical-align: middle;
    margin: 0;
    line-height: 1;
}

/* 🇫🇷 LANGUAGE DROPDOWN - HIDE BACKGROUND FLAGS */
.lang-dropdown-mobile,
.lang-dropdown {
    background: rgba(13, 27, 26, 0.95) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(0, 87, 75, 0.3) !important;
    overflow: hidden !important;
}

.lang-option-mobile,
.lang-option {
    background: transparent !important;
    border: none !important;
    transition: background 0.2s ease !important;
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
}

.lang-option-mobile:hover,
.lang-option:hover {
    background: rgba(0, 87, 75, 0.2) !important;
}

/* Fix positionnement drapeaux dans dropdown */
.lang-dropdown .flag-icon,
.lang-dropdown-mobile .flag-icon {
    position: relative !important;
    display: inline-block !important;
    margin-right: 8px !important;
    vertical-align: middle !important;
    top: 0 !important;
    left: 0 !important;
    transform: none !important;
}