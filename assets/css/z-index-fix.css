/* ===============================================
   Z-INDEX FIX - CLEAN VERSION 
   Résout les problèmes de superpositions SANS !important
   =============================================== */

/* ===== VARIABLES Z-INDEX PROPRES ===== */
:root {
    --z-base: 1;
    --z-content: 10;
    --z-overlay: 100;
    --z-navbar: 1020;
    --z-dropdown: 1000;
    --z-modal-backdrop: 10000;
    --z-modal: 10001;
    --z-tooltip: 10002;
}

/* ===== SECTIONS PRINCIPALES ===== */
.hero-section,
.intro-section,
.services-section,
.destinations-section,
.about-section,
.testimonials-section,
.contact-section {
    position: relative;
    z-index: var(--z-content);
    /* FORCE VISIBLE */
    opacity: 1 !important;
    visibility: visible !important;
    /* S'assurer que le contenu reste en dessous du header */
    max-width: 100vw;
    overflow-x: hidden;
}

/* ===== CONTENU VISIBLE ===== */
.hero-content,
.service-content,
.course-content,
.about-content,
.testimonial-content,
.intro-content,
.contact-content {
    position: relative;
    z-index: var(--z-content);
    opacity: 1 !important;
    visibility: visible !important;
}

/* Textes visibles - FORCE TOUT */
h1, h2, h3, h4, h5, h6, p, span, div,
.section-title, .section-description,
.service-title, .service-description,
.course-title, .course-description {
    position: relative;
    z-index: var(--z-content);
    opacity: 1 !important;
    visibility: visible !important;
    color: inherit !important;
}

/* ===== CARDS ET CONTENEURS ===== */
.course-card,
.service-card,
.testimonial-card,
.glass-card,
.card {
    position: relative;
    z-index: var(--z-content);
    opacity: 1 !important;
    visibility: visible !important;
}

/* Images et conteneurs d'images */
.service-image-container,
.course-image-container,
.intro-image-container,
.founder-image-container {
    position: relative;
    z-index: var(--z-base);
    opacity: 1 !important;
    visibility: visible !important;
}

/* ===== MODAL ET OVERLAYS ===== */
.course-modal {
    z-index: 9999;
    position: fixed;
}

.modal-backdrop {
    z-index: 9998;
}

.modal-content {
    z-index: 9999;
    position: relative;
}

/* ===== NAVIGATION ===== */
.nav-bar {
    z-index: 1020;
}

.mobile-menu-overlay {
    z-index: 1019;
}

/* ===== DROPDOWNS ===== */
.lang-dropdown,
.lang-dropdown-mobile {
    z-index: var(--z-dropdown);
}

/* ===== IMAGES SPÉCIFIQUES ===== */
.service-image-container,
.course-image-container {
    position: relative;
    z-index: var(--z-base);
}

/* ===== CAROUSEL ===== */
.hero-carousel {
    z-index: var(--z-base);
}

.hero-carousel .carousel-slide {
    z-index: var(--z-base);
}

.carousel-pagination {
    z-index: var(--z-content);
}
