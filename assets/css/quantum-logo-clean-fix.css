/* =============================================
   🎯 QUANTUM LOGO FIX - REMOVE UGLY "GT" FALLBACK
   Keep perfect layout, remove fallback text
   Team Quantum: Logo Perfection
   ============================================= */

/* 📱 MOBILE LOGO CIRCLE - CLEAN WITHOUT FALLBACK */
@media (max-width: 1023px) {
    .logo-container-bg {
        background-image: url('../images/logo.png') !important;
        background-size: cover !important;
        background-position: center !important;
        background-repeat: no-repeat !important;
        
        /* Clean background colors without ugly text */
        background-color: #059669 !important;
        border: 2px solid #047857 !important;
        
        /* Keep all the perfect sizing from right-side grouping */
        width: 44px !important;
        height: 44px !important;
        min-width: 44px !important;
        min-height: 44px !important;
        border-radius: 50% !important;
        flex-shrink: 0 !important;
        margin: 0 !important;
        z-index: 1001 !important;
        position: relative !important;
        
        /* Premium styling */
        box-shadow: 0 2px 8px rgba(45, 212, 191, 0.15) !important;
        transition: all 0.3s ease !important;
    }
    
    /* 🚫 REMOVE UGLY "GT" FALLBACK COMPLETELY */
    .logo-container-bg::after {
        content: '' !important; /* NO MORE "GT"! */
        display: none !important; /* Completely hidden */
    }
    
    /* 🎯 Alternative: If logo image doesn't load, show clean circle */
    .logo-container-bg::before {
        content: '' !important;
        display: block !important;
        width: 100% !important;
        height: 100% !important;
        border-radius: 50% !important;
        /* Beautiful gradient fallback instead of ugly text */
        background: linear-gradient(135deg, #059669 0%, #047857 50%, #065f46 100%) !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        z-index: -1 !important; /* Behind the main image */
    }
}

/* 🖼️ ENSURE LOGO IMAGE LOADS PROPERLY */
@media (max-width: 1023px) {
    .logo-container-bg {
        /* Try multiple potential logo paths */
        background-image: url('../images/logo.png'), 
                         url('./assets/images/logo.png'),
                         url('assets/images/logo.png') !important;
        
        /* If no image loads, show beautiful gradient circle */
        background: linear-gradient(135deg, #059669 0%, #047857 50%, #065f46 100%) !important;
    }
}

/* 🎨 ENHANCED LOGO CIRCLE STYLING - NO TEXT FALLBACK */
@media (max-width: 1023px) {
    .logo-container-bg {
        /* Professional styling without any text */
        border: 2px solid rgba(6, 95, 70, 0.3) !important;
        box-shadow: 
            0 2px 8px rgba(45, 212, 191, 0.15),
            inset 0 1px 2px rgba(255, 255, 255, 0.1) !important;
        
        /* Subtle inner glow instead of text */
        background: 
            radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 60%),
            linear-gradient(135deg, #059669 0%, #047857 50%, #065f46 100%) !important;
        
        /* Perfect transitions */
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }
    
    /* Enhanced hover effect for clean logo */
    .flex.items-center.space-x-3:first-child:hover .logo-container-bg {
        transform: scale(1.08) !important;
        box-shadow: 
            0 4px 16px rgba(45, 212, 191, 0.25),
            inset 0 1px 3px rgba(255, 255, 255, 0.15) !important;
        border-color: rgba(6, 95, 70, 0.5) !important;
    }
}

/* 💎 LOGO PERFECTION SUMMARY:
   ✅ Removed ugly "GT" fallback text completely
   ✅ Beautiful gradient circle if image doesn't load
   ✅ Multiple image path attempts for better loading
   ✅ Professional styling with subtle effects
   ✅ Maintains perfect 44px size from right-side grouping
   ✅ Keeps all the perfect spacing and positioning
   
   RESULT: Beautiful clean logo without any ugly text! 🎯
*/