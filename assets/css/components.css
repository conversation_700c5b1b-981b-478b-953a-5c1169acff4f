/*
 * Components CSS pour GolfinThaï
 * Styles des composants spécifiques
 */

/* ===== NAVIGATION ===== */
/* ✅ CONSOLIDÉ : .nav-bar migre vers critical.css pour first paint */

.nav-link {
    color: var(--color-emerald-light);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--color-emerald);
}

/* ===== BOUTONS ===== */
.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
}

/* .btn-primary défini dans main.css - éviter duplication */

/* ===== CARTES ===== */
.glass-card {
    background: rgba(26, 44, 42, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(163, 209, 200, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.service-card, .course-card, .testimonial-card {
    background: rgba(26, 44, 42, 0.9);
    border-radius: 1rem;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid rgba(163, 209, 200, 0.1);
}

.service-card:hover, .course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 87, 75, 0.2);
}

/* ===== IMAGES ===== */
.service-image, .course-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-card:hover .service-image,
.course-card:hover .course-image {
    transform: scale(1.05);
}

/* ===== FORMULAIRES ===== */
.form-input {
    width: 100%;
    padding: 1rem;
    border: 1px solid rgba(163, 209, 200, 0.3);
    border-radius: 0.5rem;
    background: rgba(26, 44, 42, 0.5);
    color: var(--color-white);
    font-size: 1rem;
    transition: border-color 0.3s ease, background-color 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--color-emerald);
    background: rgba(26, 44, 42, 0.8);
}

/* ===== CARROUSEL HERO ===== */
/* Section nettoyée - Styles migrent vers .carousel-slide dans critical.css */
