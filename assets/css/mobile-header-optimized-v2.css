/* =============================================
   🎯 HEADER MOBILE ULTRA-COMPACT - FRÈRE'S PERFECT VERSION
   Language switcher PLUS PETIT + décalé à droite
   MAXIMUM d'espace pour "GolfinThaï" logo
   ============================================= */

/* 📱 MOBILE HEADER - NOUVELLE DISTRIBUTION OPTIMISÉE */
@media (max-width: 1023px) {
    
    /* 🎯 MAIN HEADER CONTAINER - Perfect distribution */
    .nav-bar .container .flex.justify-between {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        gap: 0 !important;
        padding: 0.4rem 0 !important;
        position: relative !important;
        height: auto !important;
        min-height: 58px !important;
    }
    
    /* 🏷️ LOGO ZONE - ESPACE MAXIMUM ÉLARGI ENCORE PLUS */
    .flex.items-center.space-x-3:first-child {
        flex: 0 0 155px !important; /* +15px d'espace supplémentaire ! */
        min-width: 155px !important;
        max-width: 155px !important;
        z-index: 1000 !important;
        position: relative !important;
        
        /* LAYOUT OPTIMISÉ POUR PLUS D'ESPACE */
        display: flex !important;
        flex-direction: row !important;
        align-items: center !important;
        justify-content: flex-start !important;
        gap: 0.7rem !important; /* Gap généreux grâce à l'espace supplémentaire */
        height: 100% !important;
        overflow: visible !important;
        padding: 0.4rem 0.5rem !important;
        
        /* VISIBILITÉ MAXIMALE */
        background: transparent !important;
        border: none !important;
    }
    
    /* 🎯 LOGO CIRCLE - ENCORE PLUS GRAND */
    .logo-wrapper {
        flex: 0 0 46px !important; /* Logo encore plus grand ! */
        width: 46px !important;
        height: 46px !important;
        min-width: 46px !important;
        min-height: 46px !important;
        border-radius: 50% !important;
        overflow: hidden !important;
        flex-shrink: 0 !important;
        margin: 0 !important;
        z-index: 1001 !important;
        position: relative !important;
        
        /* Premium styling amélioré */
        box-shadow: 0 3px 12px rgba(45, 212, 191, 0.18) !important;
        border: 2px solid rgba(163, 209, 200, 0.3) !important;
        transition: all 0.3s ease !important;
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.1) 0%, rgba(45, 212, 191, 0.05) 100%) !important;
    }
    
    /* 🖼️ LOGO IMAGE - PERFECT SIZING */
    .logo-wrapper .logo-image {
        width: 100% !important;
        height: 100% !important;
        object-fit: cover !important;
        border-radius: 50% !important;
        z-index: 1002 !important;
        position: relative !important;
    }
    
    /* 🏷️ "GolfinThaï" TEXT CONTAINER - ESPACE ÉNORME */
    .flex.items-center.space-x-3:first-child > div:last-child {
        display: block !important;
        flex: 1 !important;
        z-index: 1002 !important;
        position: relative !important;
        
        /* ESPACE ÉNORME POUR LE TEXTE */
        overflow: visible !important;
        margin: 0 !important;
        padding: 0 !important;
        width: auto !important;
        max-width: 100px !important; /* ÉNORME espace grâce aux 155px ! */
        height: auto !important;
        
        /* Visibilité parfaite */
        background: transparent !important;
        border: none !important;
        text-align: left !important;
    }
    
    /* 🎯 "GolfinThaï" TITLE - TAILLE MAXIMALE */
    .header-logo-gradient {
        font-size: 1.1rem !important; /* Encore plus grand ! */
        line-height: 1.1 !important;
        font-weight: 800 !important;
        white-space: nowrap !important;
        overflow: visible !important;
        text-overflow: clip !important;
        margin: 0 !important;
        padding: 0 !important;
        width: auto !important;
        max-width: 95px !important; /* Utilise tout l'espace énorme */
        text-align: left !important;
        z-index: 1003 !important;
        position: relative !important;
        
        /* COULEURS SOMBRES COMME DEMANDÉ */
        background: linear-gradient(135deg, #065f46 0%, #047857 70%, #059669 100%) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
        
        /* Visibilité renforcée */
        filter: drop-shadow(0 1px 4px rgba(6, 95, 70, 0.35)) !important;
        transition: all 0.3s ease !important;
        
        /* FORCE DISPLAY */
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
    
    /* 🌡️ WEATHER WIDGET - GARDÉ COMPACT À 50PX */
    .weather-widget-mobile {
        flex: 0 0 48px !important; /* Légèrement réduit pour plus d'espace logo */
        min-width: 48px !important;
        max-width: 50px !important;
        text-align: center !important;
        z-index: 500 !important;
        position: relative !important;
        overflow: visible !important;
        
        /* POSITIONNEMENT TRÈS À DROITE */
        margin: 0 0.15rem 0 auto !important; /* Poussé encore plus à droite */
        padding: 0.25rem 0.05rem !important; /* Padding minimal */
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.04) 0%, rgba(45, 212, 191, 0.02) 100%) !important;
        border-radius: 5px !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        
        /* Styling minimal pour compacité */
        box-shadow: 0 1px 3px rgba(45, 212, 191, 0.05) !important;
        border: 1px solid rgba(163, 209, 200, 0.08) !important;
        backdrop-filter: blur(1px) !important;
    }
    
    /* 🌡️ TEMPERATURE - OPTIMISÉ POUR 48PX */
    .weather-temp-mobile {
        font-size: 0.72rem !important; /* Légèrement réduit */
        line-height: 1.1 !important;
        white-space: nowrap !important;
        overflow: visible !important;
        display: block !important;
        font-weight: 600 !important;
        color: #047857 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.04) !important;
        letter-spacing: 0.1px !important;
        margin: 0 !important;
        padding: 0 !important;
        width: auto !important;
        max-width: none !important;
        text-align: center !important;
        z-index: 501 !important;
        position: relative !important;
    }
    
    /* 🌡️ LOCATION TEXT - MINIMAL */
    .weather-location-mobile {
        font-size: 0.52rem !important; /* Plus petit pour économiser l'espace */
        line-height: 1 !important;
        white-space: nowrap !important;
        overflow: visible !important;
        display: block !important;
        color: #059669 !important;
        opacity: 0.75 !important;
        margin-top: 1px !important;
        font-weight: 500 !important;
        text-align: center !important;
        letter-spacing: 0.05px !important;
        z-index: 501 !important;
        position: relative !important;
    }
    
    /* 🎮 CONTROLS ZONE - ULTRA-COMPACT À DROITE */
    .lg\\:hidden.flex.items-center.space-x-3 {
        flex: 0 0 105px !important; /* Réduit pour donner plus d'espace au logo */
        min-width: 105px !important;
        max-width: 110px !important;
        gap: 0.15rem !important; /* Gaps TRÈS serrés */
        z-index: 600 !important;
        position: relative !important;
        align-items: center !important;
        justify-content: flex-end !important; /* Tout poussé au bord droit */
        overflow: visible !important;
        height: 100% !important;
        
        /* GROUPEMENT ULTRA-SERRÉ À DROITE */
        padding: 0.2rem 0.1rem !important; /* Padding minimal */
        margin: 0 !important;
    }
    
    /* 🌍 LANGUAGE SWITCHER - ULTRA-COMPACT COMME DEMANDÉ */
    .language-switcher-mobile {
        flex: 0 0 38px !important; /* RÉDUIT de 48px à 38px ! */
        min-width: 38px !important;
        max-width: 40px !important;
        
        /* POSITIONNEMENT ULTRA-SERRÉ PRÈS DU HAMBURGER */
        margin-left: 0.1rem !important; /* Très proche du weather widget */
        margin-right: 0.15rem !important; /* Très proche du hamburger */
        z-index: 700 !important;
        position: relative !important;
    }
    
    /* 🌍 LANGUAGE BUTTON - ULTRA-COMPACT */
    .language-switcher-mobile .lang-btn-mobile {
        min-width: 38px !important; /* PLUS PETIT que les 48px actuels */
        max-width: 38px !important;
        height: 32px !important; /* Plus compact en hauteur */
        padding: 3px 5px !important; /* Padding très réduit */
        font-size: 0.65rem !important; /* Font plus petite */
        gap: 0.1rem !important; /* Gap minimal */
        border-radius: 4px !important; /* Border radius réduit */
        overflow: hidden !important;
        white-space: nowrap !important;
        z-index: 701 !important;
        position: relative !important;
        
        /* Styling subtil */
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.04) 0%, rgba(45, 212, 191, 0.02) 100%) !important;
        border: 1px solid rgba(163, 209, 200, 0.1) !important;
        backdrop-filter: blur(1px) !important;
        transition: all 0.3s ease !important;
    }
    
    /* 🌍 FLAG ET TEXT - ULTRA-COMPACT */
    .language-switcher-mobile .lang-btn-mobile .flag-icon {
        font-size: 0.7rem !important; /* Flag plus petit */
    }
    
    .language-switcher-mobile .lang-btn-mobile .lang-text-mobile {
        font-size: 0.6rem !important; /* Texte plus petit */
        font-weight: 600 !important;
    }
    
    /* 🍔 HAMBURGER MENU - MAINTENU STANDARD */
    #mobile-menu-btn {
        flex: 0 0 46px !important; /* Légèrement réduit */
        min-width: 46px !important;
        max-width: 46px !important;
        height: 46px !important;
        margin: 0 !important;
        padding: 11px !important;
        z-index: 800 !important;
        position: relative !important;
        border-radius: 6px !important;
        transition: all 0.3s ease !important;
        
        /* ANCRÉ AU BORD DROIT */
        margin-left: 0.05rem !important; /* Très proche du language switcher */
        
        /* Styling subtil */
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.04) 0%, rgba(45, 212, 191, 0.02) 100%) !important;
        border: 1px solid rgba(163, 209, 200, 0.12) !important;
        backdrop-filter: blur(1px) !important;
    }
    
    /* 🍔 HAMBURGER ICON */
    #mobile-menu-btn i {
        font-size: 1rem !important;
        line-height: 1 !important;
        color: #047857 !important;
        z-index: 801 !important;
        position: relative !important;
    }
}

/* 🔥 NOUVELLE MATHÉMATIQUE D'ESPACE:
   Logo: 155px (ÉNORME espace central!) ✅
   Weather: 48px (compact optimisé) ✅
   Language: 38px (RÉDUIT comme demandé!) ✅
   Hamburger: 46px (légèrement compact) ✅
   Gap management: Elements ultra-serrés = ~135px total côté droit ✅
   
   Total: 155px (logo) + 135px (right group) = 290px pour ~320px mobile
   Espace gagné: +15px SUPPLÉMENTAIRES pour "GolfinThaï"! 🎉
   Language switcher: RÉDUIT de 10px comme demandé! ✅
   
   RÉSULTAT: "GolfinThaï" a maintenant ENCORE PLUS d'espace! 🏆
*/

/* 📱 TRÈS PETITS ÉCRANS - SCALING PROPORTIONNEL */
@media (max-width: 380px) {
    .flex.items-center.space-x-3:first-child {
        flex: 0 0 140px !important;
        max-width: 140px !important;
    }
    
    .logo-wrapper {
        width: 42px !important;
        height: 42px !important;
    }
    
    .header-logo-gradient {
        font-size: 1rem !important;
        max-width: 90px !important;
    }
    
    .weather-widget-mobile {
        flex: 0 0 44px !important;
        min-width: 44px !important;
        max-width: 46px !important;
    }
    
    .weather-temp-mobile {
        font-size: 0.68rem !important;
    }
    
    .weather-location-mobile {
        font-size: 0.48rem !important;
    }
    
    .lg\\:hidden.flex.items-center.space-x-3 {
        flex: 0 0 100px !important;
        max-width: 100px !important;
    }
    
    .language-switcher-mobile {
        flex: 0 0 36px !important;
        min-width: 36px !important;
        max-width: 36px !important;
    }
    
    .language-switcher-mobile .lang-btn-mobile {
        min-width: 36px !important;
        max-width: 36px !important;
        height: 30px !important;
        padding: 2px 4px !important;
        font-size: 0.6rem !important;
    }
    
    #mobile-menu-btn {
        flex: 0 0 44px !important;
        min-width: 44px !important;
        max-width: 44px !important;
        height: 44px !important;
        padding: 10px !important;
    }
}

/* 🎨 ENHANCED HOVER EFFECTS - PREMIUM AVEC PLUS D'ESPACE */
@media (max-width: 1023px) {
    
    /* Logo hover - Encore plus impressionnant avec l'espace supplémentaire */
    .flex.items-center.space-x-3:first-child:hover .header-logo-gradient {
        filter: drop-shadow(0 2px 10px rgba(6, 95, 70, 0.45)) brightness(1.15) !important;
        transform: scale(1.05) !important; /* Plus grand effet grâce à l'espace */
    }
    
    .flex.items-center.space-x-3:first-child:hover .logo-wrapper {
        transform: scale(1.1) !important; /* Plus grand effet */
        box-shadow: 0 5px 20px rgba(45, 212, 191, 0.3) !important;
    }
    
    /* Weather widget - Effet minimal pour le côté droit */
    .weather-widget-mobile:hover {
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.08) 0%, rgba(45, 212, 191, 0.04) 100%) !important;
        transform: translateY(-1px) scale(1.02) !important;
        box-shadow: 0 2px 8px rgba(45, 212, 191, 0.08) !important;
    }
    
    /* Language switcher ultra-compact hover */
    .language-switcher-mobile:hover .lang-btn-mobile {
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.1) 0%, rgba(45, 212, 191, 0.05) 100%) !important;
        border-color: rgba(163, 209, 200, 0.25) !important;
        transform: scale(1.05) !important; /* Effet subtil pour le petit bouton */
    }
    
    /* Hamburger hover */
    #mobile-menu-btn:hover {
        background: linear-gradient(135deg, rgba(163, 209, 200, 0.1) 0%, rgba(45, 212, 191, 0.05) 100%) !important;
        border-color: rgba(163, 209, 200, 0.25) !important;
        transform: scale(1.05) !important;
    }
    
    #mobile-menu-btn:hover i {
        color: #065f46 !important;
    }
    
    /* Transitions fluides */
    .flex.items-center.space-x-3:first-child,
    .logo-wrapper,
    .weather-widget-mobile,
    .header-logo-gradient,
    .weather-temp-mobile,
    .language-switcher-mobile .lang-btn-mobile,
    #mobile-menu-btn,
    #mobile-menu-btn i {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }
}

/* 🏆 OBJECTIFS ATTEINTS:
   
   ✅ Language switcher RÉDUIT: 48px → 38px (10px économisés!)
   ✅ Widgets PLUS à droite: gaps ultra-serrés
   ✅ Logo space AUGMENTÉ: 140px → 155px (+15px!)
   ✅ Logo circle PLUS GRAND: 44px → 46px
   ✅ Font "GolfinThaï" PLUS GRANDE: 1rem → 1.1rem
   ✅ Groupement right-side OPTIMISÉ
   ✅ Espace total logo: MAXIMUM atteint!
   
   RÉSULTAT: "GolfinThaï" a maintenant une visibilité PARFAITE! 🎯
*/
