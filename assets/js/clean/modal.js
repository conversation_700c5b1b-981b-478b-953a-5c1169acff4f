/**
 * 🏌️ GolfinThaï - Premium Modal System
 */

class PremiumModalSystem {
    constructor() {
        this.activeModal = null;
        this.isExpanded = false;
        this.golfData = this.initGolfData();
        this.init();
    }
    
    init() {
        this.setupImageClicks();
        this.setupExpandButtons();
        this.setupKeyboardHandlers();
        this.injectStyles();
    }
    
    initGolfData() {
        return {
            'AquellaGolfCountryClub.jpeg': {
                title: 'Aquella Golf & Country Club',
                short: 'Parcours championship côtier unique construit sur les vestiges du tsunami de 2004.',
                full: 'Aquella Golf & Country Club en Thaïlande a été élu Meilleur Golf de Luxe 2024. Ce parcours championship de 18 trous propose une expérience unique avec 2,5 kilomètres de plage privée exclusive.'
            },
            'BlackMountainGolfClubHuHin.jpeg': {
                title: 'Black Mountain Golf Club Hua Hin',
                short: 'Parcours championship de renommée internationale niché au pied des montagnes granitiques noires.',
                full: 'Black Mountain Golf Club à Hua Hin est classé #59 au Golf Digest Top 100 mondial. Ce parcours de 27 trous championship conçu par Phil Ryan offre une expérience exceptionnelle.'
            },
            'SantiburiSamuiCountryClubKohSamui.jpeg': {
                title: 'Santiburi Samui Country Club',
                short: 'Parcours unique de 18 trous sur l\'île de Koh Samui.',
                full: 'Santiburi Samui Country Club est le seul parcours de golf 18 trous de l\'île de Koh Samui. Niché dans un resort 5 étoiles avec plage privée.'
            },
            'Highlands.jpeg': {
                title: 'Chiang Mai Highlands Golf & Spa Resort',
                short: 'Golf montagnard d\'exception avec 27 trous répartis en trois parcours distincts.',
                full: 'Chiang Mai Highlands Golf & Spa Resort propose une expérience golfique unique en Thaïlande grâce à son altitude élevée offrant un climat frais.'
            },
            'RedMountainGolfClubPhuket.jpeg': {
                title: 'Red Mountain Golf Club Phuket',
                short: 'Parcours révolutionnaire de 18 trous sculpté dans une ancienne mine d\'étain.',
                full: 'Red Mountain Golf Club à Phuket est reconnu comme le parcours le plus spectaculaire d\'Asie. Sculpté dans une ancienne mine d\'étain.'
            },
            'blue-canyon-golf-course-thailand.jpg': {
                title: 'Blue Canyon Country Club Phuket',
                short: 'Institution légendaire du golf mondial avec deux parcours championship.',
                full: 'Blue Canyon Country Club à Phuket détient le record unique d\'être le seul parcours au monde à avoir accueilli trois fois le Johnnie Walker Classic.'
            },
            'acceuil1.jpg': {
                title: 'Accompagnement Sur Mesure',
                short: 'Service personnalisé adapté à vos envies et votre budget.',
                full: 'Notre service d\'accompagnement sur mesure s\'adapte parfaitement à vos envies et votre budget.'
            },
            'acceuil2.jpg': {
                title: 'Séjours Golf Personnalisés',
                short: 'Bénéficiez de ma connaissance approfondie de la Thaïlande.',
                full: 'Avec plus de 10 ans de vie en Thaïlande, je vous fais découvrir les parcours exceptionnels.'
            },
            'Ayutthaya.webp': {
                title: 'Golf Historique Ayutthaya',
                short: 'Golf historique dans l\'ancienne capitale royale du Siam.',
                full: 'Vivez une expérience golf unique à Ayutthaya, site du patrimoine mondial UNESCO.'
            },
            'nouvelleimage1.jpeg': {
                title: 'Parcours Golf Premium Thaïlande',
                short: 'Nouveau parcours de golf exclusif avec vue panoramique.',
                full: 'Découvrez ce parcours premium offrant des vues panoramiques exceptionnelles.'
            },
            'missionomg.jpg': {
                title: 'Expertise Golf Exception',
                short: 'Expertise golf et parcours d\'exception en Thaïlande.',
                full: 'Notre expertise vous garantit l\'accès aux golfs les plus prestigieux.'
            },
            'hl03-high.jpg': {
                title: 'Golf Premium Vues Époustouflantes',
                short: 'Golf premium avec vues époustouflantes Thaïlande.',
                full: 'Profitez de parcours premium offrant des vues spectaculaires.'
            },
            'watphrakaew-high.jpg': {
                title: 'Wat Phra Kaew - Temple Bouddha Émeraude',
                short: 'Culture Authentique Thaïlandaise - Temple du Bouddha d\'Émeraude.',
                full: 'Découvrez la richesse culturelle avec le Temple du Bouddha d\'Émeraude.'
            },
            'PR.jpg': {
                title: 'Sébastien Marciano - Fondateur',
                short: 'Fondateur et guide expert avec 20+ ans d\'expérience.',
                full: 'Sébastien Marciano, fondateur de GolfinThaï avec 20+ ans d\'expérience.'
            }
        };
    }
    
    setupImageClicks() {
        document.querySelectorAll('.clickable-image, .course-image').forEach(img => {
            img.style.cursor = 'pointer';
            img.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.openModal(img.src, img.alt || 'Image GolfinThaï');
            });
        });
    }
    
    setupExpandButtons() {
        document.querySelectorAll('.course-expand-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const img = btn.closest('.course-card').querySelector('.course-image');
                if (img) {
                    this.openModal(img.src, img.alt || 'Parcours de Golf');
                }
            });
        });
    }
    
    setupKeyboardHandlers() {
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeModal) {
                this.closeModal();
            }
        });
    }
    
    openModal(imageSrc, imageAlt) {
        if (this.activeModal) this.closeModal();
        
        const filename = imageSrc.split('/').pop();
        const data = this.golfData[filename] || {
            title: 'Image GolfinThaï',
            short: 'Découvrez la beauté de la Thaïlande avec GolfinThaï.',
            full: 'GolfinThaï vous propose des séjours golf d\'exception en Thaïlande.'
        };
        
        this.createModal(imageSrc, imageAlt, data);
        this.showModal();
    }
    
    createModal(imageSrc, imageAlt, data) {
        this.activeModal = document.createElement('div');
        this.activeModal.className = 'premium-modal';
        
        this.activeModal.innerHTML = `
            <div class="premium-modal-backdrop"></div>
            <div class="premium-modal-container">
                <button class="premium-modal-close">×</button>
                <div class="premium-modal-content">
                    <img src="${imageSrc}" alt="${imageAlt}" class="premium-modal-image">
                    <div class="premium-modal-info">
                        <h3 class="premium-modal-title">${data.title}</h3>
                        <p class="premium-modal-short">${data.short}</p>
                        <div class="premium-modal-full">${data.full}</div>
                        <button class="premium-expand-btn">Réduire ↑</button>
                    </div>
                </div>
            </div>
        `;
        
        this.setupModalEvents();
        document.body.appendChild(this.activeModal);
    }
    
    setupModalEvents() {
        const backdrop = this.activeModal.querySelector('.premium-modal-backdrop');
        const closeBtn = this.activeModal.querySelector('.premium-modal-close');
        const expandBtn = this.activeModal.querySelector('.premium-expand-btn');
        const fullText = this.activeModal.querySelector('.premium-modal-full');
        
        backdrop.onclick = () => this.closeModal();
        closeBtn.onclick = () => this.closeModal();
        
        this.isExpanded = true;
        fullText.style.display = 'block';
        expandBtn.textContent = 'Réduire ↑';
        
        expandBtn.onclick = (e) => {
            e.stopPropagation();
            this.toggleExpand(expandBtn, fullText);
        };
    }
    
    toggleExpand(button, textElement) {
        if (this.isExpanded) {
            textElement.style.display = 'none';
            button.textContent = 'En savoir plus ↓';
            this.isExpanded = false;
        } else {
            textElement.style.display = 'block';
            button.textContent = 'Réduire ↑';
            this.isExpanded = true;
        }
    }
    
    showModal() {
        document.body.style.overflow = 'hidden';
        this.activeModal.classList.add('show');
    }
    
    closeModal() {
        if (!this.activeModal) return;
        
        this.activeModal.classList.remove('show');
        
        setTimeout(() => {
            if (this.activeModal) {
                this.activeModal.remove();
                this.activeModal = null;
            }
            document.body.style.overflow = '';
        }, 300);
    }
    
    injectStyles() {
        if (document.getElementById('premium-modal-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'premium-modal-styles';
        style.textContent = `
            .premium-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                z-index: 999999;
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }
            
            .premium-modal.show {
                opacity: 1;
                visibility: visible;
            }
            
            .premium-modal-backdrop {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.95);
                backdrop-filter: blur(10px);
            }
            
            .premium-modal-container {
                position: relative;
                max-width: 95vw;
                max-height: 95vh;
                background: #0D1B1A;
                border-radius: 20px;
                overflow: hidden;
                box-shadow: 0 25px 50px rgba(0, 0, 0, 0.8);
                border: 2px solid #00574B;
                display: flex;
                flex-direction: column;
                transform: scale(0.9);
                transition: transform 0.3s ease;
            }
            
            .premium-modal.show .premium-modal-container {
                transform: scale(1);
            }
            
            .premium-modal-close {
                position: absolute;
                top: 20px;
                right: 20px;
                width: 44px;
                height: 44px;
                background: rgba(0, 87, 75, 0.9);
                border: 2px solid rgba(163, 209, 200, 0.5);
                border-radius: 50%;
                color: white;
                font-size: 24px;
                cursor: pointer;
                z-index: 10;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
            }
            
            .premium-modal-close:hover {
                background: #00574B;
                border-color: #A3D1C8;
                transform: scale(1.1);
            }
            
            .premium-modal-content {
                display: flex;
                flex-direction: column;
            }
            
            .premium-modal-image {
                width: 100%;
                max-height: 50vh;
                object-fit: cover;
                display: block;
            }
            
            .premium-modal-info {
                padding: 30px;
                text-align: center;
                flex: 1;
                overflow-y: auto;
            }
            
            .premium-modal-title {
                font-family: 'Playfair Display', serif;
                font-size: 1.8rem;
                font-weight: 700;
                color: #00574B;
                margin-bottom: 15px;
                line-height: 1.3;
            }
            
            .premium-modal-short {
                color: rgba(252, 252, 252, 0.9);
                font-size: 1.1rem;
                line-height: 1.6;
                margin-bottom: 20px;
            }
            
            .premium-modal-full {
                color: rgba(252, 252, 252, 0.8);
                font-size: 1rem;
                line-height: 1.7;
                margin-bottom: 25px;
                border-top: 1px solid rgba(255, 255, 255, 0.1);
                padding-top: 15px;
                display: block;
            }
            
            .premium-expand-btn {
                background: linear-gradient(135deg, #00574B, #A3D1C8);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 12px;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                min-width: 180px;
                min-height: 50px;
                box-shadow: 0 6px 20px rgba(0, 87, 75, 0.4);
            }
            
            .premium-expand-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0, 87, 75, 0.5);
            }
            
            @media (max-width: 768px) {
                .premium-modal-container {
                    max-width: 98vw;
                    max-height: 98vh;
                    border-radius: 16px;
                }
                
                .premium-modal-image {
                    max-height: 45vh;
                }
                
                .premium-modal-info {
                    padding: 20px;
                }
                
                .premium-modal-title {
                    font-size: 1.4rem;
                }
            }
        `;
        
        document.head.appendChild(style);
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    window.imageModal = new PremiumModalSystem();
});