/**
 * 🚀 GolfinThaï - App Robuste et Optimisée
 */

class GolfinThaiApp {
    constructor() {
        this.currentLanguage = localStorage.getItem('golfinthai-language') || 'fr';
        this.translations = {};
        this.courseData = {};
        this.activeModal = null;
        
        // Lancer l'initialisation dès que le DOM est prêt
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }

    async init() {
        // Charger les données en parallèle
        await Promise.all([
            this.loadTranslations(),
            this.loadCourseData()
        ]);
        
        // Initialiser les composants de l'interface utilisateur
        this.initUI();
        this.translatePage();
    }

    initUI() {
        this.initNavigation();
        this.initCarousel();
        this.initLanguageSwitcher();
        this.initWeather();
        this.initScrollAnimations();
        this.initForms();
        this.initModals(); // Centralisation de l'initialisation des modales
    }

    // --- CHARGEMENT DES DONNÉES ---
    async loadTranslations() {
        try {
            const response = await fetch('./data/translations.json');
            this.translations = response.ok ? await response.json() : {};
        } catch { this.translations = {}; }
    }

    async loadCourseData() {
        try {
            const response = await fetch('./data/golf-courses-premium.json');
            this.courseData = response.ok ? (await response.json()).golf_courses_premium : {};
        } catch { this.courseData = {}; }
    }

    // --- INITIALISATION DES COMPOSANTS ---
    initNavigation() {
        const menuBtn = document.getElementById('mobile-menu-btn');
        const closeBtn = document.getElementById('close-menu');
        const overlay = document.getElementById('mobile-menu');
        
        if (menuBtn && overlay) {
            menuBtn.onclick = () => this.toggleMenu(true, overlay);
            if (closeBtn) closeBtn.onclick = () => this.toggleMenu(false, overlay);
            overlay.onclick = (e) => {
                if (e.target === overlay) this.toggleMenu(false, overlay);
            };
        }
        
        document.querySelectorAll('a[href^="#"]').forEach(link => {
            link.onclick = (e) => {
                const target = document.querySelector(link.getAttribute('href'));
                if (target) {
                    e.preventDefault();
                    this.toggleMenu(false, overlay);
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            };
        });

        window.onscroll = () => {
            const navbar = document.querySelector('.nav-bar');
            if (navbar) navbar.classList.toggle('scrolled', window.scrollY > 50);
        };
    }
    
    toggleMenu(open, overlay) {
        if (overlay) overlay.classList.toggle('open', open);
        document.body.style.overflow = open ? 'hidden' : '';
    }

    initCarousel() {
        this.currentSlide = 0;
        this.slides = document.querySelectorAll('.carousel-slide');
        this.bullets = document.querySelectorAll('.pagination-bullet');
        if (this.slides.length === 0) return;

        this.bullets.forEach((bullet, i) => bullet.onclick = () => this.goToSlide(i));
        setInterval(() => this.nextSlide(), 5000);
    }

    goToSlide(index) {
        this.slides.forEach(s => s.classList.remove('active'));
        this.bullets.forEach(b => b.classList.remove('active'));
        this.slides[index].classList.add('active');
        this.bullets[index].classList.add('active');
        this.currentSlide = index;
    }

    nextSlide() {
        this.goToSlide((this.currentSlide + 1) % this.slides.length);
    }

    initLanguageSwitcher() {
        const toggle = (e) => {
            e.preventDefault();
            const dropdown = e.currentTarget.nextElementSibling;
            if (dropdown) dropdown.classList.toggle('dropdown-open');
        };
        
        document.getElementById('lang-toggle-desktop')?.addEventListener('click', toggle);
        document.getElementById('lang-toggle-mobile')?.addEventListener('click', toggle);

        document.querySelectorAll('.lang-option, .lang-option-mobile').forEach(opt => {
            opt.onclick = (e) => {
                e.preventDefault();
                this.switchLanguage(opt.dataset.lang);
            };
        });

        document.onclick = (e) => {
            if (!e.target.closest('.language-switcher-desktop, .language-switcher-mobile')) {
                document.querySelectorAll('.lang-dropdown, .lang-dropdown-mobile').forEach(d => d.classList.remove('dropdown-open'));
            }
        };
        this.updateLanguageUI();
    }

    switchLanguage(lang) {
        if (lang === this.currentLanguage) return;
        this.currentLanguage = lang;
        localStorage.setItem('golfinthai-language', lang);
        document.documentElement.lang = lang;
        this.updateLanguageUI();
        this.translatePage();
        document.querySelectorAll('.lang-dropdown, .lang-dropdown-mobile').forEach(d => d.classList.remove('dropdown-open'));
    }

    updateLanguageUI() {
        const text = this.currentLanguage === 'fr' ? 'FR' : 'EN';
        const flag = this.currentLanguage === 'fr' ? '🇫🇷' : '🇬🇧';
        document.querySelectorAll('.lang-text, .lang-text-mobile').forEach(el => el.textContent = text);
        document.querySelectorAll('#lang-toggle-desktop .flag-icon, #lang-toggle-mobile .flag-icon').forEach(el => el.textContent = flag);
        document.querySelectorAll('.lang-option, .lang-option-mobile').forEach(opt => {
            opt.classList.toggle('current', opt.dataset.lang === this.currentLanguage);
        });
    }

    translatePage() {
        if (!this.translations[this.currentLanguage]) return;
        document.querySelectorAll('[data-translate]').forEach(el => {
            const key = el.getAttribute('data-translate');
            const translation = key.split('.').reduce((o, i) => o?.[i], this.translations[this.currentLanguage]);
            if (translation) el.innerHTML = translation;
        });
    }

    initWeather() {
        const weather = { temp: 32, icon: 'fas fa-sun' };
        document.getElementById('weather-temp').textContent = `${weather.temp}°C`;
        document.getElementById('weather-icon').className = `${weather.icon} weather-icon`;
        document.getElementById('weather-temp-mobile').textContent = `${weather.temp}°`;
        document.getElementById('weather-icon-mobile').className = `${weather.icon} weather-icon`;
    }

    initScrollAnimations() {
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) entry.target.classList.add('visible');
                });
            }, { threshold: 0.1 });
            document.querySelectorAll('.reveal-animation').forEach(el => observer.observe(el));
        }
    }

    initForms() {
        const form = document.querySelector('.contact-form');
        if (!form) return;
        form.onsubmit = async (e) => {
            e.preventDefault();
            const btn = form.querySelector('button[type="submit"]');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Envoi...';
            try {
                const response = await fetch(form.action, { method: 'POST', body: new FormData(form), headers: { 'Accept': 'application/json' } });
                if (response.ok) {
                    alert('Message envoyé avec succès !');
                    form.reset();
                } else { throw new Error('Erreur serveur'); }
            } catch { alert('Erreur lors de l\'envoi. Veuillez réessayer.'); }
            finally { 
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-paper-plane mr-2"></i>Envoyer le Message';
            }
        };
    }

    // --- GESTION DES MODALES ---
    initModals() {
        document.body.addEventListener('click', (e) => {
            const courseCard = e.target.closest('.course-card');
            const imageTarget = e.target.closest('.clickable-image:not(.course-card .clickable-image)');

            if (courseCard) {
                e.preventDefault();
                const courseId = courseCard.dataset.course;
                if (courseId) this.openCourseModal(courseId);
            } else if (imageTarget) {
                e.preventDefault();
                const src = imageTarget.dataset.modalSrc || imageTarget.src;
                const alt = imageTarget.dataset.modalAlt || imageTarget.alt || 'Image GolfinThaï';
                this.openImageModal(src, alt);
            }
        });

        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeModal) this.closeModal();
        });
    }

    openCourseModal(courseId) {
        const course = this.courseData[courseId];
        if (!course) return;

        const imageSrc = document.querySelector(`.course-card[data-course="${courseId}"] .course-image`)?.src || '';
        const modalHTML = `
            <div class="modal-container course-modal">
                <button class="modal-close">×</button>
                <div class="course-modal-image">
                    <img src="${imageSrc}" alt="${course.title}">
                </div>
                <div class="course-modal-details">
                    <h3>${course.title}</h3>
                    <p class="location">${course.location}</p>
                    <p class="description">${course.detailed_description}</p>
                    <h4>Points Forts</h4>
                    <ul>${course.highlights.map(h => `<li><i class="fas fa-check-circle"></i> ${h}</li>`).join('')}</ul>
                </div>
            </div>`;
        this.createAndShowModal(modalHTML);
    }

    openImageModal(src, alt) {
        const modalHTML = `
            <div class="modal-container image-modal">
                <button class="modal-close">×</button>
                <img src="${src}" alt="${alt}">
                <p>${alt}</p>
            </div>`;
        this.createAndShowModal(modalHTML);
    }

    createAndShowModal(contentHTML) {
        if (this.activeModal) this.closeModal();

        this.activeModal = document.createElement('div');
        this.activeModal.className = 'modal-backdrop';
        this.activeModal.innerHTML = contentHTML;
        document.body.appendChild(this.activeModal);
        document.body.style.overflow = 'hidden';

        // Listeners
        this.activeModal.querySelector('.modal-close').onclick = () => this.closeModal();
        this.activeModal.onclick = (e) => {
            if (e.target === this.activeModal) this.closeModal();
        };

        // Animation
        requestAnimationFrame(() => this.activeModal.classList.add('visible'));
    }

    closeModal() {
        if (!this.activeModal) return;
        this.activeModal.classList.remove('visible');
        this.activeModal.addEventListener('transitionend', () => {
            this.activeModal.remove();
            this.activeModal = null;
            document.body.style.overflow = '';
        }, { once: true });
    }
}

// Lancement de l'application
new GolfinThaiApp();