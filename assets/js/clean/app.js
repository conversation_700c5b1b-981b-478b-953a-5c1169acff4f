/**
 * 🚀 GolfinThaï - App Simple et Fonctionnel
 */

class GolfinThaiApp {
    constructor() {
        this.currentLanguage = localStorage.getItem('golfinthai-language') || 'fr';
        this.translations = {};
        this.init();
    }
    
    async init() {
        await this.loadTranslations();
        this.initNavigation();
        this.initCarousel();
        this.initLanguageSwitcher();
        this.initWeather();
        this.initScrollAnimations();
        this.initForms();
        this.translatePage();
    }
    
    async loadTranslations() {
        try {
            const response = await fetch('./data/translations.json');
            this.translations = response.ok ? await response.json() : {};
        } catch {
            this.translations = {};
        }
    }
    
    // Navigation
    initNavigation() {
        const menuBtn = document.getElementById('mobile-menu-btn');
        const closeBtn = document.getElementById('close-menu');
        const overlay = document.getElementById('mobile-menu');
        
        if (menuBtn && overlay) {
            menuBtn.onclick = () => {
                overlay.classList.add('open');
                document.body.style.overflow = 'hidden';
            };
            
            if (closeBtn) {
                closeBtn.onclick = () => {
                    overlay.classList.remove('open');
                    document.body.style.overflow = '';
                };
            }
            
            overlay.onclick = (e) => {
                if (e.target === overlay) {
                    overlay.classList.remove('open');
                    document.body.style.overflow = '';
                }
            };
        }
        
        // Smooth scroll
        document.querySelectorAll('a[href^="#"]').forEach(link => {
            link.onclick = (e) => {
                const target = document.querySelector(link.getAttribute('href'));
                if (target) {
                    e.preventDefault();
                    if (overlay) {
                        overlay.classList.remove('open');
                        document.body.style.overflow = '';
                    }
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            };
        });
        
        // Scroll effect
        window.onscroll = () => {
            const navbar = document.querySelector('.nav-bar');
            if (navbar) {
                navbar.classList.toggle('scrolled', window.scrollY > 50);
            }
        };
    }
    
    // Carousel
    initCarousel() {
        this.currentSlide = 0;
        this.slides = document.querySelectorAll('.carousel-slide');
        this.bullets = document.querySelectorAll('.pagination-bullet');
        
        if (this.slides.length === 0) return;
        
        this.bullets.forEach((bullet, index) => {
            bullet.onclick = () => this.goToSlide(index);
        });
        
        setInterval(() => this.nextSlide(), 5000);
    }
    
    goToSlide(index) {
        this.slides.forEach(slide => slide.classList.remove('active'));
        this.bullets.forEach(bullet => bullet.classList.remove('active'));
        
        this.slides[index].classList.add('active');
        this.bullets[index].classList.add('active');
        this.currentSlide = index;
    }
    
    nextSlide() {
        const nextIndex = (this.currentSlide + 1) % this.slides.length;
        this.goToSlide(nextIndex);
    }
    
    // Language Switcher
    initLanguageSwitcher() {
        // Desktop
        const desktopBtn = document.getElementById('lang-toggle-desktop');
        const desktopDropdown = document.getElementById('lang-dropdown-desktop');
        
        if (desktopBtn && desktopDropdown) {
            desktopBtn.onclick = (e) => {
                e.preventDefault();
                desktopDropdown.classList.toggle('dropdown-open');
            };
        }
        
        // Mobile
        const mobileBtn = document.getElementById('lang-toggle-mobile');
        const mobileDropdown = document.getElementById('lang-dropdown-mobile');
        
        if (mobileBtn && mobileDropdown) {
            mobileBtn.onclick = (e) => {
                e.preventDefault();
                mobileDropdown.classList.toggle('dropdown-open');
            };
        }
        
        // Options
        document.querySelectorAll('.lang-option, .lang-option-mobile').forEach(option => {
            option.onclick = (e) => {
                e.preventDefault();
                this.switchLanguage(option.dataset.lang);
            };
        });
        
        // Close on outside click
        document.onclick = (e) => {
            if (!e.target.closest('.language-switcher-desktop') && !e.target.closest('.language-switcher-mobile')) {
                document.querySelectorAll('.lang-dropdown, .lang-dropdown-mobile').forEach(dropdown => {
                    dropdown.classList.remove('dropdown-open');
                });
            }
        };
        
        this.updateLanguageUI();
    }
    
    switchLanguage(language) {
        if (language === this.currentLanguage) return;
        
        this.currentLanguage = language;
        localStorage.setItem('golfinthai-language', language);
        document.documentElement.lang = language;
        
        this.updateLanguageUI();
        this.translatePage();
        
        // Close dropdowns
        document.querySelectorAll('.lang-dropdown, .lang-dropdown-mobile').forEach(dropdown => {
            dropdown.classList.remove('dropdown-open');
        });
    }
    
    updateLanguageUI() {
        const text = this.currentLanguage === 'fr' ? 'FR' : 'EN';
        const flag = this.currentLanguage === 'fr' ? '🇫🇷' : '🇬🇧';
        
        // Update text
        document.querySelectorAll('.lang-text, .lang-text-mobile').forEach(el => {
            el.textContent = text;
        });
        
        // Update flags in buttons
        document.querySelectorAll('#lang-toggle-desktop .flag-icon, #lang-toggle-mobile .flag-icon').forEach(flagEl => {
            flagEl.textContent = flag;
        });
        
        // Update active states
        document.querySelectorAll('.lang-option, .lang-option-mobile').forEach(option => {
            option.classList.toggle('current', option.dataset.lang === this.currentLanguage);
        });
    }
    
    translatePage() {
        if (!this.translations[this.currentLanguage]) return;
        
        document.querySelectorAll('[data-translate]').forEach(element => {
            const key = element.getAttribute('data-translate');
            const translation = this.getTranslation(key);
            if (translation) {
                element.innerHTML = translation;
            }
        });
    }
    
    getTranslation(key) {
        const keys = key.split('.');
        let result = this.translations[this.currentLanguage];
        
        for (const k of keys) {
            if (result && typeof result === 'object' && k in result) {
                result = result[k];
            } else {
                return null;
            }
        }
        
        return typeof result === 'string' ? result : null;
    }
    
    // Weather
    initWeather() {
        const weatherData = { temperature: 32, icon: 'fas fa-sun' };
        
        // Desktop
        const tempEl = document.getElementById('weather-temp');
        const iconEl = document.getElementById('weather-icon');
        if (tempEl) tempEl.textContent = `${weatherData.temperature}°C`;
        if (iconEl) iconEl.className = `${weatherData.icon} weather-icon`;
        
        // Mobile
        const tempMobileEl = document.getElementById('weather-temp-mobile');
        const iconMobileEl = document.getElementById('weather-icon-mobile');
        if (tempMobileEl) tempMobileEl.textContent = `${weatherData.temperature}°`;
        if (iconMobileEl) iconMobileEl.className = `${weatherData.icon} weather-icon`;
    }
    
    // Scroll Animations
    initScrollAnimations() {
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, { threshold: 0.1 });
            
            document.querySelectorAll('.reveal-animation').forEach(el => {
                observer.observe(el);
            });
        }
    }
    
    // Forms
    initForms() {
        const form = document.querySelector('.contact-form');
        if (!form) return;
        
        form.onsubmit = async (e) => {
            e.preventDefault();
            
            const formData = new FormData(form);
            const submitBtn = form.querySelector('button[type="submit"]');
            
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Envoi...';
            }
            
            try {
                const response = await fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: { 'Accept': 'application/json' }
                });
                
                if (response.ok) {
                    alert('Message envoyé avec succès !');
                    form.reset();
                } else {
                    throw new Error('Erreur serveur');
                }
            } catch {
                alert('Erreur lors de l\'envoi. Veuillez réessayer.');
            } finally {
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="fas fa-paper-plane mr-2"></i>Envoyer le Message';
                }
            }
        };
    }
}

// Initialize
const app = new GolfinThaiApp();
window.golfinthaiApp = app;