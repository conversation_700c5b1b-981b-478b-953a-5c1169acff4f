/**
 * Configuration principale pour GolfinThaï
 * Contient toutes les constantes et variables de configuration
 */

// Configuration principale pour GolfinThaï
window.GolfinThaiConfig = {
    // Note: Couleurs et polices définies dans critical.css (variables CSS)
    // Utilisez getComputedStyle() pour accéder aux variables CSS si nécessaire
    
    // Configuration carrousel custom (remplace Swiper)
    carousel: {
        autoplayDelay: 5000,
        fadeSpeed: 1000,
        pagination: {
            el: '.carousel-pagination',
            clickable: true
        }
    },

    // Configuration Weather API
    weather: {
        apiUrl: 'https://wttr.in/Bangkok?format=j1',
        updateInterval: 600000, // 10 minutes
        defaultTemp: '--',
        defaultIcon: 'fas fa-sun'
    },

    // Configuration des formulaires
    form: {
        action: 'https://formspree.io/f/mpwdvaad',
        method: 'POST',
        // Email de destination pour validation
        target: '<EMAIL>'
    },

    // Configuration des animations
    animations: {
        revealOffset: 100,
        revealDelay: 200,
        scrollThrottle: 100
    },

    // Contact Information
    contact: {
        whatsapp: '+33609799180',
        whatsappUrl: 'https://wa.me/33609799180',
        email: '<EMAIL>',
        instagram: 'https://instagram.com/golfinthai_'
    },

    // SEO et Meta
    meta: {
        siteName: 'GolfinThaï',
        siteUrl: 'https://golfthailande.com',
        defaultImage: 'https://golfthailande.com/assets/images/hero-golf-thailand.jpg',
        author: 'Sébastien Marciano - GolfinThaï'
    },

    // Elements du DOM fréquemment utilisés
    selectors: {
        mobileMenuBtn: '#mobile-menu-btn',
        mobileMenu: '#mobile-menu',
        closeMenuBtn: '#close-menu',
        navBar: '#main-navigation',
        heroCarousel: '.hero-carousel',
        courseCards: '.course-card',
        contactForm: '.contact-form'
    }
};
