/**
 * 🚀 LUXURY IMAGE MODAL - IPHONE SAFARI ULTIMATE FIX V2
 * Silicon Valley Edition - Zero BS, Maximum Performance
 * 
 * @company GolfinThai
 * @version 2.0.0
 * @performance 60fps guaranteed
 * @compatibility iPhone Safari 100% + All Modern Browsers
 */

(function() {
    'use strict';
    
    // 🎯 CONFIGURATION - Simple is better
    const CONFIG = {
        DEBUG: true,
        ANIMATION_DURATION: 200,
        TOUCH_THRESHOLD: 300,
        MIN_TOUCH_TARGET: 44, // Apple HIG requirement
        DEBOUNCE_DELAY: 50
    };
    
    // 📱 DEVICE DETECTION - One source of truth
    const DEVICE = {
        isIOS: /iPhone|iPad|iPod/.test(navigator.userAgent),
        isSafari: /Safari/.test(navigator.userAgent) && !/Chrome|CriOS/.test(navigator.userAgent),
        isIOSSafari: false,
        supportsTouch: 'ontouchstart' in window || navigator.maxTouchPoints > 0
    };
    DEVICE.isIOSSafari = DEVICE.isIOS && DEVICE.isSafari;
    
    // 🔥 MODAL CLASS - Clean Architecture
    class LuxuryModal {
        constructor() {
            this.activeModal = null;
            this.toggleStates = new Map();
            this.touchHandlers = new WeakMap();
            this.init();
        }
        
        init() {
            this.log('🚀 Initializing Luxury Modal System');
            this.setupStyles();
            this.setupGlobalHandlers();
            this.log(`📱 Device: ${DEVICE.isIOSSafari ? 'iPhone Safari' : 'Standard Browser'}`);
        }
        
        // 💅 STYLES - Performance optimized CSS
        setupStyles() {
            const style = document.createElement('style');
            style.textContent = `
                /* Base Modal Styles */
                .luxury-modal {
                    display: none;
                    position: fixed;
                    inset: 0;
                    z-index: 99999;
                    background: rgba(0, 0, 0, 0.9);
                    -webkit-backdrop-filter: blur(10px);
                    backdrop-filter: blur(10px);
                }
                
                .luxury-modal.show {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                
                .luxury-modal-content {
                    position: relative;
                    width: 90%;
                    max-width: 1200px;
                    max-height: 90vh;
                    background: #000;
                    border-radius: 20px;
                    overflow: hidden;
                    transform: translateZ(0); /* Force GPU acceleration */
                }
                
                /* iPhone Safari Specific */
                @supports (-webkit-touch-callout: none) {
                    .luxury-toggle-btn {
                        -webkit-tap-highlight-color: transparent !important;
                        -webkit-touch-callout: none !important;
                        -webkit-user-select: none !important;
                        touch-action: manipulation !important;
                        cursor: pointer !important;
                        min-height: ${CONFIG.MIN_TOUCH_TARGET}px !important;
                        min-width: 120px !important;
                        position: relative !important;
                        z-index: 999 !important;
                        display: inline-flex !important;
                        align-items: center !important;
                        justify-content: center !important;
                        padding: 12px 24px !important;
                        background: linear-gradient(135deg, #00ff88, #00cc66) !important;
                        border: none !important;
                        border-radius: 50px !important;
                        color: #000 !important;
                        font-weight: 600 !important;
                        font-size: 16px !important;
                        transition: none !important; /* No transitions on iOS */
                    }
                    
                    .luxury-toggle-btn:active {
                        transform: scale(0.95) !important;
                    }
                    
                    .luxury-modal-text {
                        transition: none !important;
                    }
                }
                
                /* Standard Browsers */
                @media (hover: hover) {
                    .luxury-toggle-btn {
                        transition: all 0.2s ease;
                    }
                    
                    .luxury-toggle-btn:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 10px 30px rgba(0, 255, 136, 0.5);
                    }
                }
                
                /* Text States */
                .luxury-modal-text {
                    overflow: hidden;
                    max-height: 0;
                    opacity: 0;
                    padding: 0;
                }
                
                .luxury-modal-text.expanded {
                    max-height: none;
                    opacity: 1;
                    padding: 20px 0;
                }
                
                /* Close Button */
                .luxury-close-btn {
                    position: absolute;
                    top: 20px;
                    right: 20px;
                    width: 50px;
                    height: 50px;
                    background: rgba(255, 255, 255, 0.1);
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    color: #fff;
                    font-size: 24px;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 1000;
                    -webkit-tap-highlight-color: transparent;
                    touch-action: manipulation;
                }
            `;
            document.head.appendChild(style);
        }
        
        // 🎪 GLOBAL EVENT HANDLERS
        setupGlobalHandlers() {
            // Escape key to close
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.activeModal) {
                    this.closeModal();
                }
            });
            
            // Background click to close
            document.addEventListener('click', (e) => {
                if (e.target.classList.contains('luxury-modal')) {
                    this.closeModal();
                }
            });
        }
        
        // 🖼️ OPEN MODAL
        openModal(imageSrc, imageAlt, golfData) {
            this.log(`📸 Opening modal: ${imageSrc}`);
            
            // Clean up any existing modal
            this.closeModal();
            
            // Create modal structure
            const modal = this.createModalElement(imageSrc, imageAlt, golfData);
            document.body.appendChild(modal);
            
            // Force reflow before showing
            modal.offsetHeight;
            modal.classList.add('show');
            
            // Set as active
            this.activeModal = modal;
            
            // Setup toggle button
            const toggleBtn = modal.querySelector('.luxury-toggle-btn');
            const toggleText = modal.querySelector('.luxury-modal-text');
            
            if (toggleBtn && toggleText) {
                this.setupToggleButton(toggleBtn, toggleText);
            }
            
            // Prevent body scroll
            document.body.style.overflow = 'hidden';
            
            this.log('✅ Modal opened successfully');
        }
        
        // 🏗️ CREATE MODAL ELEMENT
        createModalElement(imageSrc, imageAlt, golfData) {
            const modal = document.createElement('div');
            modal.className = 'luxury-modal';
            
            const content = document.createElement('div');
            content.className = 'luxury-modal-content';
            
            // Parse golf data
            const data = this.parseGolfData(golfData);
            
            content.innerHTML = `
                <button class="luxury-close-btn" aria-label="Fermer">×</button>
                <div class="luxury-modal-body" style="padding: 30px;">
                    <img src="${imageSrc}" alt="${imageAlt}" style="width: 100%; height: auto; border-radius: 12px; margin-bottom: 20px;">
                    <h2 style="color: #00ff88; font-size: 28px; margin-bottom: 15px;">${data.name || imageAlt}</h2>
                    <div class="luxury-modal-info" style="color: #fff; margin-bottom: 20px;">
                        ${data.location ? `<p>📍 ${data.location}</p>` : ''}
                        ${data.rating ? `<p>⭐ ${data.rating}</p>` : ''}
                        ${data.price ? `<p>💰 ${data.price}</p>` : ''}
                    </div>
                    <button class="luxury-toggle-btn">En savoir plus ↓</button>
                    <div class="luxury-modal-text">
                        ${data.description || '<p>Description bientôt disponible.</p>'}
                    </div>
                </div>
            `;
            
            modal.appendChild(content);
            
            // Setup close button
            const closeBtn = content.querySelector('.luxury-close-btn');
            this.setupCloseButton(closeBtn);
            
            return modal;
        }        
        // 🔘 SETUP TOGGLE BUTTON - The Core Fix
        setupToggleButton(button, textElement) {
            this.log('🔧 Setting up toggle button');
            
            // Initialize state
            this.toggleStates.set(button, false);
            
            if (DEVICE.isIOSSafari) {
                this.setupIOSToggle(button, textElement);
            } else {
                this.setupStandardToggle(button, textElement);
            }
        }
        
        // 📱 iOS SAFARI SPECIFIC TOGGLE
        setupIOSToggle(button, textElement) {
            this.log('📱 iOS Safari mode - Using touchstart');
            
            // Remove any existing handlers
            const clone = button.cloneNode(true);
            button.parentNode.replaceChild(clone, button);
            button = clone;
            
            // Single touchstart handler - THE KEY TO iOS SUCCESS
            button.addEventListener('touchstart', (e) => {
                e.preventDefault();
                e.stopPropagation();
                
                // Immediate visual feedback
                button.style.transform = 'scale(0.95)';
                
                // Get current state
                const isExpanded = this.toggleStates.get(button) || false;
                
                // Toggle immediately
                if (isExpanded) {
                    // Collapse
                    textElement.classList.remove('expanded');
                    textElement.style.maxHeight = '0';
                    textElement.style.opacity = '0';
                    button.innerHTML = 'En savoir plus ↓';
                    this.toggleStates.set(button, false);
                } else {
                    // Expand
                    textElement.classList.add('expanded');
                    textElement.style.maxHeight = 'none';
                    textElement.style.opacity = '1';
                    button.innerHTML = 'Réduire ↑';
                    this.toggleStates.set(button, true);
                }
                
                // Reset button scale
                setTimeout(() => {
                    button.style.transform = 'scale(1)';
                }, 100);
                
                this.log(`✅ iOS Toggle complete - ${isExpanded ? 'Collapsed' : 'Expanded'}`);
            }, { passive: false });
            
            // Prevent default click behavior on iOS
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
            }, { passive: false });
        }        
        // 🖥️ STANDARD BROWSER TOGGLE
        setupStandardToggle(button, textElement) {
            this.log('🖥️ Standard browser mode - Using click');
            
            button.addEventListener('click', (e) => {
                e.preventDefault();
                
                const isExpanded = this.toggleStates.get(button) || false;
                
                if (isExpanded) {
                    textElement.classList.remove('expanded');
                    button.innerHTML = 'En savoir plus ↓';
                    this.toggleStates.set(button, false);
                } else {
                    textElement.classList.add('expanded');
                    button.innerHTML = 'Réduire ↑';
                    this.toggleStates.set(button, true);
                }
                
                this.log(`✅ Standard Toggle - ${isExpanded ? 'Collapsed' : 'Expanded'}`);
            });
        }
        
        // ❌ SETUP CLOSE BUTTON
        setupCloseButton(button) {
            const handler = (e) => {
                e.preventDefault();
                this.closeModal();
            };
            
            if (DEVICE.isIOSSafari) {
                button.addEventListener('touchstart', handler, { passive: false });
            } else {
                button.addEventListener('click', handler);
            }
        }        
        // 🚪 CLOSE MODAL
        closeModal() {
            if (!this.activeModal) return;
            
            this.log('🚪 Closing modal');
            
            this.activeModal.classList.remove('show');
            
            setTimeout(() => {
                this.activeModal?.remove();
                this.activeModal = null;
                document.body.style.overflow = '';
            }, CONFIG.ANIMATION_DURATION);
            
            // Clear states
            this.toggleStates.clear();
        }
        
        // 📊 PARSE GOLF DATA
        parseGolfData(dataString) {
            if (!dataString) return {};
            
            try {
                // Handle both JSON and URL encoded data
                if (dataString.startsWith('{')) {
                    return JSON.parse(dataString);
                } else {
                    return JSON.parse(decodeURIComponent(dataString));
                }
            } catch (e) {
                this.log('⚠️ Could not parse golf data', 'warn');
                return {};
            }
        }
        
        // 🪵 LOGGING
        log(message, type = 'info') {
            if (!CONFIG.DEBUG) return;
            
            const styles = {
                info: 'color: #00ff88',
                warn: 'color: #ffaa00',
                error: 'color: #ff3366',
                success: 'color: #00ff88; font-weight: bold'
            };
            
            console.log(`%c[LuxuryModal] ${message}`, styles[type] || styles.info);
        }
    }    
    // 🌍 GLOBAL INSTANCE
    window.luxuryModal = new LuxuryModal();
    
    // 🚀 GLOBAL FUNCTION FOR ONCLICK
    window.openSimpleModal = function(imageSrc, imageAlt, golfData) {
        // Silicon Valley style - defensive programming
        if (!imageSrc) {
            console.error('[LuxuryModal] No image source provided');
            return false;
        }
        
        // Clean image path
        imageSrc = imageSrc.replace(/^\.\//, '');
        
        // Open modal
        window.luxuryModal.openModal(imageSrc, imageAlt, golfData);
        
        return false; // Prevent default link behavior
    };
    
    // 🔄 BACKWARD COMPATIBILITY
    window.toggleDetails = function(button) {
        console.warn('[LuxuryModal] Legacy toggleDetails called - This should not happen with V2');
        // Find the associated text element
        const modalBody = button.closest('.luxury-modal-body');
        const textElement = modalBody?.querySelector('.luxury-modal-text');
        
        if (textElement) {
            window.luxuryModal.setupToggleButton(button, textElement);
            // Trigger click/touch
            if (DEVICE.isIOSSafari) {
                button.dispatchEvent(new TouchEvent('touchstart'));
            } else {
                button.click();
            }
        }
    };
    
    // 📱 MOBILE LANGUAGE SWITCH FIX
    document.addEventListener('DOMContentLoaded', function() {
        const languageSwitch = document.querySelector('.mobile-language-switch');
        
        if (languageSwitch && DEVICE.isIOSSafari) {
            languageSwitch.addEventListener('touchstart', function(e) {
                e.preventDefault();
                const currentLang = this.dataset.lang || 'fr';
                const newLang = currentLang === 'fr' ? 'en' : 'fr';
                
                this.dataset.lang = newLang;
                this.textContent = newLang === 'fr' ? '🇫🇷 FR' : '🇬🇧 EN';
                
                // Trigger language change
                window.dispatchEvent(new CustomEvent('languageChange', {
                    detail: { lang: newLang }
                }));
            }, { passive: false });
        }
    });
    
    // 🎯 INITIALIZATION LOG
    console.log('%c🚀 GolfinThai Luxury Modal V2 - Ready!', 'color: #00ff88; font-size: 16px; font-weight: bold');
    console.log('%c📱 Device:', 'color: #00ff88', DEVICE);
    
})();