/*
 * 🧪 SILICON VALLEY ULTIMATE FIX TESTER
 * Diagnostic instantané pour vérifier les fixes
 */

console.log('🔥 SILICON VALLEY ULTIMATE FIX TESTER ACTIVATED');

// Test 1: Vérifier les drapeaux
function testDrapeaux() {
    const flags = document.querySelectorAll('.flag-icon');
    console.log(`🏳️ Drapeaux trouvés: ${flags.length}`);
    
    flags.forEach((flag, index) => {
        const styles = window.getComputedStyle(flag);
        const dataLang = flag.dataset.lang;
        const background = styles.background;
        
        console.log(`🏳️ Drapeau ${index + 1}:`, {
            dataLang,
            width: styles.width,
            height: styles.height,
            background: background.substring(0, 100) + '...',
            hasOverflow: styles.overflow,
            fontSize: styles.fontSize
        });
    });
}

// Test 2: Vérifier les gradients rotatifs  
function testGradients() {
    const gradients = document.querySelectorAll('.text-gradient');
    console.log(`🌈 Éléments text-gradient trouvés: ${gradients.length}`);
    
    gradients.forEach((el, index) => {
        const styles = window.getComputedStyle(el);
        const animation = styles.animation;
        const background = styles.background;
        
        console.log(`🌈 Gradient ${index + 1}:`, {
            text: el.textContent.trim(),
            hasAnimation: animation.includes('quantumGradientRotationUltimate'),
            animationDuration: styles.animationDuration,
            background: background.substring(0, 100) + '...',
            webkitTextFillColor: styles.webkitTextFillColor
        });
    });
}

// Test 3: Vérifier les conflicts CSS
function testConflicts() {
    console.log('🔍 Diagnostic des conflits CSS...');
    
    // Test spécificité des drapeaux
    const testFlag = document.querySelector('.flag-icon');
    if (testFlag) {
        const styles = window.getComputedStyle(testFlag);
        console.log('🏳️ Spécificité drapeau OK:', {
            width: styles.width === '24px',
            height: styles.height === '18px',
            overflow: styles.overflow === 'hidden',
            fontSize: styles.fontSize === '0px'
        });
    }
    
    // Test animation gradients
    const testGradient = document.querySelector('.text-gradient');
    if (testGradient) {
        const styles = window.getComputedStyle(testGradient);
        console.log('🌈 Animation gradient OK:', {
            hasAnimation: styles.animationName.includes('quantumGradientRotationUltimate'),
            isTransparent: styles.webkitTextFillColor === 'transparent',
            hasBackgroundClip: styles.webkitBackgroundClip === 'text'
        });
    }
}

// Test 4: Performance check
function testPerformance() {
    const start = performance.now();
    
    // Forcer recalcul des styles
    document.querySelectorAll('.flag-icon, .text-gradient').forEach(el => {
        window.getComputedStyle(el).background;
    });
    
    const end = performance.now();
    console.log(`⚡ Performance CSS: ${Math.round(end - start)}ms`);
}

// Lancer tous les tests
function runAllTests() {
    console.log('🧪 === DÉBUT DES TESTS SILICON VALLEY ===');
    testDrapeaux();
    testGradients();
    testConflicts();
    testPerformance();
    console.log('🧪 === FIN DES TESTS ===');
}

// Lancer les tests quand le DOM est prêt
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllTests);
} else {
    runAllTests();
}

// Tests en continu pour debug
setInterval(() => {
    console.log('🔄 Test continu - Gradients animés:', 
        document.querySelectorAll('.text-gradient').length + ' éléments');
}, 5000);

// Export pour debug manuel
window.testSiliconValleyFix = {
    drapeaux: testDrapeaux,
    gradients: testGradients,
    conflicts: testConflicts,
    performance: testPerformance,
    all: runAllTests
};

console.log('✅ Silicon Valley Ultimate Fix Tester Ready!');
console.log('💡 Utilise: testSiliconValleyFix.all() pour relancer les tests');
