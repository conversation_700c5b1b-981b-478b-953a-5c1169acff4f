/**
 * 🎯 GOLF COURSES PREMIUM TEXT FORMATTER
 * Converts **bold** markdown syntax to HTML bold formatting
 */

// Function to format bold text in golf course descriptions
function formatGolfDescriptions() {
    console.log('🏆 Formatting golf course descriptions...');
    
    // Find all course description elements
    const descriptions = document.querySelectorAll('.course-description span[data-translate]');
    
    descriptions.forEach((element, index) => {
        const originalText = element.textContent;
        
        // Convert **text** to <strong>text</strong>
        const formattedText = originalText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        
        // Only update if there were changes
        if (formattedText !== originalText) {
            element.innerHTML = formattedText;
            console.log(`✅ Formatted description ${index + 1}: ${originalText.substring(0, 50)}...`);
        }
    });
    
    console.log('🎯 Golf course descriptions formatting completed!');
}

// Run formatting when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', formatGolfDescriptions);
} else {
    // DOM already loaded
    formatGolfDescriptions();
}

// Also run after any potential dynamic content loading
setTimeout(formatGolfDescriptions, 1000);

// Export for manual use if needed
window.formatGolfDescriptions = formatGolfDescriptions;