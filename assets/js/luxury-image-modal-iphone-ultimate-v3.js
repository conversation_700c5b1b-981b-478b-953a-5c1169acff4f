/**
 * 🚀 ULTRA PREMIUM IMAGE MODAL SYSTEM
 * Silicon Valley Edition - Zero BS, Maximum Premium Experience
 * 
 * @company GolfinThai
 * @version 3.0.0 - Premium Legend Edition + BILINGUAL
 * @performance 60fps guaranteed + Rich Legends
 * @compatibility Universal + iPhone Safari Perfect
 */

(function() {
    'use strict';
    
    // 🎯 PREMIUM CONFIGURATION
    const CONFIG = {
        DEBUG: true,
        ANIMATION_DURATION: 300,
        TOUCH_THRESHOLD: 300,
        MIN_TOUCH_TARGET: 44,
        DEBOUNCE_DELAY: 50,
        LEGEND_ANIMATION_DELAY: 200
    };
    
    // 📱 DEVICE DETECTION - One source of truth
    const DEVICE = {
        isIOS: /iPhone|iPad|iPod/.test(navigator.userAgent),
        isSafari: /Safari/.test(navigator.userAgent) && !/Chrome|CriOS/.test(navigator.userAgent),
        isIOSSafari: false,
        supportsTouch: 'ontouchstart' in window || navigator.maxTouchPoints > 0
    };
    DEVICE.isIOSSafari = DEVICE.isIOS && DEVICE.isSafari;
    
    // 🏆 PREMIUM IMAGE DATA - FRENCH VERSION (Original)
    const PREMIUM_IMAGE_DATA = {
        // Service Images
        'assets/images/acceuil1.jpg': {
            title: 'Accompagnement Sur Mesure',
            category: 'Service Premium',
            location: 'Expertise GolfinThaï',
            description: `
                <div class="premium-legend">
                    <h3>🎯 Service Personnalisé d'Exception</h3>
                    <p>Un accompagnement sur mesure qui s'adapte parfaitement à vos envies et votre vision du voyage idéal. Chaque détail est pensé pour créer votre expérience golf unique en Thaïlande, selon vos préférences et attentes.</p>
                    <div class="legend-highlights">
                        <div class="highlight-item">✨ Conception entièrement personnalisée</div>
                        <div class="highlight-item">🎯 Adaptation à vos préférences uniques</div>
                        <div class="highlight-item">🏌️ Expérience golf sur mesure exclusive</div>
                        <div class="highlight-item">👨‍🎓 Conseil expert personnalisé</div>
                        <div class="highlight-item">🌟 Excellence dans chaque détail</div>
                        <div class="highlight-item">🇹🇭 Authenticité thaïlandaise garantie</div>
                    </div>
                </div>
            `,
            tags: ['Service', 'Sur Mesure', 'Premium', 'Excellence']
        },

        'assets/images/Ayutthaya.webp': {
            title: 'Golf Historique Ayutthaya',
            category: 'Heritage Golf & Culture',
            location: 'Ayutthaya, Thaïlande',
            description: `
                <div class="premium-legend">
                    <h3>🏛️ Golf au Cœur de l'Histoire</h3>
                    <p>Découvrez l'expérience unique de jouer au golf dans l'ancienne capitale royale du Siam. Ayutthaya, site UNESCO du patrimoine mondial, offre un cadre historique exceptionnel pour vos parties de golf.</p>
                    <div class="legend-details">
                        <div class="detail-row"><strong>🏛️ Site UNESCO:</strong> Patrimoine mondial depuis 1991</div>
                        <div class="detail-row"><strong>👑 Histoire:</strong> Ancienne capitale du Siam (1351-1767)</div>
                        <div class="detail-row"><strong>🏌️ Golf Heritage:</strong> Parcours intégrés aux ruines historiques</div>
                        <div class="detail-row"><strong>📍 Localisation:</strong> 80km au nord de Bangkok</div>
                    </div>
                    <div class="legend-highlights">
                        <div class="highlight-item">🏛️ Temples historiques en bordure de parcours</div>
                        <div class="highlight-item">👑 Expérience golf culturelle unique</div>
                        <div class="highlight-item">📚 400 ans d'histoire royale</div>
                        <div class="highlight-item">🎨 Architecture Khmer authentique</div>
                        <div class="highlight-item">🌅 Sunrise sur les ruines anciennes</div>
                        <div class="highlight-item">🚤 Accessible par rivière depuis Bangkok</div>
                    </div>
                </div>
            `,
            tags: ['UNESCO', 'Histoire', 'Culture', 'Heritage Golf', 'Temples']
        },

        'assets/images/acceuil2.jpg': {
            title: 'Séjours Golf Personnalisés',
            category: 'Expertise Locale',
            location: 'Thaïlande',
            description: `
                <div class="premium-legend">
                    <h3>🌟 Expertise Locale Incomparable</h3>
                    <p>Bénéficiez de ma connaissance approfondie de la Thaïlande acquise après des années de vie sur place. Je vous guide vers les meilleurs parcours et expériences authentiques.</p>
                    <div class="legend-highlights">
                        <div class="highlight-item">🏆 Parcours d'exception</div>
                        <div class="highlight-item">🌍 Expériences authentiques</div>
                        <div class="highlight-item">👨‍🏫 Guide expert local</div>
                    </div>
                </div>
            `,
            tags: ['Expertise', 'Local', 'Personnalisé']
        },

        // Golf Courses - Championship Level
        'assets/images/AquellaGolfCountryClub.jpeg': {
            title: 'Aquella Golf & Country Club',
            category: 'Parcours Championship',
            location: 'Phang Nga, Thaïlande',
            description: `
                <div class="premium-legend">
                    <h3>🏖️ Renaissance Post-Tsunami Extraordinaire</h3>
                    <p>Parcours championship côtier unique construit sur les vestiges du tsunami de 2004, incarnant renaissance et résilience. Une histoire touchante transformée en beauté golfique.</p>
                    <div class="legend-details">
                        <div class="detail-row"><strong>🏌️ Parcours:</strong> 18 trous Championship</div>
                        <div class="detail-row"><strong>🏖️ Spécialité:</strong> 2,5 km de plage privée</div>
                        <div class="detail-row"><strong>🌊 Vue:</strong> Mer d'Andaman panoramique</div>
                        <div class="detail-row"><strong>🎋 Unique:</strong> Tunnels de bambou authentiques</div>
                    </div>
                    <div class="legend-highlights">
                        <div class="highlight-item">🌊 Vue océan à couper le souffle</div>
                        <div class="highlight-item">🏖️ Accès plage privée exclusive</div>
                        <div class="highlight-item">📚 Histoire inspirante de résilience</div>
                    </div>
                </div>
            `,
            tags: ['Championship', 'Océan', 'Unique', 'Histoire']
        },

        'assets/images/BlackMountainGolfClubHuHin.jpeg': {
            title: 'Black Mountain Golf Club',
            category: 'Parcours Championship',
            location: 'Hua Hin, Thaïlande',
            description: `
                <div class="premium-legend">
                    <h3>⛰️ Majesté des Montagnes Granitiques</h3>
                    <p>Parcours championship de renommée internationale niché au pied des montagnes granitiques noires. Architecture spectaculaire dans un cadre naturel préservé.</p>
                    <div class="legend-details">
                        <div class="detail-row"><strong>🏌️ Parcours:</strong> 27 trous (3 circuits)</div>
                        <div class="detail-row"><strong>🎯 Circuits:</strong> East, North, West</div>
                        <div class="detail-row"><strong>👨‍🎨 Architecte:</strong> Phil Ryan</div>
                        <div class="detail-row"><strong>⛰️ Cadre:</strong> Montagnes granitiques noires</div>
                    </div>
                    <div class="legend-highlights">
                        <div class="highlight-item">🏆 Renommée internationale</div>
                        <div class="highlight-item">🎨 Design Phil Ryan signature</div>
                        <div class="highlight-item">⛰️ Paysages montagneux dramatiques</div>
                    </div>
                </div>
            `,
            tags: ['Championship', 'Montagne', 'Phil Ryan', '27 Trous']
        },

        'assets/images/SantiburiSamuiCountryClubKohSamui.jpeg': {
            title: 'Santiburi Samui Country Club',
            category: 'Parcours Insulaire',
            location: 'Koh Samui, Thaïlande',
            description: `
                <div class="premium-legend">
                    <h3>🏝️ Seul Parcours de Koh Samui</h3>
                    <p>Parcours unique sculpté dans la topographie dramatique de l'île avec des dénivelés spectaculaires jusqu'à 180 mètres. Panoramas époustouflants sur le Golfe de Thaïlande.</p>
                    <div class="legend-details">
                        <div class="detail-row"><strong>🏌️ Parcours:</strong> 18 trous unique sur l'île</div>
                        <div class="detail-row"><strong>⛰️ Dénivelé:</strong> Jusqu'à 180 mètres</div>
                        <div class="detail-row"><strong>💧 Obstacles:</strong> 13 trous avec eau naturelle</div>
                        <div class="detail-row"><strong>🌊 Vue:</strong> Golfe de Thaïlande</div>
                    </div>
                    <div class="legend-highlights">
                        <div class="highlight-item">🏝️ Exclusivité île paradisiaque</div>
                        <div class="highlight-item">⛰️ Topographie dramatique</div>
                        <div class="highlight-item">💎 Cascades et obstacles naturels</div>
                    </div>
                </div>
            `,
            tags: ['Île', 'Unique', 'Dénivelé', 'Resort']
        },

        'assets/images/ChiangMaiHighlandsGolfSpaResortChiangMai.jpeg': {
            title: 'Chiang Mai Highlands Golf & Spa Resort',
            category: 'Golf Montagnard',
            location: 'Chiang Mai, Thaïlande',
            description: `
                <div class="premium-legend">
                    <h3>🏔️ Golf d'Altitude Spirituel</h3>
                    <p>Golf montagnard d'exception sur site spirituel historique. Design Schmidt-Curley avec climat frais d'altitude unique en Thaïlande et vues panoramiques 360°.</p>
                    <div class="legend-details">
                        <div class="detail-row"><strong>🏌️ Parcours:</strong> 27 trous (3 circuits)</div>
                        <div class="detail-row"><strong>🎯 Circuits:</strong> Valley, Highlands, Mountain</div>
                        <div class="detail-row"><strong>👨‍🎨 Architecte:</strong> Schmidt-Curley</div>
                        <div class="detail-row"><strong>🏔️ Spécialité:</strong> Climat montagnard frais</div>
                    </div>
                    <div class="legend-highlights">
                        <div class="highlight-item">🙏 Site spirituel historique</div>
                        <div class="highlight-item">🌡️ Climat frais unique</div>
                        <div class="highlight-item">360° Vues panoramiques montagnes</div>
                    </div>
                </div>
            `,
            tags: ['Montagne', 'Spirituel', 'Schmidt-Curley', 'Altitude']
        },

        'assets/images/RedMountainGolfClubPhuket.jpeg': {
            title: 'Red Mountain Golf Club',
            category: 'Parcours Révolutionnaire',
            location: 'Phuket, Thaïlande',
            description: `
                <div class="premium-legend">
                    <h3>💎 Mine d'Étain Transformée</h3>
                    <p>Parcours révolutionnaire sculpté dans une ancienne mine d'étain. Formations rocheuses rouges iconiques et le légendaire 17e trou "Drop Shot" avec chute de 50 mètres.</p>
                    <div class="legend-details">
                        <div class="detail-row"><strong>🏌️ Parcours:</strong> 18 trous révolutionnaire</div>
                        <div class="detail-row"><strong>⛰️ Origine:</strong> Ancienne mine d'étain</div>
                        <div class="detail-row"><strong>🎯 Trou légendaire:</strong> 17e "Drop Shot"</div>
                        <div class="detail-row"><strong>📏 Dénivelé:</strong> 150 pieds naturels</div>
                    </div>
                    <div class="legend-highlights">
                        <div class="highlight-item">🔴 Roches rouges iconiques</div>
                        <div class="highlight-item">⚡ Trou Drop Shot vertigineux</div>
                        <div class="highlight-item">🏭 Histoire minière unique</div>
                    </div>
                </div>
            `,
            tags: ['Révolutionnaire', 'Mine', 'Drop Shot', 'Unique']
        },

        'assets/images/blue-canyon-golf-course-thailand.jpg': {
            title: 'Blue Canyon Country Club',
            category: 'Institution Légendaire',
            location: 'Phuket, Thaïlande',
            description: `
                <div class="premium-legend">
                    <h3>🏆 Légende du Golf Mondial</h3>
                    <p>Institution légendaire où ont joué Tiger Woods et Greg Norman. Architecture technique Yoshikazu Kato et le parcours le plus prestigieux de Phuket.</p>
                    <div class="legend-details">
                        <div class="detail-row"><strong>🏌️ Parcours:</strong> 2 parcours Championship</div>
                        <div class="detail-row"><strong>🎯 Courses:</strong> Canyon Course & Lakes Course</div>
                        <div class="detail-row"><strong>👨‍🎨 Architecte:</strong> Yoshikazu Kato</div>
                        <div class="detail-row"><strong>🌟 Célébrités:</strong> Tiger Woods, Greg Norman</div>
                    </div>
                    <div class="legend-highlights">
                        <div class="highlight-item">🐅 Terrain de Tiger Woods</div>
                        <div class="highlight-item">🏆 Prestige international</div>
                        <div class="highlight-item">✈️ Proximité aéroport</div>
                    </div>
                </div>
            `,
            tags: ['Légende', 'Tiger Woods', 'Championship', 'Prestige']
        },

        // Cultural & Founder Images
        'assets/images/missionomg.jpg': {
            title: 'Expertise Golf et Parcours d\'Exception',
            category: 'Expertise GolfinThaï',
            location: 'Thaïlande',
            description: `
                <div class="premium-legend">
                    <h3>🎯 Passion Golf & Expertise Terrain</h3>
                    <p>Découvrez l'engagement GolfinThaï : une passion pour le golf nourrie par des années d'expérience sur les plus beaux parcours de Thaïlande.</p>
                    <div class="legend-highlights">
                        <div class="highlight-item">🏌️ Passion golf authentique</div>
                        <div class="highlight-item">🌍 Années d'expérience terrain</div>
                        <div class="highlight-item">🎯 Expertise parcours premium</div>
                    </div>
                </div>
            `,
            tags: ['Expertise', 'Passion', 'Golf']
        },

        'assets/images/hl03-high.jpg': {
            title: 'Golf Premium Vues Époustouflantes',
            category: 'Paysages Golf',
            location: 'Thaïlande',
            description: `
                <div class="premium-legend">
                    <h3>🌅 Paysages Golf Époustouflants</h3>
                    <p>Les parcours de golf thaïlandais offrent des panoramas à couper le souffle, mêlant sport et beauté naturelle dans un cadre idyllique.</p>
                    <div class="legend-highlights">
                        <div class="highlight-item">🌅 Vues panoramiques exceptionnelles</div>
                        <div class="highlight-item">🏞️ Beauté naturelle préservée</div>
                        <div class="highlight-item">⛳ Golf dans cadre idyllique</div>
                    </div>
                </div>
            `,
            tags: ['Paysage', 'Premium', 'Vue']
        },

        'assets/images/watphrakaew-high.jpg': {
            title: 'Wat Phra Kaew - Temple du Bouddha d\'Émeraude',
            category: 'Culture Thaïlandaise',
            location: 'Bangkok, Thaïlande',
            description: `
                <div class="premium-legend">
                    <h3>🏛️ Joyau Culturel de Bangkok</h3>
                    <p>Le Temple du Bouddha d'Émeraude, site le plus sacré de Thaïlande. Architecture royale éblouissante et spiritualité profonde au cœur de Bangkok.</p>
                    <div class="legend-details">
                        <div class="detail-row"><strong>🏛️ Temple:</strong> Wat Phra Kaew</div>
                        <div class="detail-row"><strong>💎 Trésor:</strong> Bouddha d'Émeraude</div>
                        <div class="detail-row"><strong>👑 Statut:</strong> Temple royal le plus sacré</div>
                        <div class="detail-row"><strong>📍 Localisation:</strong> Grand Palais, Bangkok</div>
                    </div>
                    <div class="legend-highlights">
                        <div class="highlight-item">👑 Site le plus sacré de Thaïlande</div>
                        <div class="highlight-item">💎 Bouddha d'Émeraude légendaire</div>
                        <div class="highlight-item">🏛️ Architecture royale éblouissante</div>
                    </div>
                </div>
            `,
            tags: ['Temple', 'Sacré', 'Bangkok', 'Culture']
        },

        'assets/images/PR.jpg': {
            title: 'Sébastien Marciano - Fondateur GolfinThaï',
            category: 'Fondateur Expert',
            location: 'Expert Thaïlande',
            description: `
                <div class="premium-legend">
                    <h3>👨‍🎓 Expert Passionné Thaïlande</h3>
                    <p>Sébastien Marciano, fondateur de GolfinThaï. Plus de 20 ans d'expérience, 10 ans de vie en Thaïlande, et une passion pour créer des voyages golf d'exception.</p>
                    <div class="legend-details">
                        <div class="detail-row"><strong>👨‍💼 Fondateur:</strong> Sébastien Marciano</div>
                        <div class="detail-row"><strong>🗓️ Expérience:</strong> 20+ années</div>
                        <div class="detail-row"><strong>🇹🇭 Vécu:</strong> 10 ans en Thaïlande</div>
                        <div class="detail-row"><strong>🏌️ Parcours:</strong> 50+ terrains explorés</div>
                    </div>
                    <div class="legend-highlights">
                        <div class="highlight-item">🎯 Vision voyages sur mesure</div>
                        <div class="highlight-item">🌍 Expertise locale approfondie</div>
                        <div class="highlight-item">🏌️ Passion golf authentique</div>
                    </div>
                </div>
            `,
            tags: ['Fondateur', 'Expert', 'Passion', 'Thaïlande']
        },

        // Nouvelles images
        'assets/images/nouvelleimage1.jpeg': {
            title: 'Parcours de Golf Premium Thaïlande',
            category: 'Golf d\'Exception',
            location: 'Thaïlande',
            description: `
                <div class="premium-legend">
                    <h3>🏌️ Excellence Golfique Thaïlandaise</h3>
                    <p>Découvrez l'un des nouveaux joyaux du golf thaïlandais, alliant design moderne et intégration parfaite dans les paysages tropicaux.</p>
                    <div class="legend-highlights">
                        <div class="highlight-item">🌟 Design contemporain exceptionnel</div>
                        <div class="highlight-item">🌴 Intégration paysages tropicaux</div>
                        <div class="highlight-item">🏆 Standards internationaux</div>
                    </div>
                </div>
            `,
            tags: ['Premium', 'Nouveau', 'Design', 'Excellence']
        },

        'assets/images/nouvelleimage2.jpeg': {
            title: 'Nouveau Parcours Golf Exclusif',
            category: 'Golf Exclusif',
            location: 'Thaïlande',
            description: `
                <div class="premium-legend">
                    <h3>💎 Exclusivité Golf Tropicale</h3>
                    <p>Un parcours exclusif pensé pour les golfeurs les plus exigeants, offrant une expérience unique dans un cadre tropical préservé.</p>
                    <div class="legend-highlights">
                        <div class="highlight-item">💎 Accès exclusif privilégié</div>
                        <div class="highlight-item">🌺 Cadre tropical préservé</div>
                        <div class="highlight-item">🎯 Conçu pour experts</div>
                    </div>
                </div>
            `,
            tags: ['Exclusif', 'Tropical', 'Premium', 'Unique']
        },

        'assets/images/nouvelleimage3.jpeg': {
            title: 'Nouveau Parcours Golf Exclusif Vista',
            category: 'Golf Vista',
            location: 'Thaïlande',
            description: `
                <div class="premium-legend">
                    <h3>🌅 Vistas Golf Spectaculaires</h3>
                    <p>Parcours offrant des vues spectaculaires et une expérience golf immersive dans la beauté naturelle thaïlandaise.</p>
                    <div class="legend-highlights">
                        <div class="highlight-item">🌅 Vues panoramiques exceptionnelles</div>
                        <div class="highlight-item">🏞️ Immersion nature totale</div>
                        <div class="highlight-item">📸 Moments instagram parfaits</div>
                    </div>
                </div>
            `,
            tags: ['Vista', 'Panoramique', 'Nature', 'Spectaculaire']
        }
    };

    // 🇬🇧 PREMIUM IMAGE DATA - ENGLISH VERSION
    const PREMIUM_IMAGE_DATA_EN = {
        // Service Images
        'assets/images/acceuil1.jpg': {
            title: 'Tailored Accompaniment',
            category: 'Premium Service',
            location: 'GolfinThai Expertise',
            description: `
                <div class="premium-legend">
                    <h3>🎯 Exceptional Personalized Service</h3>
                    <p>Tailored accompaniment that perfectly adapts to your desires and vision of the ideal trip. Every detail is thoughtfully planned to create your unique golf experience in Thailand, according to your preferences and expectations.</p>
                    <div class="legend-highlights">
                        <div class="highlight-item">✨ Completely personalized design</div>
                        <div class="highlight-item">🎯 Adaptation to your unique preferences</div>
                        <div class="highlight-item">🏌️ Exclusive tailored golf experience</div>
                        <div class="highlight-item">👨‍🎓 Personalized expert advice</div>
                        <div class="highlight-item">🌟 Excellence in every detail</div>
                        <div class="highlight-item">🇹🇭 Guaranteed Thai authenticity</div>
                    </div>
                </div>
            `,
            tags: ['Service', 'Tailored', 'Premium', 'Excellence']
        },

        'assets/images/Ayutthaya.webp': {
            title: 'Ayutthaya Heritage Golf',
            category: 'Heritage Golf & Culture',
            location: 'Ayutthaya, Thailand',
            description: `
                <div class="premium-legend">
                    <h3>🏛️ Golf at the Heart of History</h3>
                    <p>Discover the unique experience of playing golf in the ancient royal capital of Siam. Ayutthaya, a UNESCO World Heritage site, offers an exceptional historical setting for your golf rounds.</p>
                    <div class="legend-details">
                        <div class="detail-row"><strong>🏛️ UNESCO Site:</strong> World Heritage since 1991</div>
                        <div class="detail-row"><strong>👑 History:</strong> Former capital of Siam (1351-1767)</div>
                        <div class="detail-row"><strong>🏌️ Heritage Golf:</strong> Courses integrated with historical ruins</div>
                        <div class="detail-row"><strong>📍 Location:</strong> 80km north of Bangkok</div>
                    </div>
                    <div class="legend-highlights">
                        <div class="highlight-item">🏛️ Historic temples along the course</div>
                        <div class="highlight-item">👑 Unique cultural golf experience</div>
                        <div class="highlight-item">📚 400 years of royal history</div>
                        <div class="highlight-item">🎨 Authentic Khmer architecture</div>
                        <div class="highlight-item">🌅 Sunrise over ancient ruins</div>
                        <div class="highlight-item">🚤 River access from Bangkok</div>
                    </div>
                </div>
            `,
            tags: ['UNESCO', 'History', 'Culture', 'Heritage Golf', 'Temples']
        },

        'assets/images/acceuil2.jpg': {
            title: 'Personalized Golf Stays',
            category: 'Local Expertise',
            location: 'Thailand',
            description: `
                <div class="premium-legend">
                    <h3>🌟 Incomparable Local Expertise</h3>
                    <p>Benefit from my deep knowledge of Thailand acquired after years of living there. I guide you to the best courses and authentic experiences.</p>
                    <div class="legend-highlights">
                        <div class="highlight-item">🏆 Exceptional courses</div>
                        <div class="highlight-item">🌍 Authentic experiences</div>
                        <div class="highlight-item">👨‍🏫 Local expert guide</div>
                    </div>
                </div>
            `,
            tags: ['Expertise', 'Local', 'Personalized']
        },

        // Golf Courses - Championship Level
        'assets/images/AquellaGolfCountryClub.jpeg': {
            title: 'Aquella Golf & Country Club',
            category: 'Championship Course',
            location: 'Phang Nga, Thailand',
            description: `
                <div class="premium-legend">
                    <h3>🏖️ Extraordinary Post-Tsunami Renaissance</h3>
                    <p>Unique coastal championship course built on the remnants of the 2004 tsunami, embodying renaissance and resilience. A touching story transformed into golfing beauty.</p>
                    <div class="legend-details">
                        <div class="detail-row"><strong>🏌️ Course:</strong> 18-hole Championship</div>
                        <div class="detail-row"><strong>🏖️ Specialty:</strong> 2.5 km private beach</div>
                        <div class="detail-row"><strong>🌊 View:</strong> Panoramic Andaman Sea</div>
                        <div class="detail-row"><strong>🎋 Unique:</strong> Authentic bamboo tunnels</div>
                    </div>
                    <div class="legend-highlights">
                        <div class="highlight-item">🌊 Breathtaking ocean views</div>
                        <div class="highlight-item">🏖️ Exclusive private beach access</div>
                        <div class="highlight-item">📚 Inspiring story of resilience</div>
                    </div>
                </div>
            `,
            tags: ['Championship', 'Ocean', 'Unique', 'History']
        },

        'assets/images/BlackMountainGolfClubHuHin.jpeg': {
            title: 'Black Mountain Golf Club',
            category: 'Championship Course',
            location: 'Hua Hin, Thailand',
            description: `
                <div class="premium-legend">
                    <h3>⛰️ Majesty of Granite Mountains</h3>
                    <p>Internationally renowned championship course nestled at the foot of black granite mountains. Spectacular architecture in a pristine natural setting.</p>
                    <div class="legend-details">
                        <div class="detail-row"><strong>🏌️ Course:</strong> 27 holes (3 courses)</div>
                        <div class="detail-row"><strong>🎯 Courses:</strong> East, North, West</div>
                        <div class="detail-row"><strong>👨‍🎨 Architect:</strong> Phil Ryan</div>
                        <div class="detail-row"><strong>⛰️ Setting:</strong> Black granite mountains</div>
                    </div>
                    <div class="legend-highlights">
                        <div class="highlight-item">🏆 International reputation</div>
                        <div class="highlight-item">🎨 Phil Ryan signature design</div>
                        <div class="highlight-item">⛰️ Dramatic mountain landscapes</div>
                    </div>
                </div>
            `,
            tags: ['Championship', 'Mountain', 'Phil Ryan', '27 Holes']
        },

        'assets/images/SantiburiSamuiCountryClubKohSamui.jpeg': {
            title: 'Santiburi Samui Country Club',
            category: 'Island Course',
            location: 'Koh Samui, Thailand',
            description: `
                <div class="premium-legend">
                    <h3>🏝️ Koh Samui's Only Course</h3>
                    <p>Unique course carved into the island's dramatic topography with spectacular elevation changes up to 180 meters. Breathtaking panoramas over the Gulf of Thailand.</p>
                    <div class="legend-details">
                        <div class="detail-row"><strong>🏌️ Course:</strong> 18 holes unique on the island</div>
                        <div class="detail-row"><strong>⛰️ Elevation:</strong> Up to 180 meters</div>
                        <div class="detail-row"><strong>💧 Hazards:</strong> 13 holes with natural water</div>
                        <div class="detail-row"><strong>🌊 View:</strong> Gulf of Thailand</div>
                    </div>
                    <div class="legend-highlights">
                        <div class="highlight-item">🏝️ Paradise island exclusivity</div>
                        <div class="highlight-item">⛰️ Dramatic topography</div>
                        <div class="highlight-item">💎 Waterfalls and natural hazards</div>
                    </div>
                </div>
            `,
            tags: ['Island', 'Unique', 'Elevation', 'Resort']
        },

        'assets/images/ChiangMaiHighlandsGolfSpaResortChiangMai.jpeg': {
            title: 'Chiang Mai Highlands Golf & Spa Resort',
            category: 'Mountain Golf',
            location: 'Chiang Mai, Thailand',
            description: `
                <div class="premium-legend">
                    <h3>🏔️ Spiritual Highland Golf</h3>
                    <p>Exceptional mountain golf on historic spiritual site. Schmidt-Curley design with cool highland climate unique in Thailand and 360° panoramic views.</p>
                    <div class="legend-details">
                        <div class="detail-row"><strong>🏌️ Course:</strong> 27 holes (3 courses)</div>
                        <div class="detail-row"><strong>🎯 Courses:</strong> Valley, Highlands, Mountain</div>
                        <div class="detail-row"><strong>👨‍🎨 Architect:</strong> Schmidt-Curley</div>
                        <div class="detail-row"><strong>🏔️ Specialty:</strong> Cool mountain climate</div>
                    </div>
                    <div class="legend-highlights">
                        <div class="highlight-item">🙏 Historic spiritual site</div>
                        <div class="highlight-item">🌡️ Unique cool climate</div>
                        <div class="highlight-item">360° Mountain panoramic views</div>
                    </div>
                </div>
            `,
            tags: ['Mountain', 'Spiritual', 'Schmidt-Curley', 'Highland']
        },

        'assets/images/RedMountainGolfClubPhuket.jpeg': {
            title: 'Red Mountain Golf Club',
            category: 'Revolutionary Course',
            location: 'Phuket, Thailand',
            description: `
                <div class="premium-legend">
                    <h3>💎 Transformed Tin Mine</h3>
                    <p>Revolutionary course carved from an old tin mine. Iconic red rock formations and the legendary 17th hole "Drop Shot" with a 50-meter drop.</p>
                    <div class="legend-details">
                        <div class="detail-row"><strong>🏌️ Course:</strong> 18-hole revolutionary</div>
                        <div class="detail-row"><strong>⛰️ Origin:</strong> Former tin mine</div>
                        <div class="detail-row"><strong>🎯 Legendary hole:</strong> 17th "Drop Shot"</div>
                        <div class="detail-row"><strong>📏 Elevation:</strong> 150 feet natural</div>
                    </div>
                    <div class="legend-highlights">
                        <div class="highlight-item">🔴 Iconic red rocks</div>
                        <div class="highlight-item">⚡ Vertiginous Drop Shot hole</div>
                        <div class="highlight-item">🏭 Unique mining history</div>
                    </div>
                </div>
            `,
            tags: ['Revolutionary', 'Mine', 'Drop Shot', 'Unique']
        },

        'assets/images/blue-canyon-golf-course-thailand.jpg': {
            title: 'Blue Canyon Country Club',
            category: 'Legendary Institution',
            location: 'Phuket, Thailand',
            description: `
                <div class="premium-legend">
                    <h3>🏆 World Golf Legend</h3>
                    <p>Legendary institution where Tiger Woods and Greg Norman have played. Technical Yoshikazu Kato architecture and Phuket's most prestigious course.</p>
                    <div class="legend-details">
                        <div class="detail-row"><strong>🏌️ Course:</strong> 2 Championship courses</div>
                        <div class="detail-row"><strong>🎯 Courses:</strong> Canyon Course & Lakes Course</div>
                        <div class="detail-row"><strong>👨‍🎨 Architect:</strong> Yoshikazu Kato</div>
                        <div class="detail-row"><strong>🌟 Celebrities:</strong> Tiger Woods, Greg Norman</div>
                    </div>
                    <div class="legend-highlights">
                        <div class="highlight-item">🐅 Tiger Woods' playground</div>
                        <div class="highlight-item">🏆 International prestige</div>
                        <div class="highlight-item">✈️ Airport proximity</div>
                    </div>
                </div>
            `,
            tags: ['Legend', 'Tiger Woods', 'Championship', 'Prestige']
        },

        // Cultural & Founder Images
        'assets/images/missionomg.jpg': {
            title: 'Golf Expertise and Exceptional Courses',
            category: 'GolfinThai Expertise',
            location: 'Thailand',
            description: `
                <div class="premium-legend">
                    <h3>🎯 Golf Passion & Field Expertise</h3>
                    <p>Discover GolfinThai's commitment: a passion for golf nourished by years of experience on Thailand's most beautiful courses.</p>
                    <div class="legend-highlights">
                        <div class="highlight-item">🏌️ Authentic golf passion</div>
                        <div class="highlight-item">🌍 Years of field experience</div>
                        <div class="highlight-item">🎯 Premium course expertise</div>
                    </div>
                </div>
            `,
            tags: ['Expertise', 'Passion', 'Golf']
        },

        'assets/images/hl03-high.jpg': {
            title: 'Premium Golf Breathtaking Views',
            category: 'Golf Landscapes',
            location: 'Thailand',
            description: `
                <div class="premium-legend">
                    <h3>🌅 Breathtaking Golf Landscapes</h3>
                    <p>Thai golf courses offer breathtaking panoramas, blending sport and natural beauty in an idyllic setting.</p>
                    <div class="legend-highlights">
                        <div class="highlight-item">🌅 Exceptional panoramic views</div>
                        <div class="highlight-item">🏞️ Preserved natural beauty</div>
                        <div class="highlight-item">⛳ Golf in idyllic setting</div>
                    </div>
                </div>
            `,
            tags: ['Landscape', 'Premium', 'View']
        },

        'assets/images/watphrakaew-high.jpg': {
            title: 'Wat Phra Kaew - Temple of the Emerald Buddha',
            category: 'Thai Culture',
            location: 'Bangkok, Thailand',
            description: `
                <div class="premium-legend">
                    <h3>🏛️ Cultural Jewel of Bangkok</h3>
                    <p>The Temple of the Emerald Buddha, Thailand's most sacred site. Dazzling royal architecture and deep spirituality in the heart of Bangkok.</p>
                    <div class="legend-details">
                        <div class="detail-row"><strong>🏛️ Temple:</strong> Wat Phra Kaew</div>
                        <div class="detail-row"><strong>💎 Treasure:</strong> Emerald Buddha</div>
                        <div class="detail-row"><strong>👑 Status:</strong> Most sacred royal temple</div>
                        <div class="detail-row"><strong>📍 Location:</strong> Grand Palace, Bangkok</div>
                    </div>
                    <div class="legend-highlights">
                        <div class="highlight-item">👑 Thailand's most sacred site</div>
                        <div class="highlight-item">💎 Legendary Emerald Buddha</div>
                        <div class="highlight-item">🏛️ Dazzling royal architecture</div>
                    </div>
                </div>
            `,
            tags: ['Temple', 'Sacred', 'Bangkok', 'Culture']
        },

        'assets/images/PR.jpg': {
            title: 'Sébastien Marciano - GolfinThai Founder',
            category: 'Expert Founder',
            location: 'Thailand Expert',
            description: `
                <div class="premium-legend">
                    <h3>👨‍🎓 Passionate Thailand Expert</h3>
                    <p>Sébastien Marciano, founder of GolfinThai. Over 20 years of experience, 10 years living in Thailand, and a passion for creating exceptional golf trips.</p>
                    <div class="legend-details">
                        <div class="detail-row"><strong>👨‍💼 Founder:</strong> Sébastien Marciano</div>
                        <div class="detail-row"><strong>🗓️ Experience:</strong> 20+ years</div>
                        <div class="detail-row"><strong>🇹🇭 Lived:</strong> 10 years in Thailand</div>
                        <div class="detail-row"><strong>🏌️ Courses:</strong> 50+ explored</div>
                    </div>
                    <div class="legend-highlights">
                        <div class="highlight-item">🎯 Tailored travel vision</div>
                        <div class="highlight-item">🌍 Deep local expertise</div>
                        <div class="highlight-item">🏌️ Authentic golf passion</div>
                    </div>
                </div>
            `,
            tags: ['Founder', 'Expert', 'Passion', 'Thailand']
        },

        // New images
        'assets/images/nouvelleimage1.jpeg': {
            title: 'Premium Golf Course Thailand',
            category: 'Exceptional Golf',
            location: 'Thailand',
            description: `
                <div class="premium-legend">
                    <h3>🏌️ Thai Golf Excellence</h3>
                    <p>Discover one of Thailand's new golf gems, combining modern design with perfect integration into tropical landscapes.</p>
                    <div class="legend-highlights">
                        <div class="highlight-item">🌟 Exceptional contemporary design</div>
                        <div class="highlight-item">🌴 Tropical landscape integration</div>
                        <div class="highlight-item">🏆 International standards</div>
                    </div>
                </div>
            `,
            tags: ['Premium', 'New', 'Design', 'Excellence']
        },

        'assets/images/nouvelleimage2.jpeg': {
            title: 'New Exclusive Golf Course',
            category: 'Exclusive Golf',
            location: 'Thailand',
            description: `
                <div class="premium-legend">
                    <h3>💎 Tropical Golf Exclusivity</h3>
                    <p>An exclusive course designed for the most demanding golfers, offering a unique experience in a pristine tropical setting.</p>
                    <div class="legend-highlights">
                        <div class="highlight-item">💎 Privileged exclusive access</div>
                        <div class="highlight-item">🌺 Pristine tropical setting</div>
                        <div class="highlight-item">🎯 Designed for experts</div>
                    </div>
                </div>
            `,
            tags: ['Exclusive', 'Tropical', 'Premium', 'Unique']
        },

        'assets/images/nouvelleimage3.jpeg': {
            title: 'New Exclusive Golf Course Vista',
            category: 'Golf Vista',
            location: 'Thailand',
            description: `
                <div class="premium-legend">
                    <h3>🌅 Spectacular Golf Vistas</h3>
                    <p>Course offering spectacular views and an immersive golf experience in Thailand's natural beauty.</p>
                    <div class="legend-highlights">
                        <div class="highlight-item">🌅 Exceptional panoramic views</div>
                        <div class="highlight-item">🏞️ Total nature immersion</div>
                        <div class="highlight-item">📸 Perfect Instagram moments</div>
                    </div>
                </div>
            `,
            tags: ['Vista', 'Panoramic', 'Nature', 'Spectacular']
        }
    };

    // 🌍 LANGUAGE-AWARE IMAGE DATA FUNCTION - Same concept as your translation system
    function getImageData(imagePath) {
        const currentLang = window.translationManager?.currentLanguage || 'fr';
        
        // Debug path matching
        if (CONFIG.DEBUG) {
            console.log(`🌍 [Image Modal] Language: ${currentLang}, Path: "${imagePath}"`);
            console.log(`🔍 [Image Modal] French data exists: ${!!PREMIUM_IMAGE_DATA[imagePath]}`);
            console.log(`🔍 [Image Modal] English data exists: ${!!PREMIUM_IMAGE_DATA_EN[imagePath]}`);
        }
        
        // Use English data if language is EN and data exists, otherwise use French (original)
        if (currentLang === 'en' && PREMIUM_IMAGE_DATA_EN[imagePath]) {
            if (CONFIG.DEBUG) console.log(`✅ [Image Modal] Using English data`);
            return PREMIUM_IMAGE_DATA_EN[imagePath];
        }
        
        // Fallback to original French data
        if (CONFIG.DEBUG) console.log(`✅ [Image Modal] Using French data`);
        return PREMIUM_IMAGE_DATA[imagePath];
    }

    // 🌍 LANGUAGE-AWARE BUTTON TEXT FUNCTION - ELEGANT VERSION
    function getToggleButtonText(isExpanded) {
        const currentLang = window.translationManager?.currentLanguage || 'fr';
        
        if (currentLang === 'en') {
            return isExpanded ? 'Reduce Details' : 'Discover Details';
        } else {
            return isExpanded ? 'Réduire les Détails' : 'Découvrir les Détails';
        }
    }
    
    // 🔥 ULTRA PREMIUM MODAL CLASS
    class UltraPremiumModal {
        constructor() {
            this.activeModal = null;
            this.toggleStates = new Map();
            this.init();
        }
        
        init() {
            this.log('🚀 Initializing Ultra Premium Modal System');
            this.setupStyles();
            this.setupGlobalHandlers();
            this.log(`📱 Device: ${DEVICE.isIOSSafari ? 'iPhone Safari' : 'Standard Browser'}`);
        }
        
        // 💅 ULTRA PREMIUM STYLES
        setupStyles() {
            const style = document.createElement('style');
            style.textContent = `
                /* Ultra Premium Modal Base */
                .ultra-premium-modal {
                    display: none;
                    position: fixed;
                    inset: 0;
                    z-index: 999999;
                    background: rgba(0, 0, 0, 0.95);
                    -webkit-backdrop-filter: blur(20px);
                    backdrop-filter: blur(20px);
                    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
                }
                
                .ultra-premium-modal.show {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    animation: modalFadeIn ${CONFIG.ANIMATION_DURATION}ms ease-out;
                }
                
                @keyframes modalFadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
                
                .ultra-premium-modal-content {
                    position: relative;
                    width: 95%;
                    max-width: 1400px;
                    max-height: 95vh;
                    background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
                    border-radius: 24px;
                    overflow: hidden;
                    box-shadow: 0 30px 80px rgba(0, 255, 136, 0.3), 
                                0 0 0 1px rgba(255, 255, 255, 0.1);
                    transform: translateZ(0);
                    animation: modalSlideIn ${CONFIG.ANIMATION_DURATION}ms ease-out;
                }
                
                @keyframes modalSlideIn {
                    from { 
                        opacity: 0; 
                        transform: translateY(50px) scale(0.9); 
                    }
                    to { 
                        opacity: 1; 
                        transform: translateY(0) scale(1); 
                    }
                }
                
                .ultra-premium-modal-body {
                    padding: 40px;
                    max-height: 95vh;
                    overflow-y: auto;
                    -webkit-overflow-scrolling: touch;
                }
                
                .premium-modal-image {
                    width: 100%;
                    height: auto;
                    max-height: 60vh;
                    object-fit: cover;
                    border-radius: 16px;
                    margin-bottom: 30px;
                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
                }
                
                .premium-modal-header {
                    text-align: center;
                    margin-bottom: 30px;
                }
                
                .premium-modal-title {
                    font-size: 32px;
                    font-weight: 700;
                    background: linear-gradient(135deg, #00ff88, #00cc66);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                    margin-bottom: 10px;
                    line-height: 1.2;
                }
                
                .premium-modal-category {
                    display: inline-block;
                    padding: 8px 20px;
                    background: rgba(0, 255, 136, 0.1);
                    border: 1px solid rgba(0, 255, 136, 0.3);
                    border-radius: 50px;
                    color: #00ff88;
                    font-size: 14px;
                    font-weight: 500;
                    margin-bottom: 15px;
                }
                
                .premium-modal-location {
                    color: #999;
                    font-size: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 8px;
                }
                
                .premium-legend {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 16px;
                    padding: 0;
                    overflow: hidden;
                    margin-top: 20px;
                }
                
                .premium-legend h3 {
                    font-size: 24px;
                    color: #00ff88;
                    margin-bottom: 15px;
                    font-weight: 600;
                }
                
                .premium-legend p {
                    color: #e0e0e0;
                    line-height: 1.6;
                    margin-bottom: 20px;
                    font-size: 16px;
                }
                
                .legend-details {
                    margin: 20px 0;
                }
                
                .detail-row {
                    display: flex;
                    align-items: center;
                    padding: 10px 0;
                    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                    color: #d0d0d0;
                    font-size: 15px;
                }
                
                .detail-row:last-child {
                    border-bottom: none;
                }
                
                .detail-row strong {
                    color: #00ff88;
                    margin-right: 10px;
                    min-width: 120px;
                }
                
                .legend-highlights {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 15px;
                    margin-top: 20px;
                }
                
                .highlight-item {
                    background: rgba(0, 255, 136, 0.1);
                    border-left: 3px solid #00ff88;
                    padding: 12px 16px;
                    border-radius: 8px;
                    color: #f0f0f0;
                    font-size: 14px;
                    font-weight: 500;
                }
                
                .premium-tags {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 10px;
                    margin-top: 20px;
                    justify-content: center;
                }
                
                .premium-tag {
                    padding: 6px 14px;
                    background: linear-gradient(135deg, rgba(0, 255, 136, 0.2), rgba(0, 204, 102, 0.2));
                    border: 1px solid rgba(0, 255, 136, 0.4);
                    border-radius: 20px;
                    color: #00ff88;
                    font-size: 12px;
                    font-weight: 500;
                }
                
                .ultra-premium-toggle-btn {
                    display: block;
                    margin: 30px auto 20px;
                    padding: 16px 32px;
                    background: linear-gradient(135deg, #00ff88, #00cc66);
                    border: none;
                    border-radius: 50px;
                    color: #000;
                    font-weight: 700;
                    font-size: 16px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    box-shadow: 0 10px 30px rgba(0, 255, 136, 0.3);
                    min-height: ${CONFIG.MIN_TOUCH_TARGET}px;
                    min-width: 200px;
                    -webkit-tap-highlight-color: transparent;
                    -webkit-touch-callout: none;
                    -webkit-user-select: none;
                    touch-action: manipulation;
                }
                
                .ultra-premium-toggle-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 15px 40px rgba(0, 255, 136, 0.4);
                }
                
                .ultra-premium-toggle-btn:active {
                    transform: translateY(0) scale(0.98);
                }
                
                .premium-legend-content {
                    max-height: 0;
                    opacity: 0;
                    overflow: hidden;
                    transition: all 0.4s ease;
                    padding: 0 30px;
                }
                
                .premium-legend-content.expanded {
                    max-height: 2000px;
                    opacity: 1;
                    padding: 30px;
                }
                
                .ultra-premium-close-btn {
                    position: absolute;
                    top: 20px;
                    right: 20px;
                    width: 50px;
                    height: 50px;
                    background: rgba(255, 255, 255, 0.1);
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    color: #fff;
                    font-size: 24px;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 1000;
                    transition: all 0.3s ease;
                    -webkit-tap-highlight-color: transparent;
                    touch-action: manipulation;
                }
                
                .ultra-premium-close-btn:hover {
                    background: rgba(255, 0, 0, 0.2);
                    border-color: rgba(255, 0, 0, 0.5);
                    transform: scale(1.1);
                }
                
                /* Mobile Optimizations */
                @media (max-width: 768px) {
                    .ultra-premium-modal-content {
                        width: 98%;
                        margin: 10px;
                        max-height: 98vh;
                    }
                    
                    .ultra-premium-modal-body {
                        padding: 20px;
                    }
                    
                    .premium-modal-title {
                        font-size: 24px;
                    }
                    
                    .legend-highlights {
                        grid-template-columns: 1fr;
                    }
                    
                    .premium-modal-image {
                        max-height: 40vh;
                    }
                }
                
                /* iPhone Safari Specific Fixes */
                @supports (-webkit-touch-callout: none) {
                    .ultra-premium-modal-body {
                        -webkit-overflow-scrolling: touch;
                    }
                    
                    .ultra-premium-toggle-btn {
                        transition: none;
                    }
                }
                
                /* Scroll Styling */
                .ultra-premium-modal-body::-webkit-scrollbar {
                    width: 8px;
                }
                
                .ultra-premium-modal-body::-webkit-scrollbar-track {
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 4px;
                }
                
                .ultra-premium-modal-body::-webkit-scrollbar-thumb {
                    background: linear-gradient(135deg, #00ff88, #00cc66);
                    border-radius: 4px;
                }
            `;
            document.head.appendChild(style);
        }
        
        // 🎪 GLOBAL EVENT HANDLERS
        setupGlobalHandlers() {
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.activeModal) {
                    this.closeModal();
                }
            });
            
            document.addEventListener('click', (e) => {
                if (e.target.classList.contains('ultra-premium-modal')) {
                    this.closeModal();
                }
            });
        }
        
        // 🖼️ OPEN PREMIUM MODAL
        openModal(imageSrc, imageAlt, customData = null) {
            this.log(`📸 Opening Ultra Premium Modal: ${imageSrc}`);
            
            this.closeModal();
            
            // Clean image path and normalize
            const cleanSrc = imageSrc.replace(/^\.\//, '');
            
            // Ensure proper path format for data lookup
            let fullPath;
            if (cleanSrc.startsWith('assets/images/')) {
                fullPath = cleanSrc;
            } else {
                fullPath = `assets/images/${cleanSrc}`;
            }
            
            if (CONFIG.DEBUG) {
                console.log(`🔍 [Path Debug] Original: "${imageSrc}" → Clean: "${cleanSrc}" → Full: "${fullPath}"`);
            }
            
            // Get premium data - NOW LANGUAGE-AWARE! 🌍
            const premiumData = customData || getImageData(fullPath) || this.createFallbackData(imageAlt);
            
            const modal = this.createUltraPremiumModal(cleanSrc, premiumData);
            document.body.appendChild(modal);
            
            modal.offsetHeight;
            modal.classList.add('show');
            
            this.activeModal = modal;
            this.setupToggleButton(modal);
            
            document.body.style.overflow = 'hidden';
            
            this.log('✅ Ultra Premium Modal opened successfully');
        }
        
        // 🏗️ CREATE ULTRA PREMIUM MODAL
        createUltraPremiumModal(imageSrc, data) {
            const modal = document.createElement('div');
            modal.className = 'ultra-premium-modal';
            
            const content = document.createElement('div');
            content.className = 'ultra-premium-modal-content';
            
            content.innerHTML = `
                <button class="ultra-premium-close-btn" aria-label="Fermer">×</button>
                <div class="ultra-premium-modal-body">
                    <img src="./${imageSrc}" 
                         alt="${data.title}" 
                         class="premium-modal-image"
                         loading="lazy">
                    
                    <div class="premium-modal-header">
                        <h2 class="premium-modal-title">${data.title}</h2>
                        <div class="premium-modal-category">${data.category}</div>
                        <div class="premium-modal-location">
                            <i class="fas fa-map-marker-alt"></i>
                            ${data.location}
                        </div>
                    </div>
                    
                    <button class="ultra-premium-toggle-btn">
                        ${getToggleButtonText(false)}
                    </button>
                    
                    <div class="premium-legend">
                        <div class="premium-legend-content">
                            ${data.description}
                            
                            ${data.tags ? `
                                <div class="premium-tags">
                                    ${data.tags.map(tag => `<span class="premium-tag">${tag}</span>`).join('')}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;
            
            modal.appendChild(content);
            
            const closeBtn = content.querySelector('.ultra-premium-close-btn');
            this.setupCloseButton(closeBtn);
            
            return modal;
        }
        
        // 🔘 SETUP TOGGLE BUTTON - Universal Fix + BILINGUAL
        setupToggleButton(modal) {
            const toggleBtn = modal.querySelector('.ultra-premium-toggle-btn');
            const legendContent = modal.querySelector('.premium-legend-content');
            
            if (!toggleBtn || !legendContent) return;
            
            let isExpanded = false;
            
            const toggleFunction = (e) => {
                e.preventDefault();
                e.stopPropagation();
                
                if (isExpanded) {
                    // Collapse
                    legendContent.classList.remove('expanded');
                    toggleBtn.innerHTML = getToggleButtonText(false);
                    isExpanded = false;
                } else {
                    // Expand
                    legendContent.classList.add('expanded');
                    toggleBtn.innerHTML = getToggleButtonText(true);
                    isExpanded = true;
                }
                
                // Visual feedback
                toggleBtn.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    toggleBtn.style.transform = '';
                }, 150);
                
                this.log(`✅ Toggle ${isExpanded ? 'expanded' : 'collapsed'}`);
            };
            
            if (DEVICE.isIOSSafari) {
                toggleBtn.addEventListener('touchstart', toggleFunction, { passive: false });
                toggleBtn.addEventListener('click', (e) => e.preventDefault(), { passive: false });
            } else {
                toggleBtn.addEventListener('click', toggleFunction);
            }
        }
        
        // ❌ SETUP CLOSE BUTTON
        setupCloseButton(button) {
            const handler = (e) => {
                e.preventDefault();
                this.closeModal();
            };
            
            if (DEVICE.isIOSSafari) {
                button.addEventListener('touchstart', handler, { passive: false });
            } else {
                button.addEventListener('click', handler);
            }
        }
        
        // 🚪 CLOSE MODAL
        closeModal() {
            if (!this.activeModal) return;
            
            this.log('🚪 Closing Ultra Premium Modal');
            
            this.activeModal.classList.remove('show');
            
            setTimeout(() => {
                this.activeModal?.remove();
                this.activeModal = null;
                document.body.style.overflow = '';
            }, CONFIG.ANIMATION_DURATION);
        }
        
        // 📝 CREATE FALLBACK DATA
        createFallbackData(imageAlt) {
            const currentLang = window.translationManager?.currentLanguage || 'fr';
            
            if (currentLang === 'en') {
                return {
                    title: imageAlt || 'GolfinThai Image',
                    category: 'Premium Collection',
                    location: 'Thailand',
                    description: `
                        <div class="premium-legend">
                            <h3>🌟 GolfinThai Experience</h3>
                            <p>Discover the beauty and authenticity of Thailand through our unique golf experiences.</p>
                            <div class="legend-highlights">
                                <div class="highlight-item">🏌️ Exceptional golf</div>
                                <div class="highlight-item">🌺 Authentic culture</div>
                                <div class="highlight-item">✨ Premium experience</div>
                            </div>
                        </div>
                    `,
                    tags: ['Premium', 'GolfinThai', 'Experience']
                };
            } else {
                return {
                    title: imageAlt || 'Image GolfinThaï',
                    category: 'Collection Premium',
                    location: 'Thaïlande',
                    description: `
                        <div class="premium-legend">
                            <h3>🌟 Expérience GolfinThaï</h3>
                            <p>Découvrez la beauté et l'authenticité de la Thaïlande à travers nos expériences golf uniques.</p>
                            <div class="legend-highlights">
                                <div class="highlight-item">🏌️ Golf d'exception</div>
                                <div class="highlight-item">🌺 Culture authentique</div>
                                <div class="highlight-item">✨ Expérience premium</div>
                            </div>
                        </div>
                    `,
                    tags: ['Premium', 'GolfinThaï', 'Expérience']
                };
            }
        }
        
        // 🪵 LOGGING
        log(message, type = 'info') {
            if (!CONFIG.DEBUG) return;
            
            const styles = {
                info: 'color: #00ff88; font-weight: bold',
                warn: 'color: #ffaa00; font-weight: bold',
                error: 'color: #ff3366; font-weight: bold'
            };
            
            console.log(`%c[UltraPremiumModal] ${message}`, styles[type] || styles.info);
        }
    }
    
    // 🌍 INITIALIZE GLOBAL INSTANCE
    window.ultraPremiumModal = new UltraPremiumModal();
    
    // 🚀 ENHANCED GLOBAL FUNCTION
    window.openUltraPremiumModal = function(imageSrc, imageAlt, customData) {
        if (!imageSrc) {
            console.error('[UltraPremiumModal] No image source provided');
            return false;
        }
        
        window.ultraPremiumModal.openModal(imageSrc, imageAlt, customData);
        return false;
    };
    
    // 🔄 REPLACE OLD FUNCTION
    window.openSimpleModal = window.openUltraPremiumModal;
    
    // 🎯 INITIALIZATION
    console.log('%c🚀 GolfinThai Ultra Premium Modal V3 BILINGUAL - Ready!', 'color: #00ff88; font-size: 18px; font-weight: bold');
    console.log('%c✨ Features: Rich Legends + Universal Compatibility + Premium UX + FR/EN Translation', 'color: #00ff88');
    console.log('%c📱 Device:', 'color: #00ff88', DEVICE);
    
})();