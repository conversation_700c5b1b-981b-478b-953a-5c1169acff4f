/**
 * Widget Météo pour GolfinThaï
 * Affichage de la météo de Bangkok
 */

class WeatherWidget {
    constructor() {
        this.config = window.GolfinThaiConfig.weather;
        this.init();
    }

    init() {
        document.addEventListener('DOMContentLoaded', () => {
            // Démarrer avec les valeurs par défaut
            this.showDefaultWeather();
            
            // Essayer de charger la météo après un délai
            setTimeout(() => {
                this.loadWeather();
            }, 1000);
            
            // Mettre à jour toutes les 10 minutes
            setInterval(() => this.loadWeather(), this.config.updateInterval);
        });
    }

    async loadWeather() {
        try {
            const response = await fetch(this.config.apiUrl);
            const data = await response.json();
            
            if (data && data.current_condition && data.current_condition[0]) {
                const weather = data.current_condition[0];
                this.updateWeatherDisplay(weather);
            } else {
                this.showDefaultWeather();
            }
        } catch (error) {
            console.warn('Weather API unavailable, showing default:', error.message);
            this.showDefaultWeather();
        }
    }

    updateWeatherDisplay(weather) {
        const temp = Math.round(weather.temp_C);
        const condition = weather.weatherDesc[0]?.value || '';
        const icon = this.getWeatherIcon(weather.weatherCode);

        // Desktop weather widget
        this.updateElement('#weather-temp', `${temp}°C`);
        this.updateElement('#weather-icon', '', icon);
        
        // Mobile weather widget
        this.updateElement('#weather-temp-mobile', `${temp}°`);
        this.updateElement('#weather-icon-mobile', '', icon);
    }

    showDefaultWeather() {
        this.updateElement('#weather-temp', this.config.defaultTemp);
        this.updateElement('#weather-temp-mobile', this.config.defaultTemp);
        this.updateElement('#weather-icon', '', this.config.defaultIcon);
        this.updateElement('#weather-icon-mobile', '', this.config.defaultIcon);
    }

    updateElement(selector, text, iconClass) {
        const element = document.querySelector(selector);
        if (!element) return;
        
        if (text) {
            element.textContent = text;
        }
        
        if (iconClass) {
            // Solution robuste pour les icônes (supporte i, svg, et autres)
            const classes = iconClass + ' text-luxury-emerald weather-icon';
            
            // Méthode 1: setAttribute (fonctionne toujours)
            element.setAttribute('class', classes);
            
            // Méthode 2: classList (si disponible et modifiable)
            try {
                element.className = classes;
            } catch (e) {
                // SVG elements en lecture seule, setAttribute suffit
            }
            
            // Méthode 3: classList.add si l'élément existe déjà
            try {
                element.classList.remove(...element.classList);
                classes.split(' ').forEach(cls => {
                    if (cls.trim()) element.classList.add(cls.trim());
                });
            } catch (e) {
                // Fallback déjà géré par setAttribute
            }
        }
    }

    getWeatherIcon(weatherCode) {
        const iconMap = {
            '113': 'fas fa-sun',           // Sunny
            '116': 'fas fa-cloud-sun',     // Partly cloudy
            '119': 'fas fa-cloud',         // Cloudy
            '122': 'fas fa-cloud',         // Overcast
            '143': 'fas fa-smog',          // Mist
            '176': 'fas fa-cloud-rain',    // Patchy rain possible
            '179': 'fas fa-cloud-snow',    // Patchy snow possible
            '182': 'fas fa-cloud-rain',    // Patchy sleet possible
            '185': 'fas fa-cloud-rain',    // Patchy freezing drizzle
            '200': 'fas fa-bolt',          // Thundery outbreaks possible
            '227': 'fas fa-snowflake',     // Blowing snow
            '230': 'fas fa-snowflake',     // Blizzard
            '248': 'fas fa-smog',          // Fog
            '260': 'fas fa-smog',          // Freezing fog
            '263': 'fas fa-cloud-drizzle', // Patchy light drizzle
            '266': 'fas fa-cloud-rain',    // Light drizzle
            '281': 'fas fa-cloud-rain',    // Freezing drizzle
            '284': 'fas fa-cloud-rain',    // Heavy freezing drizzle
            '293': 'fas fa-cloud-rain',    // Patchy light rain
            '296': 'fas fa-cloud-rain',    // Light rain
            '299': 'fas fa-cloud-rain',    // Moderate rain at times
            '302': 'fas fa-cloud-rain',    // Moderate rain
            '305': 'fas fa-cloud-showers-heavy', // Heavy rain at times
            '308': 'fas fa-cloud-showers-heavy', // Heavy rain
            '311': 'fas fa-cloud-rain',    // Light freezing rain
            '314': 'fas fa-cloud-rain',    // Moderate or heavy freezing rain
            '317': 'fas fa-cloud-rain',    // Light sleet
            '320': 'fas fa-cloud-rain',    // Moderate or heavy sleet
            '323': 'fas fa-snowflake',     // Patchy light snow
            '326': 'fas fa-snowflake',     // Light snow
            '329': 'fas fa-snowflake',     // Patchy moderate snow
            '332': 'fas fa-snowflake',     // Moderate snow
            '335': 'fas fa-snowflake',     // Patchy heavy snow
            '338': 'fas fa-snowflake',     // Heavy snow
            '350': 'fas fa-cloud-rain',    // Ice pellets
            '353': 'fas fa-cloud-drizzle', // Light rain shower
            '356': 'fas fa-cloud-rain',    // Moderate or heavy rain shower
            '359': 'fas fa-cloud-showers-heavy', // Torrential rain shower
            '362': 'fas fa-cloud-rain',    // Light sleet showers
            '365': 'fas fa-cloud-rain',    // Moderate or heavy sleet showers
            '368': 'fas fa-snowflake',     // Light snow showers
            '371': 'fas fa-snowflake',     // Moderate or heavy snow showers
            '374': 'fas fa-cloud-rain',    // Light showers of ice pellets
            '377': 'fas fa-cloud-rain',    // Moderate or heavy showers of ice pellets
            '386': 'fas fa-bolt',          // Patchy light rain with thunder
            '389': 'fas fa-bolt',          // Moderate or heavy rain with thunder
            '392': 'fas fa-bolt',          // Patchy light snow with thunder
            '395': 'fas fa-bolt'           // Moderate or heavy snow with thunder
        };
        
        return iconMap[weatherCode] || this.config.defaultIcon;
    }
}

// Initialiser le widget météo
const weatherWidget = new WeatherWidget();
