/**
 * CARROUSEL SINGLETON - GolfinThaï
 * Version singleton pour éviter les instances multiples
 */

class RobustCarousel {
    constructor() {
        // Singleton pattern strict
        if (window.golfCarousel && window.golfCarousel.isInitialized) {
            console.log('🔄 Instance carrousel existante trouvée - Cleanup...');
            window.golfCarousel.destroy();
        }
        
        this.currentIndex = 0;
        this.slides = [];
        this.bullets = [];
        this.autoPlayInterval = null;
        this.isInitialized = false;
        this.initAttempts = 0;
        this.maxInitAttempts = 50;
        this.isDestroyed = false;
        this.hoverTimeout = null;
        this.firstTransitionDone = false;  // FLAG pour éviter double première transition
        
        // Stocker la référence globale
        window.golfCarousel = this;
        
        console.log('🎠 Carrousel Singleton - Initialisation...');
        this.init();
    }

    destroy() {
        console.log('🗑️ Destruction carrousel précédent...');
        this.isDestroyed = true;
        this.stopAutoPlay();
        
        // Nettoyer les timeouts
        if (this.hoverTimeout) {
            clearTimeout(this.hoverTimeout);
            this.hoverTimeout = null;
        }
        
        // Nettoyer les événements
        if (this.slides.length) {
            this.slides.forEach(slide => {
                slide.replaceWith(slide.cloneNode(true));
            });
        }
        
        if (this.bullets.length) {
            this.bullets.forEach(bullet => {
                bullet.replaceWith(bullet.cloneNode(true));
            });
        }
        
        console.log('✅ Carrousel précédent détruit');
    }

    init() {
        if (this.isDestroyed) return;
        
        this.initAttempts++;
        
        // Chercher les éléments DOM
        this.findElements();
        
        if (!this.slides.length && this.initAttempts < this.maxInitAttempts) {
            console.log(`⏳ Tentative ${this.initAttempts}/${this.maxInitAttempts} - Éléments non trouvés, retry...`);
            setTimeout(() => this.init(), 100);
            return;
        }
        
        if (!this.slides.length) {
            console.error('❌ Carrousel : Impossible de trouver les slides après', this.maxInitAttempts, 'tentatives');
            return;
        }
        
        this.setupCarousel();
        this.startAutoPlay();
        
        console.log('✅ Carrousel Singleton initialisé avec', this.slides.length, 'slides');
    }
    
    findElements() {
        // Recherche robuste des éléments
        this.slides = Array.from(document.querySelectorAll('.carousel-slide'));
        this.bullets = Array.from(document.querySelectorAll('.pagination-bullet'));
        
        console.log(`🔍 Éléments trouvés: ${this.slides.length} slides, ${this.bullets.length} bullets`);
    }
    
    setupCarousel() {
        // S'assurer qu'une slide est active au départ
        const hasActiveSlide = this.slides.some(slide => slide.classList.contains('active'));
        
        if (!hasActiveSlide && this.slides.length > 0) {
            console.log('🔧 Aucune slide active trouvée, activation de la première');
            this.slides[0].classList.add('active');
            this.currentIndex = 0;
        } else {
            // Trouver l'index de la slide active
            this.currentIndex = this.slides.findIndex(slide => slide.classList.contains('active'));
            if (this.currentIndex === -1) this.currentIndex = 0;
        }
        
        // S'assurer qu'un bullet est actif
        const hasActiveBullet = this.bullets.some(bullet => bullet.classList.contains('active'));
        
        if (!hasActiveBullet && this.bullets.length > 0) {
            console.log('🔧 Aucun bullet actif trouvé, activation du premier');
            this.bullets[0].classList.add('active');
        }
        
        // Binding des événements sur les bullets
        this.bindBulletEvents();
        
        // Gérer les états hover pour pause autoplay
        this.bindHoverEvents();
        
        // FORCER l'état initialized à true AVANT startAutoPlay
        this.isInitialized = true;
        console.log('✅ Carrousel configuré et prêt pour autoplay');
    }
    
    bindBulletEvents() {
        this.bullets.forEach((bullet, index) => {
            // Nettoyer les anciens événements
            bullet.replaceWith(bullet.cloneNode(true));
            const newBullet = this.bullets[index] = document.querySelectorAll('.pagination-bullet')[index];
            
            // Ajouter indicateur visuel de cliquabilité
            newBullet.style.cursor = 'pointer';
            newBullet.style.zIndex = '20';
            
            // Clic
            newBullet.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log(`🎯 Clic bullet ${index} - Navigation vers slide ${index}`);
                this.goToSlide(index);
            });
            
            // Support tactile
            newBullet.addEventListener('touchend', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log(`👆 Touch bullet ${index} - Navigation vers slide ${index}`);
                this.goToSlide(index);
            });
            
            // Log de binding
            console.log(`✅ Bullet ${index} bindé avec events clic et touch`);
        });
        
        console.log(`✅ ${this.bullets.length} bullets bindés avec z-index élevé`);
    }
    
    bindHoverEvents() {
        const carouselContainer = document.querySelector('.hero-carousel');
        
        if (carouselContainer) {
            carouselContainer.addEventListener('mouseenter', () => {
                console.log('⏸️ Autoplay pausé (hover)');
                // Clear any pending restart
                if (this.hoverTimeout) {
                    clearTimeout(this.hoverTimeout);
                    this.hoverTimeout = null;
                }
                this.stopAutoPlay();
            });
            
            carouselContainer.addEventListener('mouseleave', () => {
                console.log('▶️ Autoplay repris (hover out)');
                
                // Clear previous timeout
                if (this.hoverTimeout) {
                    clearTimeout(this.hoverTimeout);
                }
                
                // Debounced restart
                this.hoverTimeout = setTimeout(() => {
                    if (!this.isDestroyed) {
                        this.startAutoPlay();
                    }
                    this.hoverTimeout = null;
                }, 300);
            });
        }
    }

    goToSlide(index) {
        if (!this.isInitialized) {
            console.warn('⚠️ Carrousel pas encore initialisé');
            return;
        }
        
        // Validation de l'index
        if (index < 0 || index >= this.slides.length) {
            console.warn(`⚠️ Index invalide: ${index}`);
            return;
        }
        
        console.log(`🎠 Transition vers slide ${index}`);
        
        // Retirer toutes les classes active
        this.slides.forEach((slide, i) => {
            slide.classList.remove('active');
            if (i === index) {
                slide.classList.add('active');
            }
        });
        
        this.bullets.forEach((bullet, i) => {
            bullet.classList.remove('active');
            if (i === index) {
                bullet.classList.add('active');
            }
        });
        
        this.currentIndex = index;
        
        // NE PAS redémarrer l'autoplay - il tourne déjà !
        // L'autoplay continue naturellement
    }

    nextSlide() {
        if (!this.isInitialized || !this.slides.length) return;
        
        const nextIndex = (this.currentIndex + 1) % this.slides.length;
        this.goToSlide(nextIndex);
    }

    prevSlide() {
        if (!this.isInitialized || !this.slides.length) return;
        
        const prevIndex = this.currentIndex === 0 ? this.slides.length - 1 : this.currentIndex - 1;
        this.goToSlide(prevIndex);
    }

    startAutoPlay() {
        // Vérification initiale
        if (!this.isInitialized) {
            console.warn('⚠️ Carrousel non initialisé, retry autoplay dans 1s...');
            setTimeout(() => this.startAutoPlay(), 1000);
            return;
        }
        
        if (!this.slides.length) {
            console.warn('⚠️ Aucune slide trouvée pour autoplay');
            return;
        }
        
        // Si autoplay déjà actif, ne pas redémarrer
        if (this.autoPlayInterval) {
            console.log('⚠️ Autoplay déjà actif, pas de redémarrage');
            return;
        }
        
        console.log('🚀 Démarrage autoplay carrousel');
        
        // Première transition SEULEMENT si jamais faite
        if (!this.firstTransitionDone) {
            setTimeout(() => {
                if (this.isInitialized && !this.isDestroyed && this.slides.length > 1) {
                    this.nextSlide();
                    this.firstTransitionDone = true;
                    console.log('🎬 Première transition automatique (2s) - UNIQUE');
                }
            }, 2000);
        }
        
        // Démarrer l'autoplay interval - Clean et simple
        this.autoPlayInterval = setInterval(() => {
            try {
                if (this.isInitialized && this.slides.length > 1 && !this.isDestroyed) {
                    // Vérifier que currentIndex est valide
                    if (this.currentIndex >= this.slides.length) {
                        console.warn('🔧 Index carrousel corrigé:', this.currentIndex, '→ 0');
                        this.currentIndex = 0;
                    }
                    
                    this.nextSlide();
                    console.log(`🎠 Autoplay: slide ${this.currentIndex}/${this.slides.length - 1}`);
                } else {
                    console.warn('⚠️ Carrousel non disponible, arrêt autoplay');
                    this.stopAutoPlay();
                }
            } catch (error) {
                console.error('❌ Erreur autoplay carrousel:', error);
                console.log('🔄 Tentative de récupération...');
                
                // Tentative de récupération
                this.stopAutoPlay();
                setTimeout(() => {
                    if (this.isInitialized && !this.isDestroyed) {
                        console.log('🔄 Redémarrage autoplay après erreur');
                        this.startAutoPlay();
                    }
                }, 2000);
            }
        }, this.firstTransitionDone ? 3000 : 5000);  // 5s initial, puis 3s
        
        console.log('▶️ Autoplay interval démarré - ID:', this.autoPlayInterval);
    }

    stopAutoPlay() {
        if (this.autoPlayInterval) {
            console.log('⏹️ Arrêt autoplay - Interval ID:', this.autoPlayInterval);
            clearInterval(this.autoPlayInterval);
            this.autoPlayInterval = null;
        }
    }
    
    // API de debug
    debug() {
        return {
            isInitialized: this.isInitialized,
            currentIndex: this.currentIndex,
            totalSlides: this.slides.length,
            totalBullets: this.bullets.length,
            autoPlayActive: !!this.autoPlayInterval,
            initAttempts: this.initAttempts
        };
    }
    
    // Force restart (pour debug)
    restart() {
        console.log('🔄 Redémarrage forcé du carrousel');
        this.stopAutoPlay();
        this.isInitialized = false;
        this.initAttempts = 0;
        this.init();
    }
}

// Initialisation singleton pour robustesse
function initRobustCarousel() {
    // Eviter les double initialisations
    if (window.golfCarouselInitializing) {
        console.log('🔄 Initialisation déjà en cours...');
        return;
    }
    
    window.golfCarouselInitializing = true;
    
    // Désactiver l'ancien carrousel s'il existe
    if (window.golfCarousel && window.golfCarousel.stopAutoPlay) {
        window.golfCarousel.stopAutoPlay();
        console.log('🔄 Ancien carrousel désactivé');
    }
    
    // Créer le nouveau carrousel robuste
    window.golfCarousel = new RobustCarousel();
    
    // Marquer comme terminé
    setTimeout(() => {
        window.golfCarouselInitializing = false;
    }, 1000);
    
    // Exposer les méthodes pour debug
    window.debugCarousel = () => {
        if (window.golfCarousel) {
            console.table(window.golfCarousel.debug());
        } else {
            console.log('❌ Carrousel non initialisé');
        }
    };
    
    window.restartCarousel = () => {
        if (window.golfCarousel && window.golfCarousel.restart) {
            window.golfCarousel.restart();
        }
    };
    
    console.log('🎠 Carrousel Robuste créé');
}

// Initialisation avec fallbacks multiples
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initRobustCarousel);
} else {
    initRobustCarousel();
}

// Note: Fallback timeout supprimé pour éviter les instances multiples
