/**
 * DIAGNOSTIC LANGUES DESKTOP
 */

(function() {
    'use strict';
    
    console.log('🌍 DIAGNOSTIC LANGUES DESKTOP...');
    
    function diagnosticLangues() {
        const desktopBtn = document.getElementById('lang-toggle-desktop');
        const desktopDropdown = document.getElementById('lang-dropdown-desktop');
        const mobileBtn = document.getElementById('lang-toggle-mobile');
        
        console.log('📊 Éléments trouvés:');
        console.log('  Desktop btn:', !!desktopBtn);
        console.log('  Desktop dropdown:', !!desktopDropdown);
        console.log('  Mobile btn:', !!mobileBtn);
        
        if (desktopBtn) {
            console.log('🔍 Desktop button state:');
            console.log('  Configured:', desktopBtn.dataset.configured);
            console.log('  Style display:', getComputedStyle(desktopBtn).display);
            console.log('  Style visibility:', getComputedStyle(desktopBtn).visibility);
            console.log('  Pointer events:', getComputedStyle(desktopBtn).pointerEvents);
            
            // Test du clic
            console.log('🧪 Test clic desktop...');
            desktopBtn.addEventListener('click', function(e) {
                console.log('🌍 DESKTOP CLICK DÉTECTÉ !');
                console.log('  Event:', e);
                console.log('  Translation manager:', !!window.translationManager);
                
                if (window.translationManager) {
                    console.log('  Translation manager methods:', Object.keys(window.translationManager));
                }
            });
        }
        
        if (desktopDropdown) {
            console.log('🔍 Desktop dropdown state:');
            console.log('  Style display:', getComputedStyle(desktopDropdown).display);
            console.log('  Style visibility:', getComputedStyle(desktopDropdown).visibility);
            console.log('  Classes:', desktopDropdown.classList.toString());
            
            const options = desktopDropdown.querySelectorAll('.lang-option');
            console.log('  Options trouvées:', options.length);
            
            options.forEach((option, index) => {
                console.log(`  Option ${index}:`, option.dataset.lang, option.textContent);
            });
        }
        
        // Test du système de traduction
        if (window.translationManager) {
            console.log('✅ Translation Manager trouvé');
            console.log('  Méthodes:', Object.keys(window.translationManager));
            console.log('  Langue actuelle:', window.translationManager.currentLanguage || 'non définie');
            console.log('  Boutons configurés:', window.translationManager.buttonsSetup || 'non défini');
        } else {
            console.log('❌ Translation Manager introuvable !');
        }
    }
    
    // Test manuel du changement de langue
    window.testLanguageDesktop = function() {
        console.log('🧪 Test manuel changement langue desktop...');
        
        if (window.translationManager) {
            console.log('Test switchLanguage("en")...');
            window.translationManager.switchLanguage('en');
            
            setTimeout(() => {
                console.log('Test switchLanguage("fr")...');
                window.translationManager.switchLanguage('fr');
            }, 2000);
        } else {
            console.log('❌ Translation Manager non disponible');
        }
    };
    
    // Forcer la réinitialisation
    window.forceLanguageDesktop = function() {
        console.log('🔧 Force réinitialisation langues desktop...');
        
        if (window.translationManager) {
            window.translationManager.buttonsSetup = false;
            window.translationManager.setupLanguageButtons();
            console.log('✅ Réinitialisation forcée');
        }
    };
    
    // Lancer le diagnostic
    setTimeout(diagnosticLangues, 3000);
    
    console.log('💡 Commandes disponibles:');
    console.log('  testLanguageDesktop() - Tester changement manuel');
    console.log('  forceLanguageDesktop() - Forcer réinitialisation');
    
})();
