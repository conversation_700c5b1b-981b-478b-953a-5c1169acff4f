/**
 * Carrousel Amélioré Mobile/Desktop - GolfinThaï
 * Version corrigée pour résoudre les problèmes de transition
 */

class FixedCarousel {
    constructor() {
        this.currentSlide = 0;
        this.slides = [];
        this.bullets = [];
        this.autoplayInterval = null;
        this.isAutoplay = true;
        this.autoplayDelay = 4000; // 4 secondes au lieu de 5
        this.isTransitioning = false;
        this.touchStartX = 0;
        this.touchEndX = 0;
        this.init();
    }

    init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        this.slides = document.querySelectorAll('.carousel-slide');
        this.bullets = document.querySelectorAll('.pagination-bullet');

        if (this.slides.length === 0) {
            console.error('❌ Aucun slide de carrousel trouvé');
            return;
        }

        console.log(`✅ Carrousel initialisé avec ${this.slides.length} slides`);
        
        // Forcer le premier slide à être visible
        this.slides.forEach((slide, index) => {
            slide.classList.toggle('active', index === 0);
            slide.style.opacity = index === 0 ? '1' : '0';
        });
        
        this.bullets.forEach((bullet, index) => {
            bullet.classList.toggle('active', index === 0);
        });
        
        this.preloadImages();
        this.setupEvents();
        this.startAutoplay();
        
        console.log('🎠 Carrousel configuré et démarré');
    }

    preloadImages() {
        this.slides.forEach((slide, index) => {
            const bgImage = slide.style.backgroundImage;
            if (bgImage) {
                const url = bgImage.match(/url\(["']?(.+?)["']?\)/);
                if (url && url[1]) {
                    const img = new Image();
                    img.onload = () => {
                        console.log(`📸 Image ${index + 1} chargée`);
                    };
                    img.onerror = () => {
                        console.error(`❌ Échec chargement image ${index + 1}: ${url[1]}`);
                    };
                    img.src = url[1];
                }
            }
        });
    }

    setupEvents() {
        // Événements clavier
        document.addEventListener('keydown', (e) => {
            if (this.isTransitioning) return;
            
            if (e.key === 'ArrowLeft') {
                this.prevSlide();
                this.pauseAutoplay();
            }
            if (e.key === 'ArrowRight') {
                this.nextSlide();
                this.pauseAutoplay();
            }
        });

        // Événements souris (desktop)
        const carousel = document.querySelector('.hero-carousel');
        if (carousel) {
            carousel.addEventListener('mouseenter', () => this.pauseAutoplay());
            carousel.addEventListener('mouseleave', () => this.resumeAutoplay());
            
            // Événements touch (mobile)
            carousel.addEventListener('touchstart', (e) => {
                this.touchStartX = e.touches[0].clientX;
                this.pauseAutoplay();
            }, { passive: true });

            carousel.addEventListener('touchend', (e) => {
                if (this.isTransitioning) return;
                
                this.touchEndX = e.changedTouches[0].clientX;
                this.handleSwipe();
            }, { passive: true });
        }

        // Événements bullets avec amélioration mobile
        this.bullets.forEach((bullet, index) => {
            // Clic normal
            bullet.addEventListener('click', (e) => {
                e.preventDefault();
                if (!this.isTransitioning) {
                    this.goToSlide(index);
                    this.pauseAutoplay();
                }
            });
            
            // Touch events spécifiques mobile - OPTIMISÉ
            bullet.addEventListener('touchstart', (e) => {
                e.preventDefault();
                // Feedback tactile très subtil - était 1.3, maintenant 1.05
                bullet.style.transform = 'scale(1.05)';
                bullet.style.transition = 'transform 0.15s ease';
            }, { passive: false });
            
            bullet.addEventListener('touchend', (e) => {
                e.preventDefault();
                // Retour à la normale avec transition fluide
                bullet.style.transform = '';
                bullet.style.transition = 'transform 0.2s ease';
                if (!this.isTransitioning) {
                    this.goToSlide(index);
                    this.pauseAutoplay();
                }
            }, { passive: false });
            
            // Gestion du touchcancel pour une meilleure UX
            bullet.addEventListener('touchcancel', (e) => {
                e.preventDefault();
                bullet.style.transform = '';
                bullet.style.transition = 'transform 0.2s ease';
            }, { passive: false });
            
            // Clavier
            bullet.addEventListener('keydown', (e) => {
                if ((e.key === 'Enter' || e.key === ' ') && !this.isTransitioning) {
                    e.preventDefault();
                    this.goToSlide(index);
                    this.pauseAutoplay();
                }
            });
        });
    }

    handleSwipe() {
        const swipeThreshold = 50;
        const diff = this.touchStartX - this.touchEndX;
        
        if (Math.abs(diff) > swipeThreshold) {
            if (diff > 0) {
                // Swipe vers la gauche = slide suivant
                this.nextSlide();
            } else {
                // Swipe vers la droite = slide précédent
                this.prevSlide();
            }
        }
    }

    goToSlide(index) {
        if (index < 0 || index >= this.slides.length || index === this.currentSlide || this.isTransitioning) {
            return;
        }
        
        this.isTransitioning = true;
        
        // Animation de transition améliorée
        const currentSlide = this.slides[this.currentSlide];
        const nextSlide = this.slides[index];
        
        // Préparer le slide suivant
        nextSlide.style.opacity = '0';
        nextSlide.classList.add('active');
        
        // Transition en douceur
        setTimeout(() => {
            currentSlide.style.opacity = '0';
            nextSlide.style.opacity = '1';
            
            setTimeout(() => {
                // Nettoyer après transition
                this.slides.forEach((slide, i) => {
                    slide.classList.toggle('active', i === index);
                    slide.style.opacity = i === index ? '1' : '0';
                });
                
                this.bullets.forEach((bullet, i) => {
                    bullet.classList.toggle('active', i === index);
                });
                
                this.currentSlide = index;
                this.isTransitioning = false;
                
                console.log(`🎯 Slide ${index + 1} activé`);
            }, 800); // Durée de transition CSS
        }, 50);
    }

    nextSlide() {
        const next = (this.currentSlide + 1) % this.slides.length;
        this.goToSlide(next);
    }

    prevSlide() {
        const prev = (this.currentSlide - 1 + this.slides.length) % this.slides.length;
        this.goToSlide(prev);
    }

    startAutoplay() {
        if (this.autoplayInterval) this.stopAutoplay();
        
        this.autoplayInterval = setInterval(() => {
            if (!this.isTransitioning) {
                this.nextSlide();
            }
        }, this.autoplayDelay);
        
        this.isAutoplay = true;
        console.log('▶️ Autoplay démarré');
    }

    stopAutoplay() {
        if (this.autoplayInterval) {
            clearInterval(this.autoplayInterval);
            this.autoplayInterval = null;
        }
        this.isAutoplay = false;
        console.log('⏸️ Autoplay arrêté');
    }

    pauseAutoplay() {
        this.stopAutoplay();
        console.log('⏸️ Autoplay en pause');
    }

    resumeAutoplay() {
        if (!this.isAutoplay && !this.isTransitioning) {
            setTimeout(() => this.startAutoplay(), 2000); // Reprise après 2 secondes
        }
    }

    // Méthode publique pour navigation externe
    navigateTo(slideIndex) {
        if (slideIndex >= 0 && slideIndex < this.slides.length) {
            this.goToSlide(slideIndex);
            this.pauseAutoplay();
        }
    }

    // Méthode pour débugger
    debug() {
        console.log('🔍 État du carrousel:', {
            currentSlide: this.currentSlide,
            totalSlides: this.slides.length,
            isTransitioning: this.isTransitioning,
            isAutoplay: this.isAutoplay
        });
    }

    destroy() {
        this.stopAutoplay();
        console.log('🗑️ Carrousel détruit');
    }
}

// Attendre que le DOM soit prêt
function initCarousel() {
    // Détruire l'ancien carrousel s'il existe
    if (window.golfCarousel && window.golfCarousel.destroy) {
        window.golfCarousel.destroy();
    }
    
    // Créer le nouveau carrousel amélioré
    window.golfCarousel = new FixedCarousel();
    
    // Fonction globale pour navigation externe
    window.goToSlide = function(index) {
        if (window.golfCarousel && window.golfCarousel.goToSlide) {
            window.golfCarousel.goToSlide(index);
        }
    };
    
    console.log('🚀 Carrousel amélioré initialisé');
}

// Initialiser le carrousel
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initCarousel);
} else {
    initCarousel();
}

// Pour le debugging en console
window.debugCarousel = function() {
    if (window.golfCarousel && window.golfCarousel.debug) {
        window.golfCarousel.debug();
    }
};
