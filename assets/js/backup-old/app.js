/**
 * Application principale GolfinThaï
 * Gestion des événements et interactions
 */

class GolfinThaiApp {
    constructor() {
        this.config = window.GolfinThaiConfig;
        this.isMenuOpen = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.initAnimations();
    }

    bindEvents() {
        document.addEventListener('DOMContentLoaded', () => {
            this.handleDOMContentLoaded();
        });
        
        window.addEventListener('scroll', this.throttle(this.handleScroll.bind(this), 100));
        window.addEventListener('resize', this.debounce(this.handleResize.bind(this), 250));
    }

    handleDOMContentLoaded() {
        // Initialiser tous les modules une fois le DOM chargé
        this.initMobileMenu();
        this.initSmoothScroll();
        this.initRevealAnimations();
        this.initNavigation();
        this.updateCopyrightYear();
        this.setupIncludesLoading();
        
        // Marquer que l'app est initialisée
        document.body.dataset.appInitialized = 'true';
    }

    // Méthode manquante - Initialisation des animations
    initAnimations() {
        // Animations générales déjà gérées par initRevealAnimations
    }

    // Méthode manquante - Initialisation de la navigation
    initNavigation() {
        // Marquer le lien actif dans la navigation
        const navLinks = document.querySelectorAll('.nav-link');
        const currentHash = window.location.hash || '#home';
        
        navLinks.forEach(link => {
            if (link.getAttribute('href') === currentHash) {
                link.classList.add('active');
            }
        });
    }

    // Gestion du menu mobile - MODE COMPATIBLE
    initMobileMenu() {
        // Vérifier si le gestionnaire unifié est actif
        if (window.mobileManager) {
            console.log('✅ Menu mobile géré par mobile-unified-clean.js');
            return;
        }

        // Sinon, utiliser le mode de base (fallback)
        console.log('⚠️ Fallback: app.js gère le menu mobile');
        const mobileMenuBtn = document.querySelector(this.config.selectors.mobileMenuBtn);
        const mobileMenu = document.querySelector(this.config.selectors.mobileMenu);
        const closeMenuBtn = document.querySelector(this.config.selectors.closeMenuBtn);

        if (!mobileMenuBtn || !mobileMenu || !closeMenuBtn) {
            // Attendre que les éléments soient chargés
            setTimeout(() => {
                this.initMobileMenu();
            }, 500);
            return;
        }

        // Éviter les doublons d'event listeners
        if (mobileMenuBtn.dataset.menuInitialized) return;

        mobileMenuBtn.addEventListener('click', () => this.toggleMobileMenu(true));
        closeMenuBtn.addEventListener('click', () => this.toggleMobileMenu(false));

        // Fermer le menu en cliquant sur un lien
        const menuLinks = mobileMenu.querySelectorAll('a');
        menuLinks.forEach(link => {
            link.addEventListener('click', () => this.toggleMobileMenu(false));
        });

        // Marquer comme initialisé
        mobileMenuBtn.dataset.menuInitialized = 'true';
    }

    toggleMobileMenu(open) {
        // Vérifier si le gestionnaire unifié est actif
        if (window.mobileManager) {
            console.log('✅ Menu géré par mobile-unified-clean.js');
            return;
        }

        // Sinon, utiliser le mode de base (fallback)
        const mobileMenu = document.querySelector(this.config.selectors.mobileMenu);
        const mobileMenuBtn = document.querySelector(this.config.selectors.mobileMenuBtn);

        if (!mobileMenu || !mobileMenuBtn) {
            return;
        }

        if (open) {
            mobileMenu.classList.add('mobile-menu-open');
            mobileMenu.setAttribute('aria-hidden', 'false');
            mobileMenuBtn.setAttribute('aria-expanded', 'true');
            document.body.classList.add('body-menu-locked');
            this.isMenuOpen = true;
        } else {
            mobileMenu.classList.remove('mobile-menu-open');
            mobileMenu.setAttribute('aria-hidden', 'true');
            mobileMenuBtn.setAttribute('aria-expanded', 'false');
            document.body.classList.remove('body-menu-locked');
            this.isMenuOpen = false;
        }
    }

    // Scroll smooth pour les ancres
    initSmoothScroll() {
        const links = document.querySelectorAll('a[href^="#"]');
        links.forEach(link => {
            link.addEventListener('click', (e) => {
                const targetId = link.getAttribute('href');
                if (targetId === '#') return;
                
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    e.preventDefault();
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                    
                    // Menu mobile géré par mobile-unified-clean.js
                    // if (this.isMenuOpen) {
                    //     this.toggleMobileMenu(false);
                    // }
                }
            });
        });
    }

    // Animations de révélation au scroll
    initRevealAnimations() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('revealed');
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '50px'
        });

        const elementsToReveal = document.querySelectorAll('.reveal-animation');
        elementsToReveal.forEach(el => observer.observe(el));
    }

    // Gestion du scroll
    handleScroll() {
        const navbar = document.querySelector(this.config.selectors.navBar);
        if (navbar) {
            if (window.scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        }
    }

    // Gestion du redimensionnement
    handleResize() {
        // Menu mobile géré par mobile-unified-clean.js
        // if (window.innerWidth >= 1024 && this.isMenuOpen) {
        //     this.toggleMobileMenu(false);
        // }
    }

    // Mise à jour de l'année dans le footer
    updateCopyrightYear() {
        const yearElement = document.querySelector('.copyright-year');
        if (yearElement) {
            yearElement.textContent = new Date().getFullYear();
        }
    }

    // Gestion du chargement des includes
    setupIncludesLoading() {
        // Observer les changements dans les conteneurs d'includes
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Du contenu a été ajouté, vérifier si c'est un include
                    const target = mutation.target;
                    if (target.id && target.id.includes('include')) {
                        // Attendre un peu puis réinitialiser les traductions et événements
                        setTimeout(() => {
                            this.reinitializeAfterInclude(target);
                        }, 100);
                    }
                }
            });
        });

        // Observer les conteneurs d'includes
        const includeContainers = ['header-include', 'destinations-include', 'footer-include'];
        includeContainers.forEach(id => {
            const container = document.getElementById(id);
            if (container) {
                observer.observe(container, { childList: true, subtree: true });
            }
        });
    }

    // Réinitialiser après chargement d'un include
    reinitializeAfterInclude(container) {
        // Éviter les réinitialisations multiples
        if (container.dataset.reinitialized) return;
        
        // Réinitialiser les traductions pour le nouveau contenu
        if (window.translationManager && window.translationManager.isInitialized) {
            window.translationManager.retranslateContent(container);
        }
        
        // Réinitialiser les événements spécifiques SEULEMENT si nécessaire
        if (container.id === 'header-include' && !container.dataset.headerInitialized) {
            // Réinitialiser le menu mobile SEULEMENT pour le header
            setTimeout(() => {
                this.initMobileMenu();
                if (window.translationManager && !window.translationManager.buttonsSetup) {
                    window.translationManager.setupLanguageButtons();
                }
                container.dataset.headerInitialized = 'true';
            }, 200);
        }
        
        // Marquer comme réinitialisé
        container.dataset.reinitialized = 'true';
    }

    // Utilitaires
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }

    debounce(func, wait, immediate) {
        let timeout;
        return function executedFunction() {
            const context = this;
            const args = arguments;
            const later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }
}

// Initialiser l'application
const app = new GolfinThaiApp();
