/**
 * MOBILE TEST SUITE - Tests et diagnostic pour l'interface mobile
 */

class MobileTestSuite {
    constructor() {
        this.tests = [];
        this.results = {};
        console.log('🧪 Mobile Test Suite initialisée');
    }

    // Lancer tous les tests
    async runAllTests() {
        console.log('🚀 Lancement de la suite de tests mobile...');
        
        const tests = [
            { name: 'Touch Events', fn: () => this.testTouchEvents() },
            { name: 'Menu Manager', fn: () => this.testMenuManager() },
            { name: 'Language Switcher', fn: () => this.testLanguageSwitcher() },
            { name: 'Modal System', fn: () => this.testModalSystem() },
            { name: 'Navigation', fn: () => this.testNavigation() }
        ];

        for (const test of tests) {
            console.log(`🧪 Test: ${test.name}`);
            try {
                const result = await test.fn();
                this.results[test.name] = { success: result, error: null };
                console.log(result ? `✅ ${test.name} RÉUSSI` : `❌ ${test.name} ÉCHOUÉ`);
            } catch (error) {
                this.results[test.name] = { success: false, error: error.message };
                console.error(`❌ ${test.name} ERREUR:`, error);
            }
            await this.wait(100);
        }

        this.displayResults();
        return this.results;
    }

    // Test des événements tactiles
    testTouchEvents() {
        const touchManager = window.touchManager;
        if (!touchManager) {
            console.error('❌ TouchManager non trouvé');
            return false;
        }

        const stats = touchManager.getStats();
        console.log('📊 TouchManager stats:', stats);
        
        return stats.tapCallbacks >= 0 && stats.swipeCallbacks >= 0;
    }

    // Test du gestionnaire de menu
    testMenuManager() {
        const menuManager = window.mobileMenuManager;
        if (!menuManager) {
            console.error('❌ MobileMenuManager non trouvé');
            return false;
        }

        const state = menuManager.getState();
        console.log('📱 Menu state:', state);
        
        return state.elementsFound.menuBtn && state.elementsFound.menu;
    }

    // Test du changement de langue
    testLanguageSwitcher() {
        const translationManager = window.translationManager;
        if (!translationManager) {
            console.error('❌ TranslationManager non trouvé');
            return false;
        }

        const currentLang = translationManager.currentLanguage;
        console.log('🌍 Langue actuelle:', currentLang);
        
        return ['fr', 'en'].includes(currentLang);
    }

    // Test du système de modal
    testModalSystem() {
        const modalManager = window.modalManager;
        if (!modalManager) {
            console.error('❌ ModalManager non trouvé');
            return false;
        }

        const state = modalManager.getState();
        console.log('🖼️ Modal state:', state);
        
        return true; // Le modal manager est présent
    }

    // Test de navigation
    testNavigation() {
        const navManager = window.navigationManager;
        if (!navManager) {
            console.error('❌ NavigationManager non trouvé');
            return false;
        }

        const state = navManager.getState();
        console.log('🧭 Navigation state:', state);
        
        return true; // Le navigation manager est présent
    }

    // Afficher les résultats
    displayResults() {
        console.log('\n📊 RÉSULTATS DES TESTS:');
        console.log('================================');
        
        let passed = 0;
        let total = 0;
        
        Object.entries(this.results).forEach(([name, result]) => {
            total++;
            if (result.success) {
                passed++;
                console.log(`✅ ${name}: RÉUSSI`);
            } else {
                console.log(`❌ ${name}: ÉCHOUÉ${result.error ? ` (${result.error})` : ''}`);
            }
        });
        
        console.log('================================');
        console.log(`📈 Résultat: ${passed}/${total} tests réussis`);
        
        if (passed === total) {
            console.log('🎉 TOUS LES TESTS SONT RÉUSSIS !');
        } else {
            console.log('⚠️ Des problèmes ont été détectés');
        }
    }

    // Test manuel des interactions
    testTouchInteractions() {
        console.log('👆 Test des interactions tactiles:');
        console.log('1. Tapez sur le menu hamburger');
        console.log('2. Tapez sur une image');
        console.log('3. Tapez sur le switch de langue');
        console.log('4. Tapez sur un lien de navigation');
        
        // Observer les événements pendant 10 secondes
        const startTime = Date.now();
        const events = [];
        
        const touchHandler = (e) => {
            events.push({
                type: e.type,
                target: e.target.tagName,
                time: Date.now() - startTime
            });
        };
        
        document.addEventListener('touchstart', touchHandler);
        document.addEventListener('touchend', touchHandler);
        document.addEventListener('click', touchHandler);
        
        setTimeout(() => {
            document.removeEventListener('touchstart', touchHandler);
            document.removeEventListener('touchend', touchHandler);
            document.removeEventListener('click', touchHandler);
            
            console.log('📱 Événements capturés:', events);
        }, 10000);
    }

    // Test automatique du menu
    async testMenuAutomatically() {
        if (!window.mobileMenuManager) {
            console.error('❌ MobileMenuManager non disponible');
            return false;
        }

        try {
            console.log('🧪 Test automatique du menu...');
            
            // Test ouverture
            window.mobileMenuManager.open();
            await this.wait(300);
            
            const isOpen1 = window.mobileMenuManager.isOpen;
            console.log('📱 Menu ouvert:', isOpen1);
            
            // Test fermeture
            window.mobileMenuManager.close();
            await this.wait(300);
            
            const isOpen2 = window.mobileMenuManager.isOpen;
            console.log('📱 Menu fermé:', !isOpen2);
            
            const success = isOpen1 && !isOpen2;
            console.log(success ? '✅ Test menu réussi' : '❌ Test menu échoué');
            
            return success;
        } catch (error) {
            console.error('❌ Erreur test menu:', error);
            return false;
        }
    }

    // Utilitaire pour attendre
    wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Rapport complet du système
    getSystemReport() {
        const report = {
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight
            },
            modules: {},
            elements: {}
        };

        // Status des modules
        if (window.golfApp) {
            report.modules = window.golfApp.getSystemStatus();
        }

        // Éléments critiques
        report.elements = {
            mobileMenuBtn: !!document.getElementById('mobile-menu-btn'),
            mobileMenu: !!document.getElementById('mobile-menu'),
            courseModal: !!document.getElementById('course-modal'),
            langToggleDesktop: !!document.getElementById('lang-toggle-desktop'),
            langToggleMobile: !!document.getElementById('lang-toggle-mobile')
        };

        return report;
    }
}

// Instance globale pour les tests
window.mobileTestSuite = new MobileTestSuite();

// Raccourcis pour debug
window.testMobile = () => window.mobileTestSuite.runAllTests();
window.testTouchInteractions = () => window.mobileTestSuite.testTouchInteractions();
window.testMenu = () => window.mobileTestSuite.testMenuAutomatically();
window.getSystemReport = () => window.mobileTestSuite.getSystemReport();

// Auto-test après chargement (seulement en mode debug)
document.addEventListener('DOMContentLoaded', () => {
    if (window.location.search.includes('test=auto')) {
        setTimeout(() => {
            console.log('🚀 Auto-test activé');
            window.testMobile();
        }, 2000);
    }
});

console.log(`
🧪 MOBILE TEST SUITE CHARGÉE

Commandes disponibles:
- window.testMobile() : Lance tous les tests
- window.testTouchInteractions() : Test manuel des interactions
- window.testMenu() : Test automatique du menu
- window.getSystemReport() : Rapport complet du système

Pour auto-test au chargement: ajoutez ?test=auto à l'URL
`);