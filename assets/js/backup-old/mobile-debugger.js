/**
 * Utilitaires de débogage mobile - GolfinThaï
 * Script pour diagnostiquer et corriger les problèmes mobiles
 */

class MobileDebugger {
    constructor() {
        this.isDebugMode = window.location.search.includes('debug=mobile');
        this.touchLog = [];
        this.init();
    }

    init() {
        if (this.isDebugMode) {
            this.setupDebugMode();
            this.addDebugUI();
            console.log('🔍 Mode debug mobile activé');
        }
        
        // Toujours surveiller les erreurs critiques
        this.monitorCriticalIssues();
    }

    setupDebugMode() {
        // Log des événements tactiles
        document.addEventListener('touchstart', (e) => {
            this.logTouch('touchstart', e);
        }, { passive: true });

        document.addEventListener('touchend', (e) => {
            this.logTouch('touchend', e);
        }, { passive: true });

        document.addEventListener('click', (e) => {
            this.logTouch('click', e);
        });

        // Log des problèmes de menu
        const menuBtn = document.getElementById('mobile-menu-btn');
        if (menuBtn) {
            menuBtn.addEventListener('click', () => {
                console.log('🍔 Clic menu hamburger détecté');
                this.checkMenuState();
            });
        }

        // Log des modales
        document.addEventListener('modal-open', (e) => {
            console.log('📱 Modal ouverte:', e.detail);
        });
    }

    logTouch(type, event) {
        const target = event.target;
        const timestamp = Date.now();
        
        const touchInfo = {
            type,
            timestamp,
            target: target.tagName + (target.className ? '.' + target.className.split(' ')[0] : ''),
            targetText: target.textContent?.slice(0, 20) || '',
            isImage: target.tagName === 'IMG',
            isCard: target.closest('.course-card, .service-card') !== null,
            coordinates: event.touches ? {
                x: event.touches[0]?.clientX,
                y: event.touches[0]?.clientY
            } : {
                x: event.clientX,
                y: event.clientY
            }
        };

        this.touchLog.push(touchInfo);
        
        // Garder seulement les 50 derniers événements
        if (this.touchLog.length > 50) {
            this.touchLog.shift();
        }

        console.log(`👆 ${type}:`, touchInfo);
    }

    checkMenuState() {
        const menu = document.getElementById('mobile-menu');
        const menuBtn = document.getElementById('mobile-menu-btn');
        const body = document.body;

        const state = {
            menuVisible: menu ? getComputedStyle(menu).display !== 'none' : false,
            menuClasses: menu ? menu.className : 'N/A',
            bodyClasses: body.className,
            menuBtnExpanded: menuBtn ? menuBtn.getAttribute('aria-expanded') : 'N/A',
            appMenuState: window.golfApp ? window.golfApp.isMenuOpen : 'N/A'
        };

        console.log('🍔 État du menu:', state);
        return state;
    }

    addDebugUI() {
        const debugPanel = document.createElement('div');
        debugPanel.id = 'mobile-debug-panel';
        debugPanel.innerHTML = `
            <div style="
                position: fixed;
                top: 10px;
                right: 10px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 10px;
                border-radius: 8px;
                font-family: monospace;
                font-size: 12px;
                z-index: 99999;
                max-width: 300px;
                max-height: 200px;
                overflow-y: auto;
            ">
                <div><strong>🔍 Debug Mobile</strong></div>
                <button onclick="window.mobileDebugger.clearLogs()" style="
                    background: #22c55e;
                    color: white;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 10px;
                    margin: 4px 0;
                ">Clear Logs</button>
                <button onclick="window.mobileDebugger.checkAllStates()" style="
                    background: #3b82f6;
                    color: white;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 10px;
                    margin: 4px 2px;
                ">Check States</button>
                <div id="debug-info" style="margin-top: 8px; font-size: 10px;"></div>
            </div>
        `;
        document.body.appendChild(debugPanel);

        // Mise à jour périodique des infos
        setInterval(() => {
            this.updateDebugInfo();
        }, 1000);
    }

    updateDebugInfo() {
        const debugInfo = document.getElementById('debug-info');
        if (!debugInfo) return;

        const info = {
            viewport: `${window.innerWidth}x${window.innerHeight}`,
            touchEvents: this.touchLog.length,
            lastTouch: this.touchLog.length > 0 ? this.touchLog[this.touchLog.length - 1].type : 'none',
            menuOpen: window.golfApp ? window.golfApp.isMenuOpen : 'unknown',
            modalOpen: window.golfModalManager ? window.golfModalManager.isOpen : 'unknown'
        };

        debugInfo.innerHTML = `
            <div>Viewport: ${info.viewport}</div>
            <div>Touch Events: ${info.touchEvents}</div>
            <div>Last Touch: ${info.lastTouch}</div>
            <div>Menu: ${info.menuOpen}</div>
            <div>Modal: ${info.modalOpen}</div>
        `;
    }

    clearLogs() {
        this.touchLog = [];
        console.clear();
        console.log('🧹 Logs effacés');
    }

    checkAllStates() {
        console.log('📊 État complet du système:');
        console.log('Menu:', this.checkMenuState());
        console.log('Modal:', window.golfModalManager ? window.golfModalManager.debug() : 'Non disponible');
        console.log('Touch Log:', this.touchLog.slice(-5)); // 5 derniers événements
        console.log('App State:', window.golfApp || 'Non disponible');
    }

    monitorCriticalIssues() {
        // Détecter les problèmes de menu
        let menuClickCount = 0;
        const menuBtn = document.getElementById('mobile-menu-btn');
        
        if (menuBtn) {
            menuBtn.addEventListener('click', () => {
                menuClickCount++;
                
                setTimeout(() => {
                    const menu = document.getElementById('mobile-menu');
                    const isVisible = menu && getComputedStyle(menu).display !== 'none';
                    
                    if (menuClickCount > 1 && !isVisible) {
                        console.warn('⚠️ PROBLÈME DÉTECTÉ: Menu ne s\'ouvre pas après plusieurs clics');
                        this.suggestMenuFix();
                    }
                    
                    menuClickCount = 0;
                }, 500);
            });
        }

        // Détecter les problèmes d'images
        document.addEventListener('click', (e) => {
            const image = e.target.closest('img[src*="assets/images"]');
            if (image && !e.target.closest('.hero-carousel, .logo')) {
                setTimeout(() => {
                    const modal = document.getElementById('course-modal');
                    const isModalOpen = modal && getComputedStyle(modal).display !== 'none';
                    
                    if (!isModalOpen) {
                        console.warn('⚠️ PROBLÈME DÉTECTÉ: Image cliquée mais modal non ouverte');
                        this.suggestImageFix();
                    }
                }, 300);
            }
        });
    }

    suggestMenuFix() {
        console.log(`
🔧 SOLUTION MENU HAMBURGER:
1. Vérifier que app-mobile-fixed.js est chargé
2. Vérifier les classes CSS mobile-menu-enhanced.css
3. Redémarrer l'application: window.golfApp.destroy(); puis rechargement
4. Vérifier la console pour d'autres erreurs
        `);
    }

    suggestImageFix() {
        console.log(`
🔧 SOLUTION IMAGES MODAL:
1. Vérifier que modal-mobile-enhanced-fixed.js est chargé
2. Attendre 1-2 secondes avant de recliquer
3. Essayer de scroller légèrement puis recliquer
4. Vérifier la console pour d'autres erreurs
        `);
    }

    // Méthode publique pour activer/désactiver le debug
    static toggle() {
        const currentUrl = new URL(window.location);
        if (currentUrl.searchParams.has('debug')) {
            currentUrl.searchParams.delete('debug');
        } else {
            currentUrl.searchParams.set('debug', 'mobile');
        }
        window.location.href = currentUrl.toString();
    }
}

// Initialiser le debugger
const mobileDebugger = new MobileDebugger();

// Exposer globalement
window.mobileDebugger = mobileDebugger;

// Helper pour activer rapidement le debug
window.enableMobileDebug = () => MobileDebugger.toggle();

console.log('📱 Mobile Debugger chargé. Utilisez ?debug=mobile dans l\'URL ou window.enableMobileDebug() pour activer.');
