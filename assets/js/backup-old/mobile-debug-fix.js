/**
 * MOBILE DEBUG FIX - Script de correction et diagnostic
 * À charger temporairement pour débugger les problèmes mobile
 */

class MobileDebugFix {
    constructor() {
        this.init();
    }

    init() {
        console.log('🔧 Mobile Debug Fix activé');
        
        // Forcer les événements après chargement complet
        setTimeout(() => {
            this.fixMenuHamburger();
            this.fixLanguageSwitcher();
            this.addDebugListeners();
        }, 1000);
        
        // Double vérification après 3 secondes
        setTimeout(() => {
            this.diagnoseProblems();
        }, 3000);
    }

    fixMenuHamburger() {
        const menuBtn = document.getElementById('mobile-menu-btn');
        const menu = document.getElementById('mobile-menu');
        const closeBtn = document.getElementById('close-menu');
        
        if (!menuBtn || !menu) {
            console.error('❌ Éléments menu manquants:', { menuBtn: !!menuBtn, menu: !!menu });
            return;
        }
        
        // Forcer l'interactivité CSS
        menuBtn.style.pointerEvents = 'auto';
        menuBtn.style.touchAction = 'manipulation';
        menuBtn.style.zIndex = '9999';
        
        // Event listener de secours direct
        menuBtn.addEventListener('touchstart', (e) => {
            console.log('🍔 Menu touchstart détecté');
        });
        
        menuBtn.addEventListener('touchend', (e) => {
            e.preventDefault();
            console.log('🍔 Menu touchend - forçage ouverture');
            menu.classList.toggle('mobile-menu-open');
        });
        
        // Fermeture forcée
        if (closeBtn) {
            closeBtn.addEventListener('touchend', (e) => {
                e.preventDefault();
                console.log('❌ Close touchend - forçage fermeture');
                menu.classList.remove('mobile-menu-open');
            });
        }
        
        console.log('✅ Menu hamburger forcé');
    }

    fixLanguageSwitcher() {
        // Desktop
        const desktopBtn = document.getElementById('lang-toggle-desktop');
        const desktopDropdown = document.getElementById('lang-dropdown-desktop');
        
        if (desktopBtn) {
            desktopBtn.style.pointerEvents = 'auto';
            desktopBtn.style.touchAction = 'manipulation';
            
            desktopBtn.addEventListener('touchend', (e) => {
                e.preventDefault();
                console.log('🌍 Desktop lang touchend');
                if (desktopDropdown) {
                    desktopDropdown.classList.toggle('dropdown-open');
                }
            });
        }
        
        // Mobile
        const mobileBtn = document.getElementById('lang-toggle-mobile');
        const mobileDropdown = document.getElementById('lang-dropdown-mobile');
        
        if (mobileBtn) {
            mobileBtn.style.pointerEvents = 'auto';
            mobileBtn.style.touchAction = 'manipulation';
            
            mobileBtn.addEventListener('touchend', (e) => {
                e.preventDefault();
                console.log('📱 Mobile lang touchend');
                if (mobileDropdown) {
                    mobileDropdown.classList.toggle('dropdown-open');
                }
            });
        }
        
        // Options de langue
        document.querySelectorAll('.lang-option, .lang-option-mobile').forEach(option => {
            option.style.pointerEvents = 'auto';
            option.style.touchAction = 'manipulation';
            
            option.addEventListener('touchend', (e) => {
                e.preventDefault();
                const lang = option.dataset.lang;
                console.log('🔄 Option lang touchend:', lang);
                
                if (window.translationManager) {
                    window.translationManager.switchLanguage(lang);
                }
            });
        });
        
        console.log('✅ Language switcher forcé');
    }

    addDebugListeners() {
        // Logger tous les touches sur les éléments problématiques
        document.addEventListener('touchstart', (e) => {
            const target = e.target;
            if (target.id === 'mobile-menu-btn' || 
                target.id === 'lang-toggle-mobile' || 
                target.id === 'lang-toggle-desktop') {
                console.log('👆 Touch détecté sur:', target.id);
            }
        });
        
        // Logger tous les clics
        document.addEventListener('click', (e) => {
            const target = e.target;
            if (target.id === 'mobile-menu-btn' || 
                target.id === 'lang-toggle-mobile' || 
                target.id === 'lang-toggle-desktop') {
                console.log('🖱️ Click détecté sur:', target.id);
            }
        });
    }

    diagnoseProblems() {
        console.log('🔍 DIAGNOSTIC MOBILE:');
        
        // Vérifier les éléments
        const elements = {
            'Menu btn': document.getElementById('mobile-menu-btn'),
            'Menu overlay': document.getElementById('mobile-menu'),
            'Lang desktop': document.getElementById('lang-toggle-desktop'),
            'Lang mobile': document.getElementById('lang-toggle-mobile'),
            'Dropdown desktop': document.getElementById('lang-dropdown-desktop'),
            'Dropdown mobile': document.getElementById('lang-dropdown-mobile')
        };
        
        Object.entries(elements).forEach(([name, element]) => {
            if (element) {
                const styles = window.getComputedStyle(element);
                console.log(`✅ ${name}:`, {
                    pointerEvents: styles.pointerEvents,
                    touchAction: styles.touchAction,
                    zIndex: styles.zIndex,
                    display: styles.display,
                    visibility: styles.visibility
                });
            } else {
                console.error(`❌ ${name}: INTROUVABLE`);
            }
        });
        
        // Vérifier les managers
        console.log('📱 Managers disponibles:', {
            touchManager: !!window.touchManager,
            mobileMenuManager: !!window.mobileMenuManager,
            translationManager: !!window.translationManager
        });
    }

    // Fonction d'urgence pour forcer l'ouverture du menu
    forceMenuOpen() {
        const menu = document.getElementById('mobile-menu');
        if (menu) {
            menu.classList.add('mobile-menu-open');
            document.body.classList.add('body-menu-locked');
            console.log('🚨 Menu forcé ouvert');
        }
    }

    // Fonction d'urgence pour changer de langue
    forceLanguageSwitch() {
        if (window.translationManager) {
            const currentLang = window.translationManager.currentLanguage;
            const newLang = currentLang === 'fr' ? 'en' : 'fr';
            window.translationManager.switchLanguage(newLang);
            console.log('🚨 Langue forcée:', newLang);
        }
    }
}

// Initialiser le debug fix
window.mobileDebugFix = new MobileDebugFix();

// Exposer les fonctions d'urgence globalement
window.forceMenuOpen = () => window.mobileDebugFix.forceMenuOpen();
window.forceLanguageSwitch = () => window.mobileDebugFix.forceLanguageSwitch();
window.diagnoseMobile = () => window.mobileDebugFix.diagnoseProblems();

console.log('🔧 Mobile Debug Fix chargé. Commandes disponibles:');
console.log('- forceMenuOpen() : Forcer ouverture menu');
console.log('- forceLanguageSwitch() : Forcer changement langue');
console.log('- diagnoseMobile() : Diagnostic complet');
