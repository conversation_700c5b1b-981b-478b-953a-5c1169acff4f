/**
 * DEBUG MENU HAMBURGER - Test et fix brutal
 */

(function() {
    'use strict';
    
    console.log('🔧 DEBUG MENU HAMBURGER - Démarrage...');
    
    function forceMenuHamburger() {
        const menuBtn = document.getElementById('mobile-menu-btn');
        const menuOverlay = document.getElementById('mobile-menu');
        
        if (!menuBtn) {
            console.log('❌ Bouton hamburger introuvable !');
            console.log('Éléments trouvés:', document.querySelectorAll('[id*="menu"]'));
            return;
        }
        
        if (!menuOverlay) {
            console.log('❌ Menu overlay introuvable !');
            console.log('Éléments trouvés:', document.querySelectorAll('[id*="mobile"]'));
            return;
        }
        
        console.log('✅ Éléments trouvés:', { menuBtn: !!menuBtn, menuOverlay: !!menuOverlay });
        
        // FORCE BRUTALE des styles CSS
        menuBtn.style.cssText = `
            display: flex !important;
            pointer-events: auto !important;
            touch-action: manipulation !important;
            cursor: pointer !important;
            z-index: 99999 !important;
            position: relative !important;
            min-width: 44px !important;
            min-height: 44px !important;
            opacity: 1 !important;
            visibility: visible !important;
            background: none !important;
            border: none !important;
            outline: none !important;
            align-items: center !important;
            justify-content: center !important;
        `;
        
        // FORCE BRUTALE d'un listener onclick
        menuBtn.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            console.log('🍔 CLICK BRUTAL DÉTECTÉ !');
            
            const isOpen = menuOverlay.classList.contains('mobile-menu-open') || 
                          menuOverlay.style.display === 'flex';
            
            if (isOpen) {
                // Fermer
                menuOverlay.classList.remove('mobile-menu-open');
                menuOverlay.style.display = 'none';
                document.body.classList.remove('body-menu-locked');
                console.log('❌ Menu fermé brutalement');
            } else {
                // Ouvrir
                menuOverlay.classList.add('mobile-menu-open');
                menuOverlay.style.cssText = `
                    display: flex !important;
                    position: fixed !important;
                    top: 0 !important;
                    left: 0 !important;
                    width: 100% !important;
                    height: 100% !important;
                    background: rgba(0, 0, 0, 0.95) !important;
                    z-index: 99998 !important;
                    flex-direction: column !important;
                    align-items: center !important;
                    justify-content: center !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                `;
                document.body.classList.add('body-menu-locked');
                document.body.style.overflow = 'hidden';
                console.log('✅ Menu ouvert brutalement');
            }
        };
        
        // FORCE BRUTALE aussi pour touch
        menuBtn.ontouchend = menuBtn.onclick;
        
        console.log('💪 Fix brutal appliqué au menu hamburger !');
        console.log('🔴 Bouton avec bordure rouge pour debug');
    }
    
    // Test immédiat
    function testMenu() {
        const menuBtn = document.getElementById('mobile-menu-btn');
        if (menuBtn) {
            console.log('🧪 Test du menu...');
            menuBtn.click();
            
            setTimeout(() => {
                const menuOverlay = document.getElementById('mobile-menu');
                const isOpen = menuOverlay && (
                    menuOverlay.classList.contains('mobile-menu-open') || 
                    menuOverlay.style.display === 'flex'
                );
                
                console.log('📊 Résultat test:', isOpen ? '✅ FONCTIONNE' : '❌ NE FONCTIONNE PAS');
                
                if (isOpen) {
                    // Refermer pour nettoyer
                    setTimeout(() => menuBtn.click(), 1000);
                }
            }, 500);
        }
    }
    
    // API globale
    window.debugMenuHamburger = {
        force: forceMenuHamburger,
        test: testMenu
    };
    
    // Démarrage automatique
    function init() {
        setTimeout(() => {
            forceMenuHamburger();
            console.log('💡 Pour tester: debugMenuHamburger.test()');
        }, 2000);
    }
    
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
})();
