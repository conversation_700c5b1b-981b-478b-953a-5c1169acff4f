/**
 * Application principale GolfinThaï - Version Mobile Corrigée
 * Correction des problèmes de menu hamburger et interactions mobiles
 */

class GolfinThaiApp {
    constructor() {
        this.config = window.GolfinThaiConfig;
        this.isMenuOpen = false;
        this.mobileMenuListeners = new Set(); // Track active listeners
        this.init();
    }

    init() {
        this.bindEvents();
        this.initAnimations();
    }

    bindEvents() {
        document.addEventListener('DOMContentLoaded', () => {
            this.handleDOMContentLoaded();
        });
        
        window.addEventListener('scroll', this.throttle(this.handleScroll.bind(this), 100));
        window.addEventListener('resize', this.debounce(this.handleResize.bind(this), 250));
    }

    handleDOMContentLoaded() {
        // Initialiser tous les modules une fois le DOM chargé
        this.initMobileMenu();
        this.initSmoothScroll();
        this.initRevealAnimations();
        this.initNavigation();
        this.updateCopyrightYear();
        this.setupIncludesLoading();
        
        // Marquer que l'app est initialisée
        document.body.dataset.appInitialized = 'true';
    }

    initAnimations() {
        // Animations générales déjà gérées par initRevealAnimations
    }

    initNavigation() {
        // Marquer le lien actif dans la navigation
        const navLinks = document.querySelectorAll('.nav-link');
        const currentHash = window.location.hash || '#home';
        
        navLinks.forEach(link => {
            if (link.getAttribute('href') === currentHash) {
                link.classList.add('active');
            }
        });
    }

    // 🔥 CORRECTION PRINCIPALE : Menu mobile amélioré
    initMobileMenu() {
        const mobileMenuBtn = document.querySelector(this.config.selectors.mobileMenuBtn);
        const mobileMenu = document.querySelector(this.config.selectors.mobileMenu);
        const closeMenuBtn = document.querySelector(this.config.selectors.closeMenuBtn);

        if (!mobileMenuBtn || !mobileMenu || !closeMenuBtn) {
            // Attendre que les éléments soient chargés
            setTimeout(() => {
                this.initMobileMenu();
            }, 500);
            return;
        }

        // 🔥 NOUVEAU : Nettoyer les anciens event listeners avant d'ajouter les nouveaux
        this.cleanupMobileMenuListeners();

        // Créer les nouvelles fonctions d'event listeners
        const openMenuHandler = (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.toggleMobileMenu(true);
        };

        const closeMenuHandler = (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.toggleMobileMenu(false);
        };

        // Ajouter les event listeners avec support mobile amélioré
        mobileMenuBtn.addEventListener('click', openMenuHandler);
        mobileMenuBtn.addEventListener('touchend', openMenuHandler, { passive: false });
        
        closeMenuBtn.addEventListener('click', closeMenuHandler);
        closeMenuBtn.addEventListener('touchend', closeMenuHandler, { passive: false });
        
        // Stocker les références pour nettoyage ultérieur
        this.mobileMenuListeners.add({ element: mobileMenuBtn, type: 'click', handler: openMenuHandler });
        this.mobileMenuListeners.add({ element: mobileMenuBtn, type: 'touchend', handler: openMenuHandler });
        this.mobileMenuListeners.add({ element: closeMenuBtn, type: 'click', handler: closeMenuHandler });
        this.mobileMenuListeners.add({ element: closeMenuBtn, type: 'touchend', handler: closeMenuHandler });
        
        // Fermer le menu en cliquant sur un lien
        const menuLinks = mobileMenu.querySelectorAll('a');
        menuLinks.forEach(link => {
            const linkHandler = () => this.toggleMobileMenu(false);
            link.addEventListener('click', linkHandler);
            link.addEventListener('touchend', linkHandler);
            
            this.mobileMenuListeners.add({ element: link, type: 'click', handler: linkHandler });
            this.mobileMenuListeners.add({ element: link, type: 'touchend', handler: linkHandler });
        });

        // 🔥 NOUVEAU : Gestion avancée des touches pour mobile
        document.addEventListener('keydown', (e) => {
            if (this.isMenuOpen && e.key === 'Escape') {
                this.toggleMobileMenu(false);
            }
        });

        console.log('✅ Menu mobile initialisé avec support tactile amélioré');
    }

    // 🔥 NOUVEAU : Nettoyage des event listeners
    cleanupMobileMenuListeners() {
        this.mobileMenuListeners.forEach(({ element, type, handler }) => {
            if (element) {
                element.removeEventListener(type, handler);
            }
        });
        this.mobileMenuListeners.clear();
    }

    // 🔥 AMÉLIORÉ : Toggle menu avec support tactile
    toggleMobileMenu(open) {
        const mobileMenu = document.querySelector(this.config.selectors.mobileMenu);
        const mobileMenuBtn = document.querySelector(this.config.selectors.mobileMenuBtn);
        
        if (!mobileMenu || !mobileMenuBtn) {
            return;
        }
        
        if (open && !this.isMenuOpen) {
            // Ouvrir le menu
            mobileMenu.classList.remove('mobile-menu-closed');
            mobileMenu.classList.add('mobile-menu-open');
            mobileMenu.setAttribute('aria-hidden', 'false');
            mobileMenuBtn.setAttribute('aria-expanded', 'true');
            
            // Lock body scroll
            document.body.classList.add('body-menu-locked');
            
            // Focus management for accessibility
            setTimeout(() => {
                const firstLink = mobileMenu.querySelector('a');
                if (firstLink) firstLink.focus();
            }, 100);
            
            this.isMenuOpen = true;
            console.log('📱 Menu mobile ouvert');
            
        } else if (!open && this.isMenuOpen) {
            // Fermer le menu
            mobileMenu.classList.remove('mobile-menu-open');
            mobileMenu.classList.add('mobile-menu-closed');
            mobileMenu.setAttribute('aria-hidden', 'true');
            mobileMenuBtn.setAttribute('aria-expanded', 'false');
            
            // Unlock body scroll
            document.body.classList.remove('body-menu-locked');
            
            // Return focus to menu button
            mobileMenuBtn.focus();
            
            // Nettoyer la classe fermée après l'animation
            setTimeout(() => {
                mobileMenu.classList.remove('mobile-menu-closed');
            }, 300);
            
            this.isMenuOpen = false;
            console.log('❌ Menu mobile fermé');
        }
    }

    // Scroll smooth pour les ancres - AMÉLIORÉ
    initSmoothScroll() {
        const links = document.querySelectorAll('a[href^="#"]');
        links.forEach(link => {
            const smoothScrollHandler = (e) => {
                const targetId = link.getAttribute('href');
                if (targetId === '#') return;
                
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    e.preventDefault();
                    
                    // Fermer le menu mobile si ouvert AVANT le scroll
                    if (this.isMenuOpen) {
                        this.toggleMobileMenu(false);
                        // Attendre la fermeture du menu avant de scroller
                        setTimeout(() => {
                            targetElement.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                            });
                        }, 350);
                    } else {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                }
            };

            link.addEventListener('click', smoothScrollHandler);
            // Support tactile pour mobile
            link.addEventListener('touchend', smoothScrollHandler, { passive: false });
        });
    }

    // Animations de révélation au scroll
    initRevealAnimations() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('revealed');
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '50px'
        });

        const elementsToReveal = document.querySelectorAll('.reveal-animation');
        elementsToReveal.forEach(el => observer.observe(el));
    }

    // Gestion du scroll
    handleScroll() {
        const navbar = document.querySelector(this.config.selectors.navBar);
        if (navbar) {
            if (window.scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        }
    }

    // 🔥 AMÉLIORÉ : Gestion du redimensionnement
    handleResize() {
        // Fermer le menu mobile si on passe en desktop
        if (window.innerWidth >= 1024 && this.isMenuOpen) {
            this.toggleMobileMenu(false);
        }
        
        // Réinitialiser le menu si nécessaire après changement d'orientation
        if (window.innerWidth < 1024) {
            const mobileMenuBtn = document.querySelector(this.config.selectors.mobileMenuBtn);
            if (mobileMenuBtn && this.mobileMenuListeners.size === 0) {
                // Réinitialiser le menu si aucun listener n'est actif
                setTimeout(() => this.initMobileMenu(), 100);
            }
        }
    }

    // Mise à jour de l'année dans le footer
    updateCopyrightYear() {
        const yearElement = document.querySelector('.copyright-year');
        if (yearElement) {
            yearElement.textContent = new Date().getFullYear();
        }
    }

    // 🔥 AMÉLIORÉ : Gestion du chargement des includes
    setupIncludesLoading() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    const target = mutation.target;
                    if (target.id && target.id.includes('include')) {
                        setTimeout(() => {
                            this.reinitializeAfterInclude(target);
                        }, 100);
                    }
                }
            });
        });

        const includeContainers = ['header-include', 'destinations-include', 'footer-include'];
        includeContainers.forEach(id => {
            const container = document.getElementById(id);
            if (container) {
                observer.observe(container, { childList: true, subtree: true });
            }
        });
    }

    // 🔥 CORRIGÉ : Réinitialiser après chargement d'un include
    reinitializeAfterInclude(container) {
        // Éviter les réinitialisations multiples mais permettre si nécessaire
        if (container.dataset.reinitialized === 'true') return;
        
        // Réinitialiser les traductions pour le nouveau contenu
        if (window.translationManager && window.translationManager.isInitialized) {
            window.translationManager.retranslateContent(container);
        }
        
        // Réinitialiser les événements spécifiques pour le header
        if (container.id === 'header-include') {
            // 🔥 CORRECTION : Toujours réinitialiser le menu pour les nouveaux éléments
            setTimeout(() => {
                this.initMobileMenu(); // Plus de vérification dataset.headerInitialized
                if (window.translationManager && !window.translationManager.buttonsSetup) {
                    window.translationManager.setupLanguageButtons();
                }
            }, 200);
        }
        
        // Marquer comme réinitialisé
        container.dataset.reinitialized = 'true';
    }

    // Utilitaires
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }

    debounce(func, wait, immediate) {
        let timeout;
        return function executedFunction() {
            const context = this;
            const args = arguments;
            const later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    // 🔥 NOUVEAU : Nettoyage pour réinitialisation
    destroy() {
        this.cleanupMobileMenuListeners();
        this.isMenuOpen = false;
    }
}

// Initialiser l'application
const app = new GolfinThaiApp();

// Exposer globalement pour debug
window.golfApp = app;