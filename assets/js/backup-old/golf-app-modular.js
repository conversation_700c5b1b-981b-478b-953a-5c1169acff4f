/**
 * GOLF APP - Coordinateur Principal (Clean Version)
 * Architecture modulaire - Chaque fonctionnalité dans son module
 */

class GolfApp {
    constructor() {
        this.modules = {
            touch: null,
            menu: null,
            modal: null,
            navigation: null
        };
        
        this.isInitialized = false;
        this.init();
    }

    init() {
        // Attendre que tous les modules soient chargés
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        // Vérifier que tous les modules sont disponibles
        this.modules.touch = window.touchManager;
        this.modules.menu = window.mobileMenuManager;
        this.modules.modal = window.modalManager;
        this.modules.navigation = window.navigationManager;

        // Initialiser les autres fonctionnalités
        this.initAnimations();
        this.initScrollEffects();
        this.initIncludesHandling();
        this.updateCopyrightYear();
        
        this.isInitialized = true;
        console.log('✅ GolfApp initialisé avec architecture modulaire');
        
        // Debug des modules
        this.logModulesStatus();
    }

    initAnimations() {
        // Animations de révélation au scroll
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('revealed');
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '50px'
        });

        const elementsToReveal = document.querySelectorAll('.reveal-animation');
        elementsToReveal.forEach(el => observer.observe(el));
    }

    initScrollEffects() {
        // Navbar scrolled effect
        let scrollTimeout;
        
        window.addEventListener('scroll', () => {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                const navbar = document.querySelector('.nav-bar');
                if (navbar) {
                    if (window.scrollY > 100) {
                        navbar.classList.add('scrolled');
                    } else {
                        navbar.classList.remove('scrolled');
                    }
                }
            }, 10);
        }, { passive: true });
    }

    initIncludesHandling() {
        // Observer pour les includes qui se chargent dynamiquement
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    const target = mutation.target;
                    
                    // Header include chargé
                    if (target.id === 'header-include') {
                        this.handleHeaderLoaded();
                    }
                    
                    // Autres includes
                    if (target.id && target.id.includes('include')) {
                        this.handleIncludeLoaded(target);
                    }
                }
            });
        });

        // Observer les conteneurs d'includes
        const includeContainers = ['header-include', 'destinations-include', 'footer-include', 'testimonials-include'];
        includeContainers.forEach(id => {
            const container = document.getElementById(id);
            if (container) {
                observer.observe(container, { childList: true, subtree: true });
            }
        });
    }

    handleHeaderLoaded() {
        // Réinitialiser le menu mobile après chargement du header
        setTimeout(() => {
            if (this.modules.menu) {
                this.modules.menu.reinitialize();
            }
        }, 200);
    }

    handleIncludeLoaded(container) {
        // Réinitialiser les traductions pour le nouveau contenu
        setTimeout(() => {
            if (window.translationManager && window.translationManager.isInitialized) {
                window.translationManager.retranslateContent(container);
            }
        }, 100);
    }

    updateCopyrightYear() {
        const yearElement = document.querySelector('.copyright-year');
        if (yearElement) {
            yearElement.textContent = new Date().getFullYear();
        }
    }

    // Gestion du redimensionnement
    handleResize() {
        // Fermer le menu mobile si on passe en desktop
        if (window.innerWidth >= 1024 && this.modules.menu && this.modules.menu.isOpen) {
            this.modules.menu.close();
        }
    }

    // Debug et monitoring
    logModulesStatus() {
        const status = {
            touchManager: !!this.modules.touch,
            mobileMenuManager: !!this.modules.menu,
            modalManager: !!this.modules.modal,
            navigationManager: !!this.modules.navigation,
            initialized: this.isInitialized
        };
        
        console.log('📊 Status des modules:', status);
        
        // Vérifier les stats détaillées
        if (this.modules.touch) {
            console.log('👆 TouchManager stats:', this.modules.touch.getStats());
        }
        
        if (this.modules.menu) {
            console.log('🍔 MenuManager state:', this.modules.menu.getState());
        }
        
        if (this.modules.modal) {
            console.log('🖼️ ModalManager state:', this.modules.modal.getState());
        }
        
        if (this.modules.navigation) {
            console.log('🎯 NavigationManager state:', this.modules.navigation.getState());
        }
    }

    // API publique pour debug
    getSystemStatus() {
        return {
            app: {
                initialized: this.isInitialized,
                modules: Object.keys(this.modules).reduce((acc, key) => {
                    acc[key] = !!this.modules[key];
                    return acc;
                }, {})
            },
            touch: this.modules.touch ? this.modules.touch.getStats() : null,
            menu: this.modules.menu ? this.modules.menu.getState() : null,
            modal: this.modules.modal ? this.modules.modal.getState() : null,
            navigation: this.modules.navigation ? this.modules.navigation.getState() : null
        };
    }

    // Force restart d'un module
    restartModule(moduleName) {
        switch (moduleName) {
            case 'menu':
                if (this.modules.menu) {
                    this.modules.menu.reinitialize();
                }
                break;
            case 'modal':
                if (this.modules.modal) {
                    this.modules.modal.setup();
                }
                break;
            case 'navigation':
                if (this.modules.navigation) {
                    this.modules.navigation.setup();
                }
                break;
            default:
                console.warn('Module inconnu:', moduleName);
        }
    }
}

// Attendre que tous les modules soient chargés avant d'initialiser l'app
document.addEventListener('DOMContentLoaded', () => {
    // Petite pause pour s'assurer que tous les scripts sont chargés
    setTimeout(() => {
        window.golfApp = new GolfApp();
        
        // Gestion du redimensionnement
        window.addEventListener('resize', () => {
            if (window.golfApp) {
                window.golfApp.handleResize();
            }
        });
    }, 100);
});

console.log('🚀 Golf App - Architecture modulaire chargée');