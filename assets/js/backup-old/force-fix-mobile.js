/**
 * Fix manuel pour forcer l'application des corrections
 * À exécuter dans la console si les corrections automatiques ne fonctionnent pas
 */

function forceFixMobile() {
    console.log('🔧 Application forcée des corrections mobile...');
    
    // 1. Fix Menu Hamburger
    const menuBtn = document.getElementById('mobile-menu-btn');
    const menuOverlay = document.getElementById('mobile-menu');
    const closeBtn = document.getElementById('close-menu');
    
    if (menuBtn && menuOverlay) {
        // Nettoyer les anciens listeners - DÉSACTIVÉ pour éviter de casser les autres systèmes
        // const newMenuBtn = menuBtn.cloneNode(true);
        // menuBtn.parentNode.replaceChild(newMenuBtn, menuBtn);
        
        // Ajouter le nouveau listener - COMPATIBLE
        menuBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('🍔 Menu clicked!');
            
            if (menuOverlay.classList.contains('mobile-menu-open')) {
                // Fermer
                menuOverlay.classList.remove('mobile-menu-open');
                menuOverlay.setAttribute('aria-hidden', 'true');
                menuBtn.setAttribute('aria-expanded', 'false');
                document.body.classList.remove('body-menu-locked');
                console.log('❌ Menu fermé');
            } else {
                // Ouvrir
                menuOverlay.classList.add('mobile-menu-open');
                menuOverlay.setAttribute('aria-hidden', 'false');
                menuBtn.setAttribute('aria-expanded', 'true');
                document.body.classList.add('body-menu-locked');
                console.log('✅ Menu ouvert');
            }
        });
        
        console.log('✅ Menu hamburger fixé');
    } else {
        console.error('❌ Éléments du menu non trouvés');
    }
    
    // 2. Fix bouton fermer
    if (closeBtn && menuOverlay) {
        // Pas de cloneNode destructif
        // const newCloseBtn = closeBtn.cloneNode(true);
        // closeBtn.parentNode.replaceChild(newCloseBtn, closeBtn);
        
        closeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            menuOverlay.classList.remove('mobile-menu-open');
            menuOverlay.setAttribute('aria-hidden', 'true');
            document.getElementById('mobile-menu-btn').setAttribute('aria-expanded', 'false');
            document.body.classList.remove('body-menu-locked');
            console.log('❌ Menu fermé via bouton close');
        });
    }
    
    // 3. Fix Language Switcher
    const langBtn = document.getElementById('lang-toggle-mobile');
    const langDropdown = document.getElementById('lang-dropdown-mobile');
    
    if (langBtn && langDropdown) {
        // Nettoyer les anciens listeners - DÉSACTIVÉ
        // const newLangBtn = langBtn.cloneNode(true);
        // langBtn.parentNode.replaceChild(newLangBtn, langBtn);
        
        // Ajouter le nouveau listener - COMPATIBLE
        langBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('🌍 Language button clicked!');
            
            const isOpen = langDropdown.classList.contains('dropdown-open') || 
                          langDropdown.style.display === 'block';
            
            if (isOpen) {
                // Fermer
                langDropdown.classList.remove('dropdown-open');
                langDropdown.style.display = 'none';
                langBtn.setAttribute('aria-expanded', 'false');
                console.log('❌ Dropdown fermé');
            } else {
                // Ouvrir
                langDropdown.classList.add('dropdown-open');
                langDropdown.style.display = 'block';
                langBtn.setAttribute('aria-expanded', 'true');
                console.log('✅ Dropdown ouvert');
            }
        });
        
        // Fix les options de langue
        const langOptions = langDropdown.querySelectorAll('.lang-option-mobile');
        langOptions.forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const lang = this.dataset.lang;
                console.log('🌍 Language selected:', lang);
                
                if (lang && window.translationManager) {
                    window.translationManager.switchLanguage(lang);
                }
                
                // Fermer le dropdown
                langDropdown.classList.remove('dropdown-open');
                langDropdown.style.display = 'none';
                document.getElementById('lang-toggle-mobile').setAttribute('aria-expanded', 'false');
            });
        });
        
        console.log('✅ Language switcher fixé');
    } else {
        console.error('❌ Éléments de langue non trouvés');
    }
    
    console.log('🎉 Corrections appliquées!');
}

// Exécuter automatiquement après 2 secondes
setTimeout(forceFixMobile, 2000);

// Exposer globalement
window.forceFixMobile = forceFixMobile;

console.log('💡 Pour réappliquer manuellement, tapez: forceFixMobile()');
