/**
 * TEST SIMPLE MENU HAMBURGER
 * Investigation étape par étape
 */

(function() {
    'use strict';
    
    console.log('🔬 TEST SIMPLE MENU HAMBURGER...');
    
    function testSimple() {
        const menuBtn = document.getElementById('mobile-menu-btn');
        const menuOverlay = document.getElementById('mobile-menu');
        
        if (!menuBtn) {
            console.log('❌ STOP: Bouton hamburger introuvable');
            return;
        }
        
        if (!menuOverlay) {
            console.log('❌ STOP: Menu overlay introuvable');
            return;
        }
        
        console.log('✅ Éléments trouvés');
        
        // TEST 1: Le bouton est-il cliquable visuellement ?
        const rect = menuBtn.getBoundingClientRect();
        const elementAtCenter = document.elementFromPoint(
            rect.left + rect.width / 2,
            rect.top + rect.height / 2
        );
        
        console.log('TEST 1 - Élément au centre du bouton:', elementAtCenter);
        console.log('Est-ce le bouton lui-même ?', elementAtCenter === menuBtn);
        
        if (elementAtCenter !== menuBtn) {
            console.log('⚠️ PROBLÈME: Un autre élément cache le bouton !');
            console.log('Élément qui cache:', elementAtCenter);
        }
        
        // TEST 2: Styles CSS du bouton
        const styles = getComputedStyle(menuBtn);
        console.log('TEST 2 - Styles bouton:');
        console.log('  pointer-events:', styles.pointerEvents);
        console.log('  z-index:', styles.zIndex);
        console.log('  position:', styles.position);
        
        // TEST 3: Styles CSS du menu overlay  
        const menuStyles = getComputedStyle(menuOverlay);
        console.log('TEST 3 - Styles menu:');
        console.log('  display:', menuStyles.display);
        console.log('  z-index:', menuStyles.zIndex);
        console.log('  position:', menuStyles.position);
        
        // TEST 4: Test d'ouverture forcée
        console.log('TEST 4 - Ouverture forcée...');
        menuOverlay.style.cssText = `
            display: flex !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background: rgba(255, 0, 0, 0.8) !important;
            z-index: 999999 !important;
            align-items: center !important;
            justify-content: center !important;
        `;
        
        setTimeout(() => {
            const isVisible = menuOverlay.offsetWidth > 0 && menuOverlay.offsetHeight > 0;
            console.log('Menu visible après force ?', isVisible);
            
            if (isVisible) {
                console.log('✅ Le menu PEUT s\'afficher');
                
                // Refermer
                setTimeout(() => {
                    menuOverlay.style.display = 'none';
                    console.log('Menu refermé');
                }, 2000);
            } else {
                console.log('❌ Le menu ne peut PAS s\'afficher');
            }
        }, 500);
    }
    
    // TEST 5: Ajouter un listener direct ultra simple
    function testListener() {
        const menuBtn = document.getElementById('mobile-menu-btn');
        if (!menuBtn) return;
        
        console.log('TEST 5 - Ajout listener direct...');
        
        // Supprimer TOUS les autres listeners
        const newBtn = menuBtn.cloneNode(true);
        menuBtn.parentNode.replaceChild(newBtn, menuBtn);
        
        // Ajouter UN SEUL listener ultra simple
        newBtn.onclick = function(e) {
            console.log('🎯 CLICK ULTRA SIMPLE DÉTECTÉ !');
            
            const menuOverlay = document.getElementById('mobile-menu');
            if (!menuOverlay) {
                console.log('❌ Menu overlay introuvable dans le click');
                return;
            }
            
            // Toggle ultra simple
            if (menuOverlay.style.display === 'flex') {
                menuOverlay.style.display = 'none';
                console.log('Menu fermé par click simple');
            } else {
                menuOverlay.style.cssText = `
                    display: flex !important;
                    position: fixed !important;
                    top: 0 !important;
                    left: 0 !important;
                    width: 100% !important;
                    height: 100% !important;
                    background: rgba(0, 0, 0, 0.9) !important;
                    z-index: 999999 !important;
                    flex-direction: column !important;
                    align-items: center !important;
                    justify-content: center !important;
                    color: white !important;
                    font-size: 24px !important;
                `;
                
                menuOverlay.innerHTML = `
                    <div style="text-align: center;">
                        <h2>🎉 MENU OUVERT !</h2>
                        <p>Cliquez à nouveau pour fermer</p>
                        <p>Si vous voyez ça, le problème est résolu !</p>
                    </div>
                `;
                
                console.log('✅ Menu ouvert par click simple !');
            }
        };
        
        console.log('✅ Listener direct installé');
        console.log('🎯 TESTEZ MAINTENANT le bouton hamburger !');
    }
    
    // API globale
    window.testSimpleMenu = {
        diagnostic: testSimple,
        listener: testListener,
        reset: function() {
            const menuOverlay = document.getElementById('mobile-menu');
            if (menuOverlay) {
                menuOverlay.style.display = 'none';
                console.log('Menu reset');
            }
        }
    };
    
    // Auto-run
    setTimeout(() => {
        testSimple();
        
        setTimeout(() => {
            console.log('\n💡 COMMANDES DISPONIBLES:');
            console.log('testSimpleMenu.listener() - Installer listener direct');
            console.log('testSimpleMenu.reset() - Reset le menu');
            console.log('\n🎯 Pour résoudre, tapez: testSimpleMenu.listener()');
        }, 1000);
    }, 4000);
    
})();
