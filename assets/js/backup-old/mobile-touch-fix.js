/**
 * Fix Mobile Touch Events - Éviter le double clic
 * Amélioration spécifique pour les modals sur mobile
 */

class MobileTouchFix {
    constructor() {
        this.isTouch = false;
        this.clickTimeout = null;
        this.init();
    }

    init() {
        // Détecter si c'est un appareil tactile
        this.isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
        
        if (this.isTouch) {
            this.setupTouchEvents();
            console.log('📱 Mobile touch events configured');
        }
    }

    setupTouchEvents() {
        // Attendre que les cartes soient chargées
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                this.configureCourseCards();
            }, 1000);
        });
    }

    configureCourseCards() {
        const courseCards = document.querySelectorAll('.course-card');
        
        courseCards.forEach((card, index) => {
            // Supprimer les anciens event listeners
            const newCard = card.cloneNode(true);
            card.parentNode.replaceChild(newCard, card);
            
            // Ajouter le nouvel event listener optimisé
            this.addTouchListener(newCard, index);
        });
        
        console.log(`✅ Configured ${courseCards.length} course cards for mobile`);
    }

    addTouchListener(card, index) {
        let touchStarted = false;
        let touchMoved = false;
        
        // Touch start
        card.addEventListener('touchstart', (e) => {
            touchStarted = true;
            touchMoved = false;
            card.classList.add('touch-feedback-pressed');
        }, { passive: true });
        
        // Touch move - empêcher l'ouverture si l'utilisateur scroll
        card.addEventListener('touchmove', (e) => {
            touchMoved = true;
            card.classList.remove('touch-feedback-pressed');
            card.classList.add('touch-feedback-released');
        }, { passive: true });
        
        // Touch end - ouvrir la modal
        card.addEventListener('touchend', (e) => {
            if (touchStarted && !touchMoved) {
                e.preventDefault();
                e.stopPropagation();
                
                // Animation de feedback
                card.classList.remove('touch-feedback-pressed');
                card.classList.add('touch-feedback-released');
                
                // Petit délai pour l'animation puis ouvrir
                setTimeout(() => {
                    this.openModal(card);
                }, 100);
            }
            
            touchStarted = false;
        }, { passive: false });
        
        // Fallback click pour desktop
        card.addEventListener('click', (e) => {
            if (!this.isTouch) {
                e.preventDefault();
                this.openModal(card);
            }
        });
    }

    openModal(card) {
        // Empêcher les ouvertures multiples
        if (card.dataset.opening === 'true') {
            return;
        }
        
        card.dataset.opening = 'true';
        
        // Trouver le course ID
        const courseId = card.dataset.course;
        
        if (courseId && window.golfModalManager) {
            console.log(`🎯 Opening modal for: ${courseId}`);
            window.golfModalManager.openModal(courseId);
        } else {
            console.error('❌ Modal manager not found or no course ID');
        }
        
        // Reset après un délai
        setTimeout(() => {
            card.dataset.opening = 'false';
        }, 1000);
    }

    // Fonction utilitaire pour débugger
    debug() {
        return {
            isTouch: this.isTouch,
            courseCards: document.querySelectorAll('.course-card').length,
            modalManager: !!window.golfModalManager
        };
    }
}

// Initialiser le fix mobile
const mobileTouchFix = new MobileTouchFix();

// Exposer globalement pour debug
window.mobileTouchFix = mobileTouchFix;
window.debugTouch = () => {
    console.log('Touch Debug:', mobileTouchFix.debug());
    return mobileTouchFix.debug();
};

// Réinitialiser après chargement des includes
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        if (mobileTouchFix.isTouch) {
            mobileTouchFix.configureCourseCards();
        }
    }, 2000);
});

console.log('📱 Mobile touch fix loaded - use window.debugTouch() for debug');
