/**
 * FALLBACK MOBILE - Script de secours
 * Se déclenche si le gestionnaire principal ne fonctionne pas
 */

// Attendre que tout soit chargé
window.addEventListener('load', function() {
    setTimeout(() => {
        console.log('🔄 Fallback Mobile: Vérification...');
        
        // Vérifier si le gestionnaire principal fonctionne
        if (window.mobileManager && typeof window.mobileManager.openImageModal === 'function') {
            console.log('✅ Gestionnaire principal OK - Fallback non nécessaire');
            return;
        }
        
        console.warn('⚠️ Gestionnaire principal manquant - Activation du fallback');
        
        // FALLBACK 1: Menu Mobile
        setupFallbackMenu();
        
        // FALLBACK 2: Modales d'images
        setupFallbackModals();
        
    }, 4000); // Attendre 4 secondes
});

function setupFallbackMenu() {
    const menuBtn = document.getElementById('mobile-menu-btn');
    const menu = document.getElementById('mobile-menu');
    const closeBtn = document.getElementById('close-menu');
    
    if (!menuBtn || !menu) {
        console.error('❌ Fallback Menu: Éléments non trouvés');
        return;
    }
    
    console.log('🔧 Fallback Menu: Configuration...');
    
    // Éviter les doublons
    if (menuBtn.dataset.fallbackMenu) return;
    menuBtn.dataset.fallbackMenu = 'true';
    
    let isMenuOpen = false;
    
    // Bouton hamburger
    menuBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('🍔 Fallback: Menu click');
        
        if (isMenuOpen) {
            closeMenu();
        } else {
            openMenu();
        }
    });
    
    // Bouton fermer
    if (closeBtn) {
        closeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeMenu();
        });
    }
    
    // Liens du menu
    const menuLinks = menu.querySelectorAll('a');
    menuLinks.forEach(link => {
        link.addEventListener('click', function() {
            console.log('🔗 Fallback: Navigation vers', link.getAttribute('href'));
            setTimeout(() => {
                closeMenu();
            }, 100);
        });
    });
    
    function openMenu() {
        menu.classList.add('mobile-menu-open');
        menu.setAttribute('aria-hidden', 'false');
        menuBtn.setAttribute('aria-expanded', 'true');
        document.body.classList.add('body-menu-locked');
        isMenuOpen = true;
        console.log('✅ Fallback: Menu ouvert');
    }
    
    function closeMenu() {
        menu.classList.remove('mobile-menu-open');
        menu.setAttribute('aria-hidden', 'true');
        menuBtn.setAttribute('aria-expanded', 'false');
        document.body.classList.remove('body-menu-locked');
        isMenuOpen = false;
        console.log('❌ Fallback: Menu fermé');
    }
    
    console.log('✅ Fallback Menu configuré');
}

function setupFallbackModals() {
    const modal = document.getElementById('course-modal');
    
    if (!modal) {
        console.error('❌ Fallback Modal: Modal non trouvée');
        return;
    }
    
    console.log('🔧 Fallback Modal: Configuration...');
    
    let isModalOpen = false;
    let scrollPosition = 0;
    
    // Event listener sur toutes les images
    document.addEventListener('click', function(e) {
        const image = e.target.closest('img[src*="assets/images"]');
        
        if (image && isImageClickable(image)) {
            e.preventDefault();
            console.log('🖼️ Fallback: Image cliquée', image.src);
            openImageModal(image);
        }
    });
    
    // Bouton fermer modal
    const modalClose = modal.querySelector('.modal-close');
    if (modalClose) {
        modalClose.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeModal();
        });
    }
    
    // Fermer sur clic backdrop
    modal.addEventListener('click', function(e) {
        if (e.target === modal || e.target.classList.contains('modal-backdrop')) {
            closeModal();
        }
    });
    
    // Fermer avec Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && isModalOpen) {
            closeModal();
        }
    });
    
    function isImageClickable(image) {
        // Exclure certaines images
        if (image.closest('.hero-carousel') ||
            image.closest('.logo') ||
            image.closest('button') ||
            image.closest('a') ||
            image.alt?.toLowerCase().includes('logo')) {
            return false;
        }
        return true;
    }
    
    function openImageModal(imageElement) {
        if (isModalOpen) {
            closeModal();
            setTimeout(() => {
                openImageModal(imageElement);
            }, 350);
            return;
        }
        
        // Sauvegarder position scroll
        scrollPosition = window.pageYOffset;
        
        // Remplir la modal
        const modalImage = modal.querySelector('.modal-image');
        const modalTitle = modal.querySelector('.modal-title');
        const modalBody = modal.querySelector('.modal-body');
        
        if (modalImage) {
            modalImage.src = imageElement.src;
            modalImage.alt = imageElement.alt || 'Image agrandie';
        }
        
        if (modalTitle) {
            modalTitle.textContent = imageElement.alt || 'Image';
        }
        
        if (modalBody) {
            modalBody.innerHTML = `<p>${imageElement.alt || 'Image agrandie'}</p>`;
        }
        
        // Ouvrir modal
        modal.classList.remove('modal-hidden');
        modal.classList.add('modal-open');
        modal.setAttribute('aria-hidden', 'false');
        modal.style.display = 'flex';
        document.body.classList.add('body-modal-locked');
        
        isModalOpen = true;
        console.log('✅ Fallback: Modal ouverte');
    }
    
    function closeModal() {
        if (!isModalOpen) return;
        
        modal.classList.remove('modal-open');
        modal.classList.add('modal-closed');
        
        setTimeout(() => {
            modal.classList.remove('modal-closed');
            modal.classList.add('modal-hidden');
            modal.setAttribute('aria-hidden', 'true');
            modal.style.display = 'none';
            
            // Nettoyer
            const modalImage = modal.querySelector('.modal-image');
            if (modalImage) {
                modalImage.src = '';
                modalImage.alt = '';
            }
            
            // Débloquer scroll
            document.body.classList.remove('body-modal-locked');
            window.scrollTo(0, scrollPosition);
            
            isModalOpen = false;
            console.log('❌ Fallback: Modal fermée');
        }, 300);
    }
    
    console.log('✅ Fallback Modal configuré');
}

console.log('📦 Fallback Mobile chargé');
