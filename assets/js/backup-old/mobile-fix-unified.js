/**
 * Mobile Fix Unified - Correction des problèmes tactiles
 * Résout les conflits entre menu hamburger et changeur de langue
 */

(function() {
    'use strict';
    
    console.log('🔧 Mobile Fix Unified - Initialisation...');
    
    // Attendre que le DOM soit complètement chargé
    function waitForElements(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', callback);
        } else {
            // DOM déjà chargé, mais attendre les includes
            setTimeout(callback, 1500); // Délai pour les includes
        }
    }
    
    // Fonction principale de correction
    function applyMobileFixes() {
        console.log('🔧 Application des corrections mobile...');
        
        // 1. Fix Menu Hamburger
        fixHamburgerMenu();
        
        // 2. Fix Language Switcher
        fixLanguageSwitcher();
        
        // 3. Fix CSS conflicts
        fixCSSConflicts();
        
        // 4. Réappliquer après chargement des includes
        setTimeout(() => {
            fixHamburgerMenu();
            fixLanguageSwitcher();
        }, 1000);
    }
    
    // Fix pour le menu hamburger
    let hamburgerRetries = 0;
    const MAX_RETRIES = 5;
    
    function fixHamburgerMenu() {
        const menuBtn = document.getElementById('mobile-menu-btn');
        const closeBtn = document.getElementById('close-menu');
        const menuOverlay = document.getElementById('mobile-menu');
        const menuLinks = menuOverlay ? menuOverlay.querySelectorAll('a') : [];
        
        if (!menuBtn || !menuOverlay) {
            hamburgerRetries++;
            console.log(`⚠️ Menu elements not found (attempt ${hamburgerRetries}/${MAX_RETRIES})`);
            console.log('menuBtn:', !!menuBtn, 'menuOverlay:', !!menuOverlay);
            console.log('Header present:', !!document.querySelector('header'));
            console.log('Header include:', !!document.getElementById('header-include'));
            
            if (hamburgerRetries < MAX_RETRIES) {
                setTimeout(fixHamburgerMenu, 1000);
            } else {
                console.error('❌ Menu elements not found after max retries');
                // Essayer une dernière fois avec un sélecteur plus large
                const altMenuBtn = document.querySelector('[aria-label*="menu mobile"]');
                const altMenuOverlay = document.querySelector('.mobile-menu-overlay');
                if (altMenuBtn && altMenuOverlay) {
                    console.log('✅ Found elements with alternative selectors');
                    // Continuer avec ces éléments...
                }
            }
            return;
        }
        
        console.log('✅ Fixing hamburger menu...');
        
        // Nettoyer les anciens listeners - DÉSACTIVÉ pour éviter de casser les autres systèmes
        // const newMenuBtn = menuBtn.cloneNode(true);
        // menuBtn.parentNode.replaceChild(newMenuBtn, menuBtn);
        
        // Ajouter un seul listener unifié - COMPATIBLE avec les autres systèmes
        menuBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleMenu();
        });
        
        // Fix pour le bouton fermer
        if (closeBtn) {
            closeBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                closeMenu();
            });
        }
        
        // Fix pour les liens du menu
        menuLinks.forEach(link => {
            link.addEventListener('click', function() {
                closeMenu();
            });
        });
        
        console.log('✅ Hamburger menu fixed');
    }
    
    // Fix pour le changeur de langue
    function fixLanguageSwitcher() {
        const mobileLangBtn = document.getElementById('lang-toggle-mobile');
        const desktopLangBtn = document.getElementById('lang-toggle-desktop');
        const mobileLangDropdown = document.getElementById('lang-dropdown-mobile');
        const desktopLangDropdown = document.getElementById('lang-dropdown-desktop');
        
        // Fix mobile language button
        if (mobileLangBtn && mobileLangDropdown) {
            console.log('✅ Fixing mobile language switcher...');
            
            // Nettoyer les anciens listeners - DÉSACTIVÉ pour éviter les conflits
            // const newLangBtn = mobileLangBtn.cloneNode(true);
            // mobileLangBtn.parentNode.replaceChild(newLangBtn, mobileLangBtn);
            
            // Un seul listener - COMPATIBLE
            mobileLangBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                toggleLanguageDropdown('mobile');
            });
            
            // Options de langue
            const langOptions = mobileLangDropdown.querySelectorAll('.lang-option-mobile');
            langOptions.forEach(option => {
                option.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    const lang = this.dataset.lang;
                    if (lang && window.translationManager) {
                        window.translationManager.switchLanguage(lang);
                        toggleLanguageDropdown('mobile'); // Fermer après sélection
                    }
                });
            });
        }
        
        // Fix desktop language button - DÉSACTIVÉ pour ne pas interférer avec translation-simple.js
        /*
        if (desktopLangBtn && desktopLangDropdown) {
            // Pas de cloneNode destructif
            // const newDesktopBtn = desktopLangBtn.cloneNode(true);
            // desktopLangBtn.parentNode.replaceChild(newDesktopBtn, desktopLangBtn);
            
            desktopLangBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                toggleLanguageDropdown('desktop');
            });
            
            // Options de langue desktop
            const desktopOptions = desktopLangDropdown.querySelectorAll('.lang-option');
            desktopOptions.forEach(option => {
                option.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    const lang = this.dataset.lang;
                    if (lang && window.translationManager) {
                        window.translationManager.switchLanguage(lang);
                        toggleLanguageDropdown('desktop'); // Fermer après sélection
                    }
                });
            });
        }
        */
        
        console.log('✅ Language switcher fixed');
    }
    
    // Fix des conflits CSS
    function fixCSSConflicts() {
        const style = document.createElement('style');
        style.textContent = `
            /* Corrections CSS pour mobile */
            @media (max-width: 768px) {
                /* Réactiver les interactions tactiles */
                #mobile-menu-btn,
                #close-menu,
                .mobile-menu-link,
                #lang-toggle-mobile,
                #lang-toggle-desktop,
                .lang-btn,
                .lang-btn-mobile,
                .lang-option,
                .lang-option-mobile {
                    -webkit-touch-callout: default !important;
                    -webkit-user-select: auto !important;
                    user-select: auto !important;
                    touch-action: manipulation !important;
                    pointer-events: auto !important;
                    cursor: pointer !important;
                }
                
                /* Z-index appropriés */
                .nav-bar {
                    z-index: 9900 !important;
                }
                
                .language-switcher-mobile,
                .language-switcher-desktop {
                    z-index: 9910 !important;
                }
                
                .lang-dropdown-mobile,
                .lang-dropdown-desktop {
                    z-index: 9920 !important;
                }
                
                .mobile-menu-overlay {
                    z-index: 9999 !important;
                }
                
                /* Taille minimale pour les zones tactiles */
                #mobile-menu-btn,
                #close-menu,
                #lang-toggle-mobile {
                    min-width: 44px !important;
                    min-height: 44px !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                }
                
                /* Feedback visuel amélioré */
                #mobile-menu-btn:active,
                #lang-toggle-mobile:active,
                .lang-option-mobile:active {
                    opacity: 0.7 !important;
                    transform: scale(0.95) !important;
                }
            }
        `;
        document.head.appendChild(style);
        console.log('✅ CSS conflicts fixed');
    }
    
    // Fonctions utilitaires
    function toggleMenu() {
        const menuOverlay = document.getElementById('mobile-menu');
        const menuBtn = document.getElementById('mobile-menu-btn');
        
        if (!menuOverlay) return;
        
        const isOpen = menuOverlay.classList.contains('mobile-menu-open');
        
        if (isOpen) {
            closeMenu();
        } else {
            openMenu();
        }
    }
    
    function openMenu() {
        const menuOverlay = document.getElementById('mobile-menu');
        const menuBtn = document.getElementById('mobile-menu-btn');
        
        if (!menuOverlay) return;
        
        menuOverlay.classList.add('mobile-menu-open');
        menuOverlay.setAttribute('aria-hidden', 'false');
        if (menuBtn) menuBtn.setAttribute('aria-expanded', 'true');
        document.body.classList.add('body-menu-locked');
        
        console.log('📱 Menu opened');
    }
    
    function closeMenu() {
        const menuOverlay = document.getElementById('mobile-menu');
        const menuBtn = document.getElementById('mobile-menu-btn');
        
        if (!menuOverlay) return;
        
        menuOverlay.classList.remove('mobile-menu-open');
        menuOverlay.setAttribute('aria-hidden', 'true');
        if (menuBtn) menuBtn.setAttribute('aria-expanded', 'false');
        document.body.classList.remove('body-menu-locked');
        
        console.log('❌ Menu closed');
    }
    
    function toggleLanguageDropdown(type) {
        const dropdown = document.getElementById(`lang-dropdown-${type}`);
        const button = document.getElementById(`lang-toggle-${type}`);
        
        if (!dropdown || !button) return;
        
        const isOpen = dropdown.classList.contains('dropdown-open');
        
        // Fermer tous les dropdowns
        document.querySelectorAll('.lang-dropdown, .lang-dropdown-mobile').forEach(dd => {
            dd.classList.remove('dropdown-open');
            dd.style.display = 'none';
        });
        
        document.querySelectorAll('[id^="lang-toggle-"]').forEach(btn => {
            btn.setAttribute('aria-expanded', 'false');
        });
        
        // Ouvrir si fermé
        if (!isOpen) {
            dropdown.classList.add('dropdown-open');
            dropdown.style.display = 'block';
            button.setAttribute('aria-expanded', 'true');
            console.log(`🌍 ${type} language dropdown opened`);
        } else {
            console.log(`🌍 ${type} language dropdown closed`);
        }
    }
    
    // Fermer les dropdowns en cliquant ailleurs
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.language-switcher-mobile') && 
            !e.target.closest('.language-switcher-desktop')) {
            document.querySelectorAll('.lang-dropdown, .lang-dropdown-mobile').forEach(dd => {
                dd.classList.remove('dropdown-open');
                dd.style.display = 'none';
            });
        }
    });
    
    // Initialiser les corrections
    waitForElements(applyMobileFixes);
    
    // Exposer globalement pour debug
    window.mobileFixUnified = {
        reapply: applyMobileFixes,
        toggleMenu: toggleMenu,
        toggleLanguage: toggleLanguageDropdown
    };
    
    console.log('✅ Mobile Fix Unified loaded');
})();
