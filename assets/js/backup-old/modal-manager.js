/**
 * MODAL MANAGER - Gestion Exclusive des Modales d'Images
 * TAP uniquement (pas de swipe) - Images cliquables professionnelles
 */

class ModalManager {
    constructor() {
        this.modal = null;
        this.isOpen = false;
        this.currentImage = null;
        
        this.init();
    }

    init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        this.modal = document.getElementById('course-modal');
        if (!this.modal) {
            console.warn('⚠️ Modal course-modal introuvable');
            return;
        }

        this.bindEvents();
        this.setupModalEvents();
        console.log('✅ ModalManager initialisé - TAP uniquement');
    }

    bindEvents() {
        // IMAGES : TAP uniquement via TouchManager
        if (window.touchManager) {
            // Images dans les service cards
            window.touchManager.onTap('.service-card', (element) => {
                if (!element.closest('button, a')) {
                    const image = element.querySelector('.service-image');
                    if (image) {
                        this.openImageModal(image);
                    }
                }
            });

            // Images générales cliquables (EXCLUSIONS importantes)
            window.touchManager.onTap('img[src*="assets/images"]', (element) => {
                if (this.isImageClickable(element)) {
                    this.openImageModal(element);
                }
            });

            // Course cards
            window.touchManager.onTap('.course-card', (element) => {
                if (!element.closest('button, a')) {
                    this.openCourseModal(element);
                }
            });
        }

        // Fallback pour desktop (click classique)
        document.addEventListener('click', (e) => {
            if (this.isOpen) return;

            const target = e.target;

            // Service cards
            const serviceCard = target.closest('.service-card');
            if (serviceCard && !target.closest('button, a')) {
                const image = serviceCard.querySelector('.service-image');
                if (image) {
                    e.preventDefault();
                    this.openImageModal(image);
                    return;
                }
            }

            // Images générales
            const image = target.closest('img[src*="assets/images"]');
            if (image && this.isImageClickable(image)) {
                e.preventDefault();
                this.openImageModal(image);
                return;
            }

            // Course cards
            const courseCard = target.closest('.course-card');
            if (courseCard && !target.closest('button, a')) {
                e.preventDefault();
                this.openCourseModal(courseCard);
                return;
            }
        });
    }

    isImageClickable(image) {
        // EXCLUSIONS strictes pour éviter les conflits
        const exclusions = [
            '.logo',                    // Logos
            '.hero-carousel',           // Carrousel hero
            '.nav-bar',                 // Navigation
            '.language-switcher',       // Switch langue
            'button',                   // Boutons
            'a',                        // Liens
            '.pagination-bullet'        // Pagination carrousel
        ];

        // Vérifier si l'image est dans un élément exclu
        for (let exclusion of exclusions) {
            if (image.closest(exclusion)) {
                return false;
            }
        }

        // Vérifier attributs alt pour logos
        const alt = image.alt ? image.alt.toLowerCase() : '';
        if (alt.includes('logo')) {
            return false;
        }

        return true;
    }

    openImageModal(imageElement) {
        if (!this.modal || !imageElement) return;

        const imageData = {
            id: 'image-modal',
            image: imageElement.src,
            alt: imageElement.alt || 'Image agrandie',
            title: imageElement.alt || 'Image',
            description: this.getImageDescription(imageElement)
        };

        this.populateImageModal(imageData);
        this.showModal();
        
        console.log('🖼️ Modal image ouverte:', imageData.title);
    }

    openCourseModal(courseCard) {
        if (!this.modal || !courseCard) return;

        const courseData = this.extractCourseData(courseCard);
        this.populateCourseModal(courseData);
        this.showModal();
        
        console.log('🏌️ Modal course ouverte:', courseData.title);
    }

    getImageDescription(imageElement) {
        const parentCard = imageElement.closest('.service-card, .course-card');
        if (parentCard) {
            const description = parentCard.querySelector('.service-description, .course-description');
            if (description) {
                return description.textContent.trim();
            }
        }
        return 'Appuyez sur Échap ou cliquez en dehors pour fermer.';
    }

    extractCourseData(courseCard) {
        const image = courseCard.querySelector('.course-image');
        const title = courseCard.querySelector('.course-title');
        const description = courseCard.querySelector('.course-description');
        const location = courseCard.querySelector('.course-location-badge');
        const features = courseCard.querySelector('.course-features');

        return {
            id: courseCard.getAttribute('data-course') || '',
            image: image ? image.src : '',
            alt: image ? image.alt : '',
            title: title ? title.textContent.trim() : '',
            description: description ? description.textContent.trim() : '',
            location: location ? location.textContent.trim() : '',
            features: features ? features.textContent.trim() : ''
        };
    }

    populateImageModal(data) {
        const modalImage = this.modal.querySelector('.modal-image');
        const modalTitle = this.modal.querySelector('.modal-title');
        const modalLocation = this.modal.querySelector('.modal-location');
        const modalSubtitle = this.modal.querySelector('.modal-subtitle');
        const modalBody = this.modal.querySelector('.modal-body');

        if (modalImage) {
            modalImage.src = data.image;
            modalImage.alt = data.alt;
        }

        if (modalTitle) modalTitle.textContent = data.title;
        if (modalLocation) modalLocation.textContent = '';
        if (modalSubtitle) modalSubtitle.textContent = '';
        
        if (modalBody) {
            modalBody.innerHTML = `
                <div class="modal-description">
                    <p>${data.description}</p>
                </div>
            `;
        }
    }

    populateCourseModal(data) {
        const modalImage = this.modal.querySelector('.modal-image');
        const modalTitle = this.modal.querySelector('.modal-title');
        const modalLocation = this.modal.querySelector('.modal-location');
        const modalSubtitle = this.modal.querySelector('.modal-subtitle');
        const modalBody = this.modal.querySelector('.modal-body');

        if (modalImage) {
            modalImage.src = data.image;
            modalImage.alt = data.alt;
        }

        if (modalTitle) modalTitle.textContent = data.title;
        if (modalLocation) modalLocation.textContent = data.location;
        if (modalSubtitle) modalSubtitle.textContent = data.features;
        
        if (modalBody) {
            modalBody.innerHTML = `
                <div class="modal-description-section">
                    <p class="modal-description">${data.description}</p>
                </div>
                <div class="modal-features-section">
                    <h4 class="modal-section-title">
                        <i class="fas fa-golf-ball" aria-hidden="true"></i>
                        Caractéristiques du parcours
                    </h4>
                    <p class="modal-features">${data.features}</p>
                </div>
            `;
        }
    }

    setupModalEvents() {
        if (!this.modal) return;

        // Bouton fermer
        const closeBtn = this.modal.querySelector('.modal-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.close();
            });

            if (window.touchManager) {
                window.touchManager.onTap('.modal-close', () => this.close());
            }
        }

        // Clic sur backdrop
        const backdrop = this.modal.querySelector('.modal-backdrop');
        if (backdrop) {
            backdrop.addEventListener('click', () => this.close());
            
            if (window.touchManager) {
                window.touchManager.onTap('.modal-backdrop', () => this.close());
            }
        }

        // Swipe vers le haut pour fermer (UNIQUEMENT pour fermer)
        if (window.touchManager) {
            window.touchManager.onSwipe('.modal-content', (element, direction) => {
                if (direction === 'up') {
                    this.close();
                }
            });
        }

        // Échap pour fermer
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });
    }

    showModal() {
        this.modal.classList.add('modal-open');
        this.modal.setAttribute('aria-hidden', 'false');
        document.body.classList.add('body-modal-locked');
        this.isOpen = true;

        // Focus sur bouton fermer
        setTimeout(() => {
            const closeBtn = this.modal.querySelector('.modal-close');
            if (closeBtn) closeBtn.focus();
        }, 100);
    }

    close() {
        if (!this.modal || !this.isOpen) return;

        this.modal.classList.remove('modal-open');
        this.modal.setAttribute('aria-hidden', 'true');
        document.body.classList.remove('body-modal-locked');
        this.isOpen = false;
        this.currentImage = null;

        console.log('❌ Modal fermée');
    }

    // Debug
    getState() {
        return {
            isOpen: this.isOpen,
            modalFound: !!this.modal,
            currentImage: this.currentImage
        };
    }
}

// Instance globale
window.modalManager = new ModalManager();