/**
 * NAVIGATION MANAGER - Gestion Exclusive du Smooth Scroll
 * Responsabilité unique : navigation entre sections
 */

class NavigationManager {
    constructor() {
        this.init();
    }

    init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        this.bindSmoothScroll();
        this.handleActiveLinks();
        console.log('✅ NavigationManager initialisé');
    }

    bindSmoothScroll() {
        // Smooth scroll pour tous les liens d'ancres
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href^="#"]');
            if (!link) return;

            // EXCLUSIONS : Ne pas intercepter les clics sur les éléments modaux
            const isImageOrCard = e.target.closest('.service-card, .course-card, img[src*="assets/images"], .service-image, .course-image');
            if (isImageOrCard) {
                console.log('🖼️ Navigation ignorée pour image/card');
                return; // Laisser le modal-manager s'en occuper
            }

            const href = link.getAttribute('href');
            if (href === '#') return;

            const targetSection = document.querySelector(href);
            if (!targetSection) return;

            e.preventDefault();
            
            // Fermer le menu mobile si ouvert
            if (window.mobileMenuManager && window.mobileMenuManager.isOpen) {
                window.mobileMenuManager.close();
                
                // Attendre la fermeture du menu avant de scroller
                setTimeout(() => {
                    this.scrollToSection(targetSection);
                }, 300);
            } else {
                this.scrollToSection(targetSection);
            }
        });

        // Support tactile pour les liens de navigation
        if (window.touchManager) {
            window.touchManager.onTap('a[href^="#"]', (element, originalEvent) => {
                // EXCLUSIONS : Ne pas intercepter les clics sur les éléments modaux
                const isImageOrCard = originalEvent.target.closest('.service-card, .course-card, img[src*="assets/images"], .service-image, .course-image');
                if (isImageOrCard) {
                    console.log('🖼️ TouchManager navigation ignorée pour image/card');
                    return; // Laisser le modal-manager s'en occuper
                }

                const href = element.getAttribute('href');
                if (href === '#') return;

                const targetSection = document.querySelector(href);
                if (!targetSection) return;

                // Fermer le menu mobile si ouvert
                if (window.mobileMenuManager && window.mobileMenuManager.isOpen) {
                    window.mobileMenuManager.close();
                    
                    setTimeout(() => {
                        this.scrollToSection(targetSection);
                    }, 300);
                } else {
                    this.scrollToSection(targetSection);
                }
            });
        }
    }

    scrollToSection(targetSection) {
        // Calcul de l'offset pour la navbar fixe
        const navbar = document.querySelector('.nav-bar');
        const navbarHeight = navbar ? navbar.offsetHeight : 0;
        const extraOffset = 20; // Marge supplémentaire

        const targetPosition = targetSection.offsetTop - navbarHeight - extraOffset;

        window.scrollTo({
            top: Math.max(0, targetPosition),
            behavior: 'smooth'
        });

        console.log('🎯 Navigation vers:', targetSection.id);
    }

    handleActiveLinks() {
        // Observer pour marquer les liens actifs
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('a[href^="#"]');

        if (sections.length === 0) return;

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // Marquer le lien correspondant comme actif
                    navLinks.forEach(link => {
                        const href = link.getAttribute('href');
                        if (href === `#${entry.target.id}`) {
                            link.classList.add('active');
                        } else {
                            link.classList.remove('active');
                        }
                    });
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '-20% 0px -20% 0px'
        });

        sections.forEach(section => observer.observe(section));
    }

    // Navigation programmatique
    navigateTo(sectionId) {
        const targetSection = document.getElementById(sectionId);
        if (targetSection) {
            this.scrollToSection(targetSection);
        }
    }

    // Debug
    getState() {
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('a[href^="#"]');
        
        return {
            sectionsCount: sections.length,
            navLinksCount: navLinks.length,
            currentSection: this.getCurrentSection()
        };
    }

    getCurrentSection() {
        const sections = document.querySelectorAll('section[id]');
        const scrollTop = window.pageYOffset;
        
        for (let section of sections) {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            
            if (scrollTop >= sectionTop - 100 && scrollTop < sectionTop + sectionHeight - 100) {
                return section.id;
            }
        }
        
        return null;
    }
}

// Instance globale
window.navigationManager = new NavigationManager();