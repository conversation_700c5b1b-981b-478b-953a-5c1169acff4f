/**
 * MOBILE MENU MANAGER - Gestion Exclusive du Menu Hamburger
 * Responsabilité unique : menu mobile uniquement
 */

class MobileMenuManager {
    constructor() {
        this.isOpen = false;
        this.elements = {
            menuBtn: null,
            menu: null,
            closeBtn: null,
            links: []
        };
        
        this.init();
    }

    init() {
        // Attendre que le DOM et les includes soient chargés
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        this.findElements();
        this.bindEvents();
        console.log('✅ MobileMenuManager initialisé');
    }

    findElements() {
        this.elements.menuBtn = document.getElementById('mobile-menu-btn');
        this.elements.menu = document.getElementById('mobile-menu');
        this.elements.closeBtn = document.getElementById('close-menu');
        this.elements.links = this.elements.menu ? 
            Array.from(this.elements.menu.querySelectorAll('a')) : [];
    }

    bindEvents() {
        if (!this.elements.menuBtn || !this.elements.menu) {
            // Retry après chargement des includes
            setTimeout(() => this.setup(), 500);
            return;
        }

        // Événements du bouton hamburger - Multiple bindings pour assurer le fonctionnement
        this.elements.menuBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('🍔 Menu click détecté');
            this.toggle();
        });

        // Support tactile ADDITIONNEL
        this.elements.menuBtn.addEventListener('touchend', (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('🍔 Menu touch détecté');
            this.toggle();
        });

        // Support tactile pour le bouton via TouchManager (si disponible)
        if (window.touchManager) {
            window.touchManager.onTap('#mobile-menu-btn', () => {
                console.log('🍔 Menu TouchManager détecté');
                this.toggle();
            });
        }

        // Bouton fermer - Multiple bindings
        if (this.elements.closeBtn) {
            this.elements.closeBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('❌ Close click détecté');
                this.close();
            });

            this.elements.closeBtn.addEventListener('touchend', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('❌ Close touch détecté');
                this.close();
            });

            if (window.touchManager) {
                window.touchManager.onTap('#close-menu', () => {
                    console.log('❌ Close TouchManager détecté');
                    this.close();
                });
            }
        }

        // Liens du menu - CORRECTION: Navigation qui fonctionne
        this.elements.links.forEach(link => {
            link.addEventListener('click', (e) => {
                // Ne pas preventDefault ici pour permettre la navigation
                this.close();
            });

            if (window.touchManager) {
                window.touchManager.onTap(`a[href="${link.getAttribute('href')}"]`, () => {
                    this.close();
                    // Déclencher la navigation après fermeture
                    setTimeout(() => {
                        link.click();
                    }, 100);
                });
            }
        });

        // Fermer avec Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });

        // Fermer si on passe en desktop
        window.addEventListener('resize', () => {
            if (window.innerWidth >= 1024 && this.isOpen) {
                this.close();
            }
        });
    }

    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    open() {
        if (!this.elements.menu || this.isOpen) return;

        // Animation d'ouverture
        this.elements.menu.classList.add('mobile-menu-open');
        this.elements.menu.setAttribute('aria-hidden', 'false');
        this.elements.menuBtn.setAttribute('aria-expanded', 'true');
        
        // Lock body scroll
        document.body.classList.add('body-menu-locked');
        
        // Focus sur le premier lien après animation
        setTimeout(() => {
            if (this.elements.links[0]) {
                this.elements.links[0].focus();
            }
        }, 100);
        
        this.isOpen = true;
        console.log('📱 Menu mobile ouvert');
    }

    close() {
        if (!this.elements.menu || !this.isOpen) return;

        // Animation de fermeture
        this.elements.menu.classList.remove('mobile-menu-open');
        this.elements.menu.setAttribute('aria-hidden', 'true');
        this.elements.menuBtn.setAttribute('aria-expanded', 'false');
        
        // Unlock body scroll
        document.body.classList.remove('body-menu-locked');
        
        // Focus retour sur bouton hamburger
        this.elements.menuBtn.focus();
        
        this.isOpen = false;
        console.log('❌ Menu mobile fermé');
    }

    // Réinitialisation après chargement d'includes
    reinitialize() {
        console.log('🔄 Réinitialisation MobileMenuManager');
        this.isOpen = false;
        this.setup();
    }

    // Debug
    getState() {
        return {
            isOpen: this.isOpen,
            elementsFound: {
                menuBtn: !!this.elements.menuBtn,
                menu: !!this.elements.menu,
                closeBtn: !!this.elements.closeBtn,
                linksCount: this.elements.links.length
            }
        };
    }
}

// Instance globale
window.mobileMenuManager = new MobileMenuManager();