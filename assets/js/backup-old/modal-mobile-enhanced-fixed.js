/**
 * Gestionnaire Modal Amélioré Mobile - Version Corrigée
 * Correction des problèmes d'interaction tactile et d'ouverture d'images
 */

class MobileEnhancedModal {
    constructor() {
        this.modal = null;
        this.isOpen = false;
        this.currentImage = null;
        this.touchStartY = 0;
        this.touchStartX = 0;
        this.lastTap = 0;
        this.tapTimeThreshold = 150; // 🔥 RÉDUIT de 300ms à 150ms
        this.isProcessingTouch = false;
        this.init();
    }

    init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupModal();
            });
        } else {
            this.setupModal();
        }
    }

    setupModal() {
        this.modal = document.getElementById('course-modal');
        if (!this.modal) {
            console.error('❌ Modal non trouvée');
            return;
        }
        
        this.bindEvents();
        this.createImageZoomFeature();
        this.createMobileTouchFeedback();
        console.log('✅ Modal mobile configurée avec interactions améliorées');
    }

    bindEvents() {
        // 🔥 NOUVEAU : Gestion tactile prioritaire sur mobile
        if (this.isMobileDevice()) {
            document.addEventListener('touchstart', (e) => this.handleTouchStart(e), { passive: true });
            document.addEventListener('touchend', (e) => this.handleTouchEnd(e), { passive: false });
        } else {
            // Desktop : utiliser click classique
            document.addEventListener('click', (e) => this.handleClick(e));
        }
        
        // Événements de fermeture de modal
        this.setupModalCloseEvents();
        
        // Gestion clavier
        document.addEventListener('keydown', (e) => {
            if (this.isOpen && e.key === 'Escape') {
                this.closeModal();
            }
        });

        // Gestes tactiles pour la modal
        this.setupModalTouchGestures();
    }

    // 🔥 NOUVEAU : Détection mobile améliorée
    isMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               (window.innerWidth <= 768) ||
               ('ontouchstart' in window);
    }

    // 🔥 NOUVEAU : Gestion touchstart
    handleTouchStart(e) {
        if (this.isOpen || this.isProcessingTouch) return;
        
        this.touchStartY = e.touches[0].clientY;
        this.touchStartX = e.touches[0].clientX;
        this.touchStartTime = Date.now();
    }

    // 🔥 AMÉLIORÉ : Gestion tactile avec feedback immédiat
    handleTouchEnd(e) {
        if (this.isOpen || this.isProcessingTouch) return;
        
        const currentTime = Date.now();
        const touchDuration = currentTime - (this.touchStartTime || 0);
        const tapInterval = currentTime - this.lastTap;
        
        // Ignorer les touches trop longues (glissement) ou trop rapides (double tap accidentel)
        if (touchDuration > 500 || tapInterval < this.tapTimeThreshold) {
            return;
        }
        
        this.lastTap = currentTime;
        this.isProcessingTouch = true;
        
        const target = e.target;
        e.preventDefault(); // Empêcher le clic fantôme
        
        // 🔥 AMÉLIORÉ : Logique de détection d'images avec feedback visuel
        this.processImageTouch(target, e);
        
        // Reset processing flag
        setTimeout(() => {
            this.isProcessingTouch = false;
        }, 200);
    }

    // 🔥 NOUVEAU : Traitement spécialisé pour les touches d'images
    processImageTouch(target, e) {
        // Exclure le logo et éléments de navigation
        if (this.isExcludedElement(target)) {
            this.isProcessingTouch = false;
            return;
        }
        
        // Course cards avec feedback visuel immédiat
        const courseCard = target.closest('.course-card');
        if (courseCard && !target.closest('button, a')) {
            this.addTouchFeedback(courseCard);
            setTimeout(() => {
                this.openModal(courseCard);
                this.removeTouchFeedback(courseCard);
            }, 150);
            return;
        }

        // Service cards avec feedback
        const serviceCard = target.closest('.service-card');
        if (serviceCard && !target.closest('button, a')) {
            const serviceImage = serviceCard.querySelector('.service-image');
            if (serviceImage) {
                this.addTouchFeedback(serviceCard);
                setTimeout(() => {
                    this.openImageInModal(serviceImage);
                    this.removeTouchFeedback(serviceCard);
                }, 150);
            }
            return;
        }

        // Images générales avec feedback direct
        const clickableImage = target.closest('img[src*="assets/images"]');
        if (clickableImage && this.isImageClickable(clickableImage, target)) {
            // 🔥 NOUVEAU : Feedback visuel direct sur l'image
            this.addImageTouchFeedback(clickableImage);
            setTimeout(() => {
                this.openImageInModal(clickableImage);
                this.removeImageTouchFeedback(clickableImage);
            }, 100); // Plus rapide pour les images directes
            return;
        }
    }

    // 🔥 NOUVEAU : Vérification si élément exclu
    isExcludedElement(target) {
        return target.closest('.logo, [src*="logo"], [alt*="Logo"], [alt*="logo"], .hero-carousel, .nav-bar, button, a, .pagination-bullet');
    }

    // 🔥 NOUVEAU : Vérification si image cliquable
    isImageClickable(image, target) {
        return !target.closest('button, a, .hero-carousel, .logo') && 
               !image.alt.toLowerCase().includes('logo') &&
               !image.src.includes('logo');
    }

    // Gestion click pour desktop (inchangé)
    handleClick(e) {
        if (this.isOpen) return;
        
        const target = e.target;
        
        if (this.isExcludedElement(target)) return;
        
        const courseCard = target.closest('.course-card');
        if (courseCard && !target.closest('button, a')) {
            e.preventDefault();
            this.openModal(courseCard);
            return;
        }

        const serviceCard = target.closest('.service-card');
        if (serviceCard && !target.closest('button, a')) {
            const serviceImage = serviceCard.querySelector('.service-image');
            if (serviceImage) {
                this.openImageInModal(serviceImage);
            }
            return;
        }

        const clickableImage = target.closest('img[src*="assets/images"]');
        if (clickableImage && this.isImageClickable(clickableImage, target)) {
            this.openImageInModal(clickableImage);
            return;
        }
    }

    // 🔥 NOUVEAU : Système de feedback tactile
    addTouchFeedback(element) {
        element.classList.add('touch-feedback-active');
    }

    removeTouchFeedback(element) {
        element.classList.remove('touch-feedback-active');
    }

    addImageTouchFeedback(image) {
        image.classList.add('image-touch-feedback');
    }

    removeImageTouchFeedback(image) {
        image.classList.remove('image-touch-feedback');
    }

    // 🔥 NOUVEAU : Styles CSS pour feedback tactile
    createMobileTouchFeedback() {
        if (!document.getElementById('mobile-touch-feedback-styles')) {
            const style = document.createElement('style');
            style.id = 'mobile-touch-feedback-styles';
            style.textContent = `
                .touch-feedback-active {
                    transform: scale(0.98) !important;
                    opacity: 0.8 !important;
                    transition: all 0.15s ease !important;
                    background-color: rgba(34, 197, 94, 0.1) !important;
                }
                
                .image-touch-feedback {
                    transform: scale(1.02) !important;
                    transition: all 0.1s ease !important;
                    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3) !important;
                    filter: brightness(1.1) !important;
                }
                
                @media (max-width: 768px) {
                    .course-card, .service-card {
                        cursor: pointer;
                        touch-action: manipulation;
                        -webkit-tap-highlight-color: rgba(34, 197, 94, 0.2);
                    }
                    
                    .service-image, .course-image {
                        cursor: pointer;
                        touch-action: manipulation;
                        -webkit-tap-highlight-color: rgba(34, 197, 94, 0.2);
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }

    // Méthodes de modal inchangées mais avec log amélioré
    openModal(courseCard) {
        if (!this.modal || !courseCard) {
            console.error('❌ Modal ou course card manquant');
            return;
        }

        const courseData = this.extractCourseData(courseCard);
        this.populateModal(courseData);
        this.showModal();
        this.setupAccessibility();
        
        console.log('📖 Modal course ouverte:', courseData.title);
    }

    openImageInModal(imageElement) {
        if (!this.modal || !imageElement) {
            console.error('❌ Modal ou image manquante');
            return;
        }

        const imageData = {
            id: 'image-modal',
            image: imageElement.src,
            alt: imageElement.alt || 'Image agrandie',
            title: imageElement.alt || 'Image',
            description: this.getImageDescription(imageElement),
            location: '',
            features: ''
        };
        
        this.populateImageModal(imageData);
        this.showModal();
        this.setupAccessibility();
        
        console.log('🖼️ Image ouverte en modal:', imageData.title);
    }

    // 🔥 NOUVEAU : Description intelligente d'image
    getImageDescription(imageElement) {
        const parentCard = imageElement.closest('.service-card, .course-card');
        if (parentCard) {
            const description = parentCard.querySelector('.service-description, .course-description');
            if (description) {
                return description.textContent.trim();
            }
        }
        return 'Cliquez en dehors ou appuyez sur Échap pour fermer.';
    }

    // Configuration de la modal (inchangé)
    setupModalCloseEvents() {
        if (this.modal) {
            const closeBtn = this.modal.querySelector('.modal-close');
            const backdrop = this.modal.querySelector('.modal-backdrop');
            
            if (closeBtn) {
                closeBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.closeModal();
                });
                
                closeBtn.addEventListener('touchend', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.closeModal();
                }, { passive: false });
            }
            
            if (backdrop) {
                backdrop.addEventListener('click', () => this.closeModal());
                backdrop.addEventListener('touchend', (e) => {
                    if (e.target === backdrop) {
                        this.closeModal();
                    }
                }, { passive: false });
            }
        }
    }

    setupModalTouchGestures() {
        if (this.modal) {
            this.modal.addEventListener('touchstart', (e) => {
                this.touchStartY = e.touches[0].clientY;
                this.touchStartX = e.touches[0].clientX;
            }, { passive: true });

            this.modal.addEventListener('touchmove', (e) => {
                if (this.isOpen) {
                    e.preventDefault();
                }
            }, { passive: false });

            this.modal.addEventListener('touchend', (e) => {
                this.handleModalSwipe(e);
            }, { passive: true });
        }
    }

    // Autres méthodes inchangées...
    populateImageModal(data) {
        const modalImage = this.modal.querySelector('.modal-image');
        const modalTitle = this.modal.querySelector('.modal-title');
        const modalLocation = this.modal.querySelector('.modal-location');
        const modalSubtitle = this.modal.querySelector('.modal-subtitle');
        const modalBody = this.modal.querySelector('.modal-body');

        if (modalImage && data.image) {
            modalImage.classList.add('modal-image-loading');
            modalImage.classList.remove('modal-image-loaded');
            modalImage.src = data.image;
            modalImage.alt = data.alt;
            
            modalImage.onload = () => {
                modalImage.classList.remove('modal-image-loading');
                modalImage.classList.add('modal-image-loaded');
            };
        }

        if (modalTitle) modalTitle.textContent = data.title;
        if (modalLocation) modalLocation.textContent = '';
        if (modalSubtitle) modalSubtitle.textContent = '';
        
        if (modalBody) {
            modalBody.innerHTML = `
                <div class="modal-image-viewer">
                    <p class="modal-description">${data.description}</p>
                </div>
            `;
        }
    }

    populateModal(data) {
        const modalImage = this.modal.querySelector('.modal-image');
        const modalTitle = this.modal.querySelector('.modal-title');
        const modalLocation = this.modal.querySelector('.modal-location');
        const modalSubtitle = this.modal.querySelector('.modal-subtitle');
        const modalBody = this.modal.querySelector('.modal-body');

        if (modalImage && data.image) {
            modalImage.classList.add('modal-image-loading');
            modalImage.classList.remove('modal-image-loaded');
            modalImage.src = data.image;
            modalImage.alt = data.alt;
            
            modalImage.onload = () => {
                modalImage.classList.remove('modal-image-loading');
                modalImage.classList.add('modal-image-loaded');
            };
            
            modalImage.onerror = () => {
                modalImage.src = './assets/images/placeholder-golf.svg';
                modalImage.alt = 'Image non disponible';
                modalImage.classList.remove('modal-image-loading');
                modalImage.classList.add('modal-image-loaded');
            };
        }

        if (modalTitle) modalTitle.textContent = data.title;
        if (modalLocation) modalLocation.textContent = data.location;
        if (modalSubtitle) modalSubtitle.textContent = data.features;
        
        if (modalBody) {
            modalBody.innerHTML = `
                <div class="modal-description-section">
                    <p class="modal-description">${data.description}</p>
                </div>
                <div class="modal-features-section">
                    <h4 class="modal-section-title">
                        <i class="fas fa-golf-ball" aria-hidden="true"></i>
                        Caractéristiques du parcours
                    </h4>
                    <p class="modal-features">${data.features}</p>
                </div>
            `;
        }
    }

    extractCourseData(courseCard) {
        const image = courseCard.querySelector('.course-image');
        const title = courseCard.querySelector('.course-title');
        const description = courseCard.querySelector('.course-description');
        const location = courseCard.querySelector('.course-location-badge');
        const features = courseCard.querySelector('.course-features');
        const courseId = courseCard.getAttribute('data-course') || '';

        return {
            id: courseId,
            image: image ? image.src : '',
            alt: image ? image.alt : '',
            title: title ? title.textContent.trim() : '',
            description: description ? description.textContent.trim() : '',
            location: location ? location.textContent.trim() : '',
            features: features ? features.textContent.trim() : ''
        };
    }

    showModal() {
        this.modal.classList.remove('modal-closed', 'modal-hidden');
        this.modal.classList.add('modal-open');
        this.modal.setAttribute('aria-hidden', 'false');
        document.body.classList.add('body-modal-locked');
        this.isOpen = true;
    }

    closeModal() {
        if (!this.modal || !this.isOpen) return;

        this.modal.classList.remove('modal-open');
        this.modal.classList.add('modal-closed');
        
        setTimeout(() => {
            this.modal.classList.remove('modal-closed');
            this.modal.classList.add('modal-hidden');
            this.modal.setAttribute('aria-hidden', 'true');
            document.body.classList.remove('body-modal-locked');
            this.isOpen = false;
            this.resetImageZoom();
            
            console.log('❌ Modal fermée');
        }, 300);
    }

    handleModalSwipe(e) {
        const swipeThreshold = 100;
        const diffY = this.touchStartY - e.changedTouches[0].clientY;
        const diffX = Math.abs(this.touchStartX - e.changedTouches[0].clientX);
        
        if (diffY > swipeThreshold && diffX < 50) {
            this.closeModal();
        }
    }

    setupAccessibility() {
        const closeBtn = this.modal.querySelector('.modal-close');
        if (closeBtn) {
            setTimeout(() => closeBtn.focus(), 100);
        }
    }

    createImageZoomFeature() {
        if (!document.getElementById('mobile-modal-styles')) {
            const style = document.createElement('style');
            style.id = 'mobile-modal-styles';
            style.textContent = `
                @keyframes modalFadeIn {
                    from { opacity: 0; transform: scale(0.9); }
                    to { opacity: 1; transform: scale(1); }
                }
                @keyframes modalFadeOut {
                    from { opacity: 1; transform: scale(1); }
                    to { opacity: 0; transform: scale(0.9); }
                }
                
                @media (max-width: 768px) {
                    .modal-content {
                        margin: 1rem !important;
                        max-height: 90vh !important;
                        width: calc(100vw - 2rem) !important;
                        border-radius: 1rem !important;
                    }
                    
                    .modal-image {
                        height: 250px !important;
                        object-fit: cover !important;
                        border-radius: 1rem 1rem 0 0 !important;
                    }
                    
                    .modal-body {
                        padding: 1.5rem !important;
                        max-height: 50vh !important;
                        overflow-y: auto !important;
                    }
                    
                    .modal-close {
                        top: 0.5rem !important;
                        right: 0.5rem !important;
                        width: 44px !important;
                        height: 44px !important;
                        font-size: 1.25rem !important;
                        background: rgba(0, 0, 0, 0.7) !important;
                        border-radius: 50% !important;
                        color: white !important;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }

    resetImageZoom() {
        if (this.currentImage) {
            this.currentImage.classList.remove('zoomed');
            this.currentImage = null;
        }
    }

    debug() {
        console.log('🔍 Debug Modal Mobile:', {
            modal: this.modal,
            isOpen: this.isOpen,
            currentImage: this.currentImage,
            isMobile: this.isMobileDevice(),
            touchProcessing: this.isProcessingTouch
        });
    }
}

// Initialiser le gestionnaire modal mobile
const mobileModalManager = new MobileEnhancedModal();

// Exposer globalement
window.golfModalManager = mobileModalManager;