/**
 * Simple Carousel for Hero Section
 * Version minimale et fonctionnelle
 */

class SimpleCarousel {
    constructor() {
        this.currentIndex = 0;
        this.slides = null;
        this.bullets = null;
        this.autoPlayInterval = null;
        this.init();
    }

    init() {
        // Trouver les éléments
        this.slides = document.querySelectorAll('.carousel-slide');
        this.bullets = document.querySelectorAll('.pagination-bullet');
        
        if (!this.slides.length) {
            console.warn('⚠️ Aucune slide trouvée pour le carousel');
            return;
        }
        
        // Démarrer l'autoplay
        this.startAutoPlay();
        
        console.log('✅ Simple Carousel initialisé avec', this.slides.length, 'slides');
    }

    goToSlide(index) {
        // Validation de l'index
        if (index < 0 || index >= this.slides.length) return;
        
        // Retirer la classe active de tous les éléments
        this.slides.forEach(slide => slide.classList.remove('active'));
        this.bullets.forEach(bullet => bullet.classList.remove('active'));
        
        // Ajouter la classe active aux éléments correspondants
        if (this.slides[index]) {
            this.slides[index].classList.add('active');
        }
        
        if (this.bullets[index]) {
            this.bullets[index].classList.add('active');
        }
        
        this.currentIndex = index;
    }

    nextSlide() {
        const nextIndex = (this.currentIndex + 1) % this.slides.length;
        this.goToSlide(nextIndex);
    }

    startAutoPlay() {
        // Nettoyer l'ancien interval
        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
        }
        
        // Démarrer le nouveau
        this.autoPlayInterval = setInterval(() => {
            this.nextSlide();
        }, 5000); // Change toutes les 5 secondes
    }

    stopAutoPlay() {
        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
            this.autoPlayInterval = null;
        }
    }

    // Fonction de debug
    debug() {
        return {
            currentIndex: this.currentIndex,
            totalSlides: this.slides ? this.slides.length : 0,
            autoPlayActive: !!this.autoPlayInterval
        };
    }
}

// Initialiser le carousel
document.addEventListener('DOMContentLoaded', function() {
    window.golfCarousel = new SimpleCarousel();
    console.log('🎠 Carousel global créé');
});

// Exposer globalement pour debug
window.debugCarousel = function() {
    if (window.golfCarousel) {
        console.log('Carousel Debug:', window.golfCarousel.debug());
    } else {
        console.log('❌ Carousel non initialisé');
    }
};