/**
 * TOUCH MANAGER - Gestionnaire Central d'Événements Tactiles
 * Architecture modulaire professionnelle - Détection précise tap vs swipe
 */

class TouchManager {
    constructor() {
        this.touchData = {
            startX: 0,
            startY: 0,
            startTime: 0,
            element: null
        };
        
        // Seuils de détection précis (standards UX mobile)
        this.thresholds = {
            maxTapDuration: 200,    // Maximum 200ms pour un tap
            maxTapDistance: 10,     // Maximum 10px de mouvement pour un tap
            minSwipeDistance: 30    // Minimum 30px pour un swipe
        };
        
        this.callbacks = {
            tap: new Map(),
            swipe: new Map()
        };
        
        this.init();
    }

    init() {
        // Event listeners passifs pour performance optimale
        document.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
        document.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false });
        
        console.log('✅ TouchManager initialisé avec détection précise');
    }

    handleTouchStart(e) {
        const touch = e.touches[0];
        this.touchData = {
            startX: touch.clientX,
            startY: touch.clientY,
            startTime: Date.now(),
            element: e.target
        };
    }

    handleTouchEnd(e) {
        if (!this.touchData.element) return;

        const touch = e.changedTouches[0];
        const endTime = Date.now();
        const duration = endTime - this.touchData.startTime;
        
        const deltaX = touch.clientX - this.touchData.startX;
        const deltaY = touch.clientY - this.touchData.startY;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

        // Détection précise : TAP vs SWIPE
        if (duration <= this.thresholds.maxTapDuration && distance <= this.thresholds.maxTapDistance) {
            // C'est un TAP
            this.handleTap(this.touchData.element, e);
        } else if (distance >= this.thresholds.minSwipeDistance) {
            // C'est un SWIPE
            this.handleSwipe(this.touchData.element, deltaX, deltaY, e);
        }
        
        // Reset
        this.touchData.element = null;
    }

    handleTap(element, originalEvent) {
        // EXCLUSION : Ne pas intercepter les éléments de navigation SAUF le menu hamburger
        if (this.isNavigationElement(element) && !element.matches('#mobile-menu-btn') && !element.closest('#mobile-menu-btn')) {
            return; // Laisser les gestionnaires natifs s'en occuper
        }
        
        // Vérifier si l'élément a un callback tap enregistré
        for (let [selector, callback] of this.callbacks.tap) {
            if (element.matches(selector) || element.closest(selector)) {
                originalEvent.preventDefault();
                callback(element, originalEvent);
                return;
            }
        }
    }

    handleSwipe(element, deltaX, deltaY, originalEvent) {
        // EXCLUSION : Ne pas intercepter les éléments de navigation SAUF le menu hamburger
        if (this.isNavigationElement(element) && !element.matches('#mobile-menu-btn') && !element.closest('#mobile-menu-btn')) {
            return;
        }
        
        // Vérifier si l'élément a un callback swipe enregistré
        for (let [selector, callback] of this.callbacks.swipe) {
            if (element.matches(selector) || element.closest(selector)) {
                const direction = Math.abs(deltaX) > Math.abs(deltaY) 
                    ? (deltaX > 0 ? 'right' : 'left')
                    : (deltaY > 0 ? 'down' : 'up');
                    
                callback(element, direction, originalEvent);
                return;
            }
        }
    }

    // Nouvelle méthode : détecter les éléments de navigation
    isNavigationElement(element) {
        const navigationSelectors = [
            '.nav-bar',
            '.language-switcher',
            '.lang-btn',
            '.lang-btn-mobile',
            '.lang-option',
            '.lang-option-mobile',
            '#mobile-menu-btn',
            '.mobile-menu-close',
            '.mobile-menu-link',
            '.mobile-menu-overlay'
        ];
        
        return navigationSelectors.some(selector => 
            element.matches(selector) || element.closest(selector)
        );
    }

    // API publique pour enregistrer des callbacks
    onTap(selector, callback) {
        this.callbacks.tap.set(selector, callback);
    }

    onSwipe(selector, callback) {
        this.callbacks.swipe.set(selector, callback);
    }

    // Supprimer des callbacks
    removeTap(selector) {
        this.callbacks.tap.delete(selector);
    }

    removeSwipe(selector) {
        this.callbacks.swipe.delete(selector);
    }

    // Debug
    getStats() {
        return {
            tapCallbacks: this.callbacks.tap.size,
            swipeCallbacks: this.callbacks.swipe.size,
            thresholds: this.thresholds
        };
    }
}

// Instance globale
window.touchManager = new TouchManager();