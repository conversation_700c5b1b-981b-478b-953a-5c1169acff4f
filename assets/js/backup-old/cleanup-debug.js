/**
 * NETTOYAGE DEBUG - Supprime tous les scripts de debug et restore le site proprement
 */

(function() {
    'use strict';
    
    console.log('🧹 NETTOYAGE DEBUG - Prêt à nettoyer...');
    
    window.cleanupDebug = {
        // Supprimer la bordure rouge du bouton hamburger
        removeRedBorder: function() {
            const menuBtn = document.getElementById('mobile-menu-btn');
            if (menuBtn) {
                menuBtn.style.border = 'none';
                console.log('✅ Bordure rouge supprimée du menu hamburger');
            }
        },
        
        // Supprimer le CSS de debug dans le head
        removeDebugCSS: function() {
            // Trouver et supprimer le style debug du hamburger
            const styles = document.querySelectorAll('style');
            styles.forEach(style => {
                if (style.textContent.includes('OVERRIDE CSS POUR MENU HAMBURGER')) {
                    // Garder les styles importants mais enlever le debug
                    style.textContent = style.textContent.replace(/border: 2px solid red !important;/g, '/* border debug removed */');
                    console.log('✅ CSS debug nettoyé');
                }
            });
        },
        
        // Supprimer tous les scripts de debug (à faire manuellement après test)
        listDebugScripts: function() {
            console.log('\n🗂️ SCRIPTS DEBUG À SUPPRIMER MANUELLEMENT:');
            console.log('1. debug-menu-force.js');
            console.log('2. debug-langues-desktop.js'); 
            console.log('3. diagnostic-menu-avance.js');
            console.log('4. Ce script (cleanup-debug.js)');
            console.log('\n📝 ÉTAPES:');
            console.log('1. Vérifier que tout fonctionne');
            console.log('2. Supprimer les scripts debug de index.html');
            console.log('3. Supprimer les fichiers debug du dossier js/');
            console.log('4. Tester une dernière fois');
        },
        
        // Test final que tout marche sans debug
        testFinal: function() {
            console.log('\n🧪 TEST FINAL - Sans scripts debug...');
            
            // Test menu hamburger
            const menuBtn = document.getElementById('mobile-menu-btn');
            const menuOverlay = document.getElementById('mobile-menu');
            
            if (menuBtn && menuOverlay) {
                console.log('✅ Éléments menu trouvés');
                
                // Simuler clic
                menuBtn.click();
                
                setTimeout(() => {
                    const isOpen = menuOverlay.classList.contains('mobile-menu-open') ||
                                  menuOverlay.style.display === 'flex';
                    
                    if (isOpen) {
                        console.log('✅ MENU HAMBURGER FONCTIONNE !');
                        // Refermer
                        menuBtn.click();
                    } else {
                        console.log('❌ Menu hamburger ne fonctionne toujours pas');
                    }
                }, 500);
            }
            
            // Test langues desktop
            if (window.translationManager) {
                console.log('✅ Système de langues OK');
            } else {
                console.log('❌ Système de langues KO');
            }
            
            // Test images (pas de scroll automatique)
            const images = document.querySelectorAll('.service-image, .course-image');
            console.log(`✅ ${images.length} images trouvées pour test scroll`);
        }
    };
    
    console.log('\n💡 COMMANDES DISPONIBLES:');
    console.log('cleanupDebug.removeRedBorder() - Enlever bordure rouge');
    console.log('cleanupDebug.removeDebugCSS() - Enlever CSS debug');
    console.log('cleanupDebug.listDebugScripts() - Lister scripts à supprimer');
    console.log('cleanupDebug.testFinal() - Test final sans debug');
    
})();
