/**
 * MOBILE UNIFIED CLEAN - GolfinThaï
 * Script unifié pour gérer menu mobile et modales sans conflits
 * Version nettoyée - Résout les problèmes de hamburger et téléportation
 */

class MobileUnifiedManager {
    constructor() {
        this.isMenuOpen = false;
        this.isModalOpen = false;
        this.scrollPosition = 0;
        
        // Éléments DOM
        this.elements = {
            menuBtn: null,
            menu: null,
            closeBtn: null,
            modal: null,
            modalClose: null
        };
        
        this.init();
    }

    init() {
        // Attendre que tout soit chargé, y compris les includes
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                // Attendre un peu plus pour les includes
                setTimeout(() => this.setup(), 1000);
            });
        } else {
            // Attendre un peu pour les includes
            setTimeout(() => this.setup(), 1000);
        }
    }

    setup() {
        this.findElements();
        this.bindMenuEvents();
        this.bindModalEvents();
        this.bindImageEvents();
        console.log('✅ Mobile Unified Manager initialisé');
    }

    findElements() {
        // Attendre que les includes soient chargés - Plus patient
        let attempts = 0;
        const maxAttempts = 50; // 5 secondes max

        const checkElements = () => {
            attempts++;
            this.elements.menuBtn = document.getElementById('mobile-menu-btn');
            this.elements.menu = document.getElementById('mobile-menu');
            this.elements.closeBtn = document.getElementById('close-menu');
            this.elements.modal = document.getElementById('course-modal');
            this.elements.modalClose = this.elements.modal?.querySelector('.modal-close');

            if (!this.elements.menuBtn || !this.elements.menu) {
                if (attempts < maxAttempts) {
                    setTimeout(checkElements, 100);
                    return;
                } else {
                    console.warn('⚠️ Éléments menu non trouvés après 5s - includes pas encore chargés ?');
                    return;
                }
            }

            console.log('✅ Éléments DOM trouvés après', attempts, 'tentatives');
        };

        checkElements();
    }

    // ===== GESTION MENU MOBILE =====
    
    bindMenuEvents() {
        // Attendre que les éléments soient disponibles
        const bindWhenReady = () => {
            if (!this.elements.menuBtn || !this.elements.menu) {
                setTimeout(bindWhenReady, 100);
                return;
            }

            // Bouton hamburger - UN SEUL EVENT LISTENER
            this.elements.menuBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggleMenu();
            });

            // Support tactile pour mobile
            this.elements.menuBtn.addEventListener('touchend', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggleMenu();
            });

            // Bouton fermer
            if (this.elements.closeBtn) {
                this.elements.closeBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.closeMenu();
                });
            }

            // Fermer menu sur clic des liens AVEC navigation
            const menuLinks = this.elements.menu.querySelectorAll('a');
            menuLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    // NE PAS empêcher la navigation par défaut
                    // e.preventDefault(); // ← SUPPRIMÉ !

                    console.log('🔗 Navigation vers:', link.getAttribute('href'));

                    // Fermer le menu APRÈS un petit délai pour permettre la navigation
                    setTimeout(() => {
                        this.closeMenu();
                    }, 100);
                });
            });

            // Fermer avec Escape
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.isMenuOpen) {
                    this.closeMenu();
                }
            });

            // Auto-fermeture responsive
            window.addEventListener('resize', () => {
                if (window.innerWidth >= 1024 && this.isMenuOpen) {
                    this.closeMenu();
                }
            });

            console.log('✅ Events menu mobile bindés');
        };

        bindWhenReady();
    }

    toggleMenu() {
        if (this.isMenuOpen) {
            this.closeMenu();
        } else {
            this.openMenu();
        }
    }

    openMenu() {
        if (!this.elements.menu || this.isMenuOpen) return;

        // Sauvegarder position scroll
        this.scrollPosition = window.pageYOffset;

        // Ouvrir menu
        this.elements.menu.classList.add('mobile-menu-open');
        this.elements.menu.setAttribute('aria-hidden', 'false');
        this.elements.menuBtn.setAttribute('aria-expanded', 'true');
        
        // Bloquer scroll
        document.body.classList.add('body-menu-locked');
        document.body.style.top = `-${this.scrollPosition}px`;
        
        this.isMenuOpen = true;
        console.log('🍔 Menu ouvert');
    }

    closeMenu() {
        if (!this.elements.menu || !this.isMenuOpen) return;

        // Fermer menu
        this.elements.menu.classList.remove('mobile-menu-open');
        this.elements.menu.setAttribute('aria-hidden', 'true');
        this.elements.menuBtn.setAttribute('aria-expanded', 'false');

        // Débloquer scroll SANS téléportation - Méthode améliorée
        document.body.classList.remove('body-menu-locked');
        document.body.style.top = '';

        // Restaurer position avec requestAnimationFrame pour éviter les conflits
        requestAnimationFrame(() => {
            window.scrollTo(0, this.scrollPosition);
        });

        this.isMenuOpen = false;
        console.log('❌ Menu fermé SANS téléportation');
    }

    // ===== GESTION MODALES =====
    
    bindModalEvents() {
        const bindWhenReady = () => {
            if (!this.elements.modal) {
                setTimeout(bindWhenReady, 100);
                return;
            }

            // Bouton fermer modal
            if (this.elements.modalClose) {
                this.elements.modalClose.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.closeModal();
                });
            }

            // Fermer sur clic backdrop
            this.elements.modal.addEventListener('click', (e) => {
                if (e.target === this.elements.modal || e.target.classList.contains('modal-backdrop')) {
                    this.closeModal();
                }
            });

            // Fermer avec Escape
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.isModalOpen) {
                    this.closeModal();
                }
            });

            console.log('✅ Events modal bindés');
        };

        bindWhenReady();
    }

    bindImageEvents() {
        // Délégation d'événements pour les images avec debug détaillé
        document.addEventListener('click', (e) => {
            console.log('🖱️ Clic détecté sur:', e.target);

            const image = e.target.closest('img[src*="assets/images"]');

            if (image) {
                console.log('🖼️ Image trouvée:', image.src);
                console.log('🔍 Image cliquable?', this.isImageClickable(image));

                if (this.isImageClickable(image)) {
                    console.log('✅ Ouverture modal pour:', image.alt);
                    e.preventDefault();
                    e.stopPropagation();
                    this.openImageModal(image);
                } else {
                    console.log('❌ Image non cliquable (exclue)');
                }
            } else {
                console.log('ℹ️ Pas une image assets/images');
            }
        });

        console.log('✅ Events images bindés avec debug');
    }

    isImageClickable(image) {
        // Exclure certaines images
        if (image.closest('.hero-carousel') ||
            image.closest('.logo') ||
            image.closest('button') ||
            image.closest('a') ||
            image.alt?.toLowerCase().includes('logo')) {
            return false;
        }
        return true;
    }

    openImageModal(imageElement) {
        if (!this.elements.modal || !imageElement) return;

        // Vérifier si une modal est déjà ouverte
        if (this.isModalOpen) {
            console.log('⚠️ Modal déjà ouverte, fermeture d\'abord...');
            this.closeModal();
            // Attendre la fermeture avant d'ouvrir la nouvelle
            setTimeout(() => {
                this.openImageModal(imageElement);
            }, 350);
            return;
        }

        // Sauvegarder position scroll AVANT d'ouvrir la modal
        this.scrollPosition = window.pageYOffset;

        // Remplir la modal avec l'image
        const modalImage = this.elements.modal.querySelector('.modal-image');
        const modalTitle = this.elements.modal.querySelector('.modal-title');
        const modalBody = this.elements.modal.querySelector('.modal-body');

        if (modalImage) {
            modalImage.src = imageElement.src;
            modalImage.alt = imageElement.alt || 'Image agrandie';
        }

        if (modalTitle) {
            modalTitle.textContent = imageElement.alt || 'Image';
        }

        if (modalBody) {
            modalBody.innerHTML = `<p>${imageElement.alt || 'Image agrandie'}</p>`;
        }

        this.showModal();
        console.log('🖼️ Image ouverte en modal');
    }

    showModal() {
        if (!this.elements.modal) return;

        this.elements.modal.classList.remove('modal-hidden');
        this.elements.modal.classList.add('modal-open');
        this.elements.modal.setAttribute('aria-hidden', 'false');
        this.elements.modal.style.display = 'flex';
        
        // Bloquer scroll
        document.body.classList.add('body-modal-locked');
        document.body.style.top = `-${this.scrollPosition}px`;
        
        this.isModalOpen = true;
    }

    closeModal() {
        if (!this.elements.modal || !this.isModalOpen) return;

        console.log('🔄 Fermeture modal en cours...');

        this.elements.modal.classList.remove('modal-open');
        this.elements.modal.classList.add('modal-closed');

        setTimeout(() => {
            // Reset complet de la modal
            this.elements.modal.classList.remove('modal-closed');
            this.elements.modal.classList.add('modal-hidden');
            this.elements.modal.setAttribute('aria-hidden', 'true');
            this.elements.modal.style.display = 'none';

            // Nettoyer le contenu de la modal pour éviter les conflits
            const modalImage = this.elements.modal.querySelector('.modal-image');
            if (modalImage) {
                modalImage.src = '';
                modalImage.alt = '';
            }

            // Débloquer scroll SANS téléportation - Méthode améliorée
            document.body.classList.remove('body-modal-locked');
            document.body.style.top = '';

            // Restaurer position avec requestAnimationFrame pour éviter les conflits
            requestAnimationFrame(() => {
                window.scrollTo(0, this.scrollPosition);
            });

            // Reset de l'état APRÈS tout le nettoyage
            this.isModalOpen = false;
            console.log('✅ Modal fermée et nettoyée - Prête pour réouverture');
        }, 300);
    }
}

// Initialiser le gestionnaire unifié avec protection
try {
    window.mobileManager = new MobileUnifiedManager();
    console.log('✅ Mobile Manager initialisé avec succès');
} catch (error) {
    console.error('❌ Erreur initialisation Mobile Manager:', error);

    // Fallback - réessayer après un délai
    setTimeout(() => {
        try {
            window.mobileManager = new MobileUnifiedManager();
            console.log('✅ Mobile Manager initialisé en fallback');
        } catch (error2) {
            console.error('❌ Échec fallback Mobile Manager:', error2);
        }
    }, 2000);
}
