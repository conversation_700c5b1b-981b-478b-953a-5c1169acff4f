/**
 * DIAGNOSTIC AVANCÉ MENU HAMBURGER MOBILE
 * Investigation complète pour trouver pourquoi ça ne marche pas
 */

(function() {
    'use strict';
    
    console.log('🔬 DIAGNOSTIC AVANCÉ MENU HAMBURGER...');
    
    function diagnosticComplet() {
        const menuBtn = document.getElementById('mobile-menu-btn');
        const menuOverlay = document.getElementById('mobile-menu');
        const closeBtn = document.getElementById('close-menu');
        
        console.log('\n=== ÉLÉMENTS HTML ===');
        console.log('Menu button:', !!menuBtn);
        console.log('Menu overlay:', !!menuOverlay);
        console.log('Close button:', !!closeBtn);
        
        if (!menuBtn) {
            console.log('❌ PROBLÈME: Bouton hamburger introuvable !');
            return;
        }
        
        if (!menuOverlay) {
            console.log('❌ PROBLÈME: Menu overlay introuvable !');
            return;
        }
        
        console.log('\n=== STYLES CSS DU BOUTON ===');
        const btnStyles = getComputedStyle(menuBtn);
        console.log('Display:', btnStyles.display);
        console.log('Visibility:', btnStyles.visibility);
        console.log('Opacity:', btnStyles.opacity);
        console.log('Pointer events:', btnStyles.pointerEvents);
        console.log('Z-index:', btnStyles.zIndex);
        console.log('Position:', btnStyles.position);
        
        console.log('\n=== STYLES CSS DU MENU ===');
        const menuStyles = getComputedStyle(menuOverlay);
        console.log('Display:', menuStyles.display);
        console.log('Visibility:', menuStyles.visibility);
        console.log('Opacity:', menuStyles.opacity);
        console.log('Z-index:', menuStyles.zIndex);
        console.log('Position:', menuStyles.position);
        
        console.log('\n=== EVENT LISTENERS ===');
        console.log('onclick défini:', typeof menuBtn.onclick);
        console.log('ontouchend défini:', typeof menuBtn.ontouchend);
        
        // Test des gestionnaires d'événements existants
        let clickCount = 0;
        let touchCount = 0;
        
        // Ajouter un listener de test
        menuBtn.addEventListener('click', function testClick(e) {
            clickCount++;
            console.log(`🖱️ CLICK #${clickCount} détecté !`);
            console.log('Event object:', e);
            console.log('Target:', e.target);
            console.log('CurrentTarget:', e.currentTarget);
        });
        
        menuBtn.addEventListener('touchend', function testTouch(e) {
            touchCount++;
            console.log(`👆 TOUCH #${touchCount} détecté !`);
            console.log('Event object:', e);
            console.log('Target:', e.target);
        });
        
        console.log('\n=== SYSTÈMES ACTIFS ===');
        console.log('TouchManager:', !!window.touchManager);
        console.log('MobileMenuManager:', !!window.mobileMenuManager);
        console.log('GolfApp:', !!window.golfApp);
        console.log('TranslationManager:', !!window.translationManager);
        
        console.log('\n=== TEST MANUEL MENU ===');
        console.log('💡 Pour tester : diagnosticMenu.testClick()');
        console.log('💡 Pour ouvrir force : diagnosticMenu.forceOpen()');
        console.log('💡 Pour fermer force : diagnosticMenu.forceClose()');
    }
    
    // API de test
    window.diagnosticMenu = {
        testClick: function() {
            const menuBtn = document.getElementById('mobile-menu-btn');
            if (menuBtn) {
                console.log('🧪 Simulation clic...');
                menuBtn.click();
            }
        },
        
        forceOpen: function() {
            const menuOverlay = document.getElementById('mobile-menu');
            if (menuOverlay) {
                console.log('💪 Force ouverture...');
                
                // Toutes les méthodes possibles
                menuOverlay.classList.add('mobile-menu-open');
                menuOverlay.style.cssText = `
                    display: flex !important;
                    position: fixed !important;
                    top: 0 !important;
                    left: 0 !important;
                    width: 100% !important;
                    height: 100% !important;
                    background: rgba(0, 0, 0, 0.95) !important;
                    z-index: 99999 !important;
                    flex-direction: column !important;
                    align-items: center !important;
                    justify-content: center !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                `;
                
                document.body.style.overflow = 'hidden';
                document.body.classList.add('body-menu-locked');
                
                console.log('✅ Menu forcé ouvert !');
            }
        },
        
        forceClose: function() {
            const menuOverlay = document.getElementById('mobile-menu');
            if (menuOverlay) {
                console.log('💪 Force fermeture...');
                
                menuOverlay.classList.remove('mobile-menu-open');
                menuOverlay.style.display = 'none';
                
                document.body.style.overflow = '';
                document.body.classList.remove('body-menu-locked');
                
                console.log('✅ Menu forcé fermé !');
            }
        },
        
        checkConflicts: function() {
            console.log('\n=== VÉRIFICATION CONFLITS ===');
            
            const menuBtn = document.getElementById('mobile-menu-btn');
            if (!menuBtn) return;
            
            // Vérifier tous les listeners attachés
            const events = getEventListeners(menuBtn);
            console.log('Event listeners:', events);
            
            // Vérifier les éléments par-dessus
            const rect = menuBtn.getBoundingClientRect();
            const elementBelow = document.elementFromPoint(rect.left + rect.width/2, rect.top + rect.height/2);
            
            console.log('Élément au point du bouton:', elementBelow);
            console.log('Est-ce le bouton lui-même ?', elementBelow === menuBtn);
            
            if (elementBelow !== menuBtn) {
                console.log('⚠️ Un autre élément cache le bouton !');
                console.log('Élément qui cache:', elementBelow);
            }
        },
        
        removeRedBorder: function() {
            const menuBtn = document.getElementById('mobile-menu-btn');
            if (menuBtn) {
                menuBtn.style.border = 'none';
                console.log('✅ Bordure rouge supprimée');
            }
        }
    };
    
    // Auto diagnostic après 3 secondes
    setTimeout(diagnosticComplet, 3000);
    
})();
