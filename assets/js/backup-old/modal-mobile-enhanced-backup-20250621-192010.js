/**
 * Gestionnaire Modal Amélioré Mobile - GolfinThaï
 * Version corrigée pour problèmes touch mobile
 */

class MobileEnhancedModal {
    constructor() {
        this.modal = null;
        this.isOpen = false;
        this.currentImage = null;
        this.touchStartY = 0;
        this.touchStartX = 0;
        this.lastTap = 0;
        this.init();
    }

    init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupModal();
            });
        } else {
            this.setupModal();
        }
    }

    setupModal() {
        this.modal = document.getElementById('course-modal');
        if (!this.modal) {
            console.error('❌ Modal non trouvée');
            return;
        }
        
        this.bindEvents();
        this.createImageZoomFeature();
        console.log('✅ Modal mobile configurée');
    }

    bindEvents() {
        // Événements principaux avec amélioration mobile
        document.addEventListener('click', (e) => this.handleClick(e));
        document.addEventListener('touchend', (e) => this.handleTouchEnd(e));
        
        // Événements de fermeture
        if (this.modal) {
            const closeBtn = this.modal.querySelector('.modal-close');
            const backdrop = this.modal.querySelector('.modal-backdrop');
            
            if (closeBtn) {
                closeBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.closeModal();
                });
                
                // Touch spécifique pour mobile
                closeBtn.addEventListener('touchend', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.closeModal();
                });
            }
            
            if (backdrop) {
                backdrop.addEventListener('click', () => this.closeModal());
                backdrop.addEventListener('touchend', (e) => {
                    if (e.target === backdrop) {
                        this.closeModal();
                    }
                });
            }
        }

        // Gestion clavier
        document.addEventListener('keydown', (e) => {
            if (this.isOpen && e.key === 'Escape') {
                this.closeModal();
            }
        });

        // Gestes tactiles pour mobile
        if (this.modal) {
            this.modal.addEventListener('touchstart', (e) => {
                this.touchStartY = e.touches[0].clientY;
                this.touchStartX = e.touches[0].clientX;
            }, { passive: true });

            this.modal.addEventListener('touchmove', (e) => {
                // Empêcher le scroll du body
                if (this.isOpen) {
                    e.preventDefault();
                }
            }, { passive: false });

            this.modal.addEventListener('touchend', (e) => {
                this.handleModalSwipe(e);
            }, { passive: true });
        }
    }

    handleClick(e) {
        // Ignorer si modal ouverte
        if (this.isOpen) return;
        
        // Exclure le logo explicitement
        if (e.target.closest('.logo, [src*="logo"], [alt*="Logo"], [alt*="logo"]')) {
            console.log('🚫 Clic sur logo ignoré');
            return;
        }
        
        // Gestion course cards
        const courseCard = e.target.closest('.course-card');
        if (courseCard && !e.target.closest('button') && !e.target.closest('a')) {
            e.preventDefault();
            this.openModal(courseCard);
            return;
        }

        // Gestion service cards
        const serviceCard = e.target.closest('.service-card');
        if (serviceCard && !e.target.closest('button') && !e.target.closest('a')) {
            const serviceImage = serviceCard.querySelector('.service-image');
            if (serviceImage) {
                this.openImageInModal(serviceImage);
            }
            return;
        }

        // Gestion images générales (sauf logo et carrousel)
        const clickableImage = e.target.closest('img[src*="assets/images"]');
        if (clickableImage && 
            !e.target.closest('button') && 
            !e.target.closest('a') && 
            !e.target.closest('.hero-carousel') &&
            !e.target.closest('.logo') &&
            !clickableImage.alt.toLowerCase().includes('logo')) {
            
            this.openImageInModal(clickableImage);
            return;
        }
    }

    handleTouchEnd(e) {
        // Ignorer si modal ouverte ou si c'est un double tap rapide
        if (this.isOpen) return;
        
        const currentTime = new Date().getTime();
        const tapLength = currentTime - this.lastTap;
        
        // Éviter les double taps accidentels
        if (tapLength < 300) {
            return;
        }
        this.lastTap = currentTime;
        
        // Même logique que handleClick mais pour touch
        const target = e.target;
        
        // Exclure le logo
        if (target.closest('.logo, [src*="logo"], [alt*="Logo"], [alt*="logo"]')) {
            return;
        }
        
        // Course cards avec feedback visuel
        const courseCard = target.closest('.course-card');
        if (courseCard && !target.closest('button') && !target.closest('a')) {
            e.preventDefault();
            
            // Feedback visuel
            courseCard.style.transform = 'scale(0.95)';
            setTimeout(() => {
                courseCard.style.transform = '';
                this.openModal(courseCard);
            }, 150);
            return;
        }

        // Service cards
        const serviceCard = target.closest('.service-card');
        if (serviceCard && !target.closest('button') && !target.closest('a')) {
            const serviceImage = serviceCard.querySelector('.service-image');
            if (serviceImage) {
                serviceCard.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    serviceCard.style.transform = '';
                    this.openImageInModal(serviceImage);
                }, 150);
            }
            return;
        }

        // Images générales
        const clickableImage = target.closest('img[src*="assets/images"]');
        if (clickableImage && 
            !target.closest('button') && 
            !target.closest('a') && 
            !target.closest('.hero-carousel') &&
            !target.closest('.logo') &&
            !clickableImage.alt.toLowerCase().includes('logo')) {
            
            this.openImageInModal(clickableImage);
            return;
        }
    }

    openModal(courseCard) {
        if (!this.modal || !courseCard) {
            console.error('❌ Modal ou course card manquant');
            return;
        }

        const courseData = this.extractCourseData(courseCard);
        this.populateModal(courseData);
        this.showModal();
        this.setupAccessibility();
        
        console.log('📖 Modal ouverte:', courseData.title);
    }

    openImageInModal(imageElement) {
        if (!this.modal || !imageElement) {
            console.error('❌ Modal ou image manquante');
            return;
        }

        const imageData = {
            id: 'image-modal',
            image: imageElement.src,
            alt: imageElement.alt || 'Image agrandie',
            title: imageElement.alt || 'Image',
            description: '',
            location: '',
            features: ''
        };
        
        this.populateImageModal(imageData);
        this.showModal();
        this.setupAccessibility();
        
        console.log('🖼️ Image ouverte en modal');
    }

    populateImageModal(data) {
        const modalImage = this.modal.querySelector('.modal-image');
        const modalTitle = this.modal.querySelector('.modal-title');
        const modalLocation = this.modal.querySelector('.modal-location');
        const modalSubtitle = this.modal.querySelector('.modal-subtitle');
        const modalBody = this.modal.querySelector('.modal-body');

        if (modalImage && data.image) {
            modalImage.style.opacity = '0';
            modalImage.src = data.image;
            modalImage.alt = data.alt;
            
            modalImage.onload = () => {
                modalImage.style.transition = 'opacity 0.3s ease';
                modalImage.style.opacity = '1';
            };
        }

        if (modalTitle) modalTitle.textContent = data.title;
        if (modalLocation) modalLocation.textContent = '';
        if (modalSubtitle) modalSubtitle.textContent = '';
        
        if (modalBody) {
            // SUPPRESSION du texte "cliquer ici pour zoomer"
            modalBody.innerHTML = `
                <div class="modal-image-viewer">
                    <p class="modal-description">${data.description}</p>
                </div>
            `;
        }
    }

    populateModal(data) {
        const modalImage = this.modal.querySelector('.modal-image');
        const modalTitle = this.modal.querySelector('.modal-title');
        const modalLocation = this.modal.querySelector('.modal-location');
        const modalSubtitle = this.modal.querySelector('.modal-subtitle');
        const modalBody = this.modal.querySelector('.modal-body');

        if (modalImage && data.image) {
            modalImage.style.opacity = '0';
            modalImage.src = data.image;
            modalImage.alt = data.alt;
            
            modalImage.onload = () => {
                modalImage.style.transition = 'opacity 0.3s ease';
                modalImage.style.opacity = '1';
            };
            
            modalImage.onerror = () => {
                modalImage.src = './assets/images/placeholder-golf.svg';
                modalImage.alt = 'Image non disponible';
                modalImage.style.opacity = '1';
            };
        }

        if (modalTitle) modalTitle.textContent = data.title;
        if (modalLocation) modalLocation.textContent = data.location;
        if (modalSubtitle) modalSubtitle.textContent = data.features;
        
        if (modalBody) {
            // SUPPRESSION du texte "cliquer ici pour zoomer"
            modalBody.innerHTML = `
                <div class="modal-description-section">
                    <p class="modal-description">${data.description}</p>
                </div>
                <div class="modal-features-section">
                    <h4 class="modal-section-title">
                        <i class="fas fa-golf-ball" aria-hidden="true"></i>
                        Caractéristiques du parcours
                    </h4>
                    <p class="modal-features">${data.features}</p>
                </div>
            `;
        }
    }

    extractCourseData(courseCard) {
        const image = courseCard.querySelector('.course-image');
        const title = courseCard.querySelector('.course-title');
        const description = courseCard.querySelector('.course-description');
        const location = courseCard.querySelector('.course-location-badge');
        const features = courseCard.querySelector('.course-features');
        const courseId = courseCard.getAttribute('data-course') || '';

        return {
            id: courseId,
            image: image ? image.src : '',
            alt: image ? image.alt : '',
            title: title ? title.textContent.trim() : '',
            description: description ? description.textContent.trim() : '',
            location: location ? location.textContent.trim() : '',
            features: features ? features.textContent.trim() : ''
        };
    }

    showModal() {
        this.modal.style.display = 'flex';
        this.modal.setAttribute('aria-hidden', 'false');
        document.body.style.overflow = 'hidden';
        this.isOpen = true;
        
        // Animation d'ouverture
        this.modal.style.animation = 'modalFadeIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards';
    }

    closeModal() {
        if (!this.modal || !this.isOpen) return;

        this.modal.style.animation = 'modalFadeOut 0.3s ease-out forwards';
        
        setTimeout(() => {
            this.modal.style.display = 'none';
            this.modal.style.animation = '';
            this.modal.setAttribute('aria-hidden', 'true');
            document.body.style.overflow = '';
            this.isOpen = false;
            this.resetImageZoom();
            
            console.log('❌ Modal fermée');
        }, 300);
    }

    handleModalSwipe(e) {
        const swipeThreshold = 100;
        const diffY = this.touchStartY - e.changedTouches[0].clientY;
        const diffX = Math.abs(this.touchStartX - e.changedTouches[0].clientX);
        
        // Swipe vertical vers le haut pour fermer (si pas de swipe horizontal significatif)
        if (diffY > swipeThreshold && diffX < 50) {
            this.closeModal();
        }
    }

    setupAccessibility() {
        const closeBtn = this.modal.querySelector('.modal-close');
        if (closeBtn) {
            setTimeout(() => closeBtn.focus(), 100);
        }
    }

    createImageZoomFeature() {
        if (!document.getElementById('mobile-modal-styles')) {
            const style = document.createElement('style');
            style.id = 'mobile-modal-styles';
            style.textContent = `
                @keyframes modalFadeIn {
                    from { opacity: 0; transform: scale(0.9); }
                    to { opacity: 1; transform: scale(1); }
                }
                @keyframes modalFadeOut {
                    from { opacity: 1; transform: scale(1); }
                    to { opacity: 0; transform: scale(0.9); }
                }
                
                @media (max-width: 768px) {
                    .modal-content {
                        margin: 1rem !important;
                        max-height: 90vh !important;
                        width: calc(100vw - 2rem) !important;
                    }
                    
                    .modal-image {
                        height: 250px !important;
                        object-fit: cover !important;
                    }
                    
                    .modal-body {
                        padding: 1.5rem !important;
                    }
                    
                    .modal-close {
                        top: 0.5rem !important;
                        right: 0.5rem !important;
                        width: 44px !important;
                        height: 44px !important;
                        font-size: 1.25rem !important;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }

    resetImageZoom() {
        if (this.currentImage) {
            this.currentImage.classList.remove('zoomed');
            this.currentImage = null;
        }
    }

    debug() {
        console.log('🔍 Debug Modal Mobile:', {
            modal: this.modal,
            isOpen: this.isOpen,
            currentImage: this.currentImage
        });
    }
}

// Initialiser le gestionnaire modal mobile
const mobileModalManager = new MobileEnhancedModal();

// Exposer globalement
window.golfModalManager = mobileModalManager;
