/**
 * 🍔 HAMBURGER MENU FIX - Silicon Valley Style
 * Fix rapide et efficace pour le menu mobile
 */

(function() {
    'use strict';
    
    console.log('🍔 Hamburger Fix V2 Loading...');
    
    // Configuration
    const CONFIG = {
        DEBUG: true,
        ANIMATION_DURATION: 300
    };
    
    // Device detection
    const IS_MOBILE = window.innerWidth <= 1023;
    const IS_IOS = /iPhone|iPad|iPod/.test(navigator.userAgent);
    
    class HamburgerMenuFix {
        constructor() {
            this.menuBtn = null;
            this.menuOverlay = null;
            this.closeBtn = null;
            this.isMenuOpen = false;
            this.init();
        }
        
        init() {
            // Wait for DOM
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.setup());
            } else {
                this.setup();
            }
        }
        
        setup() {
            this.log('🔧 Setting up hamburger menu...');
            
            // Find elements - with retry mechanism
            this.findElements();
            
            if (!this.menuBtn || !this.menuOverlay) {
                this.log('⏳ Elements not found, retrying in 500ms...');
                setTimeout(() => this.setup(), 500);
                return;
            }
            
            // Add missing ID if needed
            if (!this.menuBtn.id) {
                this.menuBtn.id = 'mobile-menu-btn';
                this.log('✅ Added missing ID to menu button');
            }
            
            // Setup events
            this.bindEvents();
            
            // Add styles
            this.addStyles();
            
            this.log('✅ Hamburger menu setup complete!');
        }
        
        findElements() {
            // Find menu button by various methods
            this.menuBtn = document.getElementById('mobile-menu-btn') || 
                          document.querySelector('[mobile-menu-btn]') ||
                          document.querySelector('[aria-label*="menu mobile"]');
            
            // Find menu overlay
            this.menuOverlay = document.getElementById('mobile-menu') ||
                              document.querySelector('.mobile-menu-overlay');
            
            // Find close button
            this.closeBtn = document.getElementById('close-menu') ||
                           this.menuOverlay?.querySelector('[id*="close"]');
            
            this.log(`Elements found - Button: ${!!this.menuBtn}, Overlay: ${!!this.menuOverlay}, Close: ${!!this.closeBtn}`);
        }
        
        bindEvents() {
            // Remove any existing handlers
            const newBtn = this.menuBtn.cloneNode(true);
            this.menuBtn.parentNode.replaceChild(newBtn, this.menuBtn);
            this.menuBtn = newBtn;
            
            // Bind based on device
            if (IS_IOS) {
                this.bindIOSEvents();
            } else {
                this.bindStandardEvents();
            }
            
            // Close button
            if (this.closeBtn) {
                this.closeBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.closeMenu();
                });
            }
            
            // Close on escape
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.isMenuOpen) {
                    this.closeMenu();
                }
            });
            
            // Close on outside click
            this.menuOverlay?.addEventListener('click', (e) => {
                if (e.target === this.menuOverlay) {
                    this.closeMenu();
                }
            });
        }
        
        bindIOSEvents() {
            this.log('📱 Binding iOS touch events');
            
            this.menuBtn.addEventListener('touchstart', (e) => {
                e.preventDefault();
                this.toggleMenu();
            }, { passive: false });
            
            // Prevent ghost clicks
            this.menuBtn.addEventListener('click', (e) => {
                e.preventDefault();
            });
        }
        
        bindStandardEvents() {
            this.log('🖥️ Binding standard click events');
            
            this.menuBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleMenu();
            });
        }
        
        toggleMenu() {
            if (this.isMenuOpen) {
                this.closeMenu();
            } else {
                this.openMenu();
            }
        }
        
        openMenu() {
            if (!this.menuOverlay || this.isMenuOpen) return;
            
            this.log('📂 Opening menu');
            
            this.isMenuOpen = true;
            this.menuOverlay.classList.add('mobile-menu-open');
            this.menuOverlay.style.display = 'flex';
            this.menuBtn.setAttribute('aria-expanded', 'true');
            
            // Lock body scroll
            document.body.style.overflow = 'hidden';
            
            // Animate
            requestAnimationFrame(() => {
                this.menuOverlay.style.opacity = '1';
                this.menuOverlay.style.transform = 'translateX(0)';
            });
        }
        
        closeMenu() {
            if (!this.menuOverlay || !this.isMenuOpen) return;
            
            this.log('📁 Closing menu');
            
            this.isMenuOpen = false;
            this.menuOverlay.classList.remove('mobile-menu-open');
            this.menuBtn.setAttribute('aria-expanded', 'false');
            
            // Animate out
            this.menuOverlay.style.opacity = '0';
            this.menuOverlay.style.transform = 'translateX(100%)';
            
            // Hide after animation
            setTimeout(() => {
                if (!this.isMenuOpen) {
                    this.menuOverlay.style.display = 'none';
                    document.body.style.overflow = '';
                }
            }, CONFIG.ANIMATION_DURATION);
        }
        
        addStyles() {
            const style = document.createElement('style');
            style.textContent = `
                /* Hamburger Menu Fix Styles */
                #mobile-menu-btn {
                    -webkit-tap-highlight-color: transparent !important;
                    touch-action: manipulation !important;
                    cursor: pointer !important;
                    z-index: 9999 !important;
                    position: relative !important;
                }
                
                .mobile-menu-overlay {
                    transition: opacity ${CONFIG.ANIMATION_DURATION}ms ease,
                               transform ${CONFIG.ANIMATION_DURATION}ms ease !important;
                    transform: translateX(100%);
                    opacity: 0;
                }
                
                .mobile-menu-overlay.mobile-menu-open {
                    transform: translateX(0) !important;
                    opacity: 1 !important;
                }
                
                /* iOS specific */
                @supports (-webkit-touch-callout: none) {
                    #mobile-menu-btn {
                        min-height: 44px !important;
                        min-width: 44px !important;
                    }
                }
            `;
            document.head.appendChild(style);
        }
        
        log(message) {
            if (CONFIG.DEBUG) {
                console.log(`[HamburgerFix] ${message}`);
            }
        }
    }
    
    // Initialize
    window.hamburgerMenuFix = new HamburgerMenuFix();
    
    // Also fix the language switch while we're at it
    function fixLanguageSwitch() {
        const mobileLangBtn = document.querySelector('.language-switcher-mobile');
        
        if (mobileLangBtn && IS_IOS) {
            mobileLangBtn.addEventListener('touchstart', function(e) {
                e.preventDefault();
                const currentLang = this.dataset.lang || 'fr';
                const newLang = currentLang === 'fr' ? 'en' : 'fr';
                
                this.dataset.lang = newLang;
                
                // Trigger language change
                window.dispatchEvent(new CustomEvent('languageChange', {
                    detail: { lang: newLang }
                }));
            }, { passive: false });
        }
    }
    
    // Fix language switch after DOM ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fixLanguageSwitch);
    } else {
        fixLanguageSwitch();
    }
    
    console.log('🍔 Hamburger Fix V2 Ready!');
    
})();