/**
 * Gestionnaire de formulaires pour GolfinThaï
 * Validation et soumission du formulaire de contact
 */

class FormsManager {
    constructor() {
        this.config = window.GolfinThaiConfig.form;
        this.init();
    }

    init() {
        document.addEventListener('DOMContentLoaded', () => {
            this.bindFormEvents();
        });
    }

    bindFormEvents() {
        const contactForm = document.querySelector('.contact-form');
        if (contactForm) {
            contactForm.addEventListener('submit', (e) => this.handleFormSubmit(e));
            
            // Validation en temps réel
            const inputs = contactForm.querySelectorAll('input, textarea');
            inputs.forEach(input => {
                input.addEventListener('blur', () => this.validateField(input));
                input.addEventListener('input', () => this.clearFieldError(input));
            });
        }
    }

    async handleFormSubmit(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        
        // Validation complète avant envoi
        if (!this.validateForm(form)) {
            return;
        }

        // Désactiver le bouton et afficher un loading
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        this.setSubmitButtonState(submitBtn, true, 'Envoi en cours...');

        try {
            const response = await fetch(this.config.action, {
                method: this.config.method,
                body: formData,
                headers: {
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                this.showSuccess(form);
                form.reset();
            } else {
                this.showError('Une erreur est survenue. Veuillez réessayer.');
            }
        } catch (error) {
            console.error('Form submission error:', error);
            this.showError('Erreur de connexion. Veuillez vérifier votre connexion internet.');
        } finally {
            this.setSubmitButtonState(submitBtn, false, originalText);
        }
    }

    validateForm(form) {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');
        
        requiredFields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        return isValid;
    }

    validateField(field) {
        const value = field.value.trim();
        const fieldType = field.type;
        let isValid = true;
        let errorMessage = '';

        // Validation des champs requis
        if (field.required && !value) {
            isValid = false;
            errorMessage = 'Ce champ est obligatoire';
        }
        // Validation email
        else if (fieldType === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                errorMessage = 'Veuillez entrer une adresse email valide';
            }
        }
        // Validation téléphone
        else if (fieldType === 'tel' && value) {
            const phoneRegex = /^[\+]?[0-9\s\-\(\)]+$/;
            if (!phoneRegex.test(value) || value.length < 8) {
                isValid = false;
                errorMessage = 'Veuillez entrer un numéro de téléphone valide';
            }
        }

        this.showFieldValidation(field, isValid, errorMessage);
        return isValid;
    }

    showFieldValidation(field, isValid, errorMessage) {
        // Supprimer les anciens messages d'erreur
        this.clearFieldError(field);

        if (!isValid) {
            field.classList.add('error');
            
            // Créer et afficher le message d'erreur
            const errorElement = document.createElement('span');
            errorElement.className = 'field-error';
            errorElement.textContent = errorMessage;
            errorElement.setAttribute('role', 'alert');
            
            field.parentNode.appendChild(errorElement);
        } else {
            field.classList.remove('error');
        }
    }

    clearFieldError(field) {
        field.classList.remove('error');
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
    }

    setSubmitButtonState(button, isLoading, text) {
        button.disabled = isLoading;
        button.innerHTML = text;
        if (isLoading) {
            button.classList.add('loading');
        } else {
            button.classList.remove('loading');
        }
    }

    showSuccess(form) {
        const message = document.createElement('div');
        message.className = 'form-success';
        message.textContent = 'Votre message a été envoyé avec succès !';
        message.setAttribute('role', 'alert');
        
        form.parentNode.insertBefore(message, form);
        
        setTimeout(() => {
            message.remove();
        }, 5000);
    }

    showError(errorText) {
        const message = document.createElement('div');
        message.className = 'form-error';
        message.textContent = errorText;
        message.setAttribute('role', 'alert');
        
        const form = document.querySelector('.contact-form');
        if (form) {
            form.parentNode.insertBefore(message, form);
            
            setTimeout(() => {
                message.remove();
            }, 5000);
        }
    }
}

// Initialiser le gestionnaire de formulaires
const formsManager = new FormsManager();
