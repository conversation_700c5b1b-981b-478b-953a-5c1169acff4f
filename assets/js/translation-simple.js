/**
 * Système de Traduction Simplifié et Robuste
 * Remplace translation.js avec un système anti-bugs
 */

class SimpleTranslationManager {
    constructor() {
        this.currentLanguage = localStorage.getItem('golfinthai-language') || 'fr';
        this.translations = {};
        this.isInitialized = false;
        this.buttonsSetup = false;
        this.pendingTranslations = [];
        this.init();
    }

    async init() {
        try {
            await this.loadTranslations();
            
            // Traduire immédiatement après chargement
            this.translatePage();
            
            // Configurer les boutons
            this.setupLanguageButtons();
            this.updateLanguageButtons();
            
            // Marquer comme initialisé
            this.isInitialized = true;
            
            // Traiter les traductions en attente
            this.processPendingTranslations();
            
            // Re-traduire après un court délai pour les éléments chargés dynamiquement
            setTimeout(() => {
                this.translatePage();
                this.updateDropdownSelection(); // Marquer la sélection correcte
            }, 1000);
            
        } catch (error) {
            console.error('Translation init failed:', error);
            // Fallback silencieux
            this.isInitialized = true;
            this.setupFallbackButtons();
        }
    }

    async loadTranslations() {
        // Traductions par défaut intégrées avec structure hiérarchique
        this.translations = {
            fr: {
                nav: {
                    home: "Accueil",
                    destinations: "Destinations",
                    about: "À Propos",
                    testimonials: "Témoignages",
                    contact: "Contactez-Nous"
                },
                lang: {
                    current: "FR"
                },
                hero: {
                    title: "GOLFEZ<br>AUTREMENT"
                },
                intro: {
                    text1: "En tant qu'expert de la Thaïlande, où j'ai vécu de nombreuses années, je vous accompagne personnellement à chaque étape.",
                    text2: "Profitez de parcours de golf de renommée mondiale tout en explorant les lieux incontournables.",
                    text3: "Et pour la langue ? Pas d'inquiétude ! Je serai à vos côtés pour rendre chaque moment simple et agréable.",
                    highlight: "Faites confiance à GolfinThaï pour un voyage authentique."
                },
                services: {
                    title: "Mon"
                },
                contact: {
                    title: "Contactez-Nous",
                    phone: { label: "Téléphone / WhatsApp" },
                    email: { label: "Email" },
                    instagram: { label: "Instagram" },
                    form: {
                        name: "Votre nom",
                        email: "Votre email",
                        phone: "Votre téléphone",
                        message: "Votre message",
                        send: "Envoyer le Message"
                    }
                }
            },
            en: {
                nav: {
                    home: "Home",
                    destinations: "Destinations", 
                    about: "About",
                    testimonials: "Testimonials",
                    contact: "Contact Us"
                },
                lang: {
                    current: "EN"
                },
                hero: {
                    title: "GOLF<br>DIFFERENTLY"
                },
                intro: {
                    text1: "As a Thailand expert, where I have lived for many years, I personally accompany you at every step.",
                    text2: "Enjoy world-renowned golf courses while exploring must-see places.",
                    text3: "And for the language? No worries! I will be by your side to make every moment simple and enjoyable.",
                    highlight: "Trust GolfinThaï for an authentic trip."
                },
                services: {
                    title: "My"
                },
                contact: {
                    title: "Contact Us",
                    phone: { label: "Phone / WhatsApp" },
                    email: { label: "Email" },
                    instagram: { label: "Instagram" },
                    form: {
                        name: "Your name",
                        email: "Your email",
                        phone: "Your phone",
                        message: "Your message",
                        send: "Send Message"
                    }
                }
            }
        };

        // Essayer de charger depuis le fichier JSON si disponible
        try {
            const response = await fetch('./data/translations.json');
            if (response.ok) {
                const jsonTranslations = await response.json();
                // Merger avec les traductions par défaut
                this.translations = this.mergeTranslations(this.translations, jsonTranslations);
            }
        } catch (e) {
            console.warn('Using default translations');
        }
    }

    mergeTranslations(defaults, loaded) {
        // Fonction pour merger les traductions en profondeur
        const result = { ...defaults };
        for (const lang in loaded) {
            if (loaded[lang]) {
                result[lang] = { ...defaults[lang], ...loaded[lang] };
            }
        }
        return result;
    }

    setupLanguageButtons() {
        if (this.buttonsSetup) return;
        
        setTimeout(() => {
            const desktopBtn = document.getElementById('lang-toggle-desktop');
            const mobileBtn = document.getElementById('lang-toggle-mobile');
            const desktopDropdown = document.getElementById('lang-dropdown-desktop');
            const mobileDropdown = document.getElementById('lang-dropdown-mobile');
            
            // Setup desktop dropdown
            if (desktopBtn && !desktopBtn.dataset.configured) {
                desktopBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('🌍 Desktop lang click détecté');
                    this.toggleDropdown('desktop');
                });
                
                // Support tactile ADDITIONNEL
                desktopBtn.addEventListener('touchend', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('🌍 Desktop lang touch détecté');
                    this.toggleDropdown('desktop');
                });
                
                desktopBtn.dataset.configured = 'true';
            }
            
            // Setup mobile dropdown
            if (mobileBtn && !mobileBtn.dataset.configured) {
                mobileBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('📱 Mobile lang click détecté');
                    this.toggleDropdown('mobile');
                });
                
                // Support tactile ADDITIONNEL
                mobileBtn.addEventListener('touchend', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('📱 Mobile lang touch détecté');
                    this.toggleDropdown('mobile');
                });
                
                mobileBtn.dataset.configured = 'true';
            }
            
            // Setup dropdown options
            this.setupDropdownOptions();
            
            // Close dropdown when clicking outside
            document.addEventListener('click', (e) => {
                if (desktopDropdown && !desktopDropdown.contains(e.target) && !desktopBtn.contains(e.target)) {
                    this.closeDropdown('desktop');
                }
                if (mobileDropdown && !mobileDropdown.contains(e.target) && !mobileBtn.contains(e.target)) {
                    this.closeDropdown('mobile');
                }
            });
            
            this.buttonsSetup = true;
        }, 500);
    }

    setupDropdownOptions() {
        // Desktop dropdown options
        const desktopOptions = document.querySelectorAll('#lang-dropdown-desktop .lang-option');
        desktopOptions.forEach(option => {
            if (!option.dataset.configured) {
                option.addEventListener('click', (e) => {
                    e.preventDefault();
                    const lang = option.dataset.lang;
                    console.log('🌍 Desktop option clicked:', lang);
                    this.switchLanguage(lang);
                    this.closeDropdown('desktop');
                });
                
                // Support tactile ADDITIONNEL
                option.addEventListener('touchend', (e) => {
                    e.preventDefault();
                    const lang = option.dataset.lang;
                    console.log('🌍 Desktop option touched:', lang);
                    this.switchLanguage(lang);
                    this.closeDropdown('desktop');
                });
                
                option.dataset.configured = 'true';
            }
        });
        
        // Mobile dropdown options
        const mobileOptions = document.querySelectorAll('#lang-dropdown-mobile .lang-option-mobile');
        mobileOptions.forEach(option => {
            if (!option.dataset.configured) {
                option.addEventListener('click', (e) => {
                    e.preventDefault();
                    const lang = option.dataset.lang;
                    console.log('📱 Mobile option clicked:', lang);
                    this.switchLanguage(lang);
                    this.closeDropdown('mobile');
                });
                
                // Support tactile ADDITIONNEL
                option.addEventListener('touchend', (e) => {
                    e.preventDefault();
                    const lang = option.dataset.lang;
                    console.log('📱 Mobile option touched:', lang);
                    this.switchLanguage(lang);
                    this.closeDropdown('mobile');
                });
                
                option.dataset.configured = 'true';
            }
        });
    }

    toggleDropdown(type) {
        const dropdown = document.getElementById(`lang-dropdown-${type}`);
        const button = document.getElementById(`lang-toggle-${type}`);
        
        if (dropdown) {
            // ✅ REMPLACÉ : dropdown.style.display !== 'none' → Classe CSS native
            const isOpen = dropdown.classList.contains('dropdown-open');
            if (isOpen) {
                this.closeDropdown(type);
            } else {
                this.openDropdown(type);
            }
        }
    }

    openDropdown(type) {
        const dropdown = document.getElementById(`lang-dropdown-${type}`);
        const button = document.getElementById(`lang-toggle-${type}`);
        
        if (dropdown) {
            // ✅ REMPLACÉ : dropdown.style.display = 'block' → Classe CSS native
            dropdown.classList.add('dropdown-open');
            dropdown.setAttribute('aria-hidden', 'false');
            
            if (button) {
                button.setAttribute('aria-expanded', 'true');
                const arrow = button.querySelector('.lang-arrow');
                // ✅ REMPLACÉ : arrow.style.transform = 'rotate(180deg)' → Classe CSS native
                if (arrow) arrow.classList.add('arrow-rotated');
            }
        }
    }

    closeDropdown(type) {
        const dropdown = document.getElementById(`lang-dropdown-${type}`);
        const button = document.getElementById(`lang-toggle-${type}`);
        
        if (dropdown) {
            // ✅ REMPLACÉ : dropdown.style.display = 'none' → Classe CSS native
            dropdown.classList.remove('dropdown-open');
            dropdown.setAttribute('aria-hidden', 'true');
            
            if (button) {
                button.setAttribute('aria-expanded', 'false');
                const arrow = button.querySelector('.lang-arrow');
                // ✅ REMPLACÉ : arrow.style.transform = 'rotate(0deg)' → Classe CSS native
                if (arrow) arrow.classList.remove('arrow-rotated');
            }
        }
    }

    setupFallbackButtons() {
        // Système de fallback simple sans erreurs
        const buttons = document.querySelectorAll('[id*="lang-toggle"]');
        buttons.forEach(btn => {
            if (!btn.dataset.fallbackConfigured) {
                btn.addEventListener('click', () => {
                    const newLang = this.currentLanguage === 'fr' ? 'en' : 'fr';
                    localStorage.setItem('golfinthai-language', newLang);
                    location.reload();
                });
                btn.dataset.fallbackConfigured = 'true';
            }
        });
    }

    translatePage() {
        if (!this.translations || !this.translations[this.currentLanguage]) {
            return;
        }

        // Traduire les éléments avec data-translate
        const elements = document.querySelectorAll('[data-translate]');
        let translatedCount = 0;
        
        elements.forEach(element => {
            const key = element.getAttribute('data-translate');
            const translation = this.getTranslation(key);
            if (translation && translation !== key) {
                element.innerHTML = translation; // Permet le HTML (comme <br>)
                translatedCount++;
            }
        });

        // Gérer les placeholders
        const placeholders = document.querySelectorAll('[data-translate-placeholder]');
        placeholders.forEach(element => {
            const key = element.getAttribute('data-translate-placeholder');
            const translation = this.getTranslation(key);
            if (translation && translation !== key) {
                element.placeholder = translation;
            }
        });

        // Log seulement si debug activé
        if (window.location.search.includes('debug=true')) {
            console.log(`🌍 Translated ${translatedCount} elements to ${this.currentLanguage}`);
        }
    }

    getTranslation(key) {
        if (!key) return key;
        
        // Gérer les clés avec des points (ex: "nav.home")
        const keys = key.split('.');
        let result = this.translations[this.currentLanguage];
        
        for (const k of keys) {
            if (result && typeof result === 'object' && k in result) {
                result = result[k];
            } else {
                // Fallback vers français si pas trouvé
                result = this.translations['fr'];
                for (const fallbackKey of keys) {
                    if (result && typeof result === 'object' && fallbackKey in result) {
                        result = result[fallbackKey];
                    } else {
                        return key; // Retourner la clé si rien trouvé
                    }
                }
                break;
            }
        }
        
        return typeof result === 'string' ? result : key;
    }

    toggleLanguage() {
        const newLanguage = this.currentLanguage === 'fr' ? 'en' : 'fr';
        this.switchLanguage(newLanguage);
    }

    switchLanguage(language) {
        this.currentLanguage = language;
        localStorage.setItem('golfinthai-language', language);
        this.translatePage();
        this.updateLanguageButtons();
        document.documentElement.lang = language;
        
        // Fermer les dropdowns après changement
        this.closeDropdown('desktop');
        this.closeDropdown('mobile');
    }

    updateLanguageButtons() {
        const text = this.currentLanguage === 'fr' ? 'FR' : 'EN';
        
        // Mise à jour du texte seulement (PAS d'emojis)
        document.querySelectorAll('.lang-text, .lang-text-mobile').forEach(el => {
            el.textContent = text;
        });

        // Vider complètement les flag-icon et appliquer data-lang pour CSS
        document.querySelectorAll('.flag-icon').forEach(flag => {
            flag.textContent = '';
            flag.innerHTML = '';
            
            // Marquer la langue pour le CSS
            const parentOption = flag.closest('[data-lang]');
            const parentToggle = flag.closest('#lang-toggle-desktop, #lang-toggle-mobile');
            
            if (parentToggle) {
                // Bouton principal : langue actuelle
                flag.dataset.lang = this.currentLanguage;
            } else if (parentOption) {
                // Option dans dropdown : langue de l'option
                flag.dataset.lang = parentOption.dataset.lang;
            }
        });

        // Marquer la langue actuelle dans les dropdowns
        this.updateDropdownSelection();
    }

    updateDropdownSelection() {
        // Marquer l'option actuelle dans le dropdown desktop
        const desktopOptions = document.querySelectorAll('#lang-dropdown-desktop .lang-option');
        desktopOptions.forEach(option => {
            option.classList.remove('current');
            if (option.dataset.lang === this.currentLanguage) {
                option.classList.add('current');
            }
        });

        // Marquer l'option actuelle dans le dropdown mobile
        const mobileOptions = document.querySelectorAll('#lang-dropdown-mobile .lang-option-mobile');
        mobileOptions.forEach(option => {
            option.classList.remove('current');
            if (option.dataset.lang === this.currentLanguage) {
                option.classList.add('current');
            }
        });
    }

    retranslateContent(container) {
        if (!this.isInitialized) {
            this.pendingTranslations.push(container);
            return;
        }

        if (container?.dataset?.translating === 'true') return;
        if (container) container.dataset.translating = 'true';

        const elements = container.querySelectorAll('[data-translate]');
        elements.forEach(element => {
            const key = element.getAttribute('data-translate');
            const translation = this.getTranslation(key);
            if (translation) {
                element.innerHTML = translation;
            }
        });

        if (container) {
            setTimeout(() => {
                container.dataset.translating = 'false';
            }, 100);
        }
    }

    processPendingTranslations() {
        while (this.pendingTranslations.length > 0) {
            const container = this.pendingTranslations.shift();
            this.retranslateContent(container);
        }
    }
}

// Initialiser le gestionnaire simplifié
window.translationManager = new SimpleTranslationManager();

// Fonction globale pour forcer la traduction
window.forceTranslate = function() {
    if (window.translationManager) {
        window.translationManager.translatePage();
        console.log('🔄 Force translation applied');
    }
};

// Auto-traduction périodique réduite (seulement les premières 10 secondes)
let autoTranslateCount = 0;
const autoTranslateInterval = setInterval(() => {
    if (window.translationManager && window.translationManager.isInitialized && autoTranslateCount < 5) {
        window.translationManager.translatePage();
        autoTranslateCount++;
    } else if (autoTranslateCount >= 5) {
        clearInterval(autoTranslateInterval);
    }
}, 2000);

// Exposer globalement pour compatibilité
window.debugTranslation = function() {
    console.log('Translation Debug:', {
        isInitialized: window.translationManager.isInitialized,
        currentLanguage: window.translationManager.currentLanguage,
        buttonsSetup: window.translationManager.buttonsSetup,
        pendingCount: window.translationManager.pendingTranslations.length,
        translationsLoaded: Object.keys(window.translationManager.translations)
    });
};

// 🚀 EXPOSER LES FONCTIONS PRINCIPALES GLOBALEMENT
window.translatePage = function() {
    if (window.translationManager) {
        return window.translationManager.translatePage();
    }
};

window.switchLanguage = function(lang) {
    if (window.translationManager) {
        return window.translationManager.switchLanguage(lang);
    }
};

window.changeLanguage = function(lang) {
    if (window.translationManager) {
        return window.translationManager.switchLanguage(lang);
    }
};
