/**
 * 🚀 SMART TEXT MANAGER - GOLFINTHAI QUANTUM EDITION
 * Auto-detection + Progressive disclosure + Smooth animations
 * Production-ready code by Senior Full-Stack Wizard Team
 */

class SmartTextManager {
    constructor(options = {}) {
        this.config = {
            // 🎯 AUTO-DETECTION THRESHOLDS (optimized for mobile UX)
            maxCharsDesktop: 280,      // Optimal reading length desktop
            maxCharsMobile: 150,       // Shorter for mobile attention span
            maxLinesDesktop: 4,        // Visual lines limit desktop
            maxLinesMobile: 3,         // Mobile lines limit
            
            // 🎨 SELECTORS (customize as needed)
            textSelectors: [
                '.service-description',
                '.course-description', 
                '.intro-text',
                '.testimonial-text',
                '.feature-description',
                '[data-smart-text]'
            ],
            excludeSelectors: [
                '.hero-title',
                '.section-title', 
                '.nav-link',
                '[data-no-truncate]'
            ],
            
            // ⚡ PERFORMANCE OPTIONS
            throttleDelay: 250,        // Resize throttling
            animationDuration: 400,    // Smooth transitions
            
            // 🌍 INTERNATIONALIZATION
            labels: {
                fr: {
                    readMore: 'Lire la suite',
                    readLess: 'Réduire',
                    readMoreIcon: 'fas fa-chevron-down',
                    readLessIcon: 'fas fa-chevron-up'
                },
                en: {
                    readMore: 'Read more',
                    readLess: 'Show less', 
                    readMoreIcon: 'fas fa-chevron-down',
                    readLessIcon: 'fas fa-chevron-up'
                }
            },
            
            ...options
        };
        
        this.elements = new Map();
        this.currentLang = this.detectLanguage();
        this.isMobile = this.detectMobile();
        this.resizeObserver = null;
        
        // 🚀 SMART INITIALIZATION
        this.init();
    }    
    /**
     * 🎯 INITIALIZATION: Auto-detect and setup
     */
    init() {
        console.log('🚀 SmartTextManager: Quantum initialization...');
        
        // Wait for DOM and fonts to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
        
        // Wait for fonts to load for accurate measurements
        if (document.fonts && document.fonts.ready) {
            document.fonts.ready.then(() => {
                setTimeout(() => this.refreshAll(), 500);
            });
        }
    }
    
    /**
     * 🔧 SETUP: Process all eligible elements
     */
    setup() {
        // Find all text elements that need smart management
        const elements = this.findEligibleElements();
        
        console.log(`📝 Found ${elements.length} eligible text elements`);
        
        elements.forEach(element => {
            this.processElement(element);
        });
        
        // Setup responsive monitoring
        this.setupResponsiveMonitoring();
        
        // Setup mutation observer for dynamic content
        this.setupMutationObserver();
        
        console.log('✅ SmartTextManager: Ready to rock!');
    }    
    /**
     * 🕵️ FIND ELIGIBLE ELEMENTS: Smart detection
     */
    findEligibleElements() {
        const elements = [];
        
        // Combine all selectors
        const selector = this.config.textSelectors.join(', ');
        const candidateElements = document.querySelectorAll(selector);
        
        candidateElements.forEach(element => {
            // Skip if excluded
            if (this.config.excludeSelectors.some(excl => element.matches(excl))) {
                return;
            }
            
            // Skip if already processed
            if (element.hasAttribute('data-smart-managed')) {
                return;
            }
            
            // Check if element has significant text content
            const textContent = element.textContent.trim();
            if (textContent.length > 50) { // Minimum threshold
                elements.push(element);
            }
        });
        
        return elements;
    }
    
    /**
     * 🎯 PROCESS ELEMENT: Smart truncation decision
     */
    processElement(element) {
        const textContent = element.textContent.trim();
        const threshold = this.getThresholdForElement(element);
        
        // Mark as processed
        element.setAttribute('data-smart-managed', 'true');
        
        // Decide if truncation is needed
        if (this.shouldTruncate(element, textContent, threshold)) {
            this.setupTruncation(element, textContent);
        }
        
        // Store element data
        this.elements.set(element, {
            originalText: textContent,
            isTruncated: this.shouldTruncate(element, textContent, threshold),
            isExpanded: false
        });
    }    
    /**
     * 🔍 SHOULD TRUNCATE: Intelligent decision making
     */
    shouldTruncate(element, text, threshold) {
        // Character count check
        if (text.length <= threshold.chars) {
            return false;
        }
        
        // Visual lines check (more accurate)
        const lineHeight = parseFloat(getComputedStyle(element).lineHeight);
        const elementHeight = element.scrollHeight;
        const visualLines = Math.ceil(elementHeight / lineHeight);
        
        return visualLines > threshold.lines || text.length > threshold.chars;
    }
    
    /**
     * 🎨 SETUP TRUNCATION: Create smart UI
     */
    setupTruncation(element, originalText) {
        const threshold = this.getThresholdForElement(element);
        
        // Create smart container
        const container = document.createElement('div');
        container.className = 'smart-text-container truncated';
        
        // Create truncated content
        const truncatedText = this.createTruncatedText(originalText, threshold.chars);
        container.innerHTML = truncatedText;
        
        // Create read more button
        const button = this.createReadMoreButton();
        
        // Replace original element content
        element.innerHTML = '';
        element.appendChild(container);
        element.appendChild(button);
        
        // Store references
        const elementData = this.elements.get(element) || {};
        elementData.container = container;
        elementData.button = button;
        elementData.originalText = originalText;
        elementData.truncatedText = truncatedText;
        this.elements.set(element, elementData);
        
        // Setup click handler
        button.addEventListener('click', (e) => {
            e.preventDefault();
            this.toggleExpansion(element);
        });
    }    
    /**
     * ✂️ CREATE TRUNCATED TEXT: Smart cutting
     */
    createTruncatedText(text, maxChars) {
        if (text.length <= maxChars) return text;
        
        // Find last complete sentence or word within limit
        let cutPoint = maxChars;
        
        // Try to cut at sentence boundary
        const lastSentence = text.lastIndexOf('.', maxChars);
        const lastExclamation = text.lastIndexOf('!', maxChars);
        const lastQuestion = text.lastIndexOf('?', maxChars);
        const lastSentenceEnd = Math.max(lastSentence, lastExclamation, lastQuestion);
        
        if (lastSentenceEnd > maxChars * 0.7) {
            cutPoint = lastSentenceEnd + 1;
        } else {
            // Fall back to word boundary
            const lastSpace = text.lastIndexOf(' ', maxChars);
            if (lastSpace > maxChars * 0.8) {
                cutPoint = lastSpace;
            }
        }
        
        return text.substring(0, cutPoint).trim();
    }
    
    /**
     * 🎨 CREATE READ MORE BUTTON: Stylish and accessible
     */
    createReadMoreButton() {
        const button = document.createElement('button');
        const labels = this.config.labels[this.currentLang];
        
        button.className = 'smart-read-more';
        button.innerHTML = `
            <span>${labels.readMore}</span>
            <i class="${labels.readMoreIcon}" aria-hidden="true"></i>
        `;
        
        // Accessibility
        button.setAttribute('aria-expanded', 'false');
        button.setAttribute('type', 'button');
        
        return button;
    }    
    /**
     * 🔄 TOGGLE EXPANSION: Smooth animations
     */
    toggleExpansion(element) {
        const data = this.elements.get(element);
        if (!data) return;
        
        const { container, button, originalText, truncatedText } = data;
        const labels = this.config.labels[this.currentLang];
        const isExpanded = data.isExpanded;
        
        // Smooth height transition
        const currentHeight = container.scrollHeight;
        container.style.height = currentHeight + 'px';
        
        if (!isExpanded) {
            // EXPAND
            container.innerHTML = originalText;
            container.classList.remove('truncated');
            container.classList.add('expanded');
            
            button.innerHTML = `
                <span>${labels.readLess}</span>
                <i class="${labels.readLessIcon}" aria-hidden="true"></i>
            `;
            button.classList.add('expanded');
            button.setAttribute('aria-expanded', 'true');
            
            // Animate to new height
            const newHeight = container.scrollHeight;
            container.style.height = newHeight + 'px';
            
        } else {
            // COLLAPSE
            container.innerHTML = truncatedText;
            container.classList.remove('expanded');
            container.classList.add('truncated');
            
            button.innerHTML = `
                <span>${labels.readMore}</span>
                <i class="${labels.readMoreIcon}" aria-hidden="true"></i>
            `;
            button.classList.remove('expanded');
            button.setAttribute('aria-expanded', 'false');
            
            // Animate to truncated height
            const newHeight = container.scrollHeight;
            container.style.height = newHeight + 'px';
        }
        
        // Clean up height after animation
        setTimeout(() => {
            container.style.height = '';
        }, this.config.animationDuration);
        
        // Update state
        data.isExpanded = !isExpanded;
        this.elements.set(element, data);
    }    
    /**
     * 📱 GET THRESHOLD: Responsive thresholds
     */
    getThresholdForElement(element) {
        // Check if element has custom threshold
        const customChars = element.getAttribute('data-max-chars');
        const customLines = element.getAttribute('data-max-lines');
        
        if (customChars || customLines) {
            return {
                chars: parseInt(customChars) || (this.isMobile ? this.config.maxCharsMobile : this.config.maxCharsDesktop),
                lines: parseInt(customLines) || (this.isMobile ? this.config.maxLinesMobile : this.config.maxLinesDesktop)
            };
        }
        
        // Default responsive thresholds
        return {
            chars: this.isMobile ? this.config.maxCharsMobile : this.config.maxCharsDesktop,
            lines: this.isMobile ? this.config.maxLinesMobile : this.config.maxLinesDesktop
        };
    }
    
    /**
     * 📱 DETECT MOBILE: Smart device detection
     */
    detectMobile() {
        return window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }
    
    /**
     * 🌍 DETECT LANGUAGE: Auto language detection
     */
    detectLanguage() {
        // Try multiple detection methods
        const htmlLang = document.documentElement.lang;
        const navigatorLang = navigator.language || navigator.userLanguage;
        const detected = (htmlLang || navigatorLang || 'fr').toLowerCase();
        
        // Map to supported languages
        if (detected.startsWith('en')) return 'en';
        return 'fr'; // Default to French for GolfinThai
    }    
    /**
     * 👀 SETUP RESPONSIVE MONITORING: React to viewport changes
     */
    setupResponsiveMonitoring() {
        let resizeTimeout;
        
        const handleResize = () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                const wasMobile = this.isMobile;
                this.isMobile = this.detectMobile();
                
                // If device type changed, refresh all elements
                if (wasMobile !== this.isMobile) {
                    console.log('📱 Device type changed, refreshing smart text...');
                    this.refreshAll();
                }
            }, this.config.throttleDelay);
        };
        
        window.addEventListener('resize', handleResize, { passive: true });
        
        // Modern ResizeObserver for more accurate detection
        if (window.ResizeObserver) {
            this.resizeObserver = new ResizeObserver(entries => {
                // Throttle to avoid excessive calls
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(() => {
                    entries.forEach(entry => {
                        const element = entry.target;
                        if (this.elements.has(element)) {
                            this.reevaluateElement(element);
                        }
                    });
                }, this.config.throttleDelay);
            });
            
            // Observe all managed elements
            this.elements.forEach((data, element) => {
                this.resizeObserver.observe(element);
            });
        }
    }    
    /**
     * 👁️ SETUP MUTATION OBSERVER: Handle dynamic content
     */
    setupMutationObserver() {
        const observer = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
                if (mutation.type === 'childList') {
                    // New nodes added, check for eligible elements
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === 1) { // Element node
                            const newElements = this.findEligibleElements();
                            newElements.forEach(element => {
                                if (!this.elements.has(element)) {
                                    this.processElement(element);
                                    if (this.resizeObserver) {
                                        this.resizeObserver.observe(element);
                                    }
                                }
                            });
                        }
                    });
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    /**
     * 🔄 REFRESH ALL: Re-evaluate all elements
     */
    refreshAll() {
        console.log('🔄 SmartTextManager: Refreshing all elements...');
        
        this.elements.forEach((data, element) => {
            this.reevaluateElement(element);
        });
    }
    
    /**
     * 🔍 REEVALUATE ELEMENT: Check if truncation still needed
     */
    reevaluateElement(element) {
        const data = this.elements.get(element);
        if (!data) return;
        
        const threshold = this.getThresholdForElement(element);
        const shouldTruncateNow = this.shouldTruncate(element, data.originalText, threshold);
        
        if (data.isTruncated !== shouldTruncateNow) {
            // Truncation needs have changed
            if (shouldTruncateNow && !data.isTruncated) {
                // Need to add truncation
                this.setupTruncation(element, data.originalText);
            } else if (!shouldTruncateNow && data.isTruncated) {
                // Remove truncation
                element.innerHTML = data.originalText;
                this.elements.delete(element);
            }
        }
    }    
    /**
     * 🧹 CLEANUP: Destroy instance
     */
    destroy() {
        // Clean up observers
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
        }
        
        // Clean up elements
        this.elements.forEach((data, element) => {
            element.innerHTML = data.originalText;
            element.removeAttribute('data-smart-managed');
        });
        
        this.elements.clear();
        console.log('🧹 SmartTextManager: Cleaned up');
    }
    
    /**
     * 🎛️ PUBLIC API: Manual control
     */
    
    // Add new element manually
    addElement(element, options = {}) {
        if (!this.elements.has(element)) {
            this.processElement(element);
            if (this.resizeObserver) {
                this.resizeObserver.observe(element);
            }
        }
    }
    
    // Remove element manually
    removeElement(element) {
        if (this.elements.has(element)) {
            const data = this.elements.get(element);
            element.innerHTML = data.originalText;
            element.removeAttribute('data-smart-managed');
            this.elements.delete(element);
            
            if (this.resizeObserver) {
                this.resizeObserver.unobserve(element);
            }
        }
    }
    
    // Get stats
    getStats() {
        const total = this.elements.size;
        const truncated = Array.from(this.elements.values()).filter(data => data.isTruncated).length;
        const expanded = Array.from(this.elements.values()).filter(data => data.isExpanded).length;
        
        return { total, truncated, expanded };
    }
}
// 🚀 AUTO-INITIALIZATION for GolfinThai
document.addEventListener('DOMContentLoaded', () => {
    // Wait a bit for other scripts and fonts to load
    setTimeout(() => {
        window.smartTextManager = new SmartTextManager({
            // Custom config for GolfinThai
            maxCharsDesktop: 300,
            maxCharsMobile: 160,
            textSelectors: [
                '.service-description',
                '.course-description', 
                '.intro-text',
                '.testimonial-text',
                '.feature-description',
                '.detail-paragraph',
                '.founder-description',
                '[data-smart-text]'
            ]
        });
        
        console.log('✅ GolfinThai SmartTextManager: Ready to ROCK! 🔥');
    }, 800);
});

// 🎯 GLOBAL ACCESS for debugging
window.debugSmartText = () => {
    if (window.smartTextManager) {
        console.log('📊 SmartTextManager Stats:', window.smartTextManager.getStats());
        window.smartTextManager.elements.forEach((data, element) => {
            console.log('Element:', element, 'Data:', data);
        });
    }
};