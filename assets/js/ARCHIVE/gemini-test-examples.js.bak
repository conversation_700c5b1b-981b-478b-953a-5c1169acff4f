/**
 * 🧪 EXEMPLES D'UTILISATION GEMINI API
 * Démo pour tester le système de fallback des 3 clés
 */

// 🧪 TESTS AUTOMATIQUES
async function runGeminiTests() {
    console.log('🚀 Démarrage des tests Gemini...');
    
    try {
        // 1. Tester toutes les clés
        console.log('\n📋 Test de toutes les clés API...');
        const keyTests = await testGeminiKeys();
        
        keyTests.forEach((result, index) => {
            if (result.status === 'SUCCESS') {
                console.log(`✅ Clé ${index + 1}: OK`);
            } else {
                console.log(`❌ Clé ${index + 1}: ${result.error}`);
            }
        });
        
        // 2. Test de génération simple
        console.log('\n💬 Test génération simple...');
        const simpleResponse = await askGemini("Dis juste 'Bonjour GolfinThaï!' en réponse.");
        console.log('Réponse:', simpleResponse);
        
        // 3. Test génération pour le golf
        console.log('\n🏌️ Test génération golf...');
        const golfResponse = await askG<PERSON><PERSON>("Écris un slogan publicitaire pour GolfinThaï en 15 mots maximum.");
        console.log('Slogan golf:', golfResponse);
        
        // 4. Statistiques
        console.log('\n📊 Statistiques:');
        const stats = geminiAPI.getStats();
        console.log(stats);
        
        return true;
        
    } catch (error) {
        console.error('❌ Erreur dans les tests:', error);
        return false;
    }
}

// 🎯 EXEMPLES SPÉCIFIQUES GOLFINTHAÏ
const golfPrompts = {
    slogan: "Crée un slogan accrocheur pour GolfinThaï, spécialiste des voyages golf en Thaïlande (max 20 mots)",
    
    description: "Écris une description courte (2-3 phrases) pour un parcours de golf de luxe en Thaïlande avec vue sur l'océan",
    
    avantages: "Liste 5 avantages principaux de choisir GolfinThaï pour organiser un voyage golf en Thaïlande",
    
    temoignage: "Crée un témoignage client authentique pour GolfinThaï (2-3 phrases, ton enthousiaste)",
    
    newsletter: "Écris un sujet d'email newsletter accrocheur pour GolfinThaï (max 50 caractères)"
};

// 🎨 GÉNÉRATEUR DE CONTENU MARKETING
async function generateMarketingContent() {
    console.log('🎨 Génération de contenu marketing GolfinThaï...');
    
    const results = {};
    
    for (const [type, prompt] of Object.entries(golfPrompts)) {
        try {
            console.log(`\n📝 Génération: ${type}...`);
            const content = await askGemini(prompt);
            results[type] = content;
            console.log(`✅ ${type}:`, content);
            
            // Délai entre requêtes
            await new Promise(resolve => setTimeout(resolve, 500));
            
        } catch (error) {
            console.error(`❌ Erreur ${type}:`, error);
            results[type] = `Erreur: ${error.message}`;
        }
    }
    
    return results;
}

// 🚀 STRESS TEST DU SYSTÈME DE FALLBACK
async function stressTestFallback() {
    console.log('🔥 Stress test du système de fallback...');
    
    const promises = [];
    const testPrompt = "Réponds juste par un emoji de golf.";
    
    // Lancer 10 requêtes simultanées pour tester le fallback
    for (let i = 0; i < 10; i++) {
        promises.push(
            askGemini(testPrompt)
                .then(response => ({
                    index: i,
                    status: 'SUCCESS',
                    response: response
                }))
                .catch(error => ({
                    index: i,
                    status: 'ERROR',
                    error: error.message
                }))
        );
    }
    
    const results = await Promise.all(promises);
    
    results.forEach(result => {
        if (result.status === 'SUCCESS') {
            console.log(`✅ Requête ${result.index}: ${result.response}`);
        } else {
            console.log(`❌ Requête ${result.index}: ${result.error}`);
        }
    });
    
    return results;
}

// 🎮 INTERFACE DE TEST INTERACTIVE
function createTestInterface() {
    // Ajouter boutons de test à la page
    const testDiv = document.createElement('div');
    testDiv.id = 'gemini-test-interface';
    testDiv.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 15px;
        border-radius: 10px;
        z-index: 99999;
        font-family: monospace;
        font-size: 12px;
        max-width: 300px;
    `;
    
    testDiv.innerHTML = `
        <h4>🤖 Gemini Tests</h4>
        <button onclick="runGeminiTests()" style="margin: 2px; padding: 5px;">Test Complet</button><br>
        <button onclick="generateMarketingContent()" style="margin: 2px; padding: 5px;">Contenu Marketing</button><br>
        <button onclick="stressTestFallback()" style="margin: 2px; padding: 5px;">Stress Test</button><br>
        <button onclick="geminiAPI.getStats()" style="margin: 2px; padding: 5px;">Statistiques</button><br>
        <button onclick="document.getElementById('gemini-test-interface').remove()" style="margin: 2px; padding: 5px; background: red;">Fermer</button>
    `;
    
    document.body.appendChild(testDiv);
}

// 🚀 FONCTIONS GLOBALES
window.runGeminiTests = runGeminiTests;
window.generateMarketingContent = generateMarketingContent;
window.stressTestFallback = stressTestFallback;
window.createTestInterface = createTestInterface;

// 🎯 AUTO-INIT
document.addEventListener('DOMContentLoaded', () => {
    // Attendre que Gemini soit prêt
    setTimeout(() => {
        if (window.geminiAPI) {
            console.log('🎮 Interface de test Gemini disponible');
            console.log('📖 Commandes disponibles:');
            console.log('  - runGeminiTests() : Test complet');
            console.log('  - generateMarketingContent() : Contenu marketing');
            console.log('  - stressTestFallback() : Test de charge');
            console.log('  - createTestInterface() : Interface visuelle');
            console.log('  - askGemini("votre question") : Question simple');
        }
    }, 2000);
});

/**
 * 📖 GUIDE D'UTILISATION :
 * 
 * 1. Ouvrir la console du navigateur (F12)
 * 
 * 2. Tester les clés :
 *    runGeminiTests()
 * 
 * 3. Générer du contenu marketing :
 *    generateMarketingContent()
 * 
 * 4. Test de charge :
 *    stressTestFallback()
 * 
 * 5. Interface visuelle :
 *    createTestInterface()
 * 
 * 6. Question simple :
 *    askGemini("Écris un titre accrocheur pour GolfinThaï")
 */