/* =============================================
   📱 MOBILE LOGO DETECTIVE & FORCE FIXER
   Detect mobile, find logo issues, force fix
   ============================================= */

(function() {
    'use strict';
    
    // Detect mobile
    const isMobile = window.innerWidth <= 1023 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    if (!isMobile) {
        console.log('🖥️ Desktop detected - Mobile logo fix not needed');
        return;
    }
    
    console.log('📱 MOBILE DETECTED - Starting logo nuclear fix...');
    
    // Wait for DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', startMobileLogoFix);
    } else {
        startMobileLogoFix();
    }
    
    function startMobileLogoFix() {
        console.log('🔥 Mobile Logo Nuclear Fix - Starting...');
        
        // Force fix immediately
        forceMobileLogoFix();
        
        // Monitor and re-fix if needed
        let checkCount = 0;
        const maxChecks = 15;
        
        const mobileLogoMonitor = setInterval(() => {
            checkCount++;
            
            const logo = document.querySelector('.logo-container-bg');
            if (logo) {
                // Check if logo has green background (means image not loaded)
                const styles = window.getComputedStyle(logo);
                const bgColor = styles.backgroundColor;
                const bgImage = styles.backgroundImage;
                
                const hasGreenBackground = bgColor.includes('rgb(5, 150, 105)') || bgColor.includes('#059669');
                const hasNoImage = bgImage === 'none' || !bgImage.includes('logo-golfinthai.jpg');
                
                console.log(`🔍 Mobile logo check #${checkCount}:`, {
                    bgColor,
                    bgImage,
                    hasGreenBackground,
                    hasNoImage
                });
                
                if (hasGreenBackground || hasNoImage) {
                    console.log('🚨 Mobile logo issue detected - Forcing fix!');
                    forceMobileLogoFix();
                } else {
                    console.log('✅ Mobile logo looks good');
                }
            } else {
                console.log('❌ Mobile logo element not found - Creating it!');
                createMobileLogo();
            }
            
            if (checkCount >= maxChecks) {
                clearInterval(mobileLogoMonitor);
                console.log('🛑 Mobile logo monitoring ended');
            }
        }, 2000);
    }
    
    function forceMobileLogoFix() {
        const logo = document.querySelector('.logo-container-bg');
        
        if (!logo) {
            createMobileLogo();
            return;
        }
        
        console.log('🔧 Forcing mobile logo fix...');
        
        // Nuclear CSS override
        logo.style.cssText = `
            width: 44px !important;
            height: 44px !important;
            min-width: 44px !important;
            min-height: 44px !important;
            max-width: 44px !important;
            max-height: 44px !important;
            border-radius: 50% !important;
            background-image: url('./assets/images/logo-golfinthai.jpg') !important;
            background-size: contain !important;
            background-position: center !important;
            background-repeat: no-repeat !important;
            background-color: rgba(255, 255, 255, 0.1) !important;
            background-blend-mode: normal !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: relative !important;
            z-index: 1001 !important;
            flex-shrink: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            border: 2px solid rgba(6, 95, 70, 0.3) !important;
            box-shadow: 0 2px 8px rgba(45, 212, 191, 0.15) !important;
            content: none !important;
        `;
        
        // Clear any text content
        logo.textContent = '';
        logo.innerHTML = '';
        
        // Test image loading
        const testImg = new Image();
        testImg.onload = () => {
            console.log('✅ Mobile logo image loaded successfully');
        };
        testImg.onerror = () => {
            console.log('❌ Mobile logo image failed - Using SVG fallback');
            useSVGFallback(logo);
        };
        testImg.src = './assets/images/logo-golfinthai.jpg';
        
        console.log('✅ Mobile logo nuclear fix applied');
    }
    
    function createMobileLogo() {
        const logoParent = document.querySelector('.flex.items-center.space-x-3');
        
        if (!logoParent) {
            console.log('❌ Logo parent container not found');
            return;
        }
        
        console.log('🔧 Creating mobile logo...');
        
        const mobileLogo = document.createElement('div');
        mobileLogo.className = 'logo-container-bg';
        mobileLogo.setAttribute('role', 'img');
        mobileLogo.setAttribute('aria-label', 'Logo GolfinThaï - Voyages Golf Thaïlande');
        mobileLogo.setAttribute('title', 'GolfinThaï');
        mobileLogo.setAttribute('itemprop', 'logo');
        
        logoParent.insertBefore(mobileLogo, logoParent.firstChild);
        
        // Apply styles
        forceMobileLogoFix();
        
        console.log('✅ Mobile logo created');
    }
    
    function useSVGFallback(logoElement) {
        logoElement.style.backgroundImage = 'none';
        logoElement.innerHTML = `
            <svg width="44" height="44" viewBox="0 0 64 64" style="border-radius: 50%; display: block;">
                <circle cx="32" cy="32" r="32" fill="#00574B"/>
                <circle cx="32" cy="24" r="6" fill="#FCFCFC"/>
                <path d="M28 30L36 38L34 40L26 32Z" fill="#A3D1C8"/>
                <circle cx="36" cy="38" r="2" fill="#A3D1C8"/>
                <path d="M20 42L32 36L44 42L40 48L24 48Z" fill="#FCFCFC"/>
                <path d="M22 42L32 38L42 42L38 46L26 46Z" fill="#A3D1C8"/>
                <circle cx="32" cy="24" r="1" fill="#00574B"/>
                <circle cx="30" cy="24" r="0.5" fill="#00574B"/>
                <circle cx="34" cy="24" r="0.5" fill="#00574B"/>
            </svg>
        `;
        console.log('✅ SVG fallback applied for mobile');
    }
    
})();

console.log('📱 Mobile Logo Detective loaded - Will fix mobile logo issues!');