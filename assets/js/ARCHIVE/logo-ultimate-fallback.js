/* =============================================
   🚨 LOGO ULTIMATE FALLBACK - DERNIÈRE CHANCE
   Si tout échoue, force le logo visible
   ============================================= */

document.addEventListener('DOMContentLoaded', function() {
    console.log('🚨 LOGO ULTIMATE FALLBACK ACTIVÉ');
    
    // Attendre 1 seconde puis vérifier
    setTimeout(checkAndForceLogoFix, 1000);
    
    function checkAndForceLogoFix() {
        // Chercher le logo actuel
        const svgLogo = document.querySelector('svg[title="GolfinThaï"]');
        const logoContainer = document.querySelector('.logo-container-bg');
        
        console.log('🔍 Vérification logo:');
        console.log('- SVG trouvé:', !!svgLogo);
        console.log('- Container trouvé:', !!logoContainer);
        
        // Si aucun logo visible, créer un de force
        if (!svgLogo && !logoContainer) {
            console.log('🚨 AUCUN LOGO TROUVÉ - CRÉATION FORCÉE');
            createEmergencyLogo();
        }
        
        // Vérifier que le SVG est bien stylé
        if (svgLogo) {
            const styles = window.getComputedStyle(svgLogo);
            const width = parseInt(styles.width);
            const height = parseInt(styles.height);
            
            console.log(`📐 Taille logo actuelle: ${width}x${height}`);
            
            if (width < 40 || height < 40) {
                console.log('⚠️ Logo trop petit - correction forcée');
                forceLogoSize(svgLogo);
            }
        }
    }
    
    function createEmergencyLogo() {
        // Trouver le container parent du logo
        const logoParent = document.querySelector('.flex.items-center.space-x-3');
        
        if (logoParent) {
            // Créer un logo d'urgence très visible
            const emergencyLogo = document.createElement('div');
            emergencyLogo.style.cssText = `
                width: 44px !important;
                height: 44px !important;
                border-radius: 50% !important;
                background: linear-gradient(135deg, #059669 0%, #047857 50%, #065f46 100%) !important;
                border: 3px solid #10b981 !important;
                box-shadow: 0 0 20px rgba(16, 185, 129, 0.5) !important;
                position: relative !important;
                flex-shrink: 0 !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                font-family: Arial, sans-serif !important;
                font-weight: bold !important;
                font-size: 14px !important;
                color: white !important;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
            `;
            emergencyLogo.textContent = 'GT';
            emergencyLogo.title = 'GolfinThaï - Logo de secours';
            
            // L'insérer au début du container
            logoParent.insertBefore(emergencyLogo, logoParent.firstChild);
            
            console.log('✅ Logo d\'urgence créé avec succès!');
        }
    }
    
    function forceLogoSize(logoElement) {
        logoElement.style.cssText += `
            width: 44px !important;
            height: 44px !important;
            min-width: 44px !important;
            min-height: 44px !important;
            max-width: 44px !important;
            max-height: 44px !important;
        `;
        console.log('✅ Taille logo forcée à 44px');
    }
    
    // Test toutes les 5 secondes pendant 30 secondes
    let testCount = 0;
    const maxTests = 6;
    
    const intervalTest = setInterval(() => {
        testCount++;
        console.log(`🔄 Test logo #${testCount}/${maxTests}`);
        
        const hasVisibleLogo = 
            document.querySelector('svg[title="GolfinThaï"]') ||
            document.querySelector('.logo-container-bg') ||
            document.querySelector('div[title*="GolfinThaï"]');
            
        if (hasVisibleLogo) {
            console.log('✅ Logo détecté - surveillance arrêtée');
            clearInterval(intervalTest);
        } else if (testCount >= maxTests) {
            console.log('🚨 AUCUN LOGO APRÈS 30s - CRÉATION FORCÉE');
            createEmergencyLogo();
            clearInterval(intervalTest);
        }
    }, 5000);
    
    console.log('🛡️ Surveillance logo activée - 30 secondes');
});

console.log('🚨 ULTIMATE FALLBACK chargé - Logo sera forcé si nécessaire!');