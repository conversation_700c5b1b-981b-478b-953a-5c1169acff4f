/**
 * 🎯 GOLF COURSE IMAGE MODAL CONNECTOR
 * Connecte les boutons d'expansion des images de golf à notre système de modal corrigé
 */

(function() {
    'use strict';
    
    function initGolfImageModals() {
        // Trouver tous les boutons d'expansion des parcours de golf
        const expandButtons = document.querySelectorAll('.course-expand-btn');
        
        expandButtons.forEach(button => {
            // Éviter les doubles bindings
            if (button.dataset.modalBound) {
                return;
            }
            button.dataset.modalBound = 'true';
            
            // Trouver l'image correspondante
            const courseCard = button.closest('.course-card');
            const courseImage = courseCard ? courseCard.querySelector('.course-image') : null;
            
            if (!courseImage) {
                return;
            }
            
            // Récupérer les informations de l'image
            const imageSrc = courseImage.src;
            const imageAlt = courseImage.alt;
            
            // Ajouter l'événement de clic
            function handleClick(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Ouvrir la modal avec l'image
                if (window.openSimpleModal && imageSrc) {
                    window.openSimpleModal(imageSrc, imageAlt);
                }
            }
            
            // Gérer les événements selon le type d'appareil
            const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
            
            if (isTouchDevice) {
                // Pour les appareils tactiles
                let touchStartTime = 0;
                
                button.addEventListener('touchstart', function(e) {
                    touchStartTime = Date.now();
                    button.style.transform = 'scale(0.9)';
                }, { passive: true });
                
                button.addEventListener('touchend', function(e) {
                    const touchDuration = Date.now() - touchStartTime;
                    button.style.transform = '';
                    
                    if (touchDuration < 500) {
                        handleClick(e);
                    }
                }, { passive: false });
                
                button.addEventListener('touchcancel', function() {
                    button.style.transform = '';
                }, { passive: true });
            }
            
            // Toujours ajouter le click classique (fonctionne sur tous les appareils)
            button.addEventListener('click', handleClick);
        });
    }
    
    // Aussi permettre de cliquer directement sur les images
    function initDirectImageClicks() {
        const courseImages = document.querySelectorAll('.course-image');
        
        courseImages.forEach(image => {
            if (image.dataset.clickBound) {
                return;
            }
            image.dataset.clickBound = 'true';
            
            function handleImageClick(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const imageSrc = image.src;
                const imageAlt = image.alt;
                
                if (window.openSimpleModal && imageSrc) {
                    window.openSimpleModal(imageSrc, imageAlt);
                }
            }
            
            // Style pour indiquer que l'image est cliquable
            image.style.cursor = 'pointer';
            
            // Ajouter les événements
            const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
            
            if (isTouchDevice) {
                let touchStartTime = 0;
                
                image.addEventListener('touchstart', function(e) {
                    touchStartTime = Date.now();
                }, { passive: true });
                
                image.addEventListener('touchend', function(e) {
                    const touchDuration = Date.now() - touchStartTime;
                    
                    if (touchDuration < 500) {
                        handleImageClick(e);
                    }
                }, { passive: false });
            }
            
            image.addEventListener('click', handleImageClick);
        });
    }
    
    // Initialiser les connexions
    function init() {
        initGolfImageModals();
        initDirectImageClicks();
    }
    
    // Démarrer quand le DOM est prêt
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    // Réessayer après un délai pour s'assurer que tout est bien connecté
    setTimeout(init, 1000);
    
})();
