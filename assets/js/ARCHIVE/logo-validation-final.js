/**
 * 🎯 LOGO VALIDATION FINAL - SENIOR DEV SOLUTION
 * Validates and ensures logo is perfect on all devices
 */

(function() {
    'use strict';
    
    console.log('🎯 Logo Validation Final - Senior Dev Solution');
    
    const LogoValidator = {
        
        // Validate logo is correctly configured
        validate: () => {
            const logoContainer = document.querySelector('.logo-container-bg');
            
            if (!logoContainer) {
                console.log('❌ Logo container not found');
                return false;
            }
            
            const styles = getComputedStyle(logoContainer);
            const backgroundSize = styles.backgroundSize;
            const backgroundPosition = styles.backgroundPosition;
            const borderRadius = styles.borderRadius;
            const width = styles.width;
            const height = styles.height;
            
            console.log('📊 Logo Analysis:');
            console.log('  Background size:', backgroundSize);
            console.log('  Background position:', backgroundPosition);
            console.log('  Border radius:', borderRadius);
            console.log('  Dimensions:', `${width} x ${height}`);
            
            // FIXED: Accept both cover (desktop) and contain (mobile)
            const isValidBackgroundSize = (backgroundSize === 'contain' || backgroundSize === 'cover');
            const isValid = (
                isValidBackgroundSize &&
                backgroundPosition.includes('center') &&
                borderRadius === '50%' &&
                width === '64px' &&
                height === '64px'
            );
            
            if (isValid) {
                console.log('✅ Logo configuration is PERFECT');
                logoContainer.style.boxShadow = '0 0 0 2px rgba(0, 255, 0, 0.5)';
                setTimeout(() => {
                    logoContainer.style.boxShadow = '';
                }, 2000);
            } else {
                console.log('❌ Logo needs adjustment');
                // REMOVED: Don't auto-fix anymore, let CSS handle it
            }
            
            return isValid;
        },
        
        // Apply definitive fix - RESPONSIVE VERSION FIXED
        fix: () => {
            console.log('🔧 Applying responsive logo fix...');
            
            const logoContainer = document.querySelector('.logo-container-bg');
            if (!logoContainer) return;
            
            // Better mobile detection - multiple checks
            const isMobile = (
                window.innerWidth <= 1024 ||
                window.innerHeight <= 1024 ||
                /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                ('ontouchstart' in window) ||
                (navigator.maxTouchPoints > 0)
            );
            
            if (isMobile) {
                // Mobile: contain (show full logo)
                logoContainer.style.cssText += `
                    background-size: contain !important;
                    background-position: center !important;
                    background-repeat: no-repeat !important;
                    background-color: rgba(255, 255, 255, 0.2) !important;
                    width: 64px !important;
                    height: 64px !important;
                    border-radius: 50% !important;
                    overflow: hidden !important;
                    flex-shrink: 0 !important;
                `;
                console.log('📱 Mobile: Applied CONTAIN for full logo visibility');
            } else {
                // Desktop: cover (fill circle)
                logoContainer.style.cssText += `
                    background-size: cover !important;
                    background-position: center !important;
                    background-repeat: no-repeat !important;
                    width: 64px !important;
                    height: 64px !important;
                    border-radius: 50% !important;
                    overflow: hidden !important;
                    flex-shrink: 0 !important;
                `;
                console.log('🖥️ Desktop: Applied COVER for filled circle');
            }
            
            console.log('✅ Responsive fix applied');
            
            // Validate after fix
            setTimeout(() => {
                LogoValidator.validate();
            }, 500);
        },
        
        // Monitor for changes and auto-fix - DISABLED
        monitor: () => {
            console.log('👀 Logo monitoring DISABLED - CSS handles everything');
            
            // REMOVED: No more auto-fixing, CSS media queries handle it properly
            return;
        }
    };
    
    // Initialize when DOM is ready - DISABLED AUTO-FIX
    const init = () => {
        setTimeout(() => {
            LogoValidator.validate(); // Only validate, don't auto-fix
            // LogoValidator.fix(); // DISABLED - CSS handles everything
            LogoValidator.monitor();
        }, 1000);
        
        // DISABLED additional fix - CSS is enough
        // setTimeout(() => {
        //     LogoValidator.fix();
        // }, 3000);
    };
    
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    // Global access for debugging
    window.LogoValidator = LogoValidator;
    
    // EMERGENCY MOBILE FIX - IMPROVED
    window.fixMobileLogoNow = function() {
        console.log('🚨 EMERGENCY MOBILE LOGO FIX');
        const logoContainer = document.querySelector('.logo-container-bg');
        if (logoContainer) {
            // FORCE mobile fix regardless of detection
            logoContainer.style.backgroundSize = 'contain';
            logoContainer.style.backgroundColor = 'rgba(255, 255, 255, 0.25)';
            logoContainer.style.backgroundPosition = 'center';
            logoContainer.style.backgroundRepeat = 'no-repeat';
            console.log('🚨 Mobile logo FORCED to CONTAIN - logo should be fully visible now');
        }
    };
    
    console.log('🎯 Logo Validation Final: Ready');
    
})();
