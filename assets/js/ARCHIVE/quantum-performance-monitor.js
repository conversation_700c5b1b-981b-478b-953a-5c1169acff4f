/**
 * ⚡ QUANTUM PERFORMANCE MONITOR - APPLE/NETFLIX LEVEL
 * Real-time performance tracking and optimization
 */

class QuantumPerformanceMonitor {
    constructor() {
        this.metrics = {
            frameRate: 0,
            memoryUsage: 0,
            loadTime: 0,
            interactionDelay: 0
        };
        
        this.isMonitoringEnabled = false; // Set to true for debugging
        this.frameCount = 0;
        this.lastTime = performance.now();
        
        this.init();
    }
    
    init() {
        console.log('⚡ Quantum Performance Monitor - Initializing...');
        this.setupPerformanceObserver();
        this.startFrameRateMonitoring();
        this.optimizeForDevice();
        this.setupIntelligentOptimizations();
        console.log('✅ Performance monitoring active');
    }
    
    /**
     * 📊 PERFORMANCE OBSERVER: Monitor Core Web Vitals
     */
    setupPerformanceObserver() {
        if ('PerformanceObserver' in window) {
            // Monitor Largest Contentful Paint (LCP)
            const lcpObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const lastEntry = entries[entries.length - 1];
                this.metrics.lcp = lastEntry.startTime;
                console.log('📊 LCP:', lastEntry.startTime.toFixed(2), 'ms');
            });
            lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
            
            // Monitor First Input Delay (FID)
            const fidObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach((entry) => {
                    this.metrics.fid = entry.processingStart - entry.startTime;
                    console.log('📊 FID:', this.metrics.fid.toFixed(2), 'ms');
                });
            });
            fidObserver.observe({ entryTypes: ['first-input'] });
            
            // Monitor Cumulative Layout Shift (CLS)
            const clsObserver = new PerformanceObserver((list) => {
                let clsValue = 0;
                list.getEntries().forEach((entry) => {
                    if (!entry.hadRecentInput) {
                        clsValue += entry.value;
                    }
                });
                this.metrics.cls = clsValue;
                console.log('📊 CLS:', clsValue.toFixed(4));
            });
            clsObserver.observe({ entryTypes: ['layout-shift'] });
        }
    }
    
    /**
     * 🎬 FRAME RATE MONITORING: Smooth 60fps guarantee
     */
    startFrameRateMonitoring() {
        const measureFrameRate = (currentTime) => {
            this.frameCount++;
            
            if (currentTime - this.lastTime >= 1000) {
                this.metrics.frameRate = this.frameCount;
                
                if (this.isMonitoringEnabled) {
                    this.updateMonitorDisplay();
                }
                
                // Auto-optimize if frame rate drops
                if (this.metrics.frameRate < 50) {
                    this.enablePerformanceMode();
                }
                
                this.frameCount = 0;
                this.lastTime = currentTime;
            }
            
            requestAnimationFrame(measureFrameRate);
        };
        
        requestAnimationFrame(measureFrameRate);
    }
    
    /**
     * 🧠 INTELLIGENT DEVICE OPTIMIZATION
     */
    optimizeForDevice() {
        const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        const isLowEnd = navigator.hardwareConcurrency <= 2 || navigator.deviceMemory <= 2;
        const hasSlowConnection = navigator.connection && navigator.connection.effectiveType.includes('2g');
        
        if (isMobile || isLowEnd || hasSlowConnection) {
            this.enableLightMode();
        }
        
        // Optimize for different screen sizes
        const screenDensity = window.devicePixelRatio || 1;
        if (screenDensity > 2) {
            this.optimizeForHighDPI();
        }
        
        console.log('🧠 Device optimization applied:', {
            isMobile,
            isLowEnd,
            hasSlowConnection,
            screenDensity
        });
    }
    
    /**
     * 🚀 INTELLIGENT OPTIMIZATIONS
     */
    setupIntelligentOptimizations() {
        // Lazy load images when they enter viewport
        this.setupIntelligentLazyLoading();
        
        // Preload critical resources on idle
        this.setupIdlePreloading();
        
        // Optimize scroll performance
        this.optimizeScrollPerformance();
        
        // Memory cleanup
        this.setupMemoryManagement();
    }
    
    setupIntelligentLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            imageObserver.unobserve(img);
                        }
                    }
                });
            }, {
                rootMargin: '50px 0px'
            });
            
            document.querySelectorAll('img[data-src]').forEach((img) => {
                imageObserver.observe(img);
            });
        }
    }
    
    setupIdlePreloading() {
        if ('requestIdleCallback' in window) {
            requestIdleCallback(() => {
                // Preload next section images
                const nextSectionImages = document.querySelectorAll('img[loading="lazy"]');
                nextSectionImages.forEach((img, index) => {
                    if (index < 3) { // Preload first 3 lazy images
                        const preloadLink = document.createElement('link');
                        preloadLink.rel = 'preload';
                        preloadLink.as = 'image';
                        preloadLink.href = img.src;
                        document.head.appendChild(preloadLink);
                    }
                });
            });
        }
    }
    
    optimizeScrollPerformance() {
        let isScrolling = false;
        
        window.addEventListener('scroll', () => {
            if (!isScrolling) {
                requestAnimationFrame(() => {
                    // Optimize heavy scroll operations here
                    isScrolling = false;
                });
                isScrolling = true;
            }
        }, { passive: true });
    }
    
    setupMemoryManagement() {
        // Clean up unused resources periodically
        setInterval(() => {
            if (window.quantumImageModal && window.quantumImageModal.preloadCache) {
                const cache = window.quantumImageModal.preloadCache;
                if (cache.size > 20) {
                    // Keep only the last 10 preloaded images
                    const entries = Array.from(cache.entries());
                    entries.slice(0, -10).forEach(([key]) => {
                        cache.delete(key);
                    });
                    console.log('🧹 Memory cleanup: Cache reduced to', cache.size, 'items');
                }
            }
        }, 30000); // Every 30 seconds
    }
    
    /**
     * 🔧 PERFORMANCE MODES
     */
    enablePerformanceMode() {
        console.log('🚀 Performance mode activated');
        
        // Reduce animation complexity
        document.documentElement.style.setProperty('--animation-duration', '0.2s');
        
        // Disable non-critical animations
        const nonCriticalAnimations = document.querySelectorAll('.reveal-animation');
        nonCriticalAnimations.forEach((el) => {
            el.style.animation = 'none';
        });
        
        // Reduce backdrop blur
        const blurElements = document.querySelectorAll('.quantum-image-modal');
        blurElements.forEach((el) => {
            el.style.backdropFilter = 'blur(10px)';
        });
    }
    
    enableLightMode() {
        console.log('💡 Light mode activated for low-end device');
        
        // Disable heavy effects
        document.documentElement.classList.add('light-mode');
        
        // Reduce gradient complexity
        const gradientElements = document.querySelectorAll('[class*="gradient"]');
        gradientElements.forEach((el) => {
            el.style.background = '#00574B';
        });
    }
    
    optimizeForHighDPI() {
        console.log('🖥️ High DPI optimization applied');
        
        // Use higher quality images for high DPI screens
        const images = document.querySelectorAll('img[src*="assets/images"]');
        images.forEach((img) => {
            if (!img.src.includes('@2x')) {
                const highDPISrc = img.src.replace(/\.(jpg|jpeg|png)$/, '@2x.$1');
                // Check if high DPI version exists
                const testImg = new Image();
                testImg.onload = () => {
                    img.src = highDPISrc;
                };
                testImg.src = highDPISrc;
            }
        });
    }
    
    /**
     * 🎮 MONITORING DISPLAY
     */
    updateMonitorDisplay() {
        let monitor = document.querySelector('.performance-monitor');
        if (!monitor) {
            monitor = document.createElement('div');
            monitor.className = 'performance-monitor';
            document.body.appendChild(monitor);
        }
        
        monitor.innerHTML = `
            FPS: ${this.metrics.frameRate}
            ${this.metrics.lcp ? `LCP: ${this.metrics.lcp.toFixed(0)}ms` : ''}
            ${this.metrics.fid ? `FID: ${this.metrics.fid.toFixed(0)}ms` : ''}
            ${this.metrics.cls ? `CLS: ${this.metrics.cls.toFixed(3)}` : ''}
        `;
        
        monitor.classList.toggle('show', this.isMonitoringEnabled);
    }
    
    /**
     * 📊 PUBLIC API
     */
    enableMonitoring() {
        this.isMonitoringEnabled = true;
        this.updateMonitorDisplay();
    }
    
    disableMonitoring() {
        this.isMonitoringEnabled = false;
        const monitor = document.querySelector('.performance-monitor');
        if (monitor) monitor.classList.remove('show');
    }
    
    getMetrics() {
        return { ...this.metrics };
    }
}

// 🚀 AUTO-INITIALIZATION
document.addEventListener('DOMContentLoaded', () => {
    window.quantumPerformanceMonitor = new QuantumPerformanceMonitor();
    
    // Enable monitoring in development (comment out for production)
    // window.quantumPerformanceMonitor.enableMonitoring();
    
    console.log('⚡ Quantum Performance Monitor: Ready to optimize! 🚀');
});

// 🎛️ GLOBAL API
window.togglePerformanceMonitor = () => {
    if (window.quantumPerformanceMonitor) {
        if (window.quantumPerformanceMonitor.isMonitoringEnabled) {
            window.quantumPerformanceMonitor.disableMonitoring();
        } else {
            window.quantumPerformanceMonitor.enableMonitoring();
        }
    }
};

// 📊 Export performance metrics for analytics
window.getPerformanceReport = () => {
    if (window.quantumPerformanceMonitor) {
        return window.quantumPerformanceMonitor.getMetrics();
    }
    return null;
};
