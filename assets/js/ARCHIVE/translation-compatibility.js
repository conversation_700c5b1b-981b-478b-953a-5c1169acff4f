/**
 * 🔄 TRANSLATION SYSTEM COMPATIBILITY - DEVS PROTOCOL
 * Ensure markdown conversion works with translation system
 */

document.addEventListener('DOMContentLoaded', function() {
    // Wait for translation system to load
    setTimeout(() => {
        // Force re-conversion after translations are applied
        if (window.forceTranslate || window.changeLanguage) {
            console.log('🔄 Translation system detected - Setting up compatibility');
            
            // Store original change language function
            const originalChangeLanguage = window.changeLanguage;
            
            if (originalChangeLanguage) {
                window.changeLanguage = function(lang) {
                    // Call original function
                    originalChangeLanguage(lang);
                    
                    // Re-apply markdown conversion after language change
                    setTimeout(() => {
                        if (window.convertMarkdownToHTML) {
                            window.convertMarkdownToHTML();
                            console.log('🎨 Markdown re-applied after language change');
                        }
                    }, 500);
                };
            }
        }
    }, 2000);
});

// Make conversion function globally available
window.convertMarkdownToHTML = function() {
    const descriptions = document.querySelectorAll('.course-description span[data-translate]');
    
    descriptions.forEach(desc => {
        let content = desc.innerHTML;
        
        // Convert **text** to <strong>text</strong>
        content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        
        desc.innerHTML = content;
        desc.closest('.course-description').setAttribute('data-converted', 'true');
    });
    
    console.log(`🎨 Re-converted ${descriptions.length} descriptions`);
};
