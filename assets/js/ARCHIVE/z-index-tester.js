/**
 * 🎯 Z-INDEX PRIORITY TESTER
 * Test and validate header element layering
 */

// 🎯 TEST Z-INDEX HIERARCHY
window.testZIndexPriority = function() {
    console.log('🎯 Testing z-index priority hierarchy...');
    
    const logo = document.querySelector('.logo-container-bg');
    const logoContainer = document.querySelector('.flex.items-center.space-x-3');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const mobileControls = document.querySelector('.lg\\:hidden.flex.items-center.space-x-3');
    
    if (!logo) {
        console.log('❌ Logo container not found');
        return;
    }
    
    // Get z-index values
    const logoZIndex = getComputedStyle(logo).zIndex;
    const logoContainerZIndex = getComputedStyle(logoContainer).zIndex;
    const weatherZIndex = weatherWidget ? getComputedStyle(weatherWidget).zIndex : 'not found';
    const controlsZIndex = mobileControls ? getComputedStyle(mobileControls).zIndex : 'not found';
    
    console.log('📊 Z-Index Hierarchy:');
    console.log('Logo container:', logoContainerZIndex);
    console.log('Logo background:', logoZIndex);
    console.log('Weather widget:', weatherZIndex);
    console.log('Mobile controls:', controlsZIndex);
    
    // Check hierarchy
    const logoZ = parseInt(logoContainerZIndex) || 0;
    const weatherZ = parseInt(weatherZIndex) || 0;
    const controlsZ = parseInt(controlsZIndex) || 0;
    
    console.log('✅ Priority check:');
    console.log('Logo > Weather:', logoZ > weatherZ);
    console.log('Controls > Weather:', controlsZ > weatherZ);
    
    if (logoZ > weatherZ && logoZ >= controlsZ) {
        console.log('🎉 Z-Index hierarchy SUCCESS!');
    } else {
        console.log('❌ Z-Index hierarchy needs fix');
    }
};

// 🔧 FORCE Z-INDEX FIX
window.forceZIndexFix = function() {
    console.log('🔧 Force applying z-index hierarchy...');
    
    const logo = document.querySelector('.logo-container-bg');
    const logoContainer = document.querySelector('.flex.items-center.space-x-3');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const mobileControls = document.querySelector('.lg\\:hidden.flex.items-center.space-x-3');
    
    // Apply nuclear z-index values
    if (logoContainer) {
        logoContainer.style.zIndex = '1000';
        logoContainer.style.position = 'relative';
    }
    
    if (logo) {
        logo.style.zIndex = '1000';
        logo.style.position = 'relative';
    }
    
    if (weatherWidget) {
        weatherWidget.style.zIndex = '100';
        weatherWidget.style.position = 'relative';
        weatherWidget.style.maxWidth = '50px';
        weatherWidget.style.overflow = 'hidden';
    }
    
    if (mobileControls) {
        mobileControls.style.zIndex = '200';
        mobileControls.style.position = 'relative';
    }
    
    console.log('✅ Force z-index fix applied!');
    
    setTimeout(() => {
        window.testZIndexPriority();
    }, 500);
};

// 🎨 VISUAL DEBUG - Show element boundaries
window.debugHeaderSpacing = function() {
    console.log('🎨 Adding visual debug to header elements...');
    
    const logo = document.querySelector('.logo-container-bg');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const mobileControls = document.querySelector('.lg\\:hidden.flex.items-center.space-x-3');
    
    // Add debug class to body
    document.body.classList.add('debug-spacing');
    
    if (logo) {
        logo.style.border = '2px solid red';
        logo.style.boxShadow = '0 0 0 1px red';
    }
    
    if (weatherWidget) {
        weatherWidget.style.border = '2px solid blue';
        weatherWidget.style.background = 'rgba(0, 0, 255, 0.1)';
    }
    
    if (mobileControls) {
        mobileControls.style.border = '2px solid green';
        mobileControls.style.background = 'rgba(0, 255, 0, 0.1)';
    }
    
    console.log('✅ Visual debug applied:');
    console.log('🔴 Red border = Logo');
    console.log('🔵 Blue border = Weather widget');
    console.log('🟢 Green border = Mobile controls');
    
    setTimeout(() => {
        // Remove debug after 10 seconds
        document.body.classList.remove('debug-spacing');
        console.log('🧹 Visual debug removed');
    }, 10000);
};

// 🚨 EMERGENCY WEATHER HIDE
window.hideWeatherWidget = function() {
    console.log('🚨 Emergency: Hiding weather widget...');
    
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    if (weatherWidget) {
        weatherWidget.style.display = 'none';
        console.log('✅ Weather widget hidden');
    } else {
        console.log('❌ Weather widget not found');
    }
};

// 🔄 SHOW WEATHER WIDGET
window.showWeatherWidget = function() {
    console.log('🔄 Showing weather widget...');
    
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    if (weatherWidget) {
        weatherWidget.style.display = 'block';
        console.log('✅ Weather widget shown');
    } else {
        console.log('❌ Weather widget not found');
    }
};

// 📏 CHECK ELEMENT POSITIONS
window.checkElementPositions = function() {
    console.log('📏 Checking element positions...');
    
    const logo = document.querySelector('.logo-container-bg');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    
    if (logo && weatherWidget) {
        const logoRect = logo.getBoundingClientRect();
        const weatherRect = weatherWidget.getBoundingClientRect();
        
        console.log('📍 Logo position:', {
            left: logoRect.left,
            right: logoRect.right,
            top: logoRect.top,
            bottom: logoRect.bottom
        });
        
        console.log('📍 Weather position:', {
            left: weatherRect.left,
            right: weatherRect.right,
            top: weatherRect.top,
            bottom: weatherRect.bottom
        });
        
        // Check for overlap
        const overlap = !(logoRect.right < weatherRect.left || 
                         weatherRect.right < logoRect.left || 
                         logoRect.bottom < weatherRect.top || 
                         weatherRect.bottom < logoRect.top);
        
        console.log('🔍 Elements overlapping:', overlap);
        
        if (overlap) {
            console.log('⚠️ OVERLAP DETECTED! Weather widget may be covering logo');
        } else {
            console.log('✅ No overlap - elements properly spaced');
        }
    }
};

// Auto-test on load
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        window.testZIndexPriority();
        window.checkElementPositions();
    }, 1000);
});

console.log(`
🎯 Z-INDEX PRIORITY TESTER LOADED:
- window.testZIndexPriority() - Test z-index hierarchy
- window.forceZIndexFix() - Force fix if broken
- window.debugHeaderSpacing() - Visual debug (10s)
- window.hideWeatherWidget() - Emergency hide weather
- window.showWeatherWidget() - Show weather widget
- window.checkElementPositions() - Check for overlaps
`);
