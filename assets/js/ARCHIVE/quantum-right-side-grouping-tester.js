/**
 * 🎯 QUANTUM RIGHT-SIDE GROUPING TESTER
 * Validates perfect right-side grouping:
 * - Weather + Language NEAR hamburger (right side)
 * - Maximum central space for "GolfinThaï"
 */

// 🎯 RIGHT-SIDE GROUPING VALIDATION
window.quantumRightSideGroupingTest = function() {
    console.log('🎯 RUNNING RIGHT-SIDE GROUPING TEST - FRÉRO\'S FINAL VISION...\n');
    
    const tests = [];
    let allPassed = true;
    
    // Get elements
    const logoContainer = document.querySelector('.flex.items-center.space-x-3:first-child');
    const logoCircle = document.querySelector('.logo-container-bg');
    const logoTitle = document.querySelector('.header-logo-gradient');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const tempElement = document.querySelector('.weather-temp-mobile');
    const controls = document.querySelector('.lg\\:hidden.flex.items-center.space-x-3');
    const langSwitcher = document.querySelector('.language-switcher-mobile');
    const hamburger = document.querySelector('#mobile-menu-btn');
    
    if (!logoContainer || !logoTitle || !weatherWidget || !tempElement || !hamburger) {
        console.log('❌ CRITICAL: Essential elements not found!');
        return false;
    }
    
    const screenWidth = window.innerWidth;
    const screenCenter = screenWidth / 2;
    
    console.log('🎯 RIGHT-SIDE GROUPING ANALYSIS:');
    
    // Test 1: Logo container should have MAXIMUM central space
    const logoRect = logoContainer.getBoundingClientRect();
    const logoMaxCentralSpace = logoRect.width >= 135 && logoRect.width <= 145;
    
    console.log(`  Logo container: ${Math.round(logoRect.width)}px (target: ~140px)`);
    console.log(`  Maximum central space: ${logoMaxCentralSpace ? '✅' : '❌'}`);
    
    tests.push({
        name: 'Logo maximum central space (140px)',
        passed: logoMaxCentralSpace,
        critical: true,
        details: `${Math.round(logoRect.width)}px (target: 135-145px)`
    });
    
    // Test 2: Weather widget still 50px perfect
    const weatherRect = weatherWidget.getBoundingClientRect();
    const weather50px = weatherRect.width >= 48 && weatherRect.width <= 55;
    
    console.log(`  Weather widget: ${Math.round(weatherRect.width)}px (target: 50px)`);
    console.log(`  50px maintained: ${weather50px ? '✅' : '❌'}`);
    
    tests.push({
        name: 'Weather widget 50px maintained',
        passed: weather50px,
        critical: true,
        details: `${Math.round(weatherRect.width)}px (target: 48-55px)`
    });
    
    // Test 3: Weather widget positioned in RIGHT side of screen
    const weatherCenterX = weatherRect.left + (weatherRect.width / 2);
    const weatherInRightSide = weatherCenterX > screenCenter + 30; // Well in right side
    
    console.log(`\n📐 RIGHT-SIDE POSITIONING:`);
    console.log(`  Screen center: ${Math.round(screenCenter)}px`);
    console.log(`  Weather center: ${Math.round(weatherCenterX)}px`);
    console.log(`  Weather in right side: ${weatherInRightSide ? '✅' : '❌'}`);
    
    tests.push({
        name: 'Weather widget in right side',
        passed: weatherInRightSide,
        critical: true,
        details: weatherInRightSide ? 'Positioned right side ✅' : 'Still in center/left ❌'
    });
    
    // Test 4: Language switcher NEAR hamburger
    if (langSwitcher) {
        const langRect = langSwitcher.getBoundingClientRect();
        const hamburgerRect = hamburger.getBoundingClientRect();
        
        const langHamburgerGap = hamburgerRect.left - langRect.right;
        const langNearHamburger = langHamburgerGap >= 0 && langHamburgerGap <= 15; // Very close
        
        console.log(`  Language position: x=${Math.round(langRect.left)}-${Math.round(langRect.right)}`);
        console.log(`  Hamburger position: x=${Math.round(hamburgerRect.left)}-${Math.round(hamburgerRect.right)}`);
        console.log(`  Gap Lang→Hamburger: ${Math.round(langHamburgerGap)}px`);
        console.log(`  Language near hamburger: ${langNearHamburger ? '✅' : '❌'}`);
        
        tests.push({
            name: 'Language switcher near hamburger',
            passed: langNearHamburger,
            critical: true,
            details: langNearHamburger ? `${Math.round(langHamburgerGap)}px gap - Close! ✅` : `${Math.round(langHamburgerGap)}px gap - Too far ❌`
        });
    }
    
    // Test 5: Weather NEAR language switcher (grouped together)
    if (langSwitcher) {
        const langRect = langSwitcher.getBoundingClientRect();
        
        const weatherLangGap = langRect.left - weatherRect.right;
        const weatherNearLang = weatherLangGap >= 0 && weatherLangGap <= 12; // Very close grouping
        
        console.log(`  Gap Weather→Language: ${Math.round(weatherLangGap)}px`);
        console.log(`  Weather near language: ${weatherNearLang ? '✅' : '❌'}`);
        
        tests.push({
            name: 'Weather near language (grouped)',
            passed: weatherNearLang,
            critical: true,
            details: weatherNearLang ? `${Math.round(weatherLangGap)}px gap - Grouped! ✅` : `${Math.round(weatherLangGap)}px gap - Too spread ❌`
        });
    }
    
    // Test 6: RIGHT-SIDE GROUP is compact
    if (langSwitcher) {
        const rightGroupStart = weatherRect.left;
        const rightGroupEnd = hamburger.getBoundingClientRect().right;
        const rightGroupWidth = rightGroupEnd - rightGroupStart;
        
        const rightGroupCompact = rightGroupWidth <= 160; // Should be compact
        
        console.log(`\n🎮 RIGHT-SIDE GROUP ANALYSIS:`);
        console.log(`  Group width: ${Math.round(rightGroupWidth)}px (target: ≤160px)`);
        console.log(`  Group is compact: ${rightGroupCompact ? '✅' : '❌'}`);
        
        tests.push({
            name: 'Right-side group compact',
            passed: rightGroupCompact,
            details: `${Math.round(rightGroupWidth)}px (target: ≤160px)`
        });
    }
    
    // Test 7: Logo circle even BIGGER with more space
    if (logoCircle) {
        const circleRect = logoCircle.getBoundingClientRect();
        const logoEvenBigger = circleRect.width >= 42 && circleRect.width <= 47;
        
        console.log(`\n🏷️ ENHANCED LOGO ANALYSIS:`);
        console.log(`  Logo circle: ${Math.round(circleRect.width)}px (target: 44px)`);
        console.log(`  Logo even bigger: ${logoEvenBigger ? '✅' : '❌'}`);
        
        tests.push({
            name: 'Logo circle even bigger (44px)',
            passed: logoEvenBigger,
            details: `${Math.round(circleRect.width)}px (target: 42-47px)`
        });
    }
    
    // Test 8: "GolfinThaï" text even BIGGER with more space
    const logoText = logoTitle.textContent || '';
    const logoComplete = logoText.includes('GolfinThaï');
    const logoStyles = getComputedStyle(logoTitle);
    const logoFontSize = parseFloat(logoStyles.fontSize);
    const logoTextEvenBigger = logoFontSize >= 15; // Should be ~1rem = 16px
    
    console.log(`  "GolfinThaï" text: "${logoText}"`);
    console.log(`  Text complete: ${logoComplete ? '✅' : '❌'}`);
    console.log(`  Font size: ${logoFontSize}px (target: 15px+)`);
    console.log(`  Text even bigger: ${logoTextEvenBigger ? '✅' : '❌'}`);
    
    tests.push({
        name: '"GolfinThaï" text even bigger',
        passed: logoComplete && logoTextEvenBigger,
        critical: true,
        details: logoComplete && logoTextEvenBigger ? 'Perfect enhanced visibility ✅' : 'Text enhancement needed ❌'
    });
    
    // Test 9: Central space utilization optimal
    const logoCenterX = logoRect.left + (logoRect.width / 2);
    const logoWellCentered = Math.abs(logoCenterX - screenCenter) <= 30; // Should be near screen center
    
    console.log(`  Logo center: ${Math.round(logoCenterX)}px vs screen center: ${Math.round(screenCenter)}px`);
    console.log(`  Logo well centered: ${logoWellCentered ? '✅' : '❌'}`);
    
    tests.push({
        name: 'Logo well centered in available space',
        passed: logoWellCentered,
        details: logoWellCentered ? 'Centered positioning ✅' : 'Off-center ❌'
    });
    
    // Test 10: Temperature still fits in 50px widget
    const tempText = tempElement.textContent || '--°';
    const tempRect = tempElement.getBoundingClientRect();
    const tempStillFits = tempRect.width <= weatherRect.width - 2;
    
    console.log(`\n🌡️ TEMPERATURE COMPATIBILITY:`);
    console.log(`  Temperature: "${tempText}"`);
    console.log(`  Still fits in 50px: ${tempStillFits ? '✅' : '❌'}`);
    
    tests.push({
        name: 'Temperature still fits in 50px',
        passed: tempStillFits,
        details: `${Math.round(tempRect.width)}px in ${Math.round(weatherRect.width)}px`
    });
    
    // Test 11: Overall space efficiency with right-side grouping
    const totalLogoSpace = logoRect.width;
    const logoSpaceRatio = Math.round((totalLogoSpace / screenWidth) * 100);
    const logoSpaceExcellent = logoSpaceRatio >= 40; // Should use significant portion for logo
    
    console.log(`\n📊 SPACE EFFICIENCY WITH RIGHT-SIDE GROUPING:`);
    console.log(`  Logo space: ${Math.round(totalLogoSpace)}px (${logoSpaceRatio}% of screen)`);
    console.log(`  Logo space excellent: ${logoSpaceExcellent ? '✅' : '❌'}`);
    
    tests.push({
        name: 'Logo space utilization excellent',
        passed: logoSpaceExcellent,
        details: `${logoSpaceRatio}% of screen (target: 40%+)`
    });
    
    // Results summary
    console.log(`\n🧪 RIGHT-SIDE GROUPING TEST RESULTS:`);
    tests.forEach((test, i) => {
        const status = test.passed ? '✅' : (test.critical ? '🚨' : '❌');
        const priority = test.critical ? ' [CRITICAL]' : '';
        console.log(`  ${i + 1}. ${status} ${test.name}${priority} - ${test.details}`);
        if (!test.passed) allPassed = false;
    });
    
    console.log(`\n${allPassed ? '🎉' : '⚠️'} OVERALL RESULT: ${allPassed ? 'RIGHT-SIDE GROUPING PERFECTION!' : 'Some adjustments needed'}`);
    
    if (allPassed) {
        console.log('🎯 FRÉRO\'S RIGHT-SIDE GROUPING VISION ACHIEVED! 🏆');
        console.log('✅ Weather + Language NEAR hamburger (right-side)!');
        console.log('✅ Maximum central space for "GolfinThaï"!');
        console.log('✅ Weather widget 50px maintained!');
        console.log('✅ Even bigger logo and text!');
        console.log('🔥 Mobile header LEGENDARY level achieved!');
    } else {
        console.log('🔧 Run quantumRightSideGroupingFix() for final adjustments');
    }
    
    return allPassed;
};

// 🔧 RIGHT-SIDE GROUPING EMERGENCY FIX
window.quantumRightSideGroupingFix = function() {
    console.log('🔧 APPLYING RIGHT-SIDE GROUPING EMERGENCY FIX...');
    
    const logoContainer = document.querySelector('.flex.items-center.space-x-3:first-child');
    const logoTitle = document.querySelector('.header-logo-gradient');
    const logoCircle = document.querySelector('.logo-container-bg');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const langSwitcher = document.querySelector('.language-switcher-mobile');
    const controls = document.querySelector('.lg\\:hidden.flex.items-center.space-x-3');
    
    if (!logoContainer || !logoTitle || !weatherWidget || !langSwitcher || !controls) {
        console.log('❌ Elements not found for right-side grouping fix');
        return;
    }
    
    console.log('🎯 Forcing right-side grouping layout...');
    
    // FORCE maximum logo space
    logoContainer.style.width = '138px';
    logoContainer.style.maxWidth = '138px';
    logoContainer.style.minWidth = '138px';
    logoContainer.style.flexDirection = 'row';
    logoContainer.style.gap = '0.6rem';
    logoContainer.style.overflow = 'visible';
    
    // FORCE even bigger logo
    if (logoCircle) {
        logoCircle.style.width = '44px';
        logoCircle.style.height = '44px';
        logoCircle.style.minWidth = '44px';
        logoCircle.style.minHeight = '44px';
    }
    
    // FORCE even bigger text
    logoTitle.style.fontSize = '1rem';
    logoTitle.style.fontWeight = '800';
    logoTitle.style.maxWidth = '88px';
    logoTitle.style.overflow = 'visible';
    logoTitle.style.whiteSpace = 'nowrap';
    logoTitle.style.zIndex = '2000';
    
    // FORCE weather widget to right side, 50px
    weatherWidget.style.width = '50px';
    weatherWidget.style.minWidth = '50px';
    weatherWidget.style.maxWidth = '52px';
    weatherWidget.style.margin = '0 0.2rem 0 auto';
    
    // FORCE controls to right-side grouping
    controls.style.justifyContent = 'flex-end';
    controls.style.gap = '0.25rem';
    controls.style.padding = '0.25rem 0.2rem';
    controls.style.width = '118px';
    controls.style.maxWidth = '122px';
    
    // FORCE language switcher near hamburger
    langSwitcher.style.marginLeft = '0.15rem';
    langSwitcher.style.marginRight = '0.2rem';
    langSwitcher.style.width = '48px';
    langSwitcher.style.maxWidth = '48px';
    
    console.log('✅ Right-side grouping fix applied!');
    console.log('🧪 Testing in 1 second...');
    
    setTimeout(() => {
        window.quantumRightSideGroupingTest();
    }, 1000);
};

// 🎨 RIGHT-SIDE GROUPING VISUAL DEBUG
window.quantumRightSideGroupingVisualDebug = function() {
    console.log('🎨 RIGHT-SIDE GROUPING VISUAL DEBUG...');
    
    const logoContainer = document.querySelector('.flex.items-center.space-x-3:first-child');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const langSwitcher = document.querySelector('.language-switcher-mobile');
    const hamburger = document.querySelector('#mobile-menu-btn');
    const controls = document.querySelector('.lg\\:hidden.flex.items-center.space-x-3');
    
    if (!logoContainer || !weatherWidget || !langSwitcher || !hamburger) {
        console.log('❌ Elements not found for visual debug');
        return;
    }
    
    // Color code the right-side grouping
    logoContainer.style.border = '4px solid #FF1493'; // Hot pink for huge central space
    logoContainer.style.backgroundColor = 'rgba(255, 20, 147, 0.15)';
    
    weatherWidget.style.border = '3px solid #FFD700'; // Gold for 50px weather
    weatherWidget.style.backgroundColor = 'rgba(255, 215, 0, 0.2)';
    
    langSwitcher.style.border = '3px solid #00CED1'; // Dark turquoise for language
    langSwitcher.style.backgroundColor = 'rgba(0, 206, 209, 0.2)';
    
    hamburger.style.border = '3px solid #FF6347'; // Tomato for hamburger anchor
    hamburger.style.backgroundColor = 'rgba(255, 99, 71, 0.2)';
    
    if (controls) {
        controls.style.border = '2px dashed #9370DB'; // Medium purple for controls group
        controls.style.backgroundColor = 'rgba(147, 112, 219, 0.1)';
    }
    
    // Add visual divider at screen center
    const screenCenter = window.innerWidth / 2;
    const divider = document.createElement('div');
    divider.style.position = 'fixed';
    divider.style.top = '0';
    divider.style.left = screenCenter + 'px';
    divider.style.width = '2px';
    divider.style.height = '100vh';
    divider.style.backgroundColor = '#FF0000';
    divider.style.zIndex = '9999';
    divider.style.pointerEvents = 'none';
    divider.id = 'quantum-center-divider';
    document.body.appendChild(divider);
    
    console.log('🎨 Right-side grouping visual debug active for 25 seconds:');
    console.log('💗 HOT PINK = Logo central space (should be HUGE ~140px)');
    console.log('🟡 GOLD = Weather widget (should be 50px, right side)');
    console.log('🟦 TURQUOISE = Language switcher (should be near hamburger)');
    console.log('🍅 TOMATO = Hamburger menu (right edge anchor)');
    console.log('🟣 PURPLE DASHED = Controls group (right-side)');
    console.log('🔴 RED LINE = Screen center (weather+language should be RIGHT of this line)');
    console.log('👁️ VERIFY: Weather+Language+Hamburger grouped tightly on RIGHT side?');
    
    setTimeout(() => {
        logoContainer.style.border = '';
        logoContainer.style.backgroundColor = '';
        weatherWidget.style.border = '';
        weatherWidget.style.backgroundColor = '';
        langSwitcher.style.border = '';
        langSwitcher.style.backgroundColor = '';
        hamburger.style.border = '';
        hamburger.style.backgroundColor = '';
        if (controls) {
            controls.style.border = '';
            controls.style.backgroundColor = '';
        }
        const dividerElement = document.getElementById('quantum-center-divider');
        if (dividerElement) {
            dividerElement.remove();
        }
        console.log('🧹 Right-side grouping visual debug cleaned up');
    }, 25000);
};

// 🚀 AUTO-TEST ON LOAD
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        console.log('\n🎯 QUANTUM RIGHT-SIDE GROUPING TESTER LOADED!\n');
        console.log('Testing Fréro\'s right-side grouping vision:');
        console.log('  🎯 Weather + Language NEAR hamburger (right-side)');
        console.log('  🎯 Maximum central space for "GolfinThaï"');
        console.log('  🎯 Weather widget 50px maintained');
        console.log('  🎯 Even bigger logo and text');
        console.log('\nAvailable commands:');
        console.log('  🧪 quantumRightSideGroupingTest() - Test right-side grouping');
        console.log('  🔧 quantumRightSideGroupingFix() - Emergency grouping fix');
        console.log('  🎨 quantumRightSideGroupingVisualDebug() - Visual boundaries (25s)');
        console.log('\n🎯 Running auto-test in 3 seconds...\n');
        
        setTimeout(() => {
            window.quantumRightSideGroupingTest();
        }, 3000);
        
    }, 2000);
});

console.log('🎯 QUANTUM RIGHT-SIDE GROUPING TESTER INITIALIZED!');