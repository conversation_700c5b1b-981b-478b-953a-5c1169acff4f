/**
 * 🔥 QUANTUM ULTRA-CLEAN MOBILE TESTER
 * Validates PERFECT mobile layout without subtitle
 * Team Quantum: <PERSON> + <PERSON><PERSON> + Alex = PERFECTION
 */

// 🎯 ULTRA-CLEAN LAYOUT VALIDATION
window.quantumUltraCleanTest = function() {
    console.log('🔥 RUNNING QUANTUM ULTRA-CLEAN LAYOUT TEST...\n');
    
    const tests = [];
    let allPassed = true;
    
    // Get all critical elements
    const logoContainer = document.querySelector('.flex.items-center.space-x-3:first-child');
    const logoCircle = document.querySelector('.logo-container-bg');
    const logoTitle = document.querySelector('.header-logo-gradient');
    const subtitle = document.querySelector('.text-xs.text-luxury-emerald-light');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const tempElement = document.querySelector('.weather-temp-mobile');
    const controls = document.querySelector('.lg\\:hidden.flex.items-center.space-x-3');
    const langSwitcher = document.querySelector('.language-switcher-mobile');
    const hamburger = document.querySelector('#mobile-menu-btn');
    
    if (!logoContainer || !logoTitle || !weatherWidget || !tempElement) {
        console.log('❌ CRITICAL: Essential elements not found!');
        return false;
    }
    
    console.log('🧹 CLEAN LAYOUT ANALYSIS:');
    
    // Test 1: Subtitle should NOT exist anymore
    const subtitleExists = subtitle !== null;
    const subtitleVisible = subtitle ? getComputedStyle(subtitle).display !== 'none' : false;
    
    console.log(`  Subtitle exists: ${subtitleExists ? '❌ SHOULD BE REMOVED' : '✅ CLEAN'}`);
    console.log(`  Subtitle visible: ${subtitleVisible ? '❌ STILL SHOWING' : '✅ HIDDEN/REMOVED'}`);
    
    tests.push({
        name: 'No subtitle pollution',
        passed: !subtitleExists || !subtitleVisible,
        details: subtitleExists ? (subtitleVisible ? 'Still visible!' : 'Hidden by CSS') : 'Completely removed ✅'
    });
    
    // Test 2: Logo container ultra-compact
    const logoRect = logoContainer.getBoundingClientRect();
    console.log(`\n🏷️ LOGO ANALYSIS:`);
    console.log(`  Container width: ${Math.round(logoRect.width)}px (target: ~90px)`);
    
    const logoCompact = logoRect.width <= 100 && logoRect.width >= 80;
    tests.push({
        name: 'Logo container ultra-compact',
        passed: logoCompact,
        details: `${Math.round(logoRect.width)}px (target: 80-100px)`
    });
    
    // Test 3: "GolfinThaï" title fully visible and prominent
    const logoText = logoTitle.textContent || '';
    const logoExpected = 'GolfinThaï';
    const logoComplete = logoText.includes(logoExpected);
    
    console.log(`  Logo text: "${logoText}" (expected: "${logoExpected}")`);
    console.log(`  Logo complete: ${logoComplete ? '✅' : '❌'}`);
    
    tests.push({
        name: '"GolfinThaï" fully visible',
        passed: logoComplete,
        details: `"${logoText}" ${logoComplete ? '✅' : '❌ INCOMPLETE'}`
    });
    
    // Test 4: Weather widget MAXIMUM space
    const weatherRect = weatherWidget.getBoundingClientRect();
    console.log(`\n🌡️ WEATHER WIDGET ANALYSIS:`);
    console.log(`  Widget width: ${Math.round(weatherRect.width)}px (target: ~100px)`);
    
    const weatherSpaceMaximized = weatherRect.width >= 95; // Should be around 100px
    tests.push({
        name: 'Weather widget maximum space',
        passed: weatherSpaceMaximized,
        details: `${Math.round(weatherRect.width)}px (target: 95px+)`
    });
    
    // Test 5: Temperature fits PERFECTLY with room to spare
    const tempText = tempElement.textContent || '--°';
    const tempRect = tempElement.getBoundingClientRect();
    
    console.log(`  Temperature: "${tempText}"`);
    console.log(`  Temp width: ${Math.round(tempRect.width)}px in ${Math.round(weatherRect.width)}px container`);
    
    const tempFitsPerfectly = tempRect.width <= weatherRect.width - 10; // 10px safety margin
    const spareSpace = Math.round(weatherRect.width - tempRect.width);
    
    console.log(`  Spare space: ${spareSpace}px (excellent: 10px+)`);
    
    tests.push({
        name: 'Temperature with ample space',
        passed: tempFitsPerfectly,
        details: `${spareSpace}px spare space (target: 10px+)`
    });
    
    // Test 6: Controls comfortable spacing
    if (controls) {
        const controlsRect = controls.getBoundingClientRect();
        console.log(`\n🎮 CONTROLS ANALYSIS:`);
        console.log(`  Controls width: ${Math.round(controlsRect.width)}px (target: ~120px)`);
        
        const controlsComfortable = controlsRect.width >= 115 && controlsRect.width <= 130;
        tests.push({
            name: 'Controls comfortable spacing',
            passed: controlsComfortable,
            details: `${Math.round(controlsRect.width)}px (target: 115-130px)`
        });
        
        // Test 7: Overall space distribution
        const totalUsed = logoRect.width + weatherRect.width + controlsRect.width;
        const screenWidth = window.innerWidth;
        const efficiency = Math.round((totalUsed / screenWidth) * 100);
        
        console.log(`\n📏 SPACE EFFICIENCY:`);
        console.log(`  Total used: ${Math.round(totalUsed)}px of ${screenWidth}px`);
        console.log(`  Efficiency: ${efficiency}% (target: 85-95%)`);
        
        const spaceOptimal = efficiency >= 85 && efficiency <= 95;
        tests.push({
            name: 'Space distribution optimal',
            passed: spaceOptimal,
            details: `${efficiency}% efficiency (target: 85-95%)`
        });
    }
    
    // Test 8: Visual quality checks
    if (logoCircle && logoTitle) {
        const logoStyles = getComputedStyle(logoTitle);
        const hasGradient = logoStyles.backgroundImage && logoStyles.backgroundImage.includes('gradient');
        
        tests.push({
            name: 'Premium gradient styling',
            passed: hasGradient,
            details: hasGradient ? 'Gradient applied ✅' : 'No gradient ❌'
        });
        
        // Test 9: Logo vertical layout
        const circleRect = logoCircle.getBoundingClientRect();
        const titleRect = logoTitle.getBoundingClientRect();
        const isVertical = circleRect.bottom <= titleRect.top + 10; // 10px tolerance
        
        tests.push({
            name: 'Vertical logo layout',
            passed: isVertical,
            details: `Circle: y${Math.round(circleRect.top)}, Title: y${Math.round(titleRect.top)}`
        });
    }
    
    // Test 10: Temperature font optimization
    const tempStyles = getComputedStyle(tempElement);
    const tempFontSize = parseFloat(tempStyles.fontSize);
    const tempIsBigEnough = tempFontSize >= 16; // Should be ~1.1rem
    
    tests.push({
        name: 'Temperature font optimized',
        passed: tempIsBigEnough,
        details: `${tempFontSize}px (target: 16px+)`
    });
    
    // Results summary
    console.log(`\n🧪 ULTRA-CLEAN LAYOUT TEST RESULTS:`);
    tests.forEach((test, i) => {
        const status = test.passed ? '✅' : '❌';
        console.log(`  ${i + 1}. ${status} ${test.name} - ${test.details}`);
        if (!test.passed) allPassed = false;
    });
    
    console.log(`\n${allPassed ? '🎉' : '⚠️'} OVERALL RESULT: ${allPassed ? 'ULTRA-CLEAN LAYOUT PERFECTION!' : 'Some optimizations needed'}`);
    
    if (allPassed) {
        console.log('🔥 TEAM QUANTUM SUCCESS! Mobile layout is ULTRA-CLEAN! 🏆');
        console.log('💎 Fréro will say "PUTAIN c\'est magnifique!"');
        console.log('🚀 Professional, spacious, perfect proportions!');
    } else {
        console.log('🔧 Run quantumUltraCleanFix() for adjustments');
    }
    
    return allPassed;
};

// 🚨 EMERGENCY ULTRA-CLEAN FIX
window.quantumUltraCleanFix = function() {
    console.log('🚨 APPLYING ULTRA-CLEAN EMERGENCY ADJUSTMENTS...');
    
    const logoContainer = document.querySelector('.flex.items-center.space-x-3:first-child');
    const logoTitle = document.querySelector('.header-logo-gradient');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const tempElement = document.querySelector('.weather-temp-mobile');
    const subtitle = document.querySelector('.text-xs.text-luxury-emerald-light');
    
    // Hide subtitle if it still exists
    if (subtitle) {
        console.log('🧹 Hiding subtitle...');
        subtitle.style.display = 'none';
        subtitle.style.visibility = 'hidden';
    }
    
    if (!logoContainer || !logoTitle || !weatherWidget || !tempElement) {
        console.log('❌ Essential elements not found for fix');
        return;
    }
    
    console.log('🔧 Applying ultra-clean adjustments...');
    
    // Force ultra-compact logo layout
    logoContainer.style.flexDirection = 'column';
    logoContainer.style.alignItems = 'center';
    logoContainer.style.justifyContent = 'center';
    logoContainer.style.width = '88px';
    logoContainer.style.maxWidth = '88px';
    logoContainer.style.minWidth = '88px';
    logoContainer.style.gap = '0.3rem';
    
    // Force logo title prominence
    logoTitle.style.fontSize = '0.9rem';
    logoTitle.style.fontWeight = '800';
    logoTitle.style.whiteSpace = 'nowrap';
    logoTitle.style.overflow = 'visible';
    logoTitle.style.textOverflow = 'clip';
    logoTitle.style.maxWidth = 'none';
    
    // Force weather widget maximum space
    weatherWidget.style.width = '98px';
    weatherWidget.style.minWidth = '98px';
    weatherWidget.style.maxWidth = '102px';
    weatherWidget.style.overflow = 'visible';
    weatherWidget.style.padding = '0.6rem 0.4rem';
    
    // Force temperature optimization
    tempElement.style.fontSize = '1.05rem';
    tempElement.style.fontWeight = '900';
    tempElement.style.overflow = 'visible';
    tempElement.style.whiteSpace = 'nowrap';
    tempElement.style.textOverflow = 'clip';
    tempElement.style.maxWidth = 'none';
    
    console.log('✅ Ultra-clean emergency fix applied!');
    console.log('🧪 Testing in 1 second...');
    
    setTimeout(() => {
        window.quantumUltraCleanTest();
    }, 1000);
};

// 🎨 ULTRA-CLEAN VISUAL DEBUG
window.quantumUltraCleanVisualDebug = function() {
    console.log('🎨 ULTRA-CLEAN VISUAL DEBUG ACTIVATED...');
    
    const logoContainer = document.querySelector('.flex.items-center.space-x-3:first-child');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const controls = document.querySelector('.lg\\:hidden.flex.items-center.space-x-3');
    const subtitle = document.querySelector('.text-xs.text-luxury-emerald-light');
    
    if (!logoContainer || !weatherWidget || !controls) {
        console.log('❌ Elements not found for visual debug');
        return;
    }
    
    // Apply visual debugging
    logoContainer.style.border = '2px solid #FF6B6B';
    logoContainer.style.backgroundColor = 'rgba(255, 107, 107, 0.1)';
    
    weatherWidget.style.border = '2px solid #32CD32';
    weatherWidget.style.backgroundColor = 'rgba(50, 205, 50, 0.1)';
    
    controls.style.border = '2px solid #4169E1';
    controls.style.backgroundColor = 'rgba(65, 105, 225, 0.1)';
    
    // Highlight subtitle if it still exists
    if (subtitle) {
        subtitle.style.border = '3px solid #FF0000';
        subtitle.style.backgroundColor = 'rgba(255, 0, 0, 0.3)';
        console.log('🚨 SUBTITLE STILL EXISTS - SHOULD BE REMOVED!');
    }
    
    console.log('🎨 Ultra-clean visual debug active for 10 seconds:');
    console.log('🔴 Red border = Logo container (should be ~90px wide)');
    console.log('🟢 Green border = Weather widget (should be ~100px wide)');
    console.log('🔵 Blue border = Controls (should be ~120px wide)');
    if (subtitle) {
        console.log('🔴 RED ALERT = Subtitle still exists (REMOVE IT!)');
    }
    
    setTimeout(() => {
        logoContainer.style.border = '';
        logoContainer.style.backgroundColor = '';
        weatherWidget.style.border = '';
        weatherWidget.style.backgroundColor = '';
        controls.style.border = '';
        controls.style.backgroundColor = '';
        if (subtitle) {
            subtitle.style.border = '';
            subtitle.style.backgroundColor = '';
        }
        console.log('🧹 Ultra-clean visual debug cleaned up');
    }, 10000);
};

// 📊 SPACE EFFICIENCY ANALYZER
window.analyzeSpaceEfficiency = function() {
    console.log('📊 ANALYZING SPACE EFFICIENCY...\n');
    
    const logoContainer = document.querySelector('.flex.items-center.space-x-3:first-child');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const controls = document.querySelector('.lg\\:hidden.flex.items-center.space-x-3');
    
    if (!logoContainer || !weatherWidget || !controls) {
        console.log('❌ Elements not found for analysis');
        return;
    }
    
    const logoRect = logoContainer.getBoundingClientRect();
    const weatherRect = weatherWidget.getBoundingClientRect();
    const controlsRect = controls.getBoundingClientRect();
    const screenWidth = window.innerWidth;
    
    const spaces = {
        logo: Math.round(logoRect.width),
        weather: Math.round(weatherRect.width),
        controls: Math.round(controlsRect.width),
        total: Math.round(logoRect.width + weatherRect.width + controlsRect.width),
        screen: screenWidth,
        efficiency: Math.round(((logoRect.width + weatherRect.width + controlsRect.width) / screenWidth) * 100)
    };
    
    console.log('📏 SPACE BREAKDOWN:');
    console.log(`  📱 Screen width: ${spaces.screen}px`);
    console.log(`  🏷️ Logo: ${spaces.logo}px (${Math.round((spaces.logo/spaces.screen)*100)}%)`);
    console.log(`  🌡️ Weather: ${spaces.weather}px (${Math.round((spaces.weather/spaces.screen)*100)}%)`);
    console.log(`  🎮 Controls: ${spaces.controls}px (${Math.round((spaces.controls/spaces.screen)*100)}%)`);
    console.log(`  📊 Total used: ${spaces.total}px (${spaces.efficiency}%)`);
    console.log(`  🎯 Remaining: ${spaces.screen - spaces.total}px (${100 - spaces.efficiency}%)`);
    
    // Efficiency rating
    let rating = '';
    if (spaces.efficiency >= 90) rating = '🔥 EXCELLENT';
    else if (spaces.efficiency >= 85) rating = '✅ GOOD';
    else if (spaces.efficiency >= 75) rating = '⚠️ ACCEPTABLE';
    else rating = '❌ NEEDS OPTIMIZATION';
    
    console.log(`\n📈 EFFICIENCY RATING: ${rating}`);
    
    return spaces;
};

// 🚀 AUTO-TEST ON LOAD
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        console.log('\n🔥 QUANTUM ULTRA-CLEAN MOBILE TESTER LOADED!\n');
        console.log('Available commands:');
        console.log('  🧪 quantumUltraCleanTest() - Complete ultra-clean validation');
        console.log('  🚨 quantumUltraCleanFix() - Emergency adjustments');
        console.log('  🎨 quantumUltraCleanVisualDebug() - Visual boundaries (10s)');
        console.log('  📊 analyzeSpaceEfficiency() - Space usage analysis');
        console.log('\n🎯 Running auto-test in 3 seconds...\n');
        
        setTimeout(() => {
            window.quantumUltraCleanTest();
        }, 3000);
        
    }, 2000);
});

console.log('🔥 QUANTUM ULTRA-CLEAN MOBILE TESTER INITIALIZED!');