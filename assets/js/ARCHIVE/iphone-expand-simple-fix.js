/**
 * 🍎 CORRECTION CIBLÉE IPHONE - Bouton "En savoir plus" uniquement
 * Ne modifie QUE le bouton problématique, laisse le reste intact
 */

(function() {
    'use strict';
    
    // Détecter iPhone/iPad uniquement
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const isSafari = /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);
    
    if (!isIOS || !isSafari) {
        return; // Pas iPhone Safari, ne rien faire
    }
    
    /**
     * Corriger uniquement les boutons "En savoir plus" qui ne fonctionnent pas
     */
    function fixExpandButtonsOniPhone() {
        // Attendre que les modales existent
        setTimeout(() => {
            const modals = document.querySelectorAll('.fullscreen-modal');
            
            modals.forEach(modal => {
                const expandBtn = modal.querySelector('.expand-btn');
                const fullText = modal.querySelector('.seo-full');
                
                if (!expandBtn || !fullText) return;
                
                // Vérifier si déjà corrigé
                if (expandBtn.dataset.iPhoneFixed) return;
                
                // Marquer comme corrigé
                expandBtn.dataset.iPhoneFixed = 'true';
                
                // Ajouter support tactile iPhone spécifique
                let isExpanded = false;
                
                function toggleExpansion(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    if (isExpanded) {
                        fullText.style.display = 'none';
                        expandBtn.textContent = 'En savoir plus ↓';
                        isExpanded = false;
                    } else {
                        fullText.style.display = 'block';
                        expandBtn.textContent = 'Réduire ↑';
                        isExpanded = true;
                    }
                }
                
                // Événements tactiles spécifiques iPhone
                let touchStartTime = 0;
                
                expandBtn.addEventListener('touchstart', function(e) {
                    touchStartTime = Date.now();
                    expandBtn.style.opacity = '0.7';
                }, { passive: true });
                
                expandBtn.addEventListener('touchend', function(e) {
                    expandBtn.style.opacity = '1';
                    
                    const touchDuration = Date.now() - touchStartTime;
                    if (touchDuration < 500) {
                        toggleExpansion(e);
                    }
                }, { passive: false });
                
                expandBtn.addEventListener('touchcancel', function() {
                    expandBtn.style.opacity = '1';
                }, { passive: true });
            });
        }, 1000);
    }
    
    /**
     * Observer les nouvelles modales qui s'ouvrent
     */
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                const element = mutation.target;
                if (element.classList.contains('fullscreen-modal')) {
                    const style = getComputedStyle(element);
                    if (style.display === 'flex' && parseFloat(style.opacity) > 0) {
                        // Nouvelle modal visible
                        setTimeout(fixExpandButtonsOniPhone, 100);
                    }
                }
            }
        });
    });
    
    observer.observe(document.body, {
        attributes: true,
        subtree: true,
        attributeFilter: ['style']
    });
    
    // Correction initiale
    document.addEventListener('DOMContentLoaded', fixExpandButtonsOniPhone);
    setTimeout(fixExpandButtonsOniPhone, 2000);
    
})();
