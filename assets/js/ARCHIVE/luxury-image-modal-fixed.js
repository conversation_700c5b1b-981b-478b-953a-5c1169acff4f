/**
 * 🎯 FULLSCREEN IMAGE MODAL - VERSION CORRIGÉE IPHONE
 * Solution propre et efficace pour les modales d'images avec support tactile iPhone
 */

class FullscreenImageModal {
    constructor() {
        this.isOpen = false;
        this.modal = null;
        this.currentImage = null;
        this.golfData = this.initGolfData();
        this.isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
        
        this.init();
    }
    
    /**
     * 🏌️ DONNÉES GOLF POUR SEO COMPACT
     */
    initGolfData() {
        return {
            'RedMountain': {
                title: 'Red Mountain Golf Club Phuket',
                short: 'Parcours le plus spectaculaire d\'Asie sculpté dans une ancienne mine d\'étain.',
                full: 'Red Mountain Golf Club à Phuket est reconnu comme le parcours le plus spectaculaire d\'Asie. Sculpté dans une ancienne mine d\'étain, ce golf unique offre des formations rocheuses rouges iconiques et un dénivelé de 50 mètres au légendaire trou 17 "Drop Shot". L\'expérience de jeu est révolutionnaire avec des vues panoramiques exceptionnelles sur l\'île de Phuket.'
            },
            'Aquella': {
                title: 'Aquella Golf & Country Club',
                short: 'Élu Meilleur Golf de Luxe de Thaïlande 2024 avec 2,5 km de plage privée.',
                full: 'Aquella Golf & Country Club en Thaïlande a été élu Meilleur Golf de Luxe 2024. Ce parcours championship de 18 trous propose une expérience unique avec 2,5 kilomètres de plage privée exclusive, des tunnels de bambou spectaculaires et une vue imprenable sur la mer d\'Andaman. Le resort 5 étoiles offre un hébergement de luxe exceptionnel.'
            },
            'BlackMountain': {
                title: 'Black Mountain Golf Club Hua Hin',
                short: '#59 Golf Digest Top 100 mondial, seul parcours thaïlandais dans ce classement.',
                full: 'Black Mountain Golf Club à Hua Hin est classé #59 au Golf Digest Top 100 mondial, étant le seul parcours thaïlandais dans ce prestigieux classement. Ce parcours de 27 trous championship conçu par Phil Ryan s\'étend dans un cadre montagneux spectaculaire avec une academy professionnelle de renommée internationale.'
            },
            'Santiburi': {
                title: 'Santiburi Samui Country Club',
                short: 'Seul parcours 18 trous de Koh Samui dans un resort 5 étoiles.',
                full: 'Santiburi Samui Country Club est le seul parcours de golf 18 trous de l\'île de Koh Samui. Niché dans un resort 5 étoiles avec plage privée, ce golf offre des dénivelés spectaculaires jusqu\'à 180 mètres d\'altitude avec des panoramas époustouflants sur le Golfe de Thaïlande et la plage Mae Nam.'
            },
            'ChiangMai': {
                title: 'Chiang Mai Highlands Golf & Spa',
                short: 'Golf montagnard à altitude élevée avec climat frais unique en Thaïlande.',
                full: 'Chiang Mai Highlands Golf & Spa Resort propose une expérience golfique unique en Thaïlande grâce à son altitude élevée offrant un climat frais. Ce parcours de 27 trous par Schmidt-Curley Design est situé sur un site spirituel historique avec un spa intégré primé et des vues panoramiques à 360° sur les montagnes du Nord.'
            },
            'blue-canyon': {
                title: 'Blue Canyon Country Club Phuket',
                short: 'Seul parcours triple-hôte du Johnnie Walker Classic au monde.',
                full: 'Blue Canyon Country Club à Phuket détient le record unique d\'être le seul parcours au monde à avoir accueilli trois fois le Johnnie Walker Classic. Ses deux parcours championship Canyon et Lakes ont vu Tiger Woods et Greg Norman écrire l\'histoire du golf international, notamment au célèbre trou 13 "Tiger Hole".'
            }
        };
    }
    
    init() {
        this.createModal();
        this.bindEvents();
    }
    
    /**
     * 🎨 CREATE MODAL WITH FIXED SEO PANEL
     */
    createModal() {
        this.modal = document.createElement('div');
        this.modal.className = 'fullscreen-modal';
        this.modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(10px);
            z-index: 99999;
            display: none;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        this.modal.innerHTML = `
            <!-- Image Container - Centered -->
            <div class="image-wrapper" style="
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
                cursor: pointer;
            ">
                <img class="modal-image" src="" alt="" style="
                    max-width: 95vw;
                    max-height: 85vh;
                    width: auto;
                    height: auto;
                    object-fit: contain;
                    border-radius: 8px;
                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
                    transition: transform 0.3s ease;
                    cursor: default;
                " onload="this.style.opacity='1'">
            </div>
            
            <!-- SEO Panel - FIXED POSITION -->
            <div class="seo-panel" style="
                position: fixed;
                bottom: 20px;
                left: 50%;
                transform: translateX(-50%);
                max-width: 600px;
                width: 90%;
                background: transparent;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 12px;
                padding: 16px;
                color: white;
                font-family: -apple-system, BlinkMacSystemFont, sans-serif;
                cursor: default;
                transition: all 0.3s ease;
                z-index: 100000;
            ">
                <h3 class="seo-title" style="
                    font-size: 1.1rem;
                    font-weight: 600;
                    margin: 0 0 8px 0;
                    color: #00ff88;
                    line-height: 1.3;
                "></h3>
                
                <p class="seo-short" style="
                    font-size: 0.95rem;
                    line-height: 1.4;
                    margin: 0 0 12px 0;
                    color: rgba(255, 255, 255, 0.9);
                "></p>
                
                <div class="seo-full" style="
                    font-size: 0.9rem;
                    line-height: 1.5;
                    color: rgba(255, 255, 255, 0.8);
                    margin: 8px 0 12px 0;
                    display: none;
                "></div>
                
                <button class="expand-btn" style="
                    background: linear-gradient(135deg, #00ff88, #00aaff);
                    color: black;
                    border: none;
                    padding: 12px 16px;
                    border-radius: 8px;
                    font-size: 0.9rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    min-height: 44px;
                    min-width: 120px;
                    touch-action: manipulation;
                    -webkit-tap-highlight-color: transparent;
                " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                    En savoir plus ↓
                </button>
            </div>
        `;
        
        // Add responsive CSS
        const style = document.createElement('style');
        style.textContent = `
            .fullscreen-modal {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            }
            
            @media (max-width: 768px) {
                .modal-image {
                    max-width: 98vw !important;
                    max-height: 75vh !important;
                }
                
                .seo-panel {
                    bottom: 15px !important;
                    width: 95% !important;
                    padding: 12px !important;
                    max-width: 500px !important;
                }
                
                .seo-title {
                    font-size: 1rem !important;
                }
                
                .seo-short {
                    font-size: 0.9rem !important;
                }
                
                .seo-full {
                    font-size: 0.85rem !important;
                }
                
                .expand-btn {
                    font-size: 0.85rem !important;
                    padding: 10px 14px !important;
                    min-height: 44px !important;
                    min-width: 110px !important;
                }
            }
            
            @media (min-width: 1200px) {
                .modal-image {
                    max-width: 90vw !important;
                    max-height: 80vh !important;
                }
                
                .seo-panel {
                    max-width: 700px !important;
                    bottom: 30px !important;
                }
            }
        `;
        document.head.appendChild(style);
        
        document.body.appendChild(this.modal);
    }
    
    /**
     * 🎛️ BIND EVENTS - OPTIMISÉ POUR TACTILE
     */
    bindEvents() {
        // ENTIRE MODAL BACKGROUND - Click anywhere to close
        this.modal.addEventListener('click', () => {
            this.close();
        });
        
        // ONLY IMAGE - Don't close when clicking image
        const img = this.modal.querySelector('.modal-image');
        img.addEventListener('click', (e) => {
            e.stopPropagation();
        });
        
        // ONLY SEO PANEL - Don't close when clicking SEO panel
        const seoPanel = this.modal.querySelector('.seo-panel');
        seoPanel.addEventListener('click', (e) => {
            e.stopPropagation();
        });
        
        // Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });
    }
    
    /**
     * 🔍 IDENTIFY GOLF FROM IMAGE SRC
     */
    identifyGolf(imageSrc) {
        if (imageSrc.includes('RedMountain')) return 'RedMountain';
        if (imageSrc.includes('Aquella')) return 'Aquella';
        if (imageSrc.includes('BlackMountain')) return 'BlackMountain';
        if (imageSrc.includes('Santiburi')) return 'Santiburi';
        if (imageSrc.includes('ChiangMai')) return 'ChiangMai';
        if (imageSrc.includes('blue-canyon')) return 'blue-canyon';
        return null;
    }
    
    /**
     * 📝 POPULATE SEO CONTENT - TACTILE OPTIMISÉ
     */
    populateSEOContent(golfInfo) {
        const title = this.modal.querySelector('.seo-title');
        const shortText = this.modal.querySelector('.seo-short');
        const fullText = this.modal.querySelector('.seo-full');
        const expandBtn = this.modal.querySelector('.expand-btn');
        
        if (golfInfo) {
            title.textContent = golfInfo.title;
            shortText.textContent = golfInfo.short;
            fullText.textContent = golfInfo.full;
        } else {
            // Default content for non-golf images
            title.textContent = 'Séjour Golf en Thaïlande';
            shortText.textContent = 'Découvrez nos destinations golf premium en Thaïlande avec GolfinThaï.';
            fullText.textContent = 'GolfinThaï vous propose des séjours golf d\'exception en Thaïlande sur les plus beaux parcours d\'Asie. Nos forfaits incluent hébergement premium, transferts privés et accès aux golfs les plus prestigieux du pays. Une expérience unique vous attend avec notre expertise locale et notre service personnalisé.';
        }
        
        // 🍎 SOLUTION TACTILE OPTIMISÉE POUR IPHONE
        let isExpanded = false;
        
        const handleToggle = (e) => {
            e.preventDefault();
            e.stopPropagation();
            
            if (isExpanded) {
                // Collapse
                fullText.style.display = 'none';
                expandBtn.textContent = 'En savoir plus ↓';
                isExpanded = false;
            } else {
                // Expand
                fullText.style.display = 'block';
                expandBtn.textContent = 'Réduire ↑';
                isExpanded = true;
            }
        };
        
        // Supprimer tous les anciens event listeners
        const newBtn = expandBtn.cloneNode(true);
        expandBtn.parentNode.replaceChild(newBtn, expandBtn);
        
        // Ajouter les événements selon le type d'appareil
        if (this.isTouchDevice) {
            // Pour les appareils tactiles (iPhone, iPad, Android)
            let touchStartTime = 0;
            
            newBtn.addEventListener('touchstart', (e) => {
                touchStartTime = Date.now();
                newBtn.style.transform = 'scale(0.95)';
                newBtn.style.opacity = '0.8';
            }, { passive: true });
            
            newBtn.addEventListener('touchend', (e) => {
                const touchDuration = Date.now() - touchStartTime;
                
                // Restaurer l'apparence
                newBtn.style.transform = 'scale(1)';
                newBtn.style.opacity = '1';
                
                // Déclencher seulement si c'est un tap rapide (< 500ms)
                if (touchDuration < 500) {
                    setTimeout(() => handleToggle(e), 50);
                }
            }, { passive: false });
            
            // Fallback pour le click normal
            newBtn.addEventListener('click', handleToggle, { passive: false });
            
        } else {
            // Pour les appareils non-tactiles (desktop)
            newBtn.addEventListener('click', handleToggle);
        }
        
        // Gérer l'annulation du touch
        newBtn.addEventListener('touchcancel', () => {
            newBtn.style.transform = 'scale(1)';
            newBtn.style.opacity = '1';
        }, { passive: true });
    }
    
    /**
     * 🖼️ OPEN IMAGE
     */
    open(imageSrc, imageAlt = '') {
        if (!imageSrc) {
            return;
        }
        
        // Set image
        const img = this.modal.querySelector('.modal-image');
        img.style.opacity = '0';
        img.src = imageSrc;
        img.alt = imageAlt || 'Golf en Thaïlande';
        
        // Populate SEO content
        const golfKey = this.identifyGolf(imageSrc);
        const golfInfo = golfKey ? this.golfData[golfKey] : null;
        this.populateSEOContent(golfInfo);
        
        // Show modal
        this.modal.style.display = 'flex';
        
        // Animate in
        requestAnimationFrame(() => {
            this.modal.style.opacity = '1';
        });
        
        // Lock body scroll
        document.body.style.overflow = 'hidden';
        
        this.isOpen = true;
        this.currentImage = imageSrc;
    }
    
    /**
     * ❌ CLOSE
     */
    close() {
        // Animate out
        this.modal.style.opacity = '0';
        
        setTimeout(() => {
            // Hide modal
            this.modal.style.display = 'none';
            
            // Unlock body scroll
            document.body.style.overflow = '';
            
            // Clear image and reset SEO panel
            const img = this.modal.querySelector('.modal-image');
            const fullText = this.modal.querySelector('.seo-full');
            const expandBtn = this.modal.querySelector('.expand-btn');
            
            img.src = '';
            img.style.opacity = '0';
            fullText.style.display = 'none';
            expandBtn.textContent = 'En savoir plus ↓';
            
            this.isOpen = false;
            this.currentImage = null;
        }, 300);
    }
}

// 🚀 INIT MODAL SYSTEM - VERSION PROPRE
let fullscreenModal;

document.addEventListener('DOMContentLoaded', () => {
    fullscreenModal = new FullscreenImageModal();
    
    // Global functions for compatibility
    window.openSimpleModal = (src, alt) => {
        if (fullscreenModal && src) {
            fullscreenModal.open(src, alt);
        }
    };
    
    window.closeSimpleModal = () => {
        if (fullscreenModal) {
            fullscreenModal.close();
        }
    };
});

// 🧹 SUPPRIMER LES MESSAGES DE DÉBOGAGE GÊNANTS
if (window.console && window.console.log) {
    const originalLog = console.log;
    console.log = function(...args) {
        const message = args.join(' ');
        // Bloquer les messages de débogage spécifiques
        const blockedTerms = [
            '🎯', '✅', '🔧', '🚨', '💎', '🔥', '📱', '🧪', 
            'fix', 'Fix', 'FIX', 'correction', 'Correction',
            'quantum', 'Quantum', 'QUANTUM', 'emergency', 'Emergency',
            'applied', 'success', 'SUCCESS', 'ready', 'READY'
        ];
        
        const shouldBlock = blockedTerms.some(term => message.includes(term));
        
        if (!shouldBlock) {
            originalLog.apply(console, args);
        }
    };
}
