/**
 * 🎯 LUXURY IMAGE MODAL - VERSION DOVITO NATIVE
 * Solution complète avec légende affichée par défaut
 * Plus de conflit, plus de patching - UX optimisée dès le départ
 */

class DovitoImageModal {
    constructor() {
        this.isOpen = false;
        this.modal = null;
        this.currentImage = null;
        this.golfData = this.initGolfData();
        
        this.init();
    }
    
    /**
     * 🏌️ DONNÉES GOLF POUR SEO COMPACT
     */
    initGolfData() {
        return {
            'RedMountain': {
                title: 'Red Mountain Golf Club Phuket',
                short: 'Parcours le plus spectaculaire d\'Asie sculpté dans une ancienne mine d\'étain.',
                full: 'Red Mountain Golf Club à Phuket est reconnu comme le parcours le plus spectaculaire d\'Asie. Sculpté dans une ancienne mine d\'étain, ce golf unique offre des formations rocheuses rouges iconiques et un dénivelé de 50 mètres au légendaire trou 17 "Drop Shot". L\'expérience de jeu est révolutionnaire avec des vues panoramiques exceptionnelles sur l\'île de Phuket.'
            },
            'Aquella': {
                title: 'Aquella Golf & Country Club',
                short: 'Élu Meilleur Golf de Luxe de Thaïlande 2024 avec 2,5 km de plage privée.',
                full: 'Aquella Golf & Country Club en Thaïlande a été élu Meilleur Golf de Luxe 2024. Ce parcours championship de 18 trous propose une expérience unique avec 2,5 kilomètres de plage privée exclusive, des tunnels de bambou spectaculaires et une vue imprenable sur la mer d\'Andaman. Le resort 5 étoiles offre un hébergement de luxe exceptionnel.'
            },
            'BlackMountain': {
                title: 'Black Mountain Golf Club Hua Hin',
                short: '#59 Golf Digest Top 100 mondial, seul parcours thaïlandais dans ce classement.',
                full: 'Black Mountain Golf Club à Hua Hin est classé #59 au Golf Digest Top 100 mondial, étant le seul parcours thaïlandais dans ce prestigieux classement. Ce parcours de 27 trous championship conçu par Phil Ryan s\'étend dans un cadre montagneux spectaculaire avec une academy professionnelle de renommée internationale.'
            },
            'Santiburi': {
                title: 'Santiburi Samui Country Club',
                short: 'Seul parcours 18 trous de Koh Samui dans un resort 5 étoiles.',
                full: 'Santiburi Samui Country Club est le seul parcours de golf 18 trous de l\'île de Koh Samui. Niché dans un resort 5 étoiles avec plage privée, ce golf offre des dénivelés spectaculaires jusqu\'à 180 mètres d\'altitude avec des panoramas époustouflants sur le Golfe de Thaïlande et la plage Mae Nam.'
            },
            'ChiangMai': {
                title: 'Chiang Mai Highlands Golf & Spa',
                short: 'Golf montagnard à altitude élevée avec climat frais unique en Thaïlande.',
                full: 'Chiang Mai Highlands Golf & Spa Resort propose une expérience golfique unique en Thaïlande grâce à son altitude élevée offrant un climat frais. Ce parcours de 27 trous par Schmidt-Curley Design est situé sur un site spirituel historique avec un spa intégré primé et des vues panoramiques à 360° sur les montagnes du Nord.'
            },
            'blue-canyon': {
                title: 'Blue Canyon Country Club Phuket',
                short: 'Seul parcours triple-hôte du Johnnie Walker Classic au monde.',
                full: 'Blue Canyon Country Club à Phuket détient le record unique d\'être le seul parcours au monde à avoir accueilli trois fois le Johnnie Walker Classic. Ses deux parcours championship Canyon et Lakes ont vu Tiger Woods et Greg Norman écrire l\'histoire du golf international, notamment au célèbre trou 13 "Tiger Hole".'
            }
        };
    }
    
    init() {
        this.createModal();
        this.bindEvents();
        console.log('🎯 Dovito Modal System - Initialized');
    }
    
    /**
     * 🎨 CREATE MODAL WITH DOVITO UX (LÉGENDE COMPLÈTE PAR DÉFAUT)
     */
    createModal() {
        this.modal = document.createElement('div');
        this.modal.className = 'dovito-fullscreen-modal';
        this.modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(10px);
            z-index: 99999;
            display: none;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        this.modal.innerHTML = `
            <!-- Image Container - Centered -->
            <div class="dovito-image-wrapper" style="
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
                cursor: pointer;
            ">
                <img class="dovito-modal-image" src="" alt="" style="
                    max-width: 95vw;
                    max-height: 85vh;
                    width: auto;
                    height: auto;
                    object-fit: contain;
                    border-radius: 8px;
                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
                    transition: transform 0.3s ease;
                    cursor: default;
                " onload="this.style.opacity='1'">
            </div>
            
            <!-- DOVITO SEO PANEL - LÉGENDE COMPLÈTE PAR DÉFAUT -->
            <div class="dovito-seo-panel" style="
                position: fixed;
                bottom: 20px;
                left: 50%;
                transform: translateX(-50%);
                max-width: 600px;
                width: 90%;
                background: rgba(0, 0, 0, 0.8);
                border: 1px solid rgba(0, 255, 136, 0.4);
                border-radius: 12px;
                padding: 16px;
                color: white;
                font-family: -apple-system, BlinkMacSystemFont, sans-serif;
                cursor: default;
                transition: all 0.3s ease;
                z-index: 100000;
                backdrop-filter: blur(10px);
            ">
                <h3 class="dovito-seo-title" style="
                    font-size: 1.1rem;
                    font-weight: 600;
                    margin: 0 0 8px 0;
                    color: #00ff88;
                    line-height: 1.3;
                "></h3>
                
                <p class="dovito-seo-short" style="
                    font-size: 0.95rem;
                    line-height: 1.4;
                    margin: 0 0 8px 0;
                    color: rgba(255, 255, 255, 0.9);
                "></p>
                
                <!-- DOVITO: TEXTE COMPLET VISIBLE PAR DÉFAUT -->
                <div class="dovito-seo-full" style="
                    font-size: 0.9rem;
                    line-height: 1.5;
                    color: rgba(255, 255, 255, 0.8);
                    margin: 8px 0 12px 0;
                    display: block;
                    border-top: 1px solid rgba(255, 255, 255, 0.1);
                    padding-top: 8px;
                "></div>
                
                <!-- DOVITO: BOUTON POUR RÉDUIRE -->
                <button class="dovito-expand-btn" style="
                    background: linear-gradient(135deg, #00ff88, #00aaff);
                    color: black;
                    border: none;
                    padding: 8px 14px;
                    border-radius: 8px;
                    font-size: 0.85rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    -webkit-appearance: none;
                    -webkit-tap-highlight-color: transparent;
                    touch-action: manipulation;
                    user-select: none;
                " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                    Réduire ↑
                </button>
            </div>
        `;
        
        // Add responsive CSS
        const style = document.createElement('style');
        style.textContent = `
            .dovito-fullscreen-modal {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            }
            
            @media (max-width: 768px) {
                .dovito-modal-image {
                    max-width: 98vw !important;
                    max-height: 75vh !important;
                }
                
                .dovito-seo-panel {
                    bottom: 15px !important;
                    width: 95% !important;
                    padding: 12px !important;
                    max-width: 500px !important;
                }
                
                .dovito-seo-title {
                    font-size: 1rem !important;
                }
                
                .dovito-seo-short {
                    font-size: 0.9rem !important;
                }
                
                .dovito-seo-full {
                    font-size: 0.85rem !important;
                }
                
                .dovito-expand-btn {
                    font-size: 0.8rem !important;
                    padding: 6px 12px !important;
                }
            }
            
            @media (min-width: 1200px) {
                .dovito-modal-image {
                    max-width: 90vw !important;
                    max-height: 80vh !important;
                }
                
                .dovito-seo-panel {
                    max-width: 700px !important;
                    bottom: 30px !important;
                }
            }
        `;
        document.head.appendChild(style);
        
        document.body.appendChild(this.modal);
    }
    
    /**
     * 🎛️ BIND EVENTS - CLICK ANYWHERE TO CLOSE
     */
    bindEvents() {
        // ENTIRE MODAL BACKGROUND - Click anywhere to close
        this.modal.addEventListener('click', () => {
            this.close();
        });
        
        // ONLY IMAGE - Don't close when clicking image
        const img = this.modal.querySelector('.dovito-modal-image');
        img.addEventListener('click', (e) => {
            e.stopPropagation();
        });
        
        // ONLY SEO PANEL - Don't close when clicking SEO panel
        const seoPanel = this.modal.querySelector('.dovito-seo-panel');
        seoPanel.addEventListener('click', (e) => {
            e.stopPropagation();
        });
        
        // Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });
    }
    
    /**
     * 🔍 IDENTIFY GOLF FROM IMAGE SRC
     */
    identifyGolf(imageSrc) {
        if (imageSrc.includes('RedMountain')) return 'RedMountain';
        if (imageSrc.includes('Aquella')) return 'Aquella';
        if (imageSrc.includes('BlackMountain')) return 'BlackMountain';
        if (imageSrc.includes('Santiburi')) return 'Santiburi';
        if (imageSrc.includes('ChiangMai')) return 'ChiangMai';
        if (imageSrc.includes('blue-canyon')) return 'blue-canyon';
        return null;
    }
    
    /**
     * 📝 POPULATE SEO CONTENT - DOVITO VERSION (TOUT AFFICHÉ PAR DÉFAUT)
     */
    populateSEOContent(golfInfo) {
        const title = this.modal.querySelector('.dovito-seo-title');
        const shortText = this.modal.querySelector('.dovito-seo-short');
        const fullText = this.modal.querySelector('.dovito-seo-full');
        const expandBtn = this.modal.querySelector('.dovito-expand-btn');
        
        if (golfInfo) {
            title.textContent = golfInfo.title;
            shortText.textContent = golfInfo.short;
            fullText.textContent = golfInfo.full;
        } else {
            // Default content for non-golf images
            title.textContent = 'Séjour Golf en Thaïlande';
            shortText.textContent = 'Découvrez nos destinations golf premium en Thaïlande avec GolfinThaï.';
            fullText.textContent = 'GolfinThaï vous propose des séjours golf d\'exception en Thaïlande sur les plus beaux parcours d\'Asie. Nos forfaits incluent hébergement premium, transferts privés et accès aux golfs les plus prestigieux du pays. Une expérience unique vous attend avec notre expertise locale et notre service personnalisé.';
        }
        
        // DOVITO LOGIC: Légende complète visible par défaut
        fullText.style.display = 'block';
        expandBtn.textContent = 'Réduire ↑';
        
        // DOVITO: Toggle functionality (RÉDUIRE/ÉTENDRE)
        const dovitoToggle = (e) => {
            e.stopPropagation();
            e.preventDefault();
            
            const isExpanded = fullText.style.display === 'block';
            
            if (isExpanded) {
                // Réduire (cacher texte complet)
                fullText.style.display = 'none';
                expandBtn.textContent = 'En savoir plus ↓';
            } else {
                // Étendre (montrer texte complet)
                fullText.style.display = 'block';
                expandBtn.textContent = 'Réduire ↑';
            }
        };
        
        // Remove any existing event listeners
        const newBtn = expandBtn.cloneNode(true);
        expandBtn.parentNode.replaceChild(newBtn, expandBtn);
        
        // Add clean event listeners
        newBtn.addEventListener('click', dovitoToggle);
        newBtn.addEventListener('touchend', dovitoToggle);
        
        // Enhanced iPhone/iPad support
        newBtn.addEventListener('touchstart', (e) => {
            newBtn.style.opacity = '0.7';
        }, { passive: true });
        
        newBtn.addEventListener('touchcancel', () => {
            newBtn.style.opacity = '1';
        }, { passive: true });
    }
    
    /**
     * 🖼️ OPEN IMAGE - DOVITO VERSION
     */
    open(imageSrc, imageAlt = '') {
        if (!imageSrc) {
            console.log('❌ Dovito Modal: No image source provided');
            return;
        }
        
        console.log('🎯 Dovito Modal: Opening image', imageSrc);
        
        // Set image
        const img = this.modal.querySelector('.dovito-modal-image');
        img.style.opacity = '0';
        img.src = imageSrc;
        img.alt = imageAlt || 'Golf en Thaïlande';
        
        // Populate SEO content with DOVITO logic
        const golfKey = this.identifyGolf(imageSrc);
        const golfInfo = golfKey ? this.golfData[golfKey] : null;
        this.populateSEOContent(golfInfo);
        
        // Show modal
        this.modal.style.display = 'flex';
        
        // Animate in
        requestAnimationFrame(() => {
            this.modal.style.opacity = '1';
        });
        
        // Lock body scroll
        document.body.style.overflow = 'hidden';
        
        this.isOpen = true;
        this.currentImage = imageSrc;
        
        console.log('✅ Dovito Modal: Image opened successfully');
    }
    
    /**
     * ❌ CLOSE
     */
    close() {
        console.log('🎯 Dovito Modal: Closing');
        
        // Animate out
        this.modal.style.opacity = '0';
        
        setTimeout(() => {
            // Hide modal
            this.modal.style.display = 'none';
            
            // Unlock body scroll
            document.body.style.overflow = '';
            
            // Clear image and reset SEO panel
            const img = this.modal.querySelector('.dovito-modal-image');
            const fullText = this.modal.querySelector('.dovito-seo-full');
            const expandBtn = this.modal.querySelector('.dovito-expand-btn');
            
            img.src = '';
            img.style.opacity = '0';
            
            // DOVITO: Reset to default state (expanded)
            fullText.style.display = 'block';
            expandBtn.textContent = 'Réduire ↑';
            
            this.isOpen = false;
            this.currentImage = null;
            
            console.log('✅ Dovito Modal: Closed successfully');
        }, 300);
    }
}

// 🚀 INIT DOVITO MODAL SYSTEM
let dovitoModal;

document.addEventListener('DOMContentLoaded', () => {
    dovitoModal = new DovitoImageModal();
    
    // Global functions for backward compatibility
    window.openSimpleModal = (src, alt) => {
        if (dovitoModal && src) {
            dovitoModal.open(src, alt);
        }
    };
    
    window.closeSimpleModal = () => {
        if (dovitoModal) {
            dovitoModal.close();
        }
    };
    
    // Compatibility with old variable name
    window.fullscreenModal = dovitoModal;
    
    console.log('🎯 Dovito Modal System - Ready!');
});
