/**
 * 🎯 QUANTUM ULTRA-COMPACT 50PX TESTER
 * Validates the ultimate compact layout:
 * - Weather widget 50px ultra-compact
 * - Weather + language shifted RIGHT
 * - Maximum space for "GolfinThaï" visibility
 */

// 🎯 ULTRA-COMPACT 50PX VALIDATION
window.quantumUltraCompact50pxTest = function() {
    console.log('🎯 RUNNING ULTRA-COMPACT 50PX TEST - FRÉRO\'S ULTIMATE VISION...\n');
    
    const tests = [];
    let allPassed = true;
    
    // Get elements
    const logoContainer = document.querySelector('.flex.items-center.space-x-3:first-child');
    const logoCircle = document.querySelector('.logo-container-bg');
    const logoTitle = document.querySelector('.header-logo-gradient');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const tempElement = document.querySelector('.weather-temp-mobile');
    const controls = document.querySelector('.lg\\:hidden.flex.items-center.space-x-3');
    const langSwitcher = document.querySelector('.language-switcher-mobile');
    
    if (!logoContainer || !logoTitle || !weatherWidget || !tempElement) {
        console.log('❌ CRITICAL: Essential elements not found!');
        return false;
    }
    
    console.log('🎯 ULTRA-COMPACT 50PX ANALYSIS:');
    
    // Test 1: Weather widget MUST be 50px
    const weatherRect = weatherWidget.getBoundingClientRect();
    const weatherUltraCompact = weatherRect.width >= 48 && weatherRect.width <= 55;
    
    console.log(`  Weather widget width: ${Math.round(weatherRect.width)}px (target: 50px)`);
    console.log(`  Ultra-compact 50px achieved: ${weatherUltraCompact ? '✅' : '❌'}`);
    
    tests.push({
        name: 'Weather widget 50px ultra-compact',
        passed: weatherUltraCompact,
        critical: true,
        details: weatherUltraCompact ? `${Math.round(weatherRect.width)}px - PERFECT! ✅` : `${Math.round(weatherRect.width)}px - NOT 50px ❌`
    });
    
    // Test 2: Logo container should have MAXIMUM space
    const logoRect = logoContainer.getBoundingClientRect();
    const logoMaxSpace = logoRect.width >= 115 && logoRect.width <= 125;
    
    console.log(`\n🏷️ LOGO SPACE ANALYSIS:`);
    console.log(`  Logo container: ${Math.round(logoRect.width)}px (target: ~120px)`);
    console.log(`  Maximum space achieved: ${logoMaxSpace ? '✅' : '❌'}`);
    
    tests.push({
        name: 'Logo container maximum space (120px)',
        passed: logoMaxSpace,
        critical: true,
        details: `${Math.round(logoRect.width)}px (target: 115-125px)`
    });
    
    // Test 3: Logo circle should be BIGGER
    if (logoCircle) {
        const circleRect = logoCircle.getBoundingClientRect();
        const logoBigger = circleRect.width >= 40 && circleRect.width <= 45;
        
        console.log(`  Logo circle size: ${Math.round(circleRect.width)}px (target: 42px)`);
        console.log(`  Logo is bigger: ${logoBigger ? '✅' : '❌'}`);
        
        tests.push({
            name: 'Bigger logo circle (42px)',
            passed: logoBigger,
            details: `${Math.round(circleRect.width)}px (target: 40-45px)`
        });
    }
    
    // Test 4: "GolfinThaï" text should be BIGGER and FULLY VISIBLE
    const logoText = logoTitle.textContent || '';
    const logoComplete = logoText.includes('GolfinThaï');
    const logoStyles = getComputedStyle(logoTitle);
    const logoFontSize = parseFloat(logoStyles.fontSize);
    const logoTextBigger = logoFontSize >= 14; // Should be ~0.95rem = 15px
    
    console.log(`  "GolfinThaï" text: "${logoText}"`);
    console.log(`  Text complete: ${logoComplete ? '✅' : '❌'}`);
    console.log(`  Font size: ${logoFontSize}px (target: 14px+)`);
    console.log(`  Text is bigger: ${logoTextBigger ? '✅' : '❌'}`);
    
    tests.push({
        name: '"GolfinThaï" bigger and complete',
        passed: logoComplete && logoTextBigger,
        critical: true,
        details: logoComplete && logoTextBigger ? 'Perfect text visibility ✅' : 'Text issues detected ❌'
    });
    
    // Test 5: Temperature should fit in ultra-compact 50px widget
    const tempText = tempElement.textContent || '--°';
    const tempRect = tempElement.getBoundingClientRect();
    const tempFitsUltraCompact = tempRect.width <= weatherRect.width - 2; // 2px tight margin
    
    console.log(`\n🌡️ ULTRA-COMPACT TEMPERATURE ANALYSIS:`);
    console.log(`  Temperature: "${tempText}"`);
    console.log(`  Temp width: ${Math.round(tempRect.width)}px in ${Math.round(weatherRect.width)}px widget`);
    console.log(`  Fits in 50px widget: ${tempFitsUltraCompact ? '✅' : '❌'}`);
    
    tests.push({
        name: 'Temperature fits in 50px widget',
        passed: tempFitsUltraCompact,
        details: `${Math.round(tempRect.width)}px in ${Math.round(weatherRect.width)}px`
    });
    
    // Test 6: Weather + language shifted RIGHT
    const screenWidth = window.innerWidth;
    const weatherCenterX = weatherRect.left + (weatherRect.width / 2);
    const screenCenterX = screenWidth / 2;
    const weatherShiftedRight = weatherCenterX > screenCenterX;
    
    console.log(`\n📐 RIGHT SHIFT ANALYSIS:`);
    console.log(`  Screen center: ${Math.round(screenCenterX)}px`);
    console.log(`  Weather center: ${Math.round(weatherCenterX)}px`);
    console.log(`  Weather shifted right: ${weatherShiftedRight ? '✅' : '❌'}`);
    
    tests.push({
        name: 'Weather widget shifted right',
        passed: weatherShiftedRight,
        details: weatherShiftedRight ? 'Positioned right of center ✅' : 'Not shifted enough ❌'
    });
    
    // Test 7: Language switcher also shifted right
    if (langSwitcher) {
        const langRect = langSwitcher.getBoundingClientRect();
        const langCenterX = langRect.left + (langRect.width / 2);
        const langShiftedRight = langCenterX > screenCenterX + 50; // Should be well right
        
        console.log(`  Language center: ${Math.round(langCenterX)}px`);
        console.log(`  Language shifted right: ${langShiftedRight ? '✅' : '❌'}`);
        
        tests.push({
            name: 'Language switcher shifted right',
            passed: langShiftedRight,
            details: langShiftedRight ? 'Well positioned right ✅' : 'Not shifted enough ❌'
        });
    }
    
    // Test 8: Overall space efficiency with 50px weather
    if (controls) {
        const controlsRect = controls.getBoundingClientRect();
        const totalUsed = logoRect.width + weatherRect.width + controlsRect.width;
        const efficiency = Math.round((totalUsed / screenWidth) * 100);
        
        console.log(`\n📊 50PX SPACE EFFICIENCY:`);
        console.log(`  Logo: ${Math.round(logoRect.width)}px`);
        console.log(`  Weather: ${Math.round(weatherRect.width)}px`);
        console.log(`  Controls: ${Math.round(controlsRect.width)}px`);
        console.log(`  Total: ${Math.round(totalUsed)}px of ${screenWidth}px (${efficiency}%)`);
        
        const efficiencyOptimal = efficiency >= 82 && efficiency <= 92;
        tests.push({
            name: 'Space efficiency with 50px weather',
            passed: efficiencyOptimal,
            details: `${efficiency}% efficiency (target: 82-92%)`
        });
    }
    
    // Test 9: Logo and weather separation (no overlap)
    const logoWeatherGap = weatherRect.left - logoRect.right;
    const noOverlap = logoWeatherGap >= 0;
    
    console.log(`\n🔍 SEPARATION CHECK:`);
    console.log(`  Gap between logo and weather: ${Math.round(logoWeatherGap)}px`);
    console.log(`  No overlap: ${noOverlap ? '✅' : '❌'}`);
    
    tests.push({
        name: 'Logo and weather properly separated',
        passed: noOverlap,
        critical: true,
        details: noOverlap ? `${Math.round(logoWeatherGap)}px gap ✅` : 'OVERLAPPING ❌'
    });
    
    // Test 10: Font optimization for bigger logo space
    const logoFontOptimal = logoFontSize >= 14 && logoFontSize <= 17;
    
    tests.push({
        name: 'Logo font optimized for space',
        passed: logoFontOptimal,
        details: `${logoFontSize}px (target: 14-17px)`
    });
    
    // Results summary
    console.log(`\n🧪 ULTRA-COMPACT 50PX TEST RESULTS:`);
    tests.forEach((test, i) => {
        const status = test.passed ? '✅' : (test.critical ? '🚨' : '❌');
        const priority = test.critical ? ' [CRITICAL]' : '';
        console.log(`  ${i + 1}. ${status} ${test.name}${priority} - ${test.details}`);
        if (!test.passed) allPassed = false;
    });
    
    console.log(`\n${allPassed ? '🎉' : '⚠️'} OVERALL RESULT: ${allPassed ? 'ULTRA-COMPACT 50PX PERFECTION!' : 'Some adjustments needed'}`);
    
    if (allPassed) {
        console.log('🎯 FRÉRO\'S ULTIMATE VISION ACHIEVED! 🏆');
        console.log('✅ Weather widget 50px ultra-compact!');
        console.log('✅ Weather + language shifted right!');
        console.log('✅ "GolfinThaï" maximum space and visibility!');
        console.log('✅ Bigger logo and text!');
        console.log('🔥 Mobile header PERFECTION level reached!');
    } else {
        console.log('🔧 Run quantumUltraCompact50pxFix() for final adjustments');
    }
    
    return allPassed;
};

// 🔧 ULTRA-COMPACT 50PX EMERGENCY FIX
window.quantumUltraCompact50pxFix = function() {
    console.log('🔧 APPLYING ULTRA-COMPACT 50PX EMERGENCY FIX...');
    
    const logoContainer = document.querySelector('.flex.items-center.space-x-3:first-child');
    const logoTitle = document.querySelector('.header-logo-gradient');
    const logoCircle = document.querySelector('.logo-container-bg');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const tempElement = document.querySelector('.weather-temp-mobile');
    
    if (!logoContainer || !logoTitle || !weatherWidget || !tempElement) {
        console.log('❌ Elements not found for 50px fix');
        return;
    }
    
    console.log('🎯 Forcing ultra-compact 50px layout...');
    
    // FORCE logo maximum space
    logoContainer.style.width = '118px';
    logoContainer.style.maxWidth = '118px';
    logoContainer.style.minWidth = '118px';
    logoContainer.style.flexDirection = 'row';
    logoContainer.style.gap = '0.5rem';
    logoContainer.style.overflow = 'visible';
    
    // FORCE bigger logo circle
    if (logoCircle) {
        logoCircle.style.width = '42px';
        logoCircle.style.height = '42px';
        logoCircle.style.minWidth = '42px';
        logoCircle.style.minHeight = '42px';
    }
    
    // FORCE bigger logo text
    logoTitle.style.fontSize = '0.95rem';
    logoTitle.style.fontWeight = '800';
    logoTitle.style.maxWidth = '70px';
    logoTitle.style.overflow = 'visible';
    logoTitle.style.whiteSpace = 'nowrap';
    logoTitle.style.zIndex = '2000';
    
    // FORCE ultra-compact 50px weather
    weatherWidget.style.width = '50px';
    weatherWidget.style.minWidth = '50px';
    weatherWidget.style.maxWidth = '52px';
    weatherWidget.style.padding = '0.3rem 0.1rem';
    weatherWidget.style.margin = '0 0.5rem 0 auto';
    weatherWidget.style.overflow = 'visible';
    
    // FORCE temperature fit in 50px
    tempElement.style.fontSize = '0.75rem';
    tempElement.style.fontWeight = '600';
    tempElement.style.padding = '0';
    tempElement.style.letterSpacing = '0.1px';
    tempElement.style.overflow = 'visible';
    tempElement.style.whiteSpace = 'nowrap';
    
    console.log('✅ Ultra-compact 50px fix applied!');
    console.log('🧪 Testing in 1 second...');
    
    setTimeout(() => {
        window.quantumUltraCompact50pxTest();
    }, 1000);
};

// 🎨 ULTRA-COMPACT VISUAL DEBUG
window.quantumUltraCompact50pxVisualDebug = function() {
    console.log('🎨 ULTRA-COMPACT 50PX VISUAL DEBUG...');
    
    const logoContainer = document.querySelector('.flex.items-center.space-x-3:first-child');
    const logoCircle = document.querySelector('.logo-container-bg');
    const logoTitle = document.querySelector('.header-logo-gradient');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const controls = document.querySelector('.lg\\:hidden.flex.items-center.space-x-3');
    
    if (!logoContainer || !weatherWidget || !controls) {
        console.log('❌ Elements not found for visual debug');
        return;
    }
    
    // Apply ultra-specific color coding
    logoContainer.style.border = '3px solid #FF1493'; // Hot pink for logo area
    logoContainer.style.backgroundColor = 'rgba(255, 20, 147, 0.1)';
    
    if (logoCircle) {
        logoCircle.style.border = '2px solid #FFD700'; // Gold for logo circle
        logoCircle.style.backgroundColor = 'rgba(255, 215, 0, 0.2)';
    }
    
    if (logoTitle) {
        logoTitle.style.border = '2px solid #00FF00'; // Bright green for text
        logoTitle.style.backgroundColor = 'rgba(0, 255, 0, 0.2)';
        logoTitle.style.textShadow = '0 0 5px #00FF00';
    }
    
    weatherWidget.style.border = '3px solid #FF4500'; // Orange red for 50px widget
    weatherWidget.style.backgroundColor = 'rgba(255, 69, 0, 0.15)';
    
    controls.style.border = '2px solid #4169E1'; // Royal blue for controls
    controls.style.backgroundColor = 'rgba(65, 105, 225, 0.1)';
    
    // Add measurement labels
    const screenWidth = window.innerWidth;
    const logoRect = logoContainer.getBoundingClientRect();
    const weatherRect = weatherWidget.getBoundingClientRect();
    
    console.log('🎨 Ultra-compact visual debug active for 20 seconds:');
    console.log('💗 HOT PINK = Logo container (should be ~120px - MAXIMUM SPACE)');
    console.log('🟡 GOLD = Logo circle (should be 42px - BIGGER)');
    console.log('🟢 BRIGHT GREEN = "GolfinThaï" text (should be visible and bigger)');
    console.log('🔥 ORANGE RED = Weather widget (should be 50px - ULTRA COMPACT)');
    console.log('🔵 ROYAL BLUE = Controls (should be shifted right)');
    console.log(`📏 Current measurements: Logo=${Math.round(logoRect.width)}px, Weather=${Math.round(weatherRect.width)}px`);
    console.log('👁️ MANUALLY VERIFY: Is the weather widget TINY (50px) and logo text BIG and CLEAR?');
    
    setTimeout(() => {
        logoContainer.style.border = '';
        logoContainer.style.backgroundColor = '';
        if (logoCircle) {
            logoCircle.style.border = '';
            logoCircle.style.backgroundColor = '';
        }
        if (logoTitle) {
            logoTitle.style.border = '';
            logoTitle.style.backgroundColor = '';
            logoTitle.style.textShadow = '';
        }
        weatherWidget.style.border = '';
        weatherWidget.style.backgroundColor = '';
        controls.style.border = '';
        controls.style.backgroundColor = '';
        console.log('🧹 Ultra-compact visual debug cleaned up');
    }, 20000);
};

// 📊 ULTRA-COMPACT SPACE ANALYSIS
window.analyzeUltraCompactSpace = function() {
    console.log('📊 ULTRA-COMPACT 50PX SPACE ANALYSIS...\n');
    
    const logoContainer = document.querySelector('.flex.items-center.space-x-3:first-child');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const controls = document.querySelector('.lg\\:hidden.flex.items-center.space-x-3');
    
    if (!logoContainer || !weatherWidget || !controls) {
        console.log('❌ Elements not found for analysis');
        return;
    }
    
    const logoRect = logoContainer.getBoundingClientRect();
    const weatherRect = weatherWidget.getBoundingClientRect();
    const controlsRect = controls.getBoundingClientRect();
    const screenWidth = window.innerWidth;
    
    const spaceGained = 70 - weatherRect.width; // Space gained from 70px → 50px
    const logoSpaceRatio = Math.round((logoRect.width / screenWidth) * 100);
    const weatherSpaceRatio = Math.round((weatherRect.width / screenWidth) * 100);
    
    console.log('📏 ULTRA-COMPACT ANALYSIS:');
    console.log(`Screen width: ${screenWidth}px`);
    console.log(`Logo space: ${Math.round(logoRect.width)}px (${logoSpaceRatio}% of screen)`);
    console.log(`Weather space: ${Math.round(weatherRect.width)}px (${weatherSpaceRatio}% of screen)`);
    console.log(`Controls space: ${Math.round(controlsRect.width)}px`);
    console.log(`Space gained from 50px widget: +${Math.round(spaceGained)}px for logo!`);
    
    // Efficiency rating
    const logoSpaceExcellent = logoSpaceRatio >= 35; // Should use good portion for logo
    const weatherSpaceMinimal = weatherSpaceRatio <= 18; // Should be minimal
    
    console.log(`\n📈 SPACE EFFICIENCY RATING:`);
    console.log(`Logo space usage: ${logoSpaceExcellent ? '🔥 EXCELLENT' : '⚠️ COULD BE BETTER'}`);
    console.log(`Weather space usage: ${weatherSpaceMinimal ? '🎯 MINIMAL (PERFECT)' : '❌ STILL TOO BIG'}`);
    
    return {
        logoWidth: Math.round(logoRect.width),
        weatherWidth: Math.round(weatherRect.width),
        spaceGained: Math.round(spaceGained),
        logoRatio: logoSpaceRatio,
        weatherRatio: weatherSpaceRatio,
        optimal: logoSpaceExcellent && weatherSpaceMinimal
    };
};

// 🚀 AUTO-TEST ON LOAD
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        console.log('\n🎯 QUANTUM ULTRA-COMPACT 50PX TESTER LOADED!\n');
        console.log('Testing Fréro\'s ultimate vision:');
        console.log('  🎯 Weather widget 50px ultra-compact');
        console.log('  🎯 Weather + language shifted RIGHT');
        console.log('  🎯 Maximum space for "GolfinThaï" visibility');
        console.log('  🎯 Bigger logo and text');
        console.log('\nAvailable commands:');
        console.log('  🧪 quantumUltraCompact50pxTest() - Test 50px layout');
        console.log('  🔧 quantumUltraCompact50pxFix() - Emergency 50px fix');
        console.log('  🎨 quantumUltraCompact50pxVisualDebug() - Visual boundaries (20s)');
        console.log('  📊 analyzeUltraCompactSpace() - Space efficiency analysis');
        console.log('\n🎯 Running auto-test in 3 seconds...\n');
        
        setTimeout(() => {
            window.quantumUltraCompact50pxTest();
        }, 3000);
        
    }, 2000);
});

console.log('🎯 QUANTUM ULTRA-COMPACT 50PX TESTER INITIALIZED!');