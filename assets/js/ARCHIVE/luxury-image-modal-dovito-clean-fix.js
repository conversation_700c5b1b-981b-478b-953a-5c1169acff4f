/**
 * 🚀 DOVITO MODAL SYSTEM - CLEAN FIX ANTI-DOUBLE-TOGGLE
 * Problème résolu : iPhone double-event causing toggle→re-toggle
 * Solution : Intelligent event management + debouncing + platform detection
 */

class DovitoToggleManager {
    constructor() {
        this.isToggling = false;
        this.lastToggleTime = 0;
        this.DEBOUNCE_TIME = 300; // 300ms protection minimum
        this.activeTimers = new Set();
        this.eventHandlers = new Map();
        this.touchDetected = false;
        this.isMobile = this.detectMobile();
        
        console.log(`🎯 DovitoToggleManager: Initialized (${this.isMobile ? 'MOBILE' : 'DESKTOP'} mode)`);
    }
    
    detectMobile() {
        return /iPhone|iPad|iPod|Android/i.test(navigator.userAgent) || 
               ('ontouchstart' in window) || 
               (navigator.maxTouchPoints > 0);
    }
    
    canToggle() {
        const now = Date.now();
        const timeSinceLastToggle = now - this.lastToggleTime;
        
        if (this.isToggling) {
            console.log(`⏸️ Toggle blocked: Already toggling`);
            return false;
        }
        
        if (timeSinceLastToggle < this.DEBOUNCE_TIME) {
            console.log(`⏸️ Toggle blocked: Debounce protection (${timeSinceLastToggle}ms < ${this.DEBOUNCE_TIME}ms)`);
            return false;
        }
        
        return true;
    }
    
    startToggle() {
        this.isToggling = true;
        this.lastToggleTime = Date.now();
        this.clearAllTimers();
        console.log(`🔄 Toggle started at ${this.lastToggleTime}`);
    }
    
    endToggle() {
        // Délai minimum pour éviter les événements fantômes
        setTimeout(() => {
            this.isToggling = false;
            console.log(`✅ Toggle completed`);
        }, 100);
    }
    
    clearAllTimers() {
        this.activeTimers.forEach(timer => clearTimeout(timer));
        this.activeTimers.clear();
    }
    
    addTimer(timerId) {
        this.activeTimers.add(timerId);
    }
    
    removeTimer(timerId) {
        this.activeTimers.delete(timerId);
        clearTimeout(timerId);
    }
}

class DovitoImageModal {
    constructor() {
        this.isOpen = false;
        this.modal = null;
        this.currentImage = null;
        this.golfData = this.initGolfData();
        this.toggleManager = new DovitoToggleManager();
        
        this.init();
    }
    
    /**
     * 🏌️ DONNÉES GOLF POUR SEO
     */
    initGolfData() {
        return {
            'RedMountain': {
                title: 'Red Mountain Golf Club Phuket',
                short: 'Parcours le plus spectaculaire d\'Asie sculpté dans une ancienne mine d\'étain.',
                full: 'Red Mountain Golf Club à Phuket est reconnu comme le parcours le plus spectaculaire d\'Asie. Sculpté dans une ancienne mine d\'étain, ce golf unique offre des formations rocheuses rouges iconiques et un dénivelé de 50 mètres au légendaire trou 17 "Drop Shot". L\'expérience de jeu est révolutionnaire avec des vues panoramiques exceptionnelles sur l\'île de Phuket.'
            },
            'Aquella': {
                title: 'Aquella Golf & Country Club',
                short: 'Élu Meilleur Golf de Luxe de Thaïlande 2024 avec 2,5 km de plage privée.',
                full: 'Aquella Golf & Country Club en Thaïlande a été élu Meilleur Golf de Luxe 2024. Ce parcours championship de 18 trous propose une expérience unique avec 2,5 kilomètres de plage privée exclusive, des tunnels de bambou spectaculaires et une vue imprenable sur la mer d\'Andaman. Le resort 5 étoiles offre un hébergement de luxe exceptionnel.'
            },
            'BlackMountain': {
                title: 'Black Mountain Golf Club Hua Hin',
                short: '#59 Golf Digest Top 100 mondial, seul parcours thaïlandais dans ce classement.',
                full: 'Black Mountain Golf Club à Hua Hin est classé #59 au Golf Digest Top 100 mondial, étant le seul parcours thaïlandais dans ce prestigieux classement. Ce parcours de 27 trous championship conçu par Phil Ryan s\'étend dans un cadre montagneux spectaculaire avec une academy professionnelle de renommée internationale.'
            },
            'Santiburi': {
                title: 'Santiburi Samui Country Club',
                short: 'Seul parcours 18 trous de Koh Samui dans un resort 5 étoiles.',
                full: 'Santiburi Samui Country Club est le seul parcours de golf 18 trous de l\'île de Koh Samui. Niché dans un resort 5 étoiles avec plage privée, ce golf offre des dénivelés spectaculaires jusqu\'à 180 mètres d\'altitude avec des panoramas époustouflants sur le Golfe de Thaïlande et la plage Mae Nam.'
            },
            'ChiangMai': {
                title: 'Chiang Mai Highlands Golf & Spa',
                short: 'Golf montagnard à altitude élevée avec climat frais unique en Thaïlande.',
                full: 'Chiang Mai Highlands Golf & Spa Resort propose une expérience golfique unique en Thaïlande grâce à son altitude élevée offrant un climat frais. Ce parcours de 27 trous par Schmidt-Curley Design est situé sur un site spirituel historique avec un spa intégré primé et des vues panoramiques à 360° sur les montagnes du Nord.'
            },
            'blue-canyon': {
                title: 'Blue Canyon Country Club Phuket',
                short: 'Seul parcours triple-hôte du Johnnie Walker Classic au monde.',
                full: 'Blue Canyon Country Club à Phuket détient le record unique d\'être le seul parcours au monde à avoir accueilli trois fois le Johnnie Walker Classic. Ses deux parcours championship Canyon et Lakes ont vu Tiger Woods et Greg Norman écrire l\'histoire du golf international, notamment au célèbre trou 13 "Tiger Hole".'
            }
        };
    }
    
    init() {
        this.createModal();
        this.bindEvents();
        console.log('🎯 Dovito Modal System - Initialized with CLEAN FIX');
    }
    
    /**
     * 🎨 CREATE MODAL WITH CLEAN EVENT ARCHITECTURE
     */
    createModal() {
        this.modal = document.createElement('div');
        this.modal.className = 'dovito-fullscreen-modal';
        this.modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(10px);
            z-index: 99999;
            display: none;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        this.modal.innerHTML = `
            <!-- Image Container - Centered -->
            <div class="dovito-image-wrapper" style="
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
                cursor: pointer;
            ">
                <img class="dovito-modal-image" src="" alt="" style="
                    max-width: 95vw;
                    max-height: 85vh;
                    width: auto;
                    height: auto;
                    object-fit: contain;
                    border-radius: 8px;
                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
                    transition: transform 0.3s ease;
                    cursor: default;
                " onload="this.style.opacity='1'">
            </div>
            
            <!-- DOVITO SEO PANEL - CLEAN EVENT SYSTEM -->
            <div class="dovito-seo-panel" style="
                position: fixed;
                bottom: 20px;
                left: 50%;
                transform: translateX(-50%);
                max-width: 600px;
                width: 90%;
                background: rgba(0, 0, 0, 0.9);
                border: 2px solid rgba(0, 255, 136, 0.6);
                border-radius: 16px;
                padding: 20px;
                color: white;
                font-family: -apple-system, BlinkMacSystemFont, sans-serif;
                cursor: default;
                transition: all 0.3s ease;
                z-index: 100000;
                backdrop-filter: blur(10px);
            ">
                <h3 class="dovito-seo-title" style="
                    font-size: 1.1rem;
                    font-weight: 600;
                    margin: 0 0 8px 0;
                    color: #00ff88;
                    line-height: 1.3;
                "></h3>
                
                <p class="dovito-seo-short" style="
                    font-size: 0.95rem;
                    line-height: 1.4;
                    margin: 0 0 8px 0;
                    color: rgba(255, 255, 255, 0.9);
                "></p>
                
                <!-- DOVITO: TEXTE COMPLET VISIBLE PAR DÉFAUT -->
                <div class="dovito-seo-full" style="
                    font-size: 0.9rem;
                    line-height: 1.5;
                    color: rgba(255, 255, 255, 0.8);
                    margin: 8px 0 20px 0;
                    display: block;
                    border-top: 1px solid rgba(255, 255, 255, 0.1);
                    padding-top: 8px;
                "></div>
                
                <!-- DOVITO: BOUTON CLEAN EVENT SYSTEM -->
                <div style="text-align: center; margin-top: 15px;">
                    <button class="dovito-expand-btn" style="
                        background: linear-gradient(135deg, #00ff88, #00aaff);
                        color: black;
                        border: none;
                        padding: 18px 30px;
                        border-radius: 12px;
                        font-size: 18px;
                        font-weight: bold;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        -webkit-appearance: none;
                        -webkit-tap-highlight-color: transparent;
                        touch-action: manipulation;
                        user-select: none;
                        min-width: 200px;
                        min-height: 60px;
                        box-shadow: 0 6px 20px rgba(0, 255, 136, 0.4);
                        position: relative;
                        z-index: 100001;
                        display: block;
                        margin: 0 auto;
                    ">
                        Réduire ↑
                    </button>
                </div>
            </div>
        `;
        
        // Add responsive CSS optimized for ALL platforms
        const style = document.createElement('style');
        style.textContent = `
            .dovito-fullscreen-modal {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            }
            
            /* MOBILE OPTIMIZATIONS */
            @media (max-width: 768px) {
                .dovito-modal-image {
                    max-width: 98vw !important;
                    max-height: 70vh !important;
                }
                
                .dovito-seo-panel {
                    bottom: 10px !important;
                    width: 95% !important;
                    padding: 15px !important;
                    max-width: 500px !important;
                    border-width: 3px !important;
                }
                
                .dovito-expand-btn {
                    font-size: 20px !important;
                    padding: 20px 35px !important;
                    min-width: 250px !important;
                    min-height: 70px !important;
                    box-shadow: 0 8px 25px rgba(0, 255, 136, 0.5) !important;
                    border-radius: 18px !important;
                }
                
                .dovito-expand-btn:active {
                    transform: scale(0.95) !important;
                    box-shadow: 0 4px 15px rgba(0, 255, 136, 0.7) !important;
                }
            }
            
            /* DESKTOP HOVER EFFECTS */
            @media (min-width: 769px) {
                .dovito-expand-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 30px rgba(0, 255, 136, 0.6);
                }
            }
        `;
        document.head.appendChild(style);
        
        document.body.appendChild(this.modal);
    }
    
    /**
     * 🎛️ BIND EVENTS - CLEAN SYSTEM
     */
    bindEvents() {
        // ENTIRE MODAL BACKGROUND - Click anywhere to close
        this.modal.addEventListener('click', () => {
            this.close();
        });
        
        // ONLY IMAGE - Don't close when clicking image
        const img = this.modal.querySelector('.dovito-modal-image');
        img.addEventListener('click', (e) => {
            e.stopPropagation();
        });
        
        // ONLY SEO PANEL - Don't close when clicking SEO panel
        const seoPanel = this.modal.querySelector('.dovito-seo-panel');
        seoPanel.addEventListener('click', (e) => {
            e.stopPropagation();
        });
        
        // Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });
    }
    
    /**
     * 🔍 IDENTIFY GOLF FROM IMAGE SRC
     */
    identifyGolf(imageSrc) {
        if (imageSrc.includes('RedMountain')) return 'RedMountain';
        if (imageSrc.includes('Aquella')) return 'Aquella';
        if (imageSrc.includes('BlackMountain')) return 'BlackMountain';
        if (imageSrc.includes('Santiburi')) return 'Santiburi';
        if (imageSrc.includes('ChiangMai')) return 'ChiangMai';
        if (imageSrc.includes('blue-canyon')) return 'blue-canyon';
        return null;
    }
    
    /**
     * 📝 POPULATE SEO CONTENT - CLEAN EVENT INTELLIGENCE
     */
    populateSEOContent(golfInfo) {
        const title = this.modal.querySelector('.dovito-seo-title');
        const shortText = this.modal.querySelector('.dovito-seo-short');
        const fullText = this.modal.querySelector('.dovito-seo-full');
        const expandBtn = this.modal.querySelector('.dovito-expand-btn');
        
        if (golfInfo) {
            title.textContent = golfInfo.title;
            shortText.textContent = golfInfo.short;
            fullText.textContent = golfInfo.full;
        } else {
            // Default content for non-golf images
            title.textContent = 'Séjour Golf en Thaïlande';
            shortText.textContent = 'Découvrez nos destinations golf premium en Thaïlande avec GolfinThaï.';
            fullText.textContent = 'GolfinThaï vous propose des séjours golf d\'exception en Thaïlande sur les plus beaux parcours d\'Asie. Nos forfaits incluent hébergement premium, transferts privés et accès aux golfs les plus prestigieux du pays. Une expérience unique vous attend avec notre expertise locale et notre service personnalisé.';
        }
        
        // DOVITO LOGIC: Légende complète visible par défaut
        fullText.style.display = 'block';
        expandBtn.textContent = 'Réduire ↑';
        
        // 🚀 CLEAN TOGGLE FUNCTION - ANTI-DOUBLE-EVENT
        const cleanToggle = (eventSource) => {
            // Protection contre double déclenchement
            if (!this.toggleManager.canToggle()) {
                return false;
            }
            
            this.toggleManager.startToggle();
            
            console.log(`🎯 CLEAN TOGGLE triggered by: ${eventSource}`);
            
            const isExpanded = fullText.style.display === 'block';
            
            if (isExpanded) {
                // Réduire (cacher texte complet)
                fullText.style.display = 'none';
                expandBtn.textContent = 'En savoir plus ↓';
                console.log('📦 Text REDUCED via CLEAN SYSTEM');
            } else {
                // Étendre (montrer texte complet)
                fullText.style.display = 'block';
                expandBtn.textContent = 'Réduire ↑';
                console.log('📖 Text EXPANDED via CLEAN SYSTEM');
            }
            
            this.toggleManager.endToggle();
            return true;
        };
        
        // 🎯 SMART EVENT BINDING - PLATFORM DETECTION
        this.bindToggleEvents(expandBtn, cleanToggle);
        
        console.log('✅ CLEAN EVENT handlers attached to button');
    }
    
    /**
     * 🔧 SMART EVENT BINDING - INTELLIGENT PLATFORM DETECTION
     */
    bindToggleEvents(button, toggleFunction) {
        // Clear any existing event handlers for this button
        this.clearButtonEvents(button);
        
        if (this.toggleManager.isMobile) {
            this.bindMobileEvents(button, toggleFunction);
        } else {
            this.bindDesktopEvents(button, toggleFunction);
        }
    }
    
    /**
     * 📱 MOBILE EVENT BINDING - TOUCH OPTIMIZED
     */
    bindMobileEvents(button, toggleFunction) {
        console.log('📱 Binding MOBILE events');
        
        let touchStartTime = 0;
        let touchMoved = false;
        let touchProcessed = false;
        
        // Touch Start - Visual feedback only
        const handleTouchStart = (e) => {
            touchStartTime = Date.now();
            touchMoved = false;
            touchProcessed = false;
            
            // Visual feedback
            button.style.transform = 'scale(0.95)';
            button.style.boxShadow = '0 4px 15px rgba(0, 255, 136, 0.7)';
            
            console.log('👆 Mobile TouchStart - Visual feedback applied');
        };
        
        // Touch Move - Detect if user is scrolling
        const handleTouchMove = (e) => {
            touchMoved = true;
            console.log('👆 Mobile TouchMove - Scroll detected, will cancel toggle');
        };
        
        // Touch End - Main logic
        const handleTouchEnd = (e) => {
            e.preventDefault();
            e.stopPropagation();
            
            // Reset visual state
            button.style.transform = 'scale(1)';
            button.style.boxShadow = '0 6px 20px rgba(0, 255, 136, 0.4)';
            
            // Check if this is a valid tap
            const touchDuration = Date.now() - touchStartTime;
            
            if (touchMoved) {
                console.log('👆 Mobile TouchEnd - Cancelled (scroll detected)');
                return;
            }
            
            if (touchDuration > 1000) {
                console.log('👆 Mobile TouchEnd - Cancelled (long press)');
                return;
            }
            
            if (touchProcessed) {
                console.log('👆 Mobile TouchEnd - Already processed');
                return;
            }
            
            touchProcessed = true;
            const success = toggleFunction('MOBILE_TOUCH');
            
            if (success) {
                // Prevent synthetic click event
                this.preventSyntheticClick();
                console.log('👆 Mobile TouchEnd - Toggle successful, click prevented');
            }
        };
        
        // Touch Cancel - Reset state
        const handleTouchCancel = () => {
            button.style.transform = 'scale(1)';
            button.style.boxShadow = '0 6px 20px rgba(0, 255, 136, 0.4)';
            touchProcessed = false;
            console.log('👆 Mobile TouchCancel - State reset');
        };
        
        // Bind touch events
        button.addEventListener('touchstart', handleTouchStart, { passive: true });
        button.addEventListener('touchmove', handleTouchMove, { passive: true });
        button.addEventListener('touchend', handleTouchEnd, { passive: false });
        button.addEventListener('touchcancel', handleTouchCancel, { passive: true });
        
        // Store handlers for cleanup
        this.toggleManager.eventHandlers.set(button, {
            touchstart: handleTouchStart,
            touchmove: handleTouchMove,
            touchend: handleTouchEnd,
            touchcancel: handleTouchCancel
        });
    }
    
    /**
     * 🖥️ DESKTOP EVENT BINDING - CLICK OPTIMIZED
     */
    bindDesktopEvents(button, toggleFunction) {
        console.log('🖥️ Binding DESKTOP events');
        
        const handleClick = (e) => {
            e.preventDefault();
            e.stopPropagation();
            
            const success = toggleFunction('DESKTOP_CLICK');
            
            if (success) {
                console.log('🖥️ Desktop Click - Toggle successful');
            }
        };
        
        // Bind click event
        button.addEventListener('click', handleClick, { passive: false });
        
        // Store handler for cleanup
        this.toggleManager.eventHandlers.set(button, {
            click: handleClick
        });
    }
    
    /**
     * 🚫 PREVENT SYNTHETIC CLICK AFTER TOUCH
     */
    preventSyntheticClick() {
        const preventClick = (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('🚫 Synthetic click prevented');
            
            // Remove this one-time listener
            document.removeEventListener('click', preventClick, true);
        };
        
        // Add temporary click preventer
        document.addEventListener('click', preventClick, true);
        
        // Safety cleanup after 500ms
        const timerId = setTimeout(() => {
            document.removeEventListener('click', preventClick, true);
            this.toggleManager.removeTimer(timerId);
        }, 500);
        
        this.toggleManager.addTimer(timerId);
    }
    
    /**
     * 🧹 CLEAR BUTTON EVENTS
     */
    clearButtonEvents(button) {
        const handlers = this.toggleManager.eventHandlers.get(button);
        if (!handlers) return;
        
        // Remove all event listeners
        Object.keys(handlers).forEach(eventType => {
            button.removeEventListener(eventType, handlers[eventType]);
        });
        
        // Clear from map
        this.toggleManager.eventHandlers.delete(button);
        
        console.log('🧹 Button events cleared');
    }
    
    /**
     * 🖼️ OPEN IMAGE - DOVITO VERSION CLEAN
     */
    open(imageSrc, imageAlt = '') {
        if (!imageSrc) {
            console.log('❌ Dovito Modal: No image source provided');
            return;
        }
        
        console.log('🎯 Dovito Modal: Opening image', imageSrc);
        
        // Set image
        const img = this.modal.querySelector('.dovito-modal-image');
        img.style.opacity = '0';
        img.src = imageSrc;
        img.alt = imageAlt || 'Golf en Thaïlande';
        
        // Populate SEO content with CLEAN EVENT logic
        const golfKey = this.identifyGolf(imageSrc);
        const golfInfo = golfKey ? this.golfData[golfKey] : null;
        this.populateSEOContent(golfInfo);
        
        // Show modal
        this.modal.style.display = 'flex';
        
        // Animate in
        requestAnimationFrame(() => {
            this.modal.style.opacity = '1';
        });
        
        // Lock body scroll
        document.body.style.overflow = 'hidden';
        
        this.isOpen = true;
        this.currentImage = imageSrc;
        
        console.log('✅ Dovito Modal: Image opened with CLEAN EVENTS');
    }
    
    /**
     * ❌ CLOSE - CLEAN CLEANUP
     */
    close() {
        console.log('🎯 Dovito Modal: Closing');
        
        // Clear all button events
        const expandBtn = this.modal.querySelector('.dovito-expand-btn');
        if (expandBtn) {
            this.clearButtonEvents(expandBtn);
        }
        
        // Clear all active timers
        this.toggleManager.clearAllTimers();
        
        // Animate out
        this.modal.style.opacity = '0';
        
        setTimeout(() => {
            // Hide modal
            this.modal.style.display = 'none';
            
            // Unlock body scroll
            document.body.style.overflow = '';
            
            // Clear image and reset SEO panel
            const img = this.modal.querySelector('.dovito-modal-image');
            const fullText = this.modal.querySelector('.dovito-seo-full');
            const expandBtn = this.modal.querySelector('.dovito-expand-btn');
            
            img.src = '';
            img.style.opacity = '0';
            
            // DOVITO: Reset to default state (expanded)
            if (fullText && expandBtn) {
                fullText.style.display = 'block';
                expandBtn.textContent = 'Réduire ↑';
            }
            
            this.isOpen = false;
            this.currentImage = null;
            
            console.log('✅ Dovito Modal: Closed successfully with CLEAN CLEANUP');
        }, 300);
    }
}

// 🚀 GLOBAL INIT - CLEAN SYSTEM
let dovitoModal;

document.addEventListener('DOMContentLoaded', () => {
    dovitoModal = new DovitoImageModal();
    
    // Global functions for backward compatibility
    window.openSimpleModal = (src, alt) => {
        if (dovitoModal && src) {
            dovitoModal.open(src, alt);
        }
    };
    
    window.closeSimpleModal = () => {
        if (dovitoModal) {
            dovitoModal.close();
        }
    };
    
    // Compatibility with old variable name
    window.fullscreenModal = dovitoModal;
    
    console.log('🎯 Dovito Modal System - Ready with CLEAN FIX ANTI-DOUBLE-TOGGLE!');
    console.log(`📱 Platform detected: ${dovitoModal.toggleManager.isMobile ? 'MOBILE' : 'DESKTOP'}`);
});

/**
 * 🎯 CLEAN FIX SUMMARY:
 * 
 * ✅ PROBLEMS SOLVED:
 * - Double/triple toggle on iPhone eliminated
 * - Platform-specific event handling (touch vs click)
 * - Intelligent debouncing with 300ms protection
 * - Synthetic click prevention after touch events
 * - Proper event cleanup on modal close
 * 
 * ✅ ARCHITECTURE:
 * - DovitoToggleManager: Intelligent state management
 * - Platform detection: Mobile vs Desktop optimization
 * - Event prioritization: Touch events > Click events
 * - Timer management: Automatic cleanup of conflicting timers
 * 
 * ✅ MOBILE OPTIMIZATIONS:
 * - touchstart: Visual feedback only
 * - touchmove: Scroll detection and cancellation
 * - touchend: Main toggle logic with validations
 * - touchcancel: Proper state reset
 * - Synthetic click prevention
 * 
 * ✅ DESKTOP OPTIMIZATIONS:
 * - Simple click event handling
 * - Hover effects for better UX
 * - No touch event interference
 * 
 * 🚀 RESULT: Perfect UX on ALL platforms!
 */
