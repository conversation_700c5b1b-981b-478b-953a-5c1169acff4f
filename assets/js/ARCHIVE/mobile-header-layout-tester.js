/**
 * 🎯 <PERSON>O<PERSON>LE HEADER LAYOUT TESTER
 * Validate temperature display and element spacing
 * QUANTUM DEVELOPER PRECISION LEVEL
 */

// 🌡️ TEST WEATHER WIDGET DISPLAY
window.testWeatherDisplay = function() {
    console.log('🌡️ Testing weather widget temperature display...');
    
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const tempElement = document.querySelector('.weather-temp-mobile');
    const locationElement = document.querySelector('.weather-location-mobile');
    
    if (!weatherWidget || !tempElement) {
        console.log('❌ Weather widget elements not found');
        return;
    }
    
    // Get computed styles
    const widgetStyles = getComputedStyle(weatherWidget);
    const tempStyles = getComputedStyle(tempElement);
    
    console.log('📊 Weather Widget Info:');
    console.log('Widget width:', widgetStyles.width);
    console.log('Widget max-width:', widgetStyles.maxWidth);
    console.log('Widget overflow:', widgetStyles.overflow);
    console.log('Temperature text:', tempElement.textContent);
    console.log('Temperature font-size:', tempStyles.fontSize);
    console.log('Temperature overflow:', tempStyles.overflow);
    
    // Check if temperature is fully visible
    const tempRect = tempElement.getBoundingClientRect();
    const widgetRect = weatherWidget.getBoundingClientRect();
    
    const isFullyVisible = tempRect.right <= (widgetRect.right + 5); // 5px tolerance
    
    console.log('🔍 Temperature visibility check:');
    console.log('Temperature right edge:', tempRect.right);
    console.log('Widget right edge:', widgetRect.right);
    console.log('✅ Temperature fully visible:', isFullyVisible);
    
    if (isFullyVisible) {
        console.log('🎉 Weather display SUCCESS! Temperature fully visible');
    } else {
        console.log('⚠️ Temperature might be cut off - needs adjustment');
    }
    
    return isFullyVisible;
};

// 🎯 TEST MOBILE HEADER SPACING
window.testMobileHeaderSpacing = function() {
    console.log('🎯 Testing mobile header element spacing...');
    
    const logo = document.querySelector('.flex.items-center.space-x-3:first-child');
    const weather = document.querySelector('.weather-widget-mobile');
    const controls = document.querySelector('.lg\\:hidden.flex.items-center.space-x-3');
    const hamburger = document.querySelector('#mobile-menu-btn');
    const langSwitcher = document.querySelector('.language-switcher-mobile');
    
    if (!logo || !weather || !controls) {
        console.log('❌ Header elements not found');
        return;
    }
    
    // Get positions
    const logoRect = logo.getBoundingClientRect();
    const weatherRect = weather.getBoundingClientRect();
    const controlsRect = controls.getBoundingClientRect();
    const hamburgerRect = hamburger ? hamburger.getBoundingClientRect() : null;
    const langRect = langSwitcher ? langSwitcher.getBoundingClientRect() : null;
    
    console.log('📍 Element positions:');
    console.log('🏷️ Logo:', {
        left: Math.round(logoRect.left),
        right: Math.round(logoRect.right),
        width: Math.round(logoRect.width)
    });
    console.log('🌡️ Weather:', {
        left: Math.round(weatherRect.left),
        right: Math.round(weatherRect.right),
        width: Math.round(weatherRect.width)
    });
    console.log('🌍 Language:', langRect ? {
        left: Math.round(langRect.left),
        right: Math.round(langRect.right),
        width: Math.round(langRect.width)
    } : 'not found');
    console.log('🍔 Hamburger:', hamburgerRect ? {
        left: Math.round(hamburgerRect.left),
        right: Math.round(hamburgerRect.right),
        width: Math.round(hamburgerRect.width)
    } : 'not found');
    
    // Check for overlaps
    const logoWeatherGap = weatherRect.left - logoRect.right;
    const weatherControlsGap = controlsRect.left - weatherRect.right;
    
    console.log('📏 Gaps between elements:');
    console.log('Logo → Weather gap:', Math.round(logoWeatherGap), 'px');
    console.log('Weather → Controls gap:', Math.round(weatherControlsGap), 'px');
    
    const hasGoodSpacing = logoWeatherGap >= 0 && weatherControlsGap >= 0;
    
    if (hasGoodSpacing) {
        console.log('✅ Mobile header spacing SUCCESS! All elements properly spaced');
    } else {
        console.log('⚠️ Elements might be overlapping - needs adjustment');
    }
    
    return hasGoodSpacing;
};

// 🔧 FORCE WEATHER WIDGET EXPANSION
window.forceWeatherExpansion = function() {
    console.log('🔧 Force expanding weather widget for temperature...');
    
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const tempElement = document.querySelector('.weather-temp-mobile');
    
    if (!weatherWidget || !tempElement) {
        console.log('❌ Weather elements not found');
        return;
    }
    
    // Apply nuclear expansion styles
    weatherWidget.style.minWidth = '75px';
    weatherWidget.style.maxWidth = '80px';
    weatherWidget.style.overflow = 'visible';
    weatherWidget.style.whiteSpace = 'nowrap';
    
    tempElement.style.overflow = 'visible';
    tempElement.style.whiteSpace = 'nowrap';
    tempElement.style.fontSize = '0.8rem';
    tempElement.style.fontWeight = '600';
    
    console.log('✅ Weather widget expansion applied!');
    
    setTimeout(() => {
        window.testWeatherDisplay();
    }, 500);
};

// 🎯 SHIFT LANGUAGE SWITCHER RIGHT
window.shiftLanguageSwitcher = function() {
    console.log('🎯 Shifting language switcher to the right...');
    
    const langSwitcher = document.querySelector('.language-switcher-mobile');
    
    if (!langSwitcher) {
        console.log('❌ Language switcher not found');
        return;
    }
    
    // Apply right shift styles
    langSwitcher.style.marginLeft = '0.75rem';
    langSwitcher.style.marginRight = '0.25rem';
    langSwitcher.style.maxWidth = '50px';
    langSwitcher.style.minWidth = '50px';
    
    const langBtn = langSwitcher.querySelector('.lang-btn-mobile');
    if (langBtn) {
        langBtn.style.padding = '4px 6px';
        langBtn.style.fontSize = '0.75rem';
        langBtn.style.minWidth = '48px';
        langBtn.style.maxWidth = '48px';
    }
    
    console.log('✅ Language switcher shifted right!');
    
    setTimeout(() => {
        window.testMobileHeaderSpacing();
    }, 500);
};

// 🎨 VISUAL DEBUG - Show element boundaries with precise colors
window.debugMobileHeader = function() {
    console.log('🎨 Adding visual debug to mobile header...');
    
    const logo = document.querySelector('.flex.items-center.space-x-3:first-child');
    const weather = document.querySelector('.weather-widget-mobile');
    const controls = document.querySelector('.lg\\:hidden.flex.items-center.space-x-3');
    const hamburger = document.querySelector('#mobile-menu-btn');
    const langSwitcher = document.querySelector('.language-switcher-mobile');
    
    // Apply colored borders
    if (logo) {
        logo.style.border = '2px solid red';
        logo.style.background = 'rgba(255, 0, 0, 0.05)';
    }
    
    if (weather) {
        weather.style.border = '2px solid orange';
        weather.style.background = 'rgba(255, 165, 0, 0.1)';
    }
    
    if (langSwitcher) {
        langSwitcher.style.border = '2px solid blue';
        langSwitcher.style.background = 'rgba(0, 0, 255, 0.05)';
    }
    
    if (hamburger) {
        hamburger.style.border = '2px solid green';
        hamburger.style.background = 'rgba(0, 255, 0, 0.05)';
    }
    
    console.log('✅ Visual debug applied:');
    console.log('🔴 Red = Logo container');
    console.log('🟠 Orange = Weather widget (should show full temperature)');
    console.log('🔵 Blue = Language switcher (shifted right)');
    console.log('🟢 Green = Hamburger menu (unchanged position)');
    
    setTimeout(() => {
        // Remove debug after 8 seconds
        if (logo) {
            logo.style.border = '';
            logo.style.background = '';
        }
        if (weather) {
            weather.style.border = '';
            weather.style.background = '';
        }
        if (langSwitcher) {
            langSwitcher.style.border = '';
            langSwitcher.style.background = '';
        }
        if (hamburger) {
            hamburger.style.border = '';
            hamburger.style.background = '';
        }
        console.log('🧹 Visual debug removed');
    }, 8000);
};

// 📏 MEASURE TEMPERATURE WIDTH
window.measureTemperatureWidth = function() {
    console.log('📏 Measuring temperature text width...');
    
    const tempElement = document.querySelector('.weather-temp-mobile');
    
    if (!tempElement) {
        console.log('❌ Temperature element not found');
        return;
    }
    
    const tempText = tempElement.textContent;
    const tempRect = tempElement.getBoundingClientRect();
    
    // Create a hidden span to measure text width
    const measureSpan = document.createElement('span');
    measureSpan.style.visibility = 'hidden';
    measureSpan.style.position = 'absolute';
    measureSpan.style.whiteSpace = 'nowrap';
    measureSpan.style.fontSize = getComputedStyle(tempElement).fontSize;
    measureSpan.style.fontWeight = getComputedStyle(tempElement).fontWeight;
    measureSpan.textContent = tempText;
    
    document.body.appendChild(measureSpan);
    const textWidth = measureSpan.getBoundingClientRect().width;
    document.body.removeChild(measureSpan);
    
    console.log('📊 Temperature measurement:');
    console.log('Temperature text:', `"${tempText}"`);
    console.log('Required width:', Math.round(textWidth), 'px');
    console.log('Current container width:', Math.round(tempRect.width), 'px');
    console.log('Container sufficient:', textWidth <= tempRect.width ? '✅' : '❌');
    
    if (textWidth > tempRect.width) {
        console.log(`⚠️ Need ${Math.round(textWidth - tempRect.width)}px more space for full temperature`);
    }
    
    return {
        textWidth: Math.round(textWidth),
        containerWidth: Math.round(tempRect.width),
        sufficient: textWidth <= tempRect.width
    };
};

// 🚀 COMPLETE LAYOUT TEST
window.testCompleteLayout = function() {
    console.log('🚀 Running complete mobile header layout test...');
    
    const weatherOK = window.testWeatherDisplay();
    const spacingOK = window.testMobileHeaderSpacing();
    const measurement = window.measureTemperatureWidth();
    
    console.log('\n📊 COMPLETE LAYOUT TEST RESULTS:');
    console.log('Weather display:', weatherOK ? '✅' : '❌');
    console.log('Element spacing:', spacingOK ? '✅' : '❌');
    console.log('Temperature width:', measurement.sufficient ? '✅' : '❌');
    
    if (weatherOK && spacingOK && measurement.sufficient) {
        console.log('🎉 MOBILE HEADER LAYOUT SUCCESS! All tests passed');
    } else {
        console.log('⚠️ Some issues detected - use force fix commands');
    }
    
    return weatherOK && spacingOK && measurement.sufficient;
};

// Auto-test on load
document.addEventListener('DOMContentLoaded', () => {
    // Wait for weather data to load
    setTimeout(() => {
        window.testCompleteLayout();
    }, 2000);
});

console.log(`
🎯 MOBILE HEADER LAYOUT TESTER LOADED:
- window.testWeatherDisplay() - Test temperature visibility
- window.testMobileHeaderSpacing() - Test element spacing
- window.measureTemperatureWidth() - Measure text width
- window.testCompleteLayout() - Complete test suite
- window.forceWeatherExpansion() - Force expand weather widget
- window.shiftLanguageSwitcher() - Shift language switcher right
- window.debugMobileHeader() - Visual debug (8s)
`);
