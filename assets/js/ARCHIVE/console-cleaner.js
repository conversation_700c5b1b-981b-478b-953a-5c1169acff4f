/**
 * 🧹 CONSOLE CLEANER - Supprime tous les messages de débogage gênants
 * À charger en premier pour intercepter tous les console.log
 */

(function() {
    'use strict';
    
    if (typeof window !== 'undefined' && window.console) {
        // Sauvegarder les fonctions originales
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;
        const originalInfo = console.info;
        
        // Termes à bloquer dans les messages
        const blockedTerms = [
            // Emojis de débogage
            '🎯', '✅', '🔧', '🚨', '💎', '🔥', '📱', '🧪', '⚡', '🚀', '🎨', '🔵', '🔻', '💥',
            '🍎', '📝', '🖼️', '❌', '🎉', '⚠️', '🔍', '💪', '🎛️', '📊', '🏆', '🧹', '🆔',
            
            // Termes techniques
            'fix', 'Fix', 'FIX', 'correction', 'Correction', 'CORRECTION',
            'quantum', 'Quantum', 'QUANTUM', 
            'emergency', 'Emergency', 'EMERGENCY',
            'tester', 'Tester', 'TESTER',
            'debug', 'Debug', 'DEBUG',
            'test', 'Test', 'TEST',
            
            // Messages de statut
            'applied', 'Applied', 'APPLIED',
            'success', 'Success', 'SUCCESS',
            'ready', 'Ready', 'READY',
            'completed', 'Completed', 'COMPLETED',
            'validated', 'Validated', 'VALIDATED',
            'patcher', 'Patcher', 'PATCHER',
            'loaded', 'Loaded', 'LOADED',
            
            // Termes spécifiques
            'Modal', 'modal', 'MODAL',
            'iPhone', 'iOS', 'iPad',
            'expand', 'Expand', 'EXPAND',
            'button', 'Button', 'BUTTON',
            'logo', 'Logo', 'LOGO',
            'temperature', 'Temperature',
            'compact', 'Compact', 'COMPACT',
            'ultra', 'Ultra', 'ULTRA',
            'force', 'Force', 'FORCE',
            'nuclear', 'Nuclear', 'NUCLEAR'
        ];
        
        // Fonction pour vérifier si un message doit être bloqué
        function shouldBlockMessage(args) {
            const message = args.join(' ').toLowerCase();
            return blockedTerms.some(term => message.includes(term.toLowerCase()));
        }
        
        // Remplacer console.log
        console.log = function(...args) {
            if (!shouldBlockMessage(args)) {
                originalLog.apply(console, args);
            }
        };
        
        // Remplacer console.warn
        console.warn = function(...args) {
            if (!shouldBlockMessage(args)) {
                originalWarn.apply(console, args);
            }
        };
        
        // Remplacer console.info
        console.info = function(...args) {
            if (!shouldBlockMessage(args)) {
                originalInfo.apply(console, args);
            }
        };
        
        // Garder console.error intact (erreurs importantes)
        // console.error = originalError;
        
        // Fonction pour restaurer la console originale (utile pour le débogage)
        window.restoreConsole = function() {
            console.log = originalLog;
            console.warn = originalWarn;
            console.error = originalError;
            console.info = originalInfo;
            originalLog('Console originale restaurée');
        };
        
        // Fonction pour activer le mode silencieux total
        window.silentMode = function() {
            console.log = function() {};
            console.warn = function() {};
            console.info = function() {};
            // console.error reste actif pour les vraies erreurs
        };
    }
})();
