/**
 * 🎨 MARKDOWN TO HTML + iOS ULTIMATE FIX - DEVS PROTOCOL
 * Convert **text** to <strong> + Fix iOS button guaranteed
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('🎨 Converting markdown & fixing iOS...');
    
    // ==========================================
    // 🎯 PARTIE 1: CONVERTIR **TEXT** EN <STRONG>
    // ==========================================
    
    function convertMarkdownToHTML() {
        const descriptions = document.querySelectorAll('.course-description span[data-translate]');
        
        descriptions.forEach(desc => {
            let content = desc.innerHTML;
            
            // Convert **text** to <strong>text</strong>
            content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            
            desc.innerHTML = content;
            
            // Mark as converted for CSS targeting
            desc.closest('.course-description').setAttribute('data-converted', 'true');
            
            console.log('✅ Converted markdown for:', desc.getAttribute('data-translate'));
        });
        
        console.log(`🎨 Converted ${descriptions.length} descriptions to HTML with green highlights`);
        
        // Show visual confirmation
        if (descriptions.length > 0) {
            const notice = document.createElement('div');
            notice.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: #065f46;
                color: white;
                padding: 10px 15px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 999999;
                font-weight: bold;
            `;
            notice.textContent = `✅ ${descriptions.length} descriptions avec couleurs vertes appliquées!`;
            document.body.appendChild(notice);
            
            setTimeout(() => notice.remove(), 3000);
        }
    }
    
    // Apply conversion
    setTimeout(convertMarkdownToHTML, 500);
    
    // ==========================================
    // 🍎 PARTIE 2: iOS BUTTON FIX ULTIMATE
    // ==========================================
    
    const isiOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    
    if (isiOS) {
        console.log('🍎 iOS detected - Applying ULTIMATE button fix');
        
        let attempts = 0;
        const maxAttempts = 20; // Plus d'essais
        
        function ultrateFiXiOSButton() {
            attempts++;
            const expandBtn = document.querySelector('.expand-btn');
            const fullText = document.querySelector('.seo-full');
            
            if (!expandBtn || !fullText) {
                if (attempts < maxAttempts) {
                    setTimeout(ultrateFiXiOSButton, 200);
                }
                return;
            }
            
            console.log('🍎 Applying iOS button fix - Attempt:', attempts);
            
            // NUCLEAR APPROACH: Replace completely
            const newBtn = document.createElement('button');
            newBtn.className = expandBtn.className;
            newBtn.textContent = expandBtn.textContent;
            newBtn.style.cssText = expandBtn.style.cssText + `
                user-select: none !important;
                -webkit-user-select: none !important;
                -webkit-touch-callout: none !important;
                cursor: pointer !important;
                pointer-events: auto !important;
                touch-action: manipulation !important;
            `;
            
            // iOS-specific handler
            const iosHandler = function(e) {
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();
                
                console.log('🍎 iOS button triggered!');
                
                const isExpanded = fullText.style.display === 'block';
                
                if (isExpanded) {
                    fullText.style.display = 'none';
                    newBtn.textContent = 'En savoir plus ↓';
                    console.log('🍎 Text collapsed');
                } else {
                    fullText.style.display = 'block';
                    newBtn.textContent = 'Réduire ↑';
                    console.log('🍎 Text expanded');
                }
            };
            
            // Multiple event bindings for iOS
            newBtn.addEventListener('touchstart', function(e) {
                e.stopPropagation();
            }, { passive: false });
            
            newBtn.addEventListener('touchend', iosHandler, { passive: false });
            newBtn.addEventListener('click', iosHandler, { passive: false });
            
            // For extra compatibility
            newBtn.ontouchend = iosHandler;
            newBtn.onclick = iosHandler;
            
            // Replace in DOM
            expandBtn.parentNode.replaceChild(newBtn, expandBtn);
            console.log('✅ iOS button replaced successfully');
        }
        
        // Observer for modal opening
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && 
                    mutation.attributeName === 'style' &&
                    mutation.target.classList.contains('fullscreen-modal')) {
                    
                    const modal = mutation.target;
                    if (modal.style.display === 'flex') {
                        setTimeout(ultrateFiXiOSButton, 50);
                        setTimeout(ultrateFiXiOSButton, 150);
                        setTimeout(ultrateFiXiOSButton, 300);
                    }
                }
            });
        });
        
        // Wait for modal creation
        setTimeout(() => {
            const modal = document.querySelector('.fullscreen-modal');
            if (modal) {
                observer.observe(modal, { 
                    attributes: true, 
                    attributeFilter: ['style'] 
                });
                console.log('✅ iOS modal observer set');
            }
        }, 1000);
    }
});
