/**
 * 🤖 GESTIONNAIRE API GEMINI - Système de fallback automatique
 * Utilise les 3 clés API avec rotation automatique en cas d'erreur
 */

class GeminiAPIManager {
    constructor() {
        this.config = window.GolfinThaiConfig.gemini;
        this.isInitialized = false;
        this.requestsCount = {};
        this.lastRequestTime = {};
        
        this.init();
    }

    /**
     * 🚀 INITIALISATION
     */
    init() {
        // Initialiser les compteurs pour chaque clé
        this.config.apiKeys.forEach((key, index) => {
            this.requestsCount[index] = 0;
            this.lastRequestTime[index] = 0;
        });
        
        this.isInitialized = true;
        console.log('🤖 Gemini API Manager initialisé avec', this.config.apiKeys.length, 'clés');
    }

    /**
     * 🔄 OBTENIR LA CLÉ ACTUELLE
     */
    getCurrentKey() {
        return this.config.apiKeys[this.config.currentKeyIndex];
    }

    /**
     * ⏭️ PASSER À LA CLÉ SUIVANTE
     */
    rotateToNextKey() {
        this.config.currentKeyIndex = (this.config.currentKeyIndex + 1) % this.config.apiKeys.length;
        console.log(`🔄 Rotation vers clé ${this.config.currentKeyIndex + 1}/${this.config.apiKeys.length}`);
        return this.getCurrentKey();
    }

    /**
     * ⏱️ VÉRIFIER RATE LIMITING
     */
    canMakeRequest(keyIndex = this.config.currentKeyIndex) {
        const now = Date.now();
        const lastRequest = this.lastRequestTime[keyIndex];
        const timeDiff = now - lastRequest;
        
        // Minimum 100ms entre les requêtes par clé
        return timeDiff > 100;
    }

    /**
     * 🧠 REQUÊTE GEMINI AVEC FALLBACK AUTOMATIQUE
     */
    async generateContent(prompt, options = {}) {
        if (!this.isInitialized) {
            throw new Error('GeminiAPIManager not initialized');
        }

        const mergedOptions = {
            ...this.config.requestConfig,
            ...options
        };

        let lastError = null;
        let attempts = 0;
        const maxAttempts = this.config.apiKeys.length * this.config.maxRetries;

        while (attempts < maxAttempts) {
            const currentKeyIndex = this.config.currentKeyIndex;
            const currentKey = this.getCurrentKey();

            try {
                // Vérifier rate limiting
                if (!this.canMakeRequest(currentKeyIndex)) {
                    console.log(`⏳ Rate limit atteint pour clé ${currentKeyIndex + 1}, rotation...`);
                    this.rotateToNextKey();
                    await this.delay(this.config.retryDelay);
                    attempts++;
                    continue;
                }

                console.log(`🤖 Tentative ${attempts + 1}/${maxAttempts} avec clé ${currentKeyIndex + 1}`);

                // Faire la requête
                const response = await this.makeGeminiRequest(currentKey, prompt, mergedOptions);
                
                // Mettre à jour les statistiques
                this.requestsCount[currentKeyIndex]++;
                this.lastRequestTime[currentKeyIndex] = Date.now();

                console.log(`✅ Succès avec clé ${currentKeyIndex + 1}`);
                return response;

            } catch (error) {
                lastError = error;
                attempts++;

                console.log(`❌ Erreur avec clé ${currentKeyIndex + 1}:`, error.message);

                // Analyser le type d'erreur
                if (this.isRateLimitError(error) || this.isQuotaError(error)) {
                    console.log(`🚫 Rate limit/Quota dépassé pour clé ${currentKeyIndex + 1}, rotation...`);
                    this.rotateToNextKey();
                } else if (this.isAuthError(error)) {
                    console.log(`🔑 Erreur d'auth pour clé ${currentKeyIndex + 1}, rotation...`);
                    this.rotateToNextKey();
                } else {
                    console.log(`⚠️ Erreur générique, retry dans ${this.config.retryDelay}ms...`);
                }

                // Délai avant retry
                if (attempts < maxAttempts) {
                    await this.delay(this.config.retryDelay);
                }
            }
        }

        // Toutes les tentatives ont échoué
        console.error('❌ Toutes les clés API ont échoué');
        throw new Error(`Gemini API failed after ${maxAttempts} attempts. Last error: ${lastError?.message}`);
    }

    /**
     * 📡 FAIRE LA REQUÊTE VERS L'API GEMINI
     */
    async makeGeminiRequest(apiKey, prompt, options) {
        const url = `${this.config.apiEndpoint}?key=${apiKey}`;
        
        const payload = {
            contents: [{
                parts: [{
                    text: prompt
                }]
            }],
            generationConfig: {
                temperature: options.temperature,
                maxOutputTokens: options.maxTokens,
                topP: options.topP,
                topK: options.topK
            }
        };

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(`HTTP ${response.status}: ${errorData.error?.message || response.statusText}`);
        }

        const data = await response.json();
        
        if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
            throw new Error('Invalid response format from Gemini API');
        }

        return data.candidates[0].content.parts[0].text;
    }

    /**
     * 🔍 DÉTECTER LES TYPES D'ERREURS
     */
    isRateLimitError(error) {
        return error.message.includes('429') || 
               error.message.toLowerCase().includes('rate limit') ||
               error.message.toLowerCase().includes('too many requests');
    }

    isQuotaError(error) {
        return error.message.toLowerCase().includes('quota') ||
               error.message.toLowerCase().includes('exceeded');
    }

    isAuthError(error) {
        return error.message.includes('401') || 
               error.message.includes('403') ||
               error.message.toLowerCase().includes('unauthorized') ||
               error.message.toLowerCase().includes('forbidden');
    }

    /**
     * ⏱️ DÉLAI ASYNCHRONE
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 📊 STATISTIQUES D'UTILISATION
     */
    getStats() {
        return {
            currentKeyIndex: this.config.currentKeyIndex,
            requestsPerKey: this.requestsCount,
            totalRequests: Object.values(this.requestsCount).reduce((a, b) => a + b, 0),
            lastRequestTimes: this.lastRequestTime
        };
    }

    /**
     * 🧪 TEST DE TOUTES LES CLÉS
     */
    async testAllKeys() {
        console.log('🧪 Test de toutes les clés API Gemini...');
        
        const results = [];
        
        for (let i = 0; i < this.config.apiKeys.length; i++) {
            const key = this.config.apiKeys[i];
            console.log(`Testing clé ${i + 1}...`);
            
            try {
                const testPrompt = "Dis juste 'Hello' en réponse.";
                const response = await this.makeGeminiRequest(key, testPrompt, {
                    temperature: 0.1,
                    maxTokens: 10
                });
                
                results.push({
                    keyIndex: i,
                    status: 'SUCCESS',
                    response: response
                });
                
                console.log(`✅ Clé ${i + 1} fonctionne`);
                
            } catch (error) {
                results.push({
                    keyIndex: i,
                    status: 'ERROR',
                    error: error.message
                });
                
                console.log(`❌ Clé ${i + 1} échoue:`, error.message);
            }
            
            // Délai entre les tests
            await this.delay(500);
        }
        
        return results;
    }
}

// 🚀 INITIALISATION GLOBALE
let geminiAPI;

document.addEventListener('DOMContentLoaded', () => {
    geminiAPI = new GeminiAPIManager();
    
    // Rendre disponible globalement
    window.geminiAPI = geminiAPI;
    
    console.log('🤖 Gemini API Manager prêt !');
});

// 🧪 FONCTION DE TEST GLOBALE
window.testGeminiKeys = async function() {
    if (!window.geminiAPI) {
        console.error('❌ Gemini API Manager non initialisé');
        return;
    }
    
    return await window.geminiAPI.testAllKeys();
};

// 💬 FONCTION D'EXEMPLE D'USAGE
window.askGemini = async function(prompt) {
    if (!window.geminiAPI) {
        console.error('❌ Gemini API Manager non initialisé');
        return;
    }
    
    try {
        const response = await window.geminiAPI.generateContent(prompt);
        console.log('🤖 Réponse Gemini:', response);
        return response;
    } catch (error) {
        console.error('❌ Erreur Gemini:', error);
        throw error;
    }
};

/**
 * 📖 EXEMPLE D'UTILISATION :
 * 
 * // Test de toutes les clés :
 * testGeminiKeys()
 * 
 * // Poser une question :
 * askGemini("Écris un slogan pour GolfinThaï en 10 mots max")
 * 
 * // Utilisation avancée :
 * geminiAPI.generateContent("Votre prompt", {
 *     temperature: 0.9,
 *     maxTokens: 500
 * })
 * 
 * // Voir les stats :
 * geminiAPI.getStats()
 */