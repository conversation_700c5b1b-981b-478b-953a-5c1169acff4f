/**
 * 🎯 QUANTUM NAVIGATION BUTTON - MICRO-INTERACTIONS
 * Premium button behaviors - Apple/Netflix level
 */

class QuantumNavButton {
    constructor() {
        this.buttons = [];
        this.init();
    }
    
    init() {
        console.log('🎯 Quantum Navigation Button - Initializing...');
        this.setupButtons();
        this.bindEvents();
        console.log('✅ Quantum Navigation Button ready');
    }
    
    /**
     * 🔧 SETUP BUTTONS: Find and enhance
     */
    setupButtons() {
        // Find existing contact buttons and enhance them
        const contactButtons = document.querySelectorAll('a[href="#contact"], .btn-primary');
        
        contactButtons.forEach(btn => {
            if (!btn.classList.contains('quantum-contact-btn')) {
                this.enhanceButton(btn);
            }
        });
        
        // Find quantum buttons
        const quantumButtons = document.querySelectorAll('.quantum-contact-btn');
        quantumButtons.forEach(btn => {
            this.buttons.push(btn);
            this.setupButtonInteractions(btn);
        });
    }
    
    /**
     * ✨ ENHANCE BUTTON: Transform existing button
     */
    enhanceButton(button) {
        // Store original classes and content
        const originalText = button.textContent.trim();
        const originalHref = button.getAttribute('href');
        
        // Add quantum classes
        button.classList.add('quantum-contact-btn');
        
        // Enhanced content with icon
        if (!button.querySelector('i')) {
            const icon = document.createElement('i');
            icon.className = 'fas fa-paper-plane';
            icon.setAttribute('aria-hidden', 'true');
            
            button.innerHTML = '';
            const span = document.createElement('span');
            span.textContent = originalText;
            
            button.appendChild(span);
            button.appendChild(icon);
        }
        
        // Preserve href
        if (originalHref) {
            button.setAttribute('href', originalHref);
        }
        
        this.buttons.push(button);
        this.setupButtonInteractions(button);
        
        console.log('✨ Button enhanced:', originalText);
    }
    
    /**
     * 🎮 SETUP BUTTON INTERACTIONS: Premium behaviors
     */
    setupButtonInteractions(button) {
        // Ripple effect on click
        button.addEventListener('click', (e) => {
            this.createRipple(e, button);
        });
        
        // Hover sound effect (optional)
        button.addEventListener('mouseenter', () => {
            this.onHoverEnter(button);
        });
        
        // Enhanced focus management
        button.addEventListener('focus', () => {
            this.onFocus(button);
        });
        
        // Keyboard interaction
        button.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                this.createRipple(e, button);
            }
        });
    }
    
    /**
     * 💫 CREATE RIPPLE: Click animation
     */
    createRipple(event, button) {
        // Remove existing ripple
        button.classList.remove('ripple');
        
        // Add ripple class
        setTimeout(() => {
            button.classList.add('ripple');
        }, 10);
        
        // Remove ripple class after animation
        setTimeout(() => {
            button.classList.remove('ripple');
        }, 600);
        
        // Add micro haptic feedback (if supported)
        if (navigator.vibrate) {
            navigator.vibrate(10);
        }
    }
    
    /**
     * 🌟 HOVER ENTER: Enhanced hover state
     */
    onHoverEnter(button) {
        // Add subtle transform hint
        button.style.transform = 'translateY(-1px) scale(1.01)';
        
        // Reset after micro delay
        setTimeout(() => {
            button.style.transform = '';
        }, 100);
    }
    
    /**
     * 🎯 FOCUS: Accessibility enhancement
     */
    onFocus(button) {
        // Enhanced focus indicator
        button.style.outline = '2px solid #A3D1C8';
        button.style.outlineOffset = '2px';
        
        // Remove on blur
        button.addEventListener('blur', () => {
            button.style.outline = '';
            button.style.outlineOffset = '';
        }, { once: true });
    }
    
    /**
     * 🔄 LOADING STATE: Form submission feedback
     */
    setLoadingState(button, isLoading = true) {
        if (isLoading) {
            button.classList.add('loading');
            button.style.pointerEvents = 'none';
            
            // Change icon to spinner
            const icon = button.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-spinner fa-spin';
            }
            
            // Change text
            const span = button.querySelector('span');
            if (span) {
                span.setAttribute('data-original', span.textContent);
                span.textContent = 'Envoi en cours...';
            }
        } else {
            button.classList.remove('loading');
            button.style.pointerEvents = '';
            
            // Reset icon
            const icon = button.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-paper-plane';
            }
            
            // Reset text
            const span = button.querySelector('span');
            if (span && span.getAttribute('data-original')) {
                span.textContent = span.getAttribute('data-original');
                span.removeAttribute('data-original');
            }
        }
    }
    
    /**
     * ✅ SUCCESS STATE: Positive feedback
     */
    setSuccessState(button, duration = 3000) {
        button.classList.add('success');
        
        // Change icon to checkmark
        const icon = button.querySelector('i');
        if (icon) {
            icon.className = 'fas fa-check';
        }
        
        // Change text temporarily
        const span = button.querySelector('span');
        if (span) {
            const originalText = span.textContent;
            span.textContent = 'Message envoyé !';
            
            // Reset after duration
            setTimeout(() => {
                button.classList.remove('success');
                icon.className = 'fas fa-paper-plane';
                span.textContent = originalText;
            }, duration);
        }
    }
    
    /**
     * 🎨 DYNAMIC THEMES: Color variations
     */
    setTheme(button, theme = 'primary') {
        // Remove existing theme classes
        button.classList.remove('secondary', 'success');
        
        if (theme !== 'primary') {
            button.classList.add(theme);
        }
    }
    
    /**
     * 🎛️ BIND EVENTS: Global interactions
     */
    bindEvents() {
        // Resize optimization
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.optimizeForViewport();
            }, 250);
        });
        
        // Form submission integration
        document.addEventListener('submit', (e) => {
            const form = e.target;
            if (form.querySelector('.quantum-contact-btn')) {
                const button = form.querySelector('.quantum-contact-btn');
                this.setLoadingState(button, true);
                
                // Auto-reset after 5 seconds if no success
                setTimeout(() => {
                    if (button.classList.contains('loading')) {
                        this.setLoadingState(button, false);
                    }
                }, 5000);
            }
        });
    }
    
    /**
     * 📱 OPTIMIZE FOR VIEWPORT: Responsive adjustments
     */
    optimizeForViewport() {
        const isMobile = window.innerWidth <= 768;
        
        this.buttons.forEach(button => {
            if (isMobile) {
                // Mobile optimizations
                button.style.fontSize = '0.9rem';
                button.style.padding = '0.7rem 1.25rem';
            } else {
                // Desktop optimizations
                button.style.fontSize = '';
                button.style.padding = '';
            }
        });
    }
    
    /**
     * 🧹 DESTROY: Cleanup
     */
    destroy() {
        this.buttons.forEach(button => {
            button.classList.remove('quantum-contact-btn', 'loading', 'success');
        });
        this.buttons = [];
        console.log('🧹 Quantum Navigation Button destroyed');
    }
}

// 🚀 INSTANT INITIALIZATION - No amateur delays!
document.addEventListener('DOMContentLoaded', () => {
    window.quantumNavButton = new QuantumNavButton();
    console.log('🎯 Quantum Navigation Button: INSTANT ready! ✨');
});

// 🎛️ GLOBAL API
window.setButtonLoading = (buttonSelector, isLoading = true) => {
    const button = document.querySelector(buttonSelector);
    if (button && window.quantumNavButton) {
        window.quantumNavButton.setLoadingState(button, isLoading);
    }
};

window.setButtonSuccess = (buttonSelector, duration = 3000) => {
    const button = document.querySelector(buttonSelector);
    if (button && window.quantumNavButton) {
        window.quantumNavButton.setSuccessState(button, duration);
    }
};