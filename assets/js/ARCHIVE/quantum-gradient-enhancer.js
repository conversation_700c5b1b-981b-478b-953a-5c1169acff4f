/**
 * QUANTUM GRADIENT ENHANCER - <PERSON><PERSON><PERSON><PERSON><PERSON> EMERGENCY FIX
 * CLEAN CODE - NO PARTICULES - WORKING GRADIENTS
 */

class QuantumGradientEnhancer {
    constructor() {
        this.gradientElements = [];
        this.isEnhanced = false;
        
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }
    
    init() {
        console.log('🌈 Quantum Gradient Enhancer - Clean Version...');
        
        setTimeout(() => {
            this.enhanceGradients();
            this.setupObserver();
        }, 500);
    }
    
    enhanceGradients() {
        const elements = document.querySelectorAll('.text-gradient');
        
        elements.forEach((el, index) => {
            console.log(`🔧 Fixing gradient on: ${el.textContent.trim()}`);
            this.forceGradient(el);
        });
        
        this.isEnhanced = true;
        console.log(`✅ Clean gradients applied: ${elements.length} elements`);
    }
    
    forceGradient(element) {
        // CLEAN APPLICATION - NO CONFLICTS
        element.style.background = `
            linear-gradient(135deg, 
                #00574B 0%, 
                #A3D1C8 20%, 
                #2d8b7f 40%,
                #4fcfa6 60%,
                #7fc4b8 80%, 
                #A3D1C8 100%)
        `;
        element.style.backgroundSize = '300% 300%';
        element.style.webkitBackgroundClip = 'text';
        element.style.webkitTextFillColor = 'transparent';
        element.style.backgroundClip = 'text';
        element.style.animation = 'shimmer 3s linear infinite';
        element.style.willChange = 'background-position';
        
        // Force restart animation
        element.style.animationPlayState = 'paused';
        element.offsetHeight; // Force reflow
        element.style.animationPlayState = 'running';
        
        console.log('✅ Gradient applied clean:', element.textContent.trim());
    }
    
    setupObserver() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.addedNodes.length > 0) {
                    const newGradients = document.querySelectorAll('.text-gradient:not([data-enhanced])');
                    newGradients.forEach(el => {
                        el.setAttribute('data-enhanced', 'true');
                        this.forceGradient(el);
                    });
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    debug() {
        const elements = document.querySelectorAll('.text-gradient');
        console.log('🔍 Gradient Debug:');
        console.log(`Total: ${elements.length}`);
        
        elements.forEach((el, i) => {
            const style = window.getComputedStyle(el);
            console.log(`${i}: ${el.textContent.trim()}`, {
                animation: style.animation,
                backgroundPosition: style.backgroundPosition
            });
        });
    }
}

// CLEAN INSTANTIATION
window.quantumGradientEnhancer = new QuantumGradientEnhancer();

// DEBUG FUNCTIONS
window.debugGradients = () => {
    if (window.quantumGradientEnhancer) {
        window.quantumGradientEnhancer.debug();
    }
};

window.forceRefreshGradients = () => {
    console.log('🔄 Force refresh...');
    if (window.quantumGradientEnhancer) {
        window.quantumGradientEnhancer.enhanceGradients();
    }
};

console.log('🌈 Clean Gradient Enhancer loaded');