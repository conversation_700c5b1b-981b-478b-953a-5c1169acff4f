/**
 * 🍎 iPhone Modal Expand Fix - Fallback simple et efficace
 * Script de secours pour s'assurer que le bouton "En savoir plus" fonctionne sur iPhone
 */

(function() {
    'use strict';
    
    // Détecter les appareils tactiles
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    
    if (!isTouchDevice && !isIOS) {
        return; // Pas besoin sur desktop
    }
    
    // Fonction pour attacher les événements tactiles aux boutons expand
    function fixExpandButtons() {
        const expandButtons = document.querySelectorAll('.expand-btn, [class*="expand"]');
        
        expandButtons.forEach(button => {
            if (button.dataset.fixedForTouch) {
                return; // Déjà corrigé
            }
            
            // Marquer comme corrigé
            button.dataset.fixedForTouch = 'true';
            
            // S'assurer que le bouton a les bonnes propriétés CSS pour le tactile
            button.style.touchAction = 'manipulation';
            button.style.webkitTapHighlightColor = 'transparent';
            button.style.minHeight = '44px';
            button.style.minWidth = '80px';
            button.style.cursor = 'pointer';
            
            // Ajouter les événements tactiles
            let touchStartTime = 0;
            let originalTransform = button.style.transform || '';
            
            button.addEventListener('touchstart', function(e) {
                touchStartTime = Date.now();
                button.style.transform = 'scale(0.95)';
                button.style.opacity = '0.8';
            }, { passive: true });
            
            button.addEventListener('touchend', function(e) {
                const touchDuration = Date.now() - touchStartTime;
                
                // Restaurer l'apparence
                button.style.transform = originalTransform;
                button.style.opacity = '1';
                
                // Si c'est un tap rapide, déclencher le clic
                if (touchDuration < 500) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // Déclencher l'événement click avec un petit délai
                    setTimeout(() => {
                        button.click();
                    }, 50);
                }
            }, { passive: false });
            
            button.addEventListener('touchcancel', function() {
                button.style.transform = originalTransform;
                button.style.opacity = '1';
            }, { passive: true });
        });
    }
    
    // Observer pour les nouveaux boutons qui apparaissent (modales dynamiques)
    function observeForNewButtons() {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            // Vérifier si c'est un bouton expand ou s'il en contient
                            if (node.classList && (node.classList.contains('expand-btn') || node.querySelector('.expand-btn'))) {
                                setTimeout(fixExpandButtons, 100);
                            }
                        }
                    });
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    // Démarrer les corrections
    function init() {
        // Corriger les boutons existants
        fixExpandButtons();
        
        // Observer pour les nouveaux boutons
        observeForNewButtons();
        
        // Vérifier périodiquement (fallback)
        setInterval(fixExpandButtons, 5000);
    }
    
    // Démarrer quand le DOM est prêt
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    // Démarrer aussi après un délai (fallback)
    setTimeout(init, 1000);
    
})();
