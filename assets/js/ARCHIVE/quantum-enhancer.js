/**
 * 💎 GOLFINTHAI BONUS FEATURES - WOW FACTOR EDITION
 * Advanced micro-interactions + Performance monitoring
 * "PUTAIN C'EST BEAU!" guaranteed features
 */

class GolfInThaiQuantumEnhancer {
    constructor() {
        this.features = {
            parallaxGradients: true,
            smartScrollEffects: true,
            adaptiveAnimations: true,
            performanceMonitor: true,
            visualFeedback: true
        };
        
        this.init();
    }
    
    init() {
        console.log('💎 GolfinThai Quantum Enhancer - Activation WOW MODE...');
        
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.activate());
        } else {
            this.activate();
        }
    }
    
    activate() {
        this.setupParallaxGradients();
        this.setupSmartScrollEffects();
        this.setupAdaptiveAnimations();
        this.setupPerformanceMonitor();
        this.setupVisualFeedback();
        
        console.log('✨ GolfinThai: WOW FACTOR ACTIVATED! 🔥');
    }    
    /**
     * 🌊 PARALLAX GRADIENTS: Mouse-following gradients
     */
    setupParallaxGradients() {
        if (!this.features.parallaxGradients) return;
        
        let mouseX = 0, mouseY = 0;
        let targetX = 0, targetY = 0;
        
        document.addEventListener('mousemove', (e) => {
            targetX = (e.clientX / window.innerWidth) * 100;
            targetY = (e.clientY / window.innerHeight) * 100;
        });
        
        const updateGradients = () => {
            // Smooth interpolation
            mouseX += (targetX - mouseX) * 0.1;
            mouseY += (targetY - mouseY) * 0.1;
            
            const gradientElements = document.querySelectorAll('.text-gradient');
            gradientElements.forEach(el => {
                if (el.style.background.includes('linear-gradient')) {
                    el.style.backgroundPosition = `${mouseX}% ${mouseY}%`;
                }
            });
            
            requestAnimationFrame(updateGradients);
        };
        
        updateGradients();
    }
    
    /**
     * 📜 SMART SCROLL EFFECTS: Intersection Observer magic
     */
    setupSmartScrollEffects() {
        if (!this.features.smartScrollEffects) return;
        
        const observerOptions = {
            root: null,
            rootMargin: '-10% 0px -10% 0px',
            threshold: [0, 0.1, 0.5, 0.9, 1]
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                const element = entry.target;
                const ratio = entry.intersectionRatio;
                
                // Gradient animation speed based on visibility
                if (element.classList.contains('text-gradient')) {
                    const animationSpeed = 0.5 + (ratio * 2); // 0.5s to 2.5s
                    element.style.animationDuration = `${animationSpeed}s`;
                }
                
                // Scale effect based on scroll position
                if (entry.isIntersecting) {
                    const scale = 0.95 + (ratio * 0.05); // 0.95 to 1.0
                    const opacity = 0.7 + (ratio * 0.3); // 0.7 to 1.0
                    
                    element.style.transform = `scale(${scale})`;
                    element.style.opacity = opacity;
                } else {
                    element.style.transform = 'scale(0.95)';
                    element.style.opacity = '0.7';
                }
            });
        }, observerOptions);
        
        // Observe all gradient elements and cards
        const targets = document.querySelectorAll('.text-gradient, .service-card, .course-card, .testimonial-card');
        targets.forEach(el => {
            el.style.transition = 'transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.6s ease';
            observer.observe(el);
        });
    }    
    /**
     * 🎭 ADAPTIVE ANIMATIONS: Device-aware animations
     */
    setupAdaptiveAnimations() {
        if (!this.features.adaptiveAnimations) return;
        
        const deviceCapabilities = this.detectDeviceCapabilities();
        
        if (deviceCapabilities.lowPower) {
            // Reduce animations for low-power devices
            document.documentElement.style.setProperty('--animation-speed', '0.2');
            console.log('🔋 Low-power device detected: Animations optimized');
        } else if (deviceCapabilities.highPerformance) {
            // Enhanced animations for high-performance devices
            this.enableAdvancedAnimations();
            console.log('🚀 High-performance device: Enhanced animations activated');
        }
        
        // Adaptive frame rate
        this.setupAdaptiveFrameRate();
    }
    
    detectDeviceCapabilities() {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        
        let gpuInfo = 'unknown';
        if (gl) {
            const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
            if (debugInfo) {
                gpuInfo = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
            }
        }
        
        const deviceMemory = navigator.deviceMemory || 4; // GB
        const hardwareConcurrency = navigator.hardwareConcurrency || 4;
        const connectionSpeed = navigator.connection?.effectiveType || '4g';
        
        return {
            lowPower: deviceMemory < 4 || hardwareConcurrency < 4 || connectionSpeed === 'slow-2g',
            highPerformance: deviceMemory >= 8 && hardwareConcurrency >= 8 && !(/mobile|android|iphone|ipad/i.test(navigator.userAgent)),
            gpuInfo,
            deviceMemory,
            cores: hardwareConcurrency
        };
    }    
    enableAdvancedAnimations() {
        console.log('🎭 Advanced animations enabled (NO PARTICLES)');
        
        // Add 3D hover effects to cards ONLY
        const cards = document.querySelectorAll('.service-card, .course-card');
        cards.forEach(card => {
            this.add3DHoverEffect(card);
        });
        
        // NO PARTICLES - CLEAN VERSION
    }
    
    add3DHoverEffect(card) {
        card.addEventListener('mousemove', (e) => {
            const rect = card.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            
            const rotateX = (y - centerY) / centerY * -5; // REDUCED intensity
            const rotateY = (x - centerX) / centerX * 5;
            
            card.style.transform = `
                perspective(1000px) 
                rotateX(${rotateX}deg) 
                rotateY(${rotateY}deg) 
                translateZ(5px)
            `;
        });
        
        card.addEventListener('mouseleave', () => {
            card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateZ(0)';
        });
    }    
    setupAdaptiveFrameRate() {
        let frameCount = 0;
        let lastTime = performance.now();
        let fps = 60;
        
        const measureFPS = () => {
            frameCount++;
            const currentTime = performance.now();
            
            if (currentTime - lastTime >= 1000) {
                fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
                frameCount = 0;
                lastTime = currentTime;
                
                // Adapt animations based on FPS
                if (fps < 30) {
                    document.documentElement.style.setProperty('--animation-speed', '0.5');
                } else if (fps > 50) {
                    document.documentElement.style.setProperty('--animation-speed', '1');
                }
            }
            
            requestAnimationFrame(measureFPS);
        };
        
        measureFPS();
    }
    
    /**
     * 📊 PERFORMANCE MONITOR: Real-time optimization
     */
    setupPerformanceMonitor() {
        if (!this.features.performanceMonitor) return;
        
        const monitor = {
            fps: 0,
            memory: 0,
            loadTime: 0
        };
        
        // Monitor Core Web Vitals
        this.monitorWebVitals(monitor);
        
        // Monitor resource loading
        this.monitorResourceLoading(monitor);
        
        // Performance dashboard (dev mode)
        if (window.location.search.includes('debug=performance')) {
            this.createPerformanceDashboard(monitor);
        }
    }    
    monitorWebVitals(monitor) {
        // FCP (First Contentful Paint)
        new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
                if (entry.name === 'first-contentful-paint') {
                    console.log('🎯 FCP:', entry.startTime.toFixed(2), 'ms');
                }
            }
        }).observe({ entryTypes: ['paint'] });
        
        // LCP (Largest Contentful Paint)
        new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
                console.log('🎯 LCP:', entry.startTime.toFixed(2), 'ms');
            }
        }).observe({ entryTypes: ['largest-contentful-paint'] });
        
        // CLS (Cumulative Layout Shift)
        new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
                if (!entry.hadRecentInput) {
                    console.log('🎯 CLS:', entry.value.toFixed(4));
                }
            }
        }).observe({ entryTypes: ['layout-shift'] });
    }
    
    monitorResourceLoading(monitor) {
        window.addEventListener('load', () => {
            const navTiming = performance.getEntriesByType('navigation')[0];
            monitor.loadTime = navTiming.loadEventEnd - navTiming.fetchStart;
            
            console.log('⏱️ Total Load Time:', monitor.loadTime.toFixed(2), 'ms');
            
            // Check for slow resources
            const resources = performance.getEntriesByType('resource');
            const slowResources = resources.filter(r => r.duration > 1000);
            
            if (slowResources.length > 0) {
                console.warn('🐌 Slow resources detected:', slowResources);
            }
        });
    }    
    createPerformanceDashboard(monitor) {
        const dashboard = document.createElement('div');
        dashboard.id = 'performance-dashboard';
        dashboard.style.cssText = `
            position: fixed; bottom: 20px; right: 20px; z-index: 10000;
            background: rgba(13, 27, 26, 0.95); backdrop-filter: blur(20px);
            color: #A3D1C8; padding: 15px; border-radius: 10px;
            font-family: 'Monaco', monospace; font-size: 12px;
            border: 1px solid rgba(0, 87, 75, 0.3);
            min-width: 250px;
        `;
        dashboard.innerHTML = `
            <div><strong>🎯 GolfinThai Performance</strong></div>
            <div>FPS: <span id="fps-display">--</span></div>
            <div>Memory: <span id="memory-display">--</span> MB</div>
            <div>Load: <span id="load-display">--</span> ms</div>
            <div>Device: <span id="device-display">--</span></div>
        `;
        
        document.body.appendChild(dashboard);
        
        // Update dashboard
        setInterval(() => {
            document.getElementById('fps-display').textContent = monitor.fps || '--';
            document.getElementById('memory-display').textContent = 
                (performance.memory?.usedJSHeapSize / 1048576).toFixed(1) || '--';
            document.getElementById('load-display').textContent = monitor.loadTime.toFixed(0);
            document.getElementById('device-display').textContent = 
                this.detectDeviceCapabilities().highPerformance ? '🚀 High' : '📱 Standard';
        }, 1000);
    }
    
    /**
     * 👆 VISUAL FEEDBACK: Micro-interactions
     */
    setupVisualFeedback() {
        if (!this.features.visualFeedback) return;
        
        // Enhanced button feedback
        this.setupButtonFeedback();
        
        // Scroll progress indicator
        this.setupScrollProgress();
        
        // Loading states
        this.setupLoadingStates();
    }    
    setupButtonFeedback() {
        const buttons = document.querySelectorAll('button, .btn, .smart-read-more');
        
        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                // Ripple effect
                const ripple = document.createElement('span');
                const rect = button.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.cssText = `
                    position: absolute; left: ${x}px; top: ${y}px;
                    width: ${size}px; height: ${size}px;
                    background: rgba(163, 209, 200, 0.4);
                    border-radius: 50%; pointer-events: none;
                    transform: scale(0); animation: ripple 0.6s ease-out;
                `;
                
                button.style.position = 'relative';
                button.style.overflow = 'hidden';
                button.appendChild(ripple);
                
                setTimeout(() => ripple.remove(), 600);
            });
        });
    }
    
    setupScrollProgress() {
        const progressBar = document.createElement('div');
        progressBar.style.cssText = `
            position: fixed; top: 0; left: 0; right: 0; height: 3px;
            background: linear-gradient(90deg, #00574B, #A3D1C8);
            transform-origin: left; transform: scaleX(0);
            z-index: 10001; transition: transform 0.1s ease;
        `;
        document.body.appendChild(progressBar);
        
        const updateProgress = () => {
            const scrolled = window.pageYOffset;
            const total = document.documentElement.scrollHeight - window.innerHeight;
            const progress = scrolled / total;
            
            progressBar.style.transform = `scaleX(${Math.min(progress, 1)})`;
        };
        
        window.addEventListener('scroll', updateProgress, { passive: true });
    }    
    setupLoadingStates() {
        // Add loading animation to images
        const images = document.querySelectorAll('img[loading="lazy"]');
        
        images.forEach(img => {
            if (!img.complete) {
                img.style.filter = 'blur(5px)';
                img.style.transition = 'filter 0.3s ease';
                
                img.addEventListener('load', () => {
                    img.style.filter = 'none';
                }, { once: true });
            }
        });
        
        // Skeleton loading for cards
        const cards = document.querySelectorAll('.service-card, .course-card');
        cards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, Math.random() * 500 + 200);
        });
    }
}

// 🎨 CLEAN CSS - NO PARTICLES
const additionalCSS = `
@keyframes ripple {
    to { transform: scale(4); opacity: 0; }
}

/* Enhanced card hover effects - CLEAN VERSION */
.service-card, .course-card {
    transition: all 0.3s ease;
    transform-style: preserve-3d;
}

/* Loading skeleton animation */
@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.loading-skeleton {
    background: linear-gradient(90deg, 
        rgba(255,255,255,0.1) 25%, 
        rgba(255,255,255,0.2) 50%, 
        rgba(255,255,255,0.1) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}
`;
// Inject additional CSS
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalCSS;
document.head.appendChild(styleSheet);

// 🚀 AUTO-INITIALIZATION
window.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        window.golfInThaiQuantumEnhancer = new GolfInThaiQuantumEnhancer();
    }, 1000);
});

// 🎛️ GLOBAL DEBUG FUNCTIONS
window.debugQuantumEnhancer = () => {
    if (window.golfInThaiQuantumEnhancer) {
        console.log('💎 Quantum Enhancer Status:', window.golfInThaiQuantumEnhancer.features);
        console.log('📊 Device Capabilities:', window.golfInThaiQuantumEnhancer.detectDeviceCapabilities());
    }
};

window.togglePerformanceMode = () => {
    const url = new URL(window.location);
    if (url.search.includes('debug=performance')) {
        url.searchParams.delete('debug');
    } else {
        url.searchParams.set('debug', 'performance');
    }
    window.location.href = url.toString();
};

console.log('💎 GolfinThai Quantum Enhancer: Loaded and ready for WOW! 🚀');