/**
 * 🍎 FIX iOS "EN SAVOIR PLUS" - SOLUTION TACTILE
 * Corrige le problème du bouton "En savoir plus" sur iPhone/iPad
 */

class iOSModalFix {
    constructor() {
        this.init();
    }
    
    init() {
        console.log('🍎 iOS Modal Fix - Initializing touch-friendly modal');
        
        // Attendre que le DOM et les modales soient prêts
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                this.fixModalTouchEvents();
            }, 1000); // Attendre que luxury-image-modal soit initialisé
        });
    }
    
    /**
     * 🎯 FIX MODAL TOUCH EVENTS - iOS COMPATIBLE
     */
    fixModalTouchEvents() {
        // Observer pour détecter quand une modale s'ouvre
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                    const modal = mutation.target;
                    if (modal.classList.contains('fullscreen-modal') && 
                        modal.style.display === 'flex') {
                        console.log('🍎 Modal détectée - Applying iOS fixes');
                        this.applyiOSFixes(modal);
                    }
                }
            });
        });
        
        // Observer les modales existantes
        const modals = document.querySelectorAll('.fullscreen-modal');
        modals.forEach(modal => {
            observer.observe(modal, { 
                attributes: true, 
                attributeFilter: ['style'] 
            });
        });
        
        console.log('✅ iOS Modal Observer active');
    }
    
    /**
     * 🛠️ APPLY iOS SPECIFIC FIXES
     */
    applyiOSFixes(modal) {
        const expandBtn = modal.querySelector('.expand-btn');
        const fullText = modal.querySelector('.seo-full');
        
        if (!expandBtn || !fullText) return;
        
        console.log('🍎 Applying iOS touch fixes to expand button');
        
        // Retirer les anciens event listeners
        expandBtn.onclick = null;
        
        // CSS mobile-friendly
        expandBtn.style.cssText += `
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            touch-action: manipulation;
            cursor: pointer;
            position: relative;
            z-index: 999999;
        `;
        
        // Ajouter classe pour état
        if (!fullText.classList.contains('seo-collapsed')) {
            fullText.classList.add('seo-collapsed');
        }
        
        // Event listeners iOS-friendly
        this.addTouchEvents(expandBtn, fullText);
        
        console.log('✅ iOS fixes applied to modal');
    }
    
    /**
     * 📱 ADD TOUCH-FRIENDLY EVENTS
     */
    addTouchEvents(expandBtn, fullText) {
        let touchStarted = false;
        
        // Fonction de toggle
        const toggleText = (e) => {
            e.preventDefault();
            e.stopPropagation();
            
            const isCollapsed = fullText.classList.contains('seo-collapsed');
            
            if (isCollapsed) {
                // Expand
                fullText.classList.remove('seo-collapsed');
                fullText.classList.add('seo-expanded');
                expandBtn.textContent = 'Réduire ↑';
                console.log('🍎 iOS: Text expanded');
            } else {
                // Collapse  
                fullText.classList.remove('seo-expanded');
                fullText.classList.add('seo-collapsed');
                expandBtn.textContent = 'En savoir plus ↓';
                console.log('🍎 iOS: Text collapsed');
            }
        };
        
        // Touch events pour iOS
        expandBtn.addEventListener('touchstart', (e) => {
            touchStarted = true;
            expandBtn.style.transform = 'scale(0.95)';
            console.log('🍎 Touch start detected');
        }, { passive: false });
        
        expandBtn.addEventListener('touchend', (e) => {
            if (touchStarted) {
                expandBtn.style.transform = 'scale(1)';
                setTimeout(() => toggleText(e), 50); // Petit délai pour le feedback visuel
                touchStarted = false;
                console.log('🍎 Touch end - toggling text');
            }
        }, { passive: false });
        
        // Click fallback pour desktop
        expandBtn.addEventListener('click', (e) => {
            if (!touchStarted) { // Éviter double trigger
                toggleText(e);
                console.log('🍎 Click fallback triggered');
            }
        }, { passive: false });
        
        // Hover effects pour desktop
        expandBtn.addEventListener('mouseenter', () => {
            if (!touchStarted) {
                expandBtn.style.transform = 'scale(1.05)';
            }
        });
        
        expandBtn.addEventListener('mouseleave', () => {
            if (!touchStarted) {
                expandBtn.style.transform = 'scale(1)';
            }
        });
        
        console.log('✅ Touch events added to expand button');
    }
}

// CSS pour les états collapsed/expanded
const iOSModalCSS = `
<style>
/* 🍎 iOS MODAL FIXES - TOUCH FRIENDLY */
.seo-collapsed {
    display: none !important;
    opacity: 0 !important;
    max-height: 0 !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
}

.seo-expanded {
    display: block !important;
    opacity: 1 !important;
    max-height: 1000px !important;
    overflow: visible !important;
    transition: all 0.3s ease !important;
}

/* iOS touch improvements */
.expand-btn {
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    -khtml-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    touch-action: manipulation !important;
    -webkit-tap-highlight-color: rgba(0,0,0,0) !important;
    outline: none !important;
    transition: transform 0.1s ease !important;
}

.expand-btn:active,
.expand-btn:focus {
    outline: none !important;
    transform: scale(0.95) !important;
}

/* iOS specific modal improvements */
@supports (-webkit-touch-callout: none) {
    .fullscreen-modal {
        -webkit-overflow-scrolling: touch !important;
        transform: translate3d(0,0,0) !important;
    }
    
    .seo-panel {
        -webkit-transform: translateX(-50%) !important;
        transform: translateX(-50%) !important;
        will-change: transform !important;
    }
    
    .expand-btn {
        min-height: 44px !important; /* iOS minimum touch target */
        min-width: 44px !important;
        border-radius: 8px !important;
        font-size: 0.9rem !important;
        padding: 8px 16px !important;
    }
}

/* iPhone specific fixes */
@media (max-width: 480px) and (-webkit-min-device-pixel-ratio: 2) {
    .expand-btn {
        font-size: 0.85rem !important;
        padding: 10px 18px !important;
        min-height: 44px !important;
        line-height: 1.2 !important;
    }
    
    .seo-panel {
        bottom: 10px !important;
        width: 96% !important;
        font-size: 0.9rem !important;
    }
    
    .seo-expanded {
        animation: slideDown 0.3s ease !important;
    }
    
    .seo-collapsed {
        animation: slideUp 0.3s ease !important;
    }
}

@keyframes slideDown {
    from { 
        opacity: 0; 
        max-height: 0; 
        transform: translateY(-10px); 
    }
    to { 
        opacity: 1; 
        max-height: 1000px; 
        transform: translateY(0); 
    }
}

@keyframes slideUp {
    from { 
        opacity: 1; 
        max-height: 1000px; 
        transform: translateY(0); 
    }
    to { 
        opacity: 0; 
        max-height: 0; 
        transform: translateY(-10px); 
    }
}
</style>
`;

// Injecter le CSS
document.head.insertAdjacentHTML('beforeend', iOSModalCSS);

// 🚀 INIT iOS MODAL FIX
let iOSModalFixer;

document.addEventListener('DOMContentLoaded', () => {
    iOSModalFixer = new iOSModalFix();
    console.log('🍎 iOS Modal Fix ready - Touch events optimized for iPhone/iPad');
});
