/**
 * 🔥 QUANTUM LOGO + TEMPERATURE ULTIMATE TESTER
 * Validates GENIUS vertical logo + perfect temperature display
 * Tests by fréro + QUANTUM GOD DEVELOPER TEAM
 */

// 🎯 ULTIMATE LOGO + TEMPERATURE TEST SUITE
window.quantumLogoTemperatureTest = function() {
    console.log('🔥 RUNNING ULTIMATE LOGO + TEMPERATURE TEST SUITE...\n');
    
    const tests = [];
    let allPassed = true;
    
    // Get all critical elements
    const logoContainer = document.querySelector('.flex.items-center.space-x-3:first-child');
    const logoCircle = document.querySelector('.logo-container-bg');
    const logoTitle = document.querySelector('.header-logo-gradient');
    const logoSubtitle = document.querySelector('.text-xs.text-luxury-emerald-light');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const tempElement = document.querySelector('.weather-temp-mobile');
    const controls = document.querySelector('.lg\\:hidden.flex.items-center.space-x-3');
    
    if (!logoContainer || !logoTitle || !weatherWidget || !tempElement) {
        console.log('❌ CRITICAL: Essential elements not found!');
        return false;
    }
    
    console.log('📊 LOGO ANALYSIS:');
    
    // Test 1: Logo title text fully visible ("GolfinThaï")
    const logoText = logoTitle.textContent || '';
    const logoExpectedText = 'GolfinThaï';
    const logoTextComplete = logoText.includes(logoExpectedText);
    console.log(`  Logo text: "${logoText}" (expected: "${logoExpectedText}")`);
    
    tests.push({
        name: '"GolfinThaï" text complete',
        passed: logoTextComplete,
        details: `"${logoText}" ${logoTextComplete ? '✅' : '❌ TRUNCATED!'}`
    });
    
    // Test 2: Logo container dimensions optimized
    const logoRect = logoContainer.getBoundingClientRect();
    console.log(`  Logo container: ${Math.round(logoRect.width)}px wide`);
    
    const logoWidthOptimal = logoRect.width <= 105 && logoRect.width >= 85; // Should be around 100px
    tests.push({
        name: 'Logo container optimized width',
        passed: logoWidthOptimal,
        details: `${Math.round(logoRect.width)}px (target: 85-105px)`
    });
    
    // Test 3: Logo layout is vertical (logo above text)
    if (logoCircle && logoTitle) {
        const circleRect = logoCircle.getBoundingClientRect();
        const titleRect = logoTitle.getBoundingClientRect();
        const isVertical = circleRect.bottom <= titleRect.top + 5; // 5px tolerance
        
        console.log(`  Logo circle position: y=${Math.round(circleRect.top)}`);
        console.log(`  Logo title position: y=${Math.round(titleRect.top)}`);
        console.log(`  Layout is vertical: ${isVertical ? 'YES ✅' : 'NO ❌'}`);
        
        tests.push({
            name: 'Vertical logo layout',
            passed: isVertical,
            details: `Circle: y${Math.round(circleRect.top)}, Title: y${Math.round(titleRect.top)}`
        });
    }
    
    console.log('\n🌡️ TEMPERATURE ANALYSIS:');
    
    // Test 4: Temperature widget has MORE space than before
    const weatherRect = weatherWidget.getBoundingClientRect();
    console.log(`  Weather widget: ${Math.round(weatherRect.width)}px wide`);
    
    const weatherSpaceImproved = weatherRect.width >= 85; // Should be 90px or more
    tests.push({
        name: 'Temperature widget space improved',
        passed: weatherSpaceImproved,
        details: `${Math.round(weatherRect.width)}px (target: 85px+)`
    });
    
    // Test 5: Temperature text fits perfectly
    const tempText = tempElement.textContent || '--°';
    const tempRect = tempElement.getBoundingClientRect();
    console.log(`  Temperature text: "${tempText}"`);
    console.log(`  Temperature width: ${Math.round(tempRect.width)}px in ${Math.round(weatherRect.width)}px container`);
    
    const temperatureFitsPerfectly = tempRect.width <= weatherRect.width;
    tests.push({
        name: 'Temperature fits perfectly',
        passed: temperatureFitsPerfectly,
        details: `${Math.round(tempRect.width)}px text in ${Math.round(weatherRect.width)}px container`
    });
    
    console.log('\n📏 OVERALL SPACING ANALYSIS:');
    
    // Test 6: Total header space distribution
    if (controls) {
        const controlsRect = controls.getBoundingClientRect();
        const totalUsedSpace = logoRect.width + weatherRect.width + controlsRect.width;
        const screenWidth = window.innerWidth;
        
        console.log(`  Logo: ${Math.round(logoRect.width)}px`);
        console.log(`  Weather: ${Math.round(weatherRect.width)}px`);
        console.log(`  Controls: ${Math.round(controlsRect.width)}px`);
        console.log(`  Total used: ${Math.round(totalUsedSpace)}px of ${screenWidth}px screen`);
        
        const spaceDistributionGood = totalUsedSpace <= screenWidth * 0.95; // Use max 95% of screen
        tests.push({
            name: 'Space distribution optimal',
            passed: spaceDistributionGood,
            details: `${Math.round(totalUsedSpace)}px used of ${screenWidth}px available`
        });
        
        // Test 7: Elements don't overlap
        const logoWeatherGap = weatherRect.left - logoRect.right;
        const weatherControlsGap = controlsRect.left - weatherRect.right;
        
        console.log(`  Logo → Weather gap: ${Math.round(logoWeatherGap)}px`);
        console.log(`  Weather → Controls gap: ${Math.round(weatherControlsGap)}px`);
        
        const noOverlaps = logoWeatherGap >= -5 && weatherControlsGap >= -5; // 5px tolerance
        tests.push({
            name: 'No element overlaps',
            passed: noOverlaps,
            details: `Gaps: ${Math.round(logoWeatherGap)}px, ${Math.round(weatherControlsGap)}px`
        });
    }
    
    // Test 8: Logo text uses proper gradient styling
    const logoStyles = getComputedStyle(logoTitle);
    const hasGradient = logoStyles.backgroundImage && logoStyles.backgroundImage.includes('gradient');
    tests.push({
        name: 'Logo has premium gradient',
        passed: hasGradient,
        details: hasGradient ? 'Gradient applied ✅' : 'No gradient detected ❌'
    });
    
    // Test 9: Temperature has proper bold weight
    const tempStyles = getComputedStyle(tempElement);
    const tempFontWeight = parseInt(tempStyles.fontWeight) || 400;
    const tempIsBold = tempFontWeight >= 700;
    tests.push({
        name: 'Temperature is bold enough',
        passed: tempIsBold,
        details: `Font weight: ${tempFontWeight} (target: 700+)`
    });
    
    // Results summary
    console.log(`\n🧪 TEST RESULTS SUMMARY:`);
    tests.forEach((test, i) => {
        const status = test.passed ? '✅' : '❌';
        console.log(`  ${i + 1}. ${status} ${test.name} - ${test.details}`);
        if (!test.passed) allPassed = false;
    });
    
    console.log(`\n${allPassed ? '🎉' : '⚠️'} OVERALL RESULT: ${allPassed ? 'GENIUS SOLUTION SUCCESS!' : 'Some adjustments needed'}`);
    
    if (allPassed) {
        console.log('🔥 FRÉRO\'S IDEA = PERFECTION! "GolfinThaï" + Temperature both visible! 🏆');
        console.log('💎 Your clients will say "PUTAIN c\'est magnifique!"');
    } else {
        console.log('🔧 Run quantumLogoEmergencyFix() for additional adjustments');
    }
    
    return allPassed;
};

// 🚨 EMERGENCY FIX - If logo/temperature issues detected
window.quantumLogoEmergencyFix = function() {
    console.log('🚨 APPLYING LOGO + TEMPERATURE EMERGENCY FIX...');
    
    const logoContainer = document.querySelector('.flex.items-center.space-x-3:first-child');
    const logoTitle = document.querySelector('.header-logo-gradient');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const tempElement = document.querySelector('.weather-temp-mobile');
    
    if (!logoContainer || !logoTitle || !weatherWidget || !tempElement) {
        console.log('❌ Elements not found for emergency fix');
        return;
    }
    
    console.log('🔧 Applying emergency adjustments...');
    
    // Force vertical logo layout
    logoContainer.style.flexDirection = 'column';
    logoContainer.style.alignItems = 'center';
    logoContainer.style.justifyContent = 'center';
    logoContainer.style.gap = '0.2rem';
    logoContainer.style.width = '95px';
    logoContainer.style.maxWidth = '95px';
    logoContainer.style.minWidth = '95px';
    
    // Force logo title visibility
    logoTitle.style.fontSize = '0.8rem';
    logoTitle.style.whiteSpace = 'nowrap';
    logoTitle.style.overflow = 'visible';
    logoTitle.style.textOverflow = 'clip';
    logoTitle.style.maxWidth = 'none';
    logoTitle.style.width = 'auto';
    
    // Force weather widget expansion
    weatherWidget.style.minWidth = '95px';
    weatherWidget.style.maxWidth = '100px';
    weatherWidget.style.width = '95px';
    weatherWidget.style.overflow = 'visible';
    
    // Force temperature visibility
    tempElement.style.fontSize = '0.9rem';
    tempElement.style.fontWeight = '800';
    tempElement.style.overflow = 'visible';
    tempElement.style.whiteSpace = 'nowrap';
    tempElement.style.textOverflow = 'clip';
    tempElement.style.maxWidth = 'none';
    
    console.log('✅ Emergency fix applied! Testing in 1 second...');
    
    setTimeout(() => {
        window.quantumLogoTemperatureTest();
    }, 1000);
};

// 🎨 VISUAL DEBUG - Show logo and temperature boundaries
window.quantumLogoVisualDebug = function() {
    console.log('🎨 QUANTUM LOGO + TEMPERATURE VISUAL DEBUG...');
    
    const logoContainer = document.querySelector('.flex.items-center.space-x-3:first-child');
    const logoTitle = document.querySelector('.header-logo-gradient');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const tempElement = document.querySelector('.weather-temp-mobile');
    
    if (!logoContainer || !logoTitle || !weatherWidget || !tempElement) {
        console.log('❌ Elements not found for visual debug');
        return;
    }
    
    // Add colored borders and labels
    logoContainer.style.border = '2px solid #FF6B6B';
    logoContainer.style.backgroundColor = 'rgba(255, 107, 107, 0.1)';
    
    logoTitle.style.border = '1px solid #FFA500';
    logoTitle.style.backgroundColor = 'rgba(255, 165, 0, 0.2)';
    
    weatherWidget.style.border = '2px solid #4ECDC4';
    weatherWidget.style.backgroundColor = 'rgba(78, 205, 196, 0.1)';
    
    tempElement.style.border = '1px solid #32CD32';
    tempElement.style.backgroundColor = 'rgba(50, 205, 50, 0.2)';
    
    console.log('🎨 Visual debug active for 12 seconds:');
    console.log('🔴 Red border = Logo container (should be vertical)');
    console.log('🟠 Orange border = "GolfinThaï" title (should be complete)');
    console.log('🟢 Teal border = Weather widget (should be wide)');
    console.log('🟢 Green border = Temperature text (should fit perfectly)');
    
    setTimeout(() => {
        // Clean up
        logoContainer.style.border = '';
        logoContainer.style.backgroundColor = '';
        logoTitle.style.border = '';
        logoTitle.style.backgroundColor = '';
        weatherWidget.style.border = '';
        weatherWidget.style.backgroundColor = '';
        tempElement.style.border = '';
        tempElement.style.backgroundColor = '';
        console.log('🧹 Visual debug cleaned up');
    }, 12000);
};

// 📱 TEST ON DIFFERENT MOBILE SIZES
window.quantumResponsiveMobileTest = function() {
    console.log('📱 TESTING LOGO + TEMPERATURE ON MOBILE SIZES...\n');
    
    const mobileBreakpoints = [
        { width: 320, name: 'iPhone SE (Small)' },
        { width: 375, name: 'iPhone 12 Mini' },
        { width: 390, name: 'iPhone 12 Pro' },
        { width: 414, name: 'iPhone 12 Pro Max' },
        { width: 430, name: 'iPhone 14 Pro Max' }
    ];
    
    const currentWidth = window.innerWidth;
    console.log(`Current screen width: ${currentWidth}px\n`);
    
    mobileBreakpoints.forEach(bp => {
        const suitable = currentWidth <= bp.width + 50; // 50px tolerance
        const status = suitable ? '✅' : '⚠️';
        console.log(`${status} ${bp.name} (${bp.width}px): ${suitable ? 'Test this size' : 'Resize browser to test'}`);
    });
    
    console.log('\n💡 To test different sizes:');
    console.log('1. Open browser dev tools (F12)');
    console.log('2. Click device simulation button');
    console.log('3. Select different mobile devices');
    console.log('4. Run quantumLogoTemperatureTest() for each size');
};

// 🚀 AUTO-TEST ON LOAD
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        console.log('\n🔥 QUANTUM LOGO + TEMPERATURE TESTER LOADED!\n');
        console.log('Available commands:');
        console.log('  🧪 quantumLogoTemperatureTest() - Ultimate test suite');
        console.log('  🚨 quantumLogoEmergencyFix() - Emergency adjustments');
        console.log('  🎨 quantumLogoVisualDebug() - Visual boundaries (12s)');
        console.log('  📱 quantumResponsiveMobileTest() - Mobile size guidance');
        console.log('\n🎯 Running auto-test in 3 seconds...\n');
        
        setTimeout(() => {
            window.quantumLogoTemperatureTest();
        }, 3000);
        
    }, 1500);
});

console.log('🔥 QUANTUM LOGO + TEMPERATURE TESTER INITIALIZED!');