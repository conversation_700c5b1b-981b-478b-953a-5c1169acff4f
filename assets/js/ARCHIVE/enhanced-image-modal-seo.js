/**
 * 🚀 ENHANCED IMAGE MODAL WITH SEO CONTENT
 * Modale d'image enrichie avec contenu SEO pour chaque golf
 */

class EnhancedImageModal {
    constructor() {
        this.isOpen = false;
        this.modal = null;
        this.golfData = this.initGolfData();
        
        this.init();
    }
    
    /**
     * 🏌️ DONNÉES GOLF POUR SEO
     */
    initGolfData() {
        return {
            'aquella': {
                title: 'Aquella Golf & Country Club - Phang Nga',
                description: 'Élu Meilleur Golf de Luxe de Thaïlande 2024, Aquella offre un parcours championship de 18 trous avec 2,5 km de plage privée exclusive. Situé à 30 minutes de Phuket, ce complexe intégré marie golf d\'exception et art de vivre tropical.',
                features: ['18 Trous Championship', '2,5 km de plage privée', 'Tunnels de bambou uniques', 'Vue mer d\'Andaman', 'Villas avec piscines privées'],
                location: 'Phang Nga, Thaïlande',
                keywords: 'golf luxe Thaïlande, parcours championnat bord de mer, Aquella Golf Club Phang Nga'
            },
            'black-mountain': {
                title: 'Black Mountain Golf Club - Hua Hin',
                description: '#59 Golf Digest Top 100 mondial, seul parcours thaïlandais dans ce classement prestigieux. 27 trous championship conçu par Phil Ryan, terrain d\'entraînement des professionnels internationaux à Hua Hin.',
                features: ['#59 Golf Digest Top 100', '27 trous championship', 'Design Phil Ryan', 'Black Mountain Golf Academy', 'Trou signature n°11'],
                location: 'Hua Hin, Thaïlande',
                keywords: 'golf championship Hua Hin, Black Mountain Golf Club, Top 100 golf mondial'
            },
            'santiburi': {
                title: 'Santiburi Samui Country Club - Koh Samui',
                description: 'Seul parcours 18 trous de Koh Samui, niché dans un resort 5 étoiles avec plage privée. Dénivelés spectaculaires jusqu\'à 180 mètres d\'altitude offrant des vues océan époustouflantes sur le Golfe de Thaïlande.',
                features: ['Seul 18 trous de Koh Samui', 'Resort 5 étoiles intégré', 'Dénivelés jusqu\'à 180m', 'Vues panoramiques océan', '13 trous avec obstacles d\'eau'],
                location: 'Koh Samui, Thaïlande',
                keywords: 'golf Koh Samui, Santiburi resort golf, parcours golf ile tropicale'
            },
            'chiang-mai': {
                title: 'Chiang Mai Highlands Golf & Spa Resort',
                description: 'Golf montagnard à altitude élevée offrant un climat frais unique en Thaïlande. 27 trous par Schmidt-Curley Design sur site spirituel historique avec spa intégré et vues panoramiques 360° sur les montagnes du nord.',
                features: ['27 trous Schmidt-Curley', 'Climat frais montagnard', 'Site spirituel historique', 'Spa intégré primé', '130+ bunkers stratégiques'],
                location: 'Chiang Mai, Thaïlande',
                keywords: 'golf montagne Chiang Mai, climate frais Thaïlande, Highlands golf resort'
            },
            'red-mountain': {
                title: 'Red Mountain Golf Club - Phuket',
                description: 'Parcours le plus spectaculaire d\'Asie sculpté dans une ancienne mine d\'étain. Formations rocheuses rouges iconiques et dénivelé de 50m au légendaire trou 17 "Drop Shot". Design révolutionnaire primé internationalement.',
                features: ['Plus spectaculaire d\'Asie', 'Ancienne mine d\'étain', 'Formations rocheuses rouges', 'Trou 17 "Drop Shot" 50m', 'Platinum Golf Course Award'],
                location: 'Phuket, Thaïlande',
                keywords: 'golf spectaculaire Phuket, Red Mountain mine étain, parcours unique Asie'
            },
            'blue-canyon': {
                title: 'Blue Canyon Country Club - Phuket',
                description: 'Seul parcours triple-hôte du Johnnie Walker Classic au monde. Deux parcours championship où Tiger Woods et Greg Norman ont écrit l\'histoire. Le parcours le plus prestigieux de Phuket, à 1km de l\'aéroport.',
                features: ['Triple-hôte Johnnie Walker Classic', 'Canyon Course et Lakes Course', 'Trou 13 "Tiger Hole"', 'Légendes du golf', 'Asia\'s Best Golf Course'],
                location: 'Phuket, Thaïlande',  
                keywords: 'golf prestige Phuket, Blue Canyon Johnnie Walker, Tiger Woods golf'
            }
        };
    }
    
    init() {
        console.log('🚀 Enhanced Image Modal with SEO Content');
        this.createModal();
        this.bindEvents();
        console.log('✅ Enhanced Image Modal: Ready!');
    }
    
    /**
     * 🎨 CREATE ENHANCED MODAL WITH SEO CONTENT
     */
    createModal() {
        this.modal = document.createElement('div');
        this.modal.className = 'enhanced-image-modal';
        this.modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.95);
            z-index: 99999;
            display: none;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            overflow-y: auto;
        `;
        
        this.modal.innerHTML = `
            <div class="modal-container" style="
                position: relative;
                max-width: 95vw;
                max-height: 95vh;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                gap: 2rem;
                padding: 2rem;
                cursor: default;
            ">
                <!-- Image Container -->
                <div class="image-container" style="
                    position: relative;
                    max-width: 80vw;
                    max-height: 60vh;
                ">
                    <img class="modal-image" src="" alt="" style="
                        max-width: 100%;
                        max-height: 100%;
                        width: auto;
                        height: auto;
                        min-width: 400px;
                        min-height: 300px;
                        object-fit: contain;
                        border-radius: 12px;
                        box-shadow: 0 20px 50px rgba(0, 0, 0, 0.8);
                        background: white;
                    ">
                    <button class="modal-close" style="
                        position: absolute;
                        top: -15px;
                        right: -15px;
                        background: #ffffff;
                        border: none;
                        border-radius: 50%;
                        width: 40px;
                        height: 40px;
                        font-size: 20px;
                        font-weight: bold;
                        cursor: pointer;
                        color: #333;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                        z-index: 1;
                    ">×</button>
                </div>
                
                <!-- SEO Content Container -->
                <div class="seo-content" style="
                    max-width: 80vw;
                    background: rgba(255, 255, 255, 0.95);
                    border-radius: 16px;
                    padding: 2rem;
                    color: #1a1a1a;
                    text-align: center;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                ">
                    <h2 class="golf-title" style="
                        font-size: 1.75rem;
                        font-weight: 700;
                        margin-bottom: 1rem;
                        color: #065f46;
                        font-family: 'Playfair Display', serif;
                    "></h2>
                    
                    <p class="golf-description" style="
                        font-size: 1rem;
                        line-height: 1.6;
                        margin-bottom: 1.5rem;
                        color: #374151;
                    "></p>
                    
                    <div class="golf-features" style="
                        display: flex;
                        flex-wrap: wrap;
                        gap: 0.75rem;
                        justify-content: center;
                        margin-bottom: 1rem;
                    "></div>
                    
                    <div class="golf-location" style="
                        font-size: 0.875rem;
                        color: #059669;
                        font-weight: 600;
                        text-transform: uppercase;
                        letter-spacing: 0.05em;
                    "></div>
                </div>
            </div>
        `;
        
        document.body.appendChild(this.modal);
        console.log('✅ Enhanced modal created with SEO content');
    }
    
    /**
     * 🎛️ BIND EVENTS
     */
    bindEvents() {
        // Close button
        const closeBtn = this.modal.querySelector('.modal-close');
        closeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.close();
        });
        
        // Background click to close
        this.modal.addEventListener('click', () => {
            this.close();
        });
        
        // Don't close when clicking content
        const container = this.modal.querySelector('.modal-container');
        container.addEventListener('click', (e) => {
            e.stopPropagation();
        });
        
        // Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });
        
        console.log('✅ Enhanced events bound');
    }
    
    /**
     * 🏌️ IDENTIFY GOLF FROM IMAGE SRC
     */
    identifyGolf(imageSrc) {
        if (imageSrc.includes('Aquella')) return 'aquella';
        if (imageSrc.includes('BlackMountain')) return 'black-mountain';
        if (imageSrc.includes('Santiburi')) return 'santiburi';
        if (imageSrc.includes('ChiangMai')) return 'chiang-mai';
        if (imageSrc.includes('RedMountain')) return 'red-mountain';
        if (imageSrc.includes('blue-canyon')) return 'blue-canyon';
        return null;
    }
    
    /**
     * 🖼️ OPEN WITH SEO CONTENT
     */
    open(imageSrc, imageAlt = '') {
        console.log('🖼️ Opening enhanced modal:', imageSrc);
        
        // Set image
        const img = this.modal.querySelector('.modal-image');
        img.src = imageSrc;
        img.alt = imageAlt;
        
        // Identify golf and populate SEO content
        const golfKey = this.identifyGolf(imageSrc);
        const golfInfo = golfKey ? this.golfData[golfKey] : null;
        
        if (golfInfo) {
            this.populateSEOContent(golfInfo);
        } else {
            this.populateDefaultContent(imageAlt);
        }
        
        // Show modal
        this.modal.style.display = 'flex';
        
        // Lock body scroll
        document.body.style.overflow = 'hidden';
        
        this.isOpen = true;
        
        console.log('✅ Enhanced modal opened with SEO content');
    }
    
    /**
     * 📝 POPULATE SEO CONTENT
     */
    populateSEOContent(golfInfo) {
        const title = this.modal.querySelector('.golf-title');
        const description = this.modal.querySelector('.golf-description');
        const features = this.modal.querySelector('.golf-features');
        const location = this.modal.querySelector('.golf-location');
        
        title.textContent = golfInfo.title;
        description.textContent = golfInfo.description;
        location.textContent = golfInfo.location;
        
        // Populate features
        features.innerHTML = '';
        golfInfo.features.forEach(feature => {
            const badge = document.createElement('span');
            badge.style.cssText = `
                background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
                color: #065f46;
                padding: 0.5rem 1rem;
                border-radius: 999px;
                font-size: 0.875rem;
                font-weight: 600;
                border: 1px solid #a7f3d0;
            `;
            badge.textContent = feature;
            features.appendChild(badge);
        });
    }
    
    /**
     * 📝 POPULATE DEFAULT CONTENT
     */
    populateDefaultContent(alt) {
        const title = this.modal.querySelector('.golf-title');
        const description = this.modal.querySelector('.golf-description');
        const features = this.modal.querySelector('.golf-features');
        const location = this.modal.querySelector('.golf-location');
        
        title.textContent = alt || 'Golf de Prestige en Thaïlande';
        description.textContent = 'Découvrez l\'excellence du golf tropical en Thaïlande avec GolfinThaï. Des parcours exceptionnels dans des cadres paradisiaques pour une expérience golfique inoubliable.';
        location.textContent = 'Thaïlande';
        features.innerHTML = '<span style="background: #f3f4f6; color: #374151; padding: 0.5rem 1rem; border-radius: 999px; font-size: 0.875rem;">Golf Premium Thaïlande</span>';
    }
    
    /**
     * ❌ CLOSE MODAL
     */
    close() {
        console.log('❌ Closing enhanced modal');
        
        // Hide modal
        this.modal.style.display = 'none';
        
        // Unlock body scroll
        document.body.style.overflow = '';
        
        // Clear image
        const img = this.modal.querySelector('.modal-image');
        img.src = '';
        
        this.isOpen = false;
        
        console.log('✅ Enhanced modal closed');
    }
}

// 🚀 INIT ENHANCED MODAL
let enhancedImageModal;

document.addEventListener('DOMContentLoaded', () => {
    enhancedImageModal = new EnhancedImageModal();
    
    // Global functions for compatibility
    window.openSimpleModal = (src, alt) => {
        if (enhancedImageModal) {
            enhancedImageModal.open(src, alt);
        }
    };
    
    window.closeSimpleModal = () => {
        if (enhancedImageModal) {
            enhancedImageModal.close();
        }
    };
    
    console.log('🚀 Enhanced Image Modal with SEO: READY!');
});