/**
 * 🛡️ DOMINATRIX IPHONE FIX - CONTRÔLE TOTAL
 * 
 * PROBLÈME RÉSOLU: Bataille entre scripts
 * - User clique → ça change 100ms → re-switch automatique
 * - Conflit entre: Dovito + Universal Fix + autres scripts iPhone
 * 
 * SOLUTION DOMINANTE:
 * - Prend le contrôle EXCLUSIF du bouton
 * - Désactive TOUS les autres event listeners
 * - Empêche les re-switch automatiques
 * - Garde le contrôle à 100%
 */

class DominatrixiPhoneFix {
    constructor() {
        this.isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        this.isSafari = /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);
        this.controlledButtons = new Map(); // Boutons sous notre contrôle exclusif
        this.textStates = new Map(); // États que NOUS contrôlons
        this.blockOtherScripts = true; // Bloquer les autres scripts
        this.debugMode = true;
        
        if (this.isIOS && this.isSafari) {
            this.init();
        } else {
            this.log('Non-iPhone Safari - Dominatrix non nécessaire');
        }
    }
    
    log(message) {
        if (this.debugMode) {
            console.log(`🛡️ Dominatrix iPhone Fix: ${message}`);
        }
    }
    
    init() {
        this.log('🛡️ PRISE DE CONTRÔLE TOTALE - Mode Dominatrix activé');
        
        // Attendre que tout soit chargé puis DOMINER
        setTimeout(() => this.takeFullControl(), 2000);
        
        // Observer et dominer toute nouvelle modal
        this.dominateNewModals();
    }
    
    takeFullControl() {
        this.log('🔥 PRISE DE CONTRÔLE TOTALE des modales existantes');
        
        // Chercher TOUTES les modales
        const modals = document.querySelectorAll(
            '.fullscreen-modal, .dovito-fullscreen-modal, .image-modal, .modal, [class*="modal"]'
        );
        
        modals.forEach(modal => {
            if (this.isModalVisible(modal)) {
                this.dominateModal(modal);
            }
        });
        
        // Hook et NEUTRALISE les autres systèmes
        this.neutralizeCompetitors();
    }
    
    dominateNewModals() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1 && this.isModalVisible(node)) {
                        this.log('🎯 Nouvelle modal détectée - DOMINATION immédiate');
                        setTimeout(() => this.dominateModal(node), 100);
                    }
                });
                
                if (mutation.type === 'attributes' && 
                    mutation.attributeName === 'style' &&
                    this.isModalVisible(mutation.target)) {
                    this.log('🎯 Modal ouverte détectée - DOMINATION immédiate');
                    setTimeout(() => this.dominateModal(mutation.target), 100);
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'class']
        });
    }
    
    isModalVisible(element) {
        if (!element || element.nodeType !== 1) return false;
        
        const style = window.getComputedStyle(element);
        const isVisible = style.display !== 'none' && 
                         style.visibility !== 'hidden' && 
                         parseFloat(style.opacity) > 0;
        
        const isModal = element.classList.contains('fullscreen-modal') ||
                       element.classList.contains('dovito-fullscreen-modal') ||
                       element.className.includes('modal');
        
        return isVisible && isModal;
    }
    
    dominateModal(modal) {
        this.log(`🛡️ DOMINATION de modal: ${modal.className}`);
        
        // Trouver bouton et texte
        const button = this.findButton(modal);
        const text = this.findText(modal);
        
        if (!button || !text) {
            this.log('❌ Bouton ou texte non trouvé - domination impossible');
            return;
        }
        
        // NEUTRALISER tous les events existants
        this.neutralizeButton(button);
        
        // FORCER l'état initial étendu
        this.forceExpandedState(text);
        
        // PRENDRE LE CONTRÔLE EXCLUSIF
        this.takeButtonControl(button, text);
        
        this.log('✅ Modal DOMINÉE avec succès');
    }
    
    findButton(modal) {
        const selectors = [
            '.expand-btn',
            '.dovito-expand-btn',
            '.toggle-btn',
            'button:contains("savoir")',
            'button:contains("Réduire")',
            '[class*="expand"]',
            '[class*="toggle"]'
        ];
        
        for (let selector of selectors) {
            const button = modal.querySelector(selector);
            if (button) return button;
        }
        
        // Fallback: chercher par contenu texte
        const allButtons = modal.querySelectorAll('button');
        for (let button of allButtons) {
            const text = button.textContent || button.innerHTML;
            if (text.includes('savoir') || text.includes('Réduire') || text.includes('↑') || text.includes('↓')) {
                return button;
            }
        }
        
        return null;
    }
    
    findText(modal) {
        const selectors = [
            '.seo-full',
            '.dovito-seo-full',
            '.full-text',
            '.expandable-text'
        ];
        
        for (let selector of selectors) {
            const text = modal.querySelector(selector);
            if (text) return text;
        }
        
        return null;
    }
    
    neutralizeButton(button) {
        this.log('🔥 NEUTRALISATION de tous les events du bouton');
        
        // Méthode BRUTALE: remplacer le bouton pour supprimer TOUS les events
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);
        
        // Neutraliser aussi onclick
        newButton.onclick = null;
        newButton.removeAttribute('onclick');
        
        return newButton;
    }
    
    forceExpandedState(text) {
        this.log('🔄 FORCE état étendu - NOTRE règle');
        
        // Supprimer TOUTES les classes de state
        text.className = text.className.replace(/\b(collapsed|seo-collapsed|hidden|expanded|seo-expanded|visible)\b/g, '');
        
        // Ajouter NOS classes
        text.classList.add('dominatrix-expanded');
        
        // CSS BRUTAL et PRIORITAIRE
        text.style.setProperty('display', 'block', 'important');
        text.style.setProperty('opacity', '1', 'important');
        text.style.setProperty('max-height', 'none', 'important');
        text.style.setProperty('overflow', 'visible', 'important');
        text.style.setProperty('visibility', 'visible', 'important');
        
        // Stockage de NOTRE état
        this.textStates.set(text, 'expanded');
    }
    
    takeButtonControl(originalButton, text) {
        // Récupérer le nouveau bouton après neutralisation
        const button = this.findReplacedButton(originalButton);
        if (!button) return;
        
        this.log('🛡️ PRISE DE CONTRÔLE EXCLUSIF du bouton');
        
        // CSS pour iPhone avec priorité maximale
        button.style.cssText = `
            min-width: 44px !important;
            min-height: 44px !important;
            touch-action: manipulation !important;
            -webkit-touch-callout: none !important;
            -webkit-tap-highlight-color: transparent !important;
            cursor: pointer !important;
            position: relative !important;
            z-index: 999999 !important;
            padding: 12px 20px !important;
            border-radius: 8px !important;
            font-size: 16px !important;
            pointer-events: auto !important;
        `;
        
        // État initial et texte bouton
        this.updateButtonText(button, true);
        
        // NOS events EXCLUSIFS
        this.addDominatrixEvents(button, text);
        
        // Stocker sous notre contrôle
        this.controlledButtons.set(button, {
            text: text,
            state: 'expanded'
        });
        
        this.log('✅ Contrôle exclusif établi');
    }
    
    findReplacedButton(originalButton) {
        const modal = originalButton.closest('[class*="modal"]');
        if (!modal) return null;
        
        // Chercher le bouton de remplacement
        const buttons = modal.querySelectorAll('button');
        for (let button of buttons) {
            const text = button.textContent || button.innerHTML;
            if (text.includes('savoir') || text.includes('Réduire') || 
                button.className === originalButton.className) {
                return button;
            }
        }
        
        return null;
    }
    
    addDominatrixEvents(button, text) {
        this.log('🔥 Ajout ÉVÉNEMENTS DOMINATRIX - contrôle total');
        
        let touchStarted = false;
        let touchStartTime = 0;
        let touchMoved = false;
        let processing = false; // Empêcher les double triggers
        
        // NOTRE fonction de toggle - priorité absolue
        const dominatrixToggle = (e) => {
            if (processing) {
                this.log('🛡️ Toggle déjà en cours - BLOQUÉ');
                return;
            }
            
            processing = true;
            
            if (e) {
                e.preventDefault();
                e.stopImmediatePropagation(); // STOP TOUT
            }
            
            const currentState = this.textStates.get(text) || 'expanded';
            this.log(`🔄 DOMINATRIX TOGGLE: ${currentState} → ${currentState === 'expanded' ? 'collapsed' : 'expanded'}`);
            
            if (currentState === 'expanded') {
                // RÉDUIRE
                this.collapseText(text);
                this.updateButtonText(button, false);
                this.textStates.set(text, 'collapsed');
                this.controlledButtons.get(button).state = 'collapsed';
            } else {
                // ÉTENDRE
                this.expandText(text);
                this.updateButtonText(button, true);
                this.textStates.set(text, 'expanded');
                this.controlledButtons.get(button).state = 'expanded';
            }
            
            // Empêcher les autres scripts d'interférer
            setTimeout(() => {
                this.blockOtherInterference(text, button);
                processing = false;
            }, 200);
        };
        
        // Touch events DOMINATRIX pour iPhone
        button.addEventListener('touchstart', (e) => {
            touchStarted = true;
            touchStartTime = Date.now();
            touchMoved = false;
            
            button.style.setProperty('transform', 'scale(0.95)', 'important');
            button.style.setProperty('opacity', '0.8', 'important');
            
            this.log('👆 DOMINATRIX TouchStart');
        }, { passive: false, capture: true }); // CAPTURE pour priorité
        
        button.addEventListener('touchmove', (e) => {
            touchMoved = true;
        }, { passive: false, capture: true });
        
        button.addEventListener('touchend', (e) => {
            button.style.setProperty('transform', 'scale(1)', 'important');
            button.style.setProperty('opacity', '1', 'important');
            
            const touchDuration = Date.now() - touchStartTime;
            
            if (touchStarted && !touchMoved && touchDuration < 1000) {
                this.log('👆 DOMINATRIX TouchEnd VALIDE - executing toggle');
                setTimeout(() => dominatrixToggle(e), 50);
            }
            
            touchStarted = false;
        }, { passive: false, capture: true });
        
        // Click fallback DOMINATRIX
        button.addEventListener('click', (e) => {
            if (!touchStarted) {
                this.log('🖱️ DOMINATRIX Click');
                dominatrixToggle(e);
            }
        }, { passive: false, capture: true });
        
        this.log('✅ Events DOMINATRIX ajoutés avec priorité maximale');
    }
    
    collapseText(text) {
        text.classList.remove('dominatrix-expanded');
        text.classList.add('dominatrix-collapsed');
        
        text.style.setProperty('display', 'none', 'important');
        text.style.setProperty('opacity', '0', 'important');
        text.style.setProperty('max-height', '0', 'important');
    }
    
    expandText(text) {
        text.classList.remove('dominatrix-collapsed');
        text.classList.add('dominatrix-expanded');
        
        text.style.setProperty('display', 'block', 'important');
        text.style.setProperty('opacity', '1', 'important');
        text.style.setProperty('max-height', 'none', 'important');
        text.style.setProperty('overflow', 'visible', 'important');
    }
    
    updateButtonText(button, isExpanded) {
        const expandText = 'En savoir plus ↓';
        const collapseText = 'Réduire ↑';
        
        const newText = isExpanded ? collapseText : expandText;
        button.textContent = newText;
        button.innerHTML = newText;
        
        this.log(`📝 Bouton text mis à jour: "${newText}"`);
    }
    
    blockOtherInterference(text, button) {
        // Vérifier que NOTRE état est toujours bon
        const ourState = this.textStates.get(text);
        const buttonData = this.controlledButtons.get(button);
        
        if (ourState === 'expanded') {
            this.expandText(text);
            this.updateButtonText(button, true);
        } else {
            this.collapseText(text);
            this.updateButtonText(button, false);
        }
        
        this.log(`🛡️ État protégé contre interférence: ${ourState}`);
    }
    
    neutralizeCompetitors() {
        this.log('🔥 NEUTRALISATION des systèmes concurrents');
        
        // Neutraliser les fonctions globales si nécessaire
        if (window.dovitoModal && this.blockOtherScripts) {
            this.log('🔇 Neutralisation Dovito Modal en cours...');
            // On peut garder le système mais bloquer ses interférences
        }
        
        // Ajouter CSS pour nos classes
        this.addDominatrixCSS();
    }
    
    addDominatrixCSS() {
        const css = `
        <style id="dominatrix-iphone-css">
        /* 🛡️ DOMINATRIX iPHONE CSS - PRIORITÉ ABSOLUE */
        
        .dominatrix-expanded {
            display: block !important;
            opacity: 1 !important;
            max-height: none !important;
            overflow: visible !important;
            visibility: visible !important;
        }
        
        .dominatrix-collapsed {
            display: none !important;
            opacity: 0 !important;
            max-height: 0 !important;
            overflow: hidden !important;
        }
        
        /* Protection contre les autres CSS */
        [class*="dominatrix-"] {
            transition: all 0.3s ease !important;
        }
        
        /* iPhone button optimizations avec priorité max */
        @supports (-webkit-touch-callout: none) {
            button[style*="touch-action"] {
                -webkit-touch-callout: none !important;
                -webkit-tap-highlight-color: transparent !important;
                touch-action: manipulation !important;
                min-height: 44px !important;
                min-width: 44px !important;
                pointer-events: auto !important;
            }
        }
        </style>
        `;
        
        document.head.insertAdjacentHTML('beforeend', css);
        this.log('✅ CSS DOMINATRIX injecté');
    }
}

// 🚀 INITIALISATION DOMINATRIX
let dominatrixiPhoneFix;

// Init immédiat
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        dominatrixiPhoneFix = new DominatrixiPhoneFix();
    });
} else {
    dominatrixiPhoneFix = new DominatrixiPhoneFix();
}

// Backup avec délai pour être SÛR
setTimeout(() => {
    if (!dominatrixiPhoneFix) {
        dominatrixiPhoneFix = new DominatrixiPhoneFix();
    }
}, 3000);

// Export global pour debug
window.dominatrixiPhoneFix = dominatrixiPhoneFix;

// Test function
window.testDominatrix = function() {
    console.log('🧪 Test Dominatrix...');
    if (dominatrixiPhoneFix) {
        dominatrixiPhoneFix.takeFullControl();
        console.log('✅ Dominatrix re-executed');
    }
};

console.log('🛡️ DOMINATRIX iPHONE FIX chargé - CONTRÔLE TOTAL activé !');
