/* =============================================
   🧹 LOGO CLEANUP SCRIPT - REMOVE DUPLICATES
   Remove all duplicate logos and keep only the clean one
   ============================================= */

(function() {
    'use strict';
    
    console.log('🧹 LOGO CLEANUP - Removing duplicates...');
    
    // Attendre que le DOM soit prêt
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', cleanupLogos);
    } else {
        cleanupLogos();
    }
    
    function cleanupLogos() {
        console.log('🔍 Scanning for duplicate logos...');
        
        // Trouver le container principal du logo
        const logoParent = document.querySelector('.flex.items-center.space-x-3');
        
        if (!logoParent) {
            console.log('❌ Logo parent container not found');
            return;
        }
        
        // Chercher tous les éléments qui pourraient être des logos
        const allLogos = [];
        
        // 1. SVG logos
        const svgLogos = logoParent.querySelectorAll('svg[title*="GolfinThaï"]');
        svgLogos.forEach(logo => allLogos.push({element: logo, type: 'SVG'}));
        
        // 2. Div logos avec background
        const divLogos = logoParent.querySelectorAll('div[title*="GolfinThaï"]');
        divLogos.forEach(logo => allLogos.push({element: logo, type: 'DIV'}));
        
        // 3. Logos avec classes
        const classLogos = logoParent.querySelectorAll('.logo-container-bg');
        classLogos.forEach(logo => allLogos.push({element: logo, type: 'CLASS'}));
        
        // 4. Logos de fallback (GT text)
        const fallbackLogos = logoParent.querySelectorAll('div');
        fallbackLogos.forEach(div => {
            if (div.textContent && div.textContent.trim() === 'GT') {
                allLogos.push({element: div, type: 'FALLBACK'});
            }
        });
        
        console.log(`🔍 Found ${allLogos.length} potential logo elements:`, allLogos.map(l => l.type));
        
        // Garder seulement le premier logo avec la classe .logo-container-bg
        let keptLogo = null;
        let removedCount = 0;
        
        allLogos.forEach((logoObj, index) => {
            const {element, type} = logoObj;
            
            // Garder le premier logo avec la bonne classe
            if (!keptLogo && element.classList.contains('logo-container-bg')) {
                keptLogo = element;
                console.log(`✅ Keeping logo #${index} (${type})`);
                
                // S'assurer qu'il a les bons styles
                ensureCleanLogoStyles(element);
            } else {
                // Supprimer les autres
                console.log(`🗑️ Removing duplicate logo #${index} (${type})`);
                element.remove();
                removedCount++;
            }
        });
        
        // Si aucun logo trouvé avec la classe, créer un propre
        if (!keptLogo) {
            console.log('🔧 No clean logo found, creating one...');
            createCleanLogo(logoParent);
        }
        
        console.log(`✅ Logo cleanup complete: ${removedCount} duplicates removed`);
    }
    
    function ensureCleanLogoStyles(logoElement) {
        // S'assurer que le logo a les bons styles
        logoElement.style.cssText = `
            width: 44px !important;
            height: 44px !important;
            border-radius: 50% !important;
            background-image: url('./assets/images/logo-golfinthai.jpg') !important;
            background-size: contain !important;
            background-position: center !important;
            background-repeat: no-repeat !important;
            background-color: rgba(255, 255, 255, 0.1) !important;
            flex-shrink: 0 !important;
            border: 2px solid rgba(6, 95, 70, 0.3) !important;
            box-shadow: 0 2px 8px rgba(45, 212, 191, 0.15) !important;
            margin: 0 !important;
            position: relative !important;
            z-index: 1001 !important;
        `;
        
        // Nettoyer le contenu texte s'il y en a
        logoElement.textContent = '';
        
        console.log('✨ Clean logo styles applied');
    }
    
    function createCleanLogo(parent) {
        const cleanLogo = document.createElement('div');
        cleanLogo.className = 'logo-container-bg';
        cleanLogo.setAttribute('role', 'img');
        cleanLogo.setAttribute('aria-label', 'Logo GolfinThaï - Voyages Golf Thaïlande');
        cleanLogo.setAttribute('title', 'GolfinThaï');
        cleanLogo.setAttribute('itemprop', 'logo');
        
        ensureCleanLogoStyles(cleanLogo);
        
        // L'insérer au début du container
        parent.insertBefore(cleanLogo, parent.firstChild);
        
        console.log('✅ Clean logo created and inserted');
    }
    
    // Vérification continue pour empêcher les nouveaux doublons
    let checkCount = 0;
    const maxChecks = 10;
    
    const preventDuplicates = setInterval(() => {
        checkCount++;
        
        const logoParent = document.querySelector('.flex.items-center.space-x-3');
        if (logoParent) {
            const logoElements = logoParent.querySelectorAll('.logo-container-bg, svg[title*="GolfinThaï"], div[title*="GolfinThaï"]');
            
            if (logoElements.length > 1) {
                console.log(`🚨 Duplicate detected again! ${logoElements.length} logos found`);
                cleanupLogos();
            }
        }
        
        if (checkCount >= maxChecks) {
            clearInterval(preventDuplicates);
            console.log('🛡️ Logo duplicate prevention ended');
        }
    }, 2000);
    
})();

console.log('🧹 Logo Cleanup Script loaded - No more duplicates!');