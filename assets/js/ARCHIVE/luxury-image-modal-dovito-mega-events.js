/**
 * 🎯 LUXURY IMAGE MODAL - EVENT HANDLERS ULTRA-FIXES IPHONE
 * Problème identifié: bouton visible mais events ne marchent pas sur iPhone
 * Solution: event handlers mega-renforcés + méthodes alternatives
 */

class DovitoImageModal {
    constructor() {
        this.isOpen = false;
        this.modal = null;
        this.currentImage = null;
        this.golfData = this.initGolfData();
        
        this.init();
    }
    
    /**
     * 🏌️ DONNÉES GOLF POUR SEO COMPACT
     */
    initGolfData() {
        return {
            'RedMountain': {
                title: 'Red Mountain Golf Club Phuket',
                short: 'Parcours le plus spectaculaire d\'Asie sculpté dans une ancienne mine d\'étain.',
                full: 'Red Mountain Golf Club à Phuket est reconnu comme le parcours le plus spectaculaire d\'Asie. Sculpté dans une ancienne mine d\'étain, ce golf unique offre des formations rocheuses rouges iconiques et un dénivelé de 50 mètres au légendaire trou 17 "Drop Shot". L\'expérience de jeu est révolutionnaire avec des vues panoramiques exceptionnelles sur l\'île de Phuket.'
            },
            'Aquella': {
                title: 'Aquella Golf & Country Club',
                short: 'Élu Meilleur Golf de Luxe de Thaïlande 2024 avec 2,5 km de plage privée.',
                full: 'Aquella Golf & Country Club en Thaïlande a été élu Meilleur Golf de Luxe 2024. Ce parcours championship de 18 trous propose une expérience unique avec 2,5 kilomètres de plage privée exclusive, des tunnels de bambou spectaculaires et une vue imprenable sur la mer d\'Andaman. Le resort 5 étoiles offre un hébergement de luxe exceptionnel.'
            },
            'BlackMountain': {
                title: 'Black Mountain Golf Club Hua Hin',
                short: '#59 Golf Digest Top 100 mondial, seul parcours thaïlandais dans ce classement.',
                full: 'Black Mountain Golf Club à Hua Hin est classé #59 au Golf Digest Top 100 mondial, étant le seul parcours thaïlandais dans ce prestigieux classement. Ce parcours de 27 trous championship conçu par Phil Ryan s\'étend dans un cadre montagneux spectaculaire avec une academy professionnelle de renommée internationale.'
            },
            'Santiburi': {
                title: 'Santiburi Samui Country Club',
                short: 'Seul parcours 18 trous de Koh Samui dans un resort 5 étoiles.',
                full: 'Santiburi Samui Country Club est le seul parcours de golf 18 trous de l\'île de Koh Samui. Niché dans un resort 5 étoiles avec plage privée, ce golf offre des dénivelés spectaculaires jusqu\'à 180 mètres d\'altitude avec des panoramas époustouflants sur le Golfe de Thaïlande et la plage Mae Nam.'
            },
            'ChiangMai': {
                title: 'Chiang Mai Highlands Golf & Spa',
                short: 'Golf montagnard à altitude élevée avec climat frais unique en Thaïlande.',
                full: 'Chiang Mai Highlands Golf & Spa Resort propose une expérience golfique unique en Thaïlande grâce à son altitude élevée offrant un climat frais. Ce parcours de 27 trous par Schmidt-Curley Design est situé sur un site spirituel historique avec un spa intégré primé et des vues panoramiques à 360° sur les montagnes du Nord.'
            },
            'blue-canyon': {
                title: 'Blue Canyon Country Club Phuket',
                short: 'Seul parcours triple-hôte du Johnnie Walker Classic au monde.',
                full: 'Blue Canyon Country Club à Phuket détient le record unique d\'être le seul parcours au monde à avoir accueilli trois fois le Johnnie Walker Classic. Ses deux parcours championship Canyon et Lakes ont vu Tiger Woods et Greg Norman écrire l\'histoire du golf international, notamment au célèbre trou 13 "Tiger Hole".'
            }
        };
    }
    
    init() {
        this.createModal();
        this.bindEvents();
        console.log('🎯 Dovito Modal System - Initialized with EVENT MEGA-FIX');
    }
    
    /**
     * 🎨 CREATE MODAL WITH ULTRA-EVENT-FIXES
     */
    createModal() {
        this.modal = document.createElement('div');
        this.modal.className = 'dovito-fullscreen-modal';
        this.modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(10px);
            z-index: 99999;
            display: none;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        this.modal.innerHTML = `
            <!-- Image Container - Centered -->
            <div class="dovito-image-wrapper" style="
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
                cursor: pointer;
            ">
                <img class="dovito-modal-image" src="" alt="" style="
                    max-width: 95vw;
                    max-height: 85vh;
                    width: auto;
                    height: auto;
                    object-fit: contain;
                    border-radius: 8px;
                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
                    transition: transform 0.3s ease;
                    cursor: default;
                " onload="this.style.opacity='1'">
            </div>
            
            <!-- DOVITO SEO PANEL - EVENT MEGA-FIXES -->
            <div class="dovito-seo-panel" style="
                position: fixed;
                bottom: 20px;
                left: 50%;
                transform: translateX(-50%);
                max-width: 600px;
                width: 90%;
                background: rgba(0, 0, 0, 0.9);
                border: 2px solid rgba(0, 255, 136, 0.6);
                border-radius: 16px;
                padding: 20px;
                color: white;
                font-family: -apple-system, BlinkMacSystemFont, sans-serif;
                cursor: default;
                transition: all 0.3s ease;
                z-index: 100000;
                backdrop-filter: blur(10px);
            ">
                <h3 class="dovito-seo-title" style="
                    font-size: 1.1rem;
                    font-weight: 600;
                    margin: 0 0 8px 0;
                    color: #00ff88;
                    line-height: 1.3;
                "></h3>
                
                <p class="dovito-seo-short" style="
                    font-size: 0.95rem;
                    line-height: 1.4;
                    margin: 0 0 8px 0;
                    color: rgba(255, 255, 255, 0.9);
                "></p>
                
                <!-- DOVITO: TEXTE COMPLET VISIBLE PAR DÉFAUT -->
                <div class="dovito-seo-full" style="
                    font-size: 0.9rem;
                    line-height: 1.5;
                    color: rgba(255, 255, 255, 0.8);
                    margin: 8px 0 20px 0;
                    display: block;
                    border-top: 1px solid rgba(255, 255, 255, 0.1);
                    padding-top: 8px;
                "></div>
                
                <!-- DOVITO: BOUTON EVENT MEGA-RENFORCÉ -->
                <div style="text-align: center; margin-top: 15px;">
                    <div class="dovito-expand-btn-wrapper" style="
                        display: inline-block;
                        cursor: pointer;
                        -webkit-tap-highlight-color: rgba(0, 255, 136, 0.3);
                    ">
                        <button class="dovito-expand-btn" style="
                            background: linear-gradient(135deg, #00ff88, #00aaff);
                            color: black;
                            border: none;
                            padding: 18px 30px;
                            border-radius: 12px;
                            font-size: 18px;
                            font-weight: bold;
                            cursor: pointer;
                            transition: all 0.2s ease;
                            -webkit-appearance: none;
                            -webkit-tap-highlight-color: transparent;
                            touch-action: manipulation;
                            user-select: none;
                            min-width: 200px;
                            min-height: 60px;
                            box-shadow: 0 6px 20px rgba(0, 255, 136, 0.4);
                            position: relative;
                            z-index: 100001;
                            display: block;
                            margin: 0 auto;
                        ">
                            Réduire ↑
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // Add responsive CSS with IPHONE FOCUS
        const style = document.createElement('style');
        style.textContent = `
            .dovito-fullscreen-modal {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            }
            
            /* IPHONE EVENT SUPER OPTIMIZED */
            @media (max-width: 768px) {
                .dovito-modal-image {
                    max-width: 98vw !important;
                    max-height: 70vh !important;
                }
                
                .dovito-seo-panel {
                    bottom: 10px !important;
                    width: 95% !important;
                    padding: 15px !important;
                    max-width: 500px !important;
                    border-width: 3px !important;
                }
                
                .dovito-expand-btn-wrapper {
                    padding: 10px !important;
                    margin: 10px !important;
                    min-height: 80px !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                }
                
                .dovito-expand-btn {
                    font-size: 20px !important;
                    padding: 20px 35px !important;
                    min-width: 250px !important;
                    min-height: 70px !important;
                    box-shadow: 0 8px 25px rgba(0, 255, 136, 0.5) !important;
                    border-radius: 18px !important;
                }
                
                .dovito-expand-btn:active {
                    transform: scale(0.95) !important;
                    box-shadow: 0 4px 15px rgba(0, 255, 136, 0.7) !important;
                }
            }
        `;
        document.head.appendChild(style);
        
        document.body.appendChild(this.modal);
    }
    
    /**
     * 🎛️ BIND EVENTS - CLICK ANYWHERE TO CLOSE
     */
    bindEvents() {
        // ENTIRE MODAL BACKGROUND - Click anywhere to close
        this.modal.addEventListener('click', () => {
            this.close();
        });
        
        // ONLY IMAGE - Don't close when clicking image
        const img = this.modal.querySelector('.dovito-modal-image');
        img.addEventListener('click', (e) => {
            e.stopPropagation();
        });
        
        // ONLY SEO PANEL - Don't close when clicking SEO panel
        const seoPanel = this.modal.querySelector('.dovito-seo-panel');
        seoPanel.addEventListener('click', (e) => {
            e.stopPropagation();
        });
        
        // Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });
    }
    
    /**
     * 🔍 IDENTIFY GOLF FROM IMAGE SRC
     */
    identifyGolf(imageSrc) {
        if (imageSrc.includes('RedMountain')) return 'RedMountain';
        if (imageSrc.includes('Aquella')) return 'Aquella';
        if (imageSrc.includes('BlackMountain')) return 'BlackMountain';
        if (imageSrc.includes('Santiburi')) return 'Santiburi';
        if (imageSrc.includes('ChiangMai')) return 'ChiangMai';
        if (imageSrc.includes('blue-canyon')) return 'blue-canyon';
        return null;
    }
    
    /**
     * 📝 POPULATE SEO CONTENT - ÉVÉNEMENTS MEGA-RENFORCÉS IPHONE
     */
    populateSEOContent(golfInfo) {
        const title = this.modal.querySelector('.dovito-seo-title');
        const shortText = this.modal.querySelector('.dovito-seo-short');
        const fullText = this.modal.querySelector('.dovito-seo-full');
        const expandBtn = this.modal.querySelector('.dovito-expand-btn');
        const btnWrapper = this.modal.querySelector('.dovito-expand-btn-wrapper');
        
        if (golfInfo) {
            title.textContent = golfInfo.title;
            shortText.textContent = golfInfo.short;
            fullText.textContent = golfInfo.full;
        } else {
            // Default content for non-golf images
            title.textContent = 'Séjour Golf en Thaïlande';
            shortText.textContent = 'Découvrez nos destinations golf premium en Thaïlande avec GolfinThaï.';
            fullText.textContent = 'GolfinThaï vous propose des séjours golf d\'exception en Thaïlande sur les plus beaux parcours d\'Asie. Nos forfaits incluent hébergement premium, transferts privés et accès aux golfs les plus prestigieux du pays. Une expérience unique vous attend avec notre expertise locale et notre service personnalisé.';
        }
        
        // DOVITO LOGIC: Légende complète visible par défaut
        fullText.style.display = 'block';
        expandBtn.textContent = 'Réduire ↑';
        
        // MEGA-RENFORCÉ EVENT HANDLER POUR IPHONE
        const dovitoToggle = (e) => {
            e.stopPropagation();
            e.preventDefault();
            
            console.log('🔘 DOVITO BUTTON MEGA-EVENT TRIGGERED!');
            
            const isExpanded = fullText.style.display === 'block';
            
            if (isExpanded) {
                // Réduire (cacher texte complet)
                fullText.style.display = 'none';
                expandBtn.textContent = 'En savoir plus ↓';
                console.log('📦 Text REDUCED via MEGA-EVENT');
            } else {
                // Étendre (montrer texte complet)
                fullText.style.display = 'block';
                expandBtn.textContent = 'Réduire ↑';
                console.log('📖 Text EXPANDED via MEGA-EVENT');
            }
        };
        
        // MÉTHODE 1: Events sur le bouton directement
        expandBtn.onclick = dovitoToggle;
        expandBtn.addEventListener('click', dovitoToggle, { passive: false, capture: true });
        expandBtn.addEventListener('touchend', dovitoToggle, { passive: false, capture: true });
        
        // MÉTHODE 2: Events sur le wrapper (plus grande zone)
        btnWrapper.onclick = dovitoToggle;
        btnWrapper.addEventListener('click', dovitoToggle, { passive: false, capture: true });
        btnWrapper.addEventListener('touchend', dovitoToggle, { passive: false, capture: true });
        
        // MÉTHODE 3: Events tactiles spéciaux iPhone
        let touchStartTime = 0;
        let touchStarted = false;
        
        const handleTouchStart = (e) => {
            touchStartTime = Date.now();
            touchStarted = true;
            expandBtn.style.transform = 'scale(0.95)';
            expandBtn.style.boxShadow = '0 4px 15px rgba(0, 255, 136, 0.7)';
            console.log('👆 MEGA-EVENT Touch Start on button');
        };
        
        const handleTouchEnd = (e) => {
            if (!touchStarted) return;
            
            const touchDuration = Date.now() - touchStartTime;
            expandBtn.style.transform = 'scale(1)';
            expandBtn.style.boxShadow = '0 6px 20px rgba(0, 255, 136, 0.4)';
            
            console.log(`👆 MEGA-EVENT Touch End after ${touchDuration}ms`);
            
            if (touchDuration < 1000) { // Tap normal
                setTimeout(() => dovitoToggle(e), 10);
            }
            
            touchStarted = false;
        };
        
        // Appliquer events tactiles aux deux éléments
        [expandBtn, btnWrapper].forEach(element => {
            element.addEventListener('touchstart', handleTouchStart, { passive: true });
            element.addEventListener('touchend', handleTouchEnd, { passive: false });
            element.addEventListener('touchcancel', () => {
                touchStarted = false;
                expandBtn.style.transform = 'scale(1)';
                expandBtn.style.boxShadow = '0 6px 20px rgba(0, 255, 136, 0.4)';
                console.log('❌ MEGA-EVENT Touch Cancel');
            }, { passive: true });
        });
        
        // MÉTHODE 4: Fallback avec timer (au cas où)
        let tapTimer;
        expandBtn.addEventListener('touchstart', () => {
            clearTimeout(tapTimer);
            tapTimer = setTimeout(() => {
                console.log('⏰ MEGA-EVENT Fallback timer triggered');
                dovitoToggle({ stopPropagation: () => {}, preventDefault: () => {} });
            }, 300);
        }, { passive: true });
        
        expandBtn.addEventListener('touchend', () => {
            clearTimeout(tapTimer);
        }, { passive: true });
        
        console.log('✅ MEGA-EVENT handlers attached to button');
    }
    
    /**
     * 🖼️ OPEN IMAGE - DOVITO VERSION
     */
    open(imageSrc, imageAlt = '') {
        if (!imageSrc) {
            console.log('❌ Dovito Modal: No image source provided');
            return;
        }
        
        console.log('🎯 Dovito Modal: Opening image', imageSrc);
        
        // Set image
        const img = this.modal.querySelector('.dovito-modal-image');
        img.style.opacity = '0';
        img.src = imageSrc;
        img.alt = imageAlt || 'Golf en Thaïlande';
        
        // Populate SEO content with MEGA-EVENT logic
        const golfKey = this.identifyGolf(imageSrc);
        const golfInfo = golfKey ? this.golfData[golfKey] : null;
        this.populateSEOContent(golfInfo);
        
        // Show modal
        this.modal.style.display = 'flex';
        
        // Animate in
        requestAnimationFrame(() => {
            this.modal.style.opacity = '1';
        });
        
        // Lock body scroll
        document.body.style.overflow = 'hidden';
        
        this.isOpen = true;
        this.currentImage = imageSrc;
        
        console.log('✅ Dovito Modal: Image opened with MEGA-EVENTS');
    }
    
    /**
     * ❌ CLOSE
     */
    close() {
        console.log('🎯 Dovito Modal: Closing');
        
        // Animate out
        this.modal.style.opacity = '0';
        
        setTimeout(() => {
            // Hide modal
            this.modal.style.display = 'none';
            
            // Unlock body scroll
            document.body.style.overflow = '';
            
            // Clear image and reset SEO panel
            const img = this.modal.querySelector('.dovito-modal-image');
            const fullText = this.modal.querySelector('.dovito-seo-full');
            const expandBtn = this.modal.querySelector('.dovito-expand-btn');
            
            img.src = '';
            img.style.opacity = '0';
            
            // DOVITO: Reset to default state (expanded)
            if (fullText && expandBtn) {
                fullText.style.display = 'block';
                expandBtn.textContent = 'Réduire ↑';
            }
            
            this.isOpen = false;
            this.currentImage = null;
            
            console.log('✅ Dovito Modal: Closed successfully');
        }, 300);
    }
}

// 🚀 INIT DOVITO MODAL SYSTEM
let dovitoModal;

document.addEventListener('DOMContentLoaded', () => {
    dovitoModal = new DovitoImageModal();
    
    // Global functions for backward compatibility
    window.openSimpleModal = (src, alt) => {
        if (dovitoModal && src) {
            dovitoModal.open(src, alt);
        }
    };
    
    window.closeSimpleModal = () => {
        if (dovitoModal) {
            dovitoModal.close();
        }
    };
    
    // Compatibility with old variable name
    window.fullscreenModal = dovitoModal;
    
    console.log('🎯 Dovito Modal System - Ready with MEGA-EVENTS!');
});
