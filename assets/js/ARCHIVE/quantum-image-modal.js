/**
 * 🖼️ QUANTUM IMAGE MODAL - APPLE/NETFLIX LEVEL
 * Perfect image viewing experience - Zero hacks, pure architecture
 * Created by Quantum God Developer
 */

class QuantumImageModal {
    constructor() {
        this.isOpen = false;
        this.currentImage = null;
        this.modal = null;
        this.imageElement = null;
        this.isZoomed = false;
        this.zoomLevel = 1;
        this.panX = 0;
        this.panY = 0;
        this.isDragging = false;
        this.touchStartDistance = 0;
        this.preloadCache = new Map(); // 🚀 INTELLIGENT PRELOADING
        
        this.init();
    }
    
    init() {
        console.log('🖼️ Quantum Image Modal - Initializing...');
        this.createModal();
        this.bindEvents();
        this.setupIntelligentPreloading(); // 🚀 APPLE-LEVEL PRELOADING
        this.setupKeyboardControls();
        this.setupTouchControls();
        console.log('✅ Quantum Image Modal ready');
    }
    
    /**
     * 🎨 CREATE MODAL: Perfect design
     */
    createModal() {
        this.modal = document.createElement('div');
        this.modal.className = 'quantum-image-modal';
        this.modal.innerHTML = `
            <div class="modal-backdrop"></div>
            <div class="modal-container">
                <button class="modal-close" aria-label="Fermer">
                    <i class="fas fa-times"></i>
                </button>
                <div class="modal-controls">
                    <button class="control-btn zoom-btn" title="Zoom">
                        <i class="fas fa-search-plus"></i>
                    </button>
                    <button class="control-btn fit-btn" title="Ajuster">
                        <i class="fas fa-compress-arrows-alt"></i>
                    </button>
                    <button class="control-btn fullscreen-btn" title="Plein écran">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
                <div class="image-container">
                    <img class="modal-image" src="" alt="" />
                    <div class="loading-spinner">
                        <div class="spinner"></div>
                    </div>
                </div>
                <div class="modal-info">
                    <div class="image-title"></div>
                    <div class="zoom-indicator">100%</div>
                </div>
            </div>
        `;
        
        document.body.appendChild(this.modal);
        this.imageElement = this.modal.querySelector('.modal-image');
        this.setupModalEvents();
    }
    
    /**
     * 🎛️ BIND EVENTS: Clean event management
     */
    bindEvents() {
        // Find all clickable images
        const images = document.querySelectorAll('img[src]:not([data-no-modal])');
        
        images.forEach(img => {
            // Skip tiny images (likely icons) AND nav/logo images
            img.addEventListener('load', () => {
                if (this.shouldBeClickable(img)) {
                    img.style.cursor = 'zoom-in';
                    img.addEventListener('click', (e) => this.openImage(e.target));
                }
            });
            
            // If already loaded
            if (img.complete && this.shouldBeClickable(img)) {
                img.style.cursor = 'zoom-in';
                img.addEventListener('click', (e) => this.openImage(e.target));
            }
        });
    }
    
    /**
     * 🔍 SHOULD BE CLICKABLE: Smart image detection
     */
    shouldBeClickable(img) {
        // Size check
        if (img.naturalWidth <= 200 || img.naturalHeight <= 150) {
            return false;
        }
        
        // Exclude header/nav images
        const parent = img.closest('.nav-bar, .header, .logo, .weather-widget');
        if (parent) {
            return false;
        }
        
        // Exclude small container images
        const container = img.parentElement;
        if (container && (container.offsetWidth < 300 || container.offsetHeight < 200)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 🚀 INTELLIGENT PRELOADING: Apple-level performance
     */
    setupIntelligentPreloading() {
        const images = document.querySelectorAll('img[src*="assets/images"]');
        
        images.forEach(img => {
            if (this.shouldBeClickable(img)) {
                // Preload on hover for instant opening
                img.addEventListener('mouseenter', () => {
                    if (!this.preloadCache.has(img.src)) {
                        const preloadImg = new Image();
                        preloadImg.src = img.src;
                        this.preloadCache.set(img.src, preloadImg);
                        console.log('🚀 Preloaded:', img.src);
                    }
                });
                
                // Also preload on touch start for mobile
                img.addEventListener('touchstart', () => {
                    if (!this.preloadCache.has(img.src)) {
                        const preloadImg = new Image();
                        preloadImg.src = img.src;
                        this.preloadCache.set(img.src, preloadImg);
                    }
                }, { passive: true });
            }
        });
        
        console.log('🚀 Intelligent preloading setup for', images.length, 'images');
    }
    
    /**
     * 🖼️ OPEN IMAGE: Smart sizing & positioning
     */
    openImage(imgElement) {
        if (this.isOpen) return;
        
        this.isOpen = true;
        this.currentImage = imgElement;
        
        // Show modal
        this.modal.classList.add('active');
        document.body.classList.add('modal-open');
        
        // Set image source
        this.imageElement.src = imgElement.src;
        this.imageElement.alt = imgElement.alt || 'Image';
        
        // FORCE IMMEDIATE VISIBILITY
        this.imageElement.style.opacity = '1';
        this.imageElement.style.visibility = 'visible';
        this.imageElement.style.display = 'block';
        
        // Update info
        const title = imgElement.alt || imgElement.title || 'Image';
        this.modal.querySelector('.image-title').textContent = title;
        
        // Show loading
        this.showLoading();
        
        console.log('🖼️ Image src set:', this.imageElement.src);
        console.log('🖼️ Image natural size will be checked on load...');
        
        // Wait for image to load
        this.imageElement.onload = () => {
            console.log('🖼️ Image loaded successfully!');
            console.log('🖼️ Natural size:', this.imageElement.naturalWidth, 'x', this.imageElement.naturalHeight);
            
            this.hideLoading();
            this.calculateOptimalSize();
            this.resetZoom();
            
            // FORCE FINAL VISIBILITY
            this.imageElement.style.opacity = '1';
            this.imageElement.style.visibility = 'visible';
            this.imageElement.style.display = 'block';
            
            console.log('✅ Image modal ready and VISIBLE!');
        };
        
        this.imageElement.onerror = () => {
            this.hideLoading();
            console.error('❌ Failed to load image:', imgElement.src);
            
            // Show error message in modal
            const errorDiv = document.createElement('div');
            errorDiv.innerHTML = `
                <div style="color: white; text-align: center; padding: 2rem;">
                    <h3>❌ Erreur de chargement</h3>
                    <p>Impossible de charger l'image</p>
                    <small>${imgElement.src}</small>
                </div>
            `;
            this.modal.querySelector('.image-container').appendChild(errorDiv);
        };
        
        console.log('🖼️ Image opened:', imgElement.src);
    }
    
    /**
     * 📐 CALCULATE OPTIMAL SIZE: PERFECT NETFLIX-LEVEL SIZING
     */
    calculateOptimalSize() {
        const viewport = {
            width: window.innerWidth * 0.9,  // 90% of viewport
            height: window.innerHeight * 0.85  // 85% for better centering
        };
        
        const image = {
            width: this.imageElement.naturalWidth,
            height: this.imageElement.naturalHeight
        };
        
        // 🎯 MINIMUM GUARANTEED VISIBILITY - The Apple Way
        const minWidth = Math.min(500, viewport.width * 0.7);  // Minimum 500px or 70% viewport
        const minHeight = Math.min(350, viewport.height * 0.6); // Minimum 350px or 60% viewport
        
        // Scale calculations
        const scaleToFit = Math.min(viewport.width / image.width, viewport.height / image.height);
        const scaleToMin = Math.max(minWidth / image.width, minHeight / image.height);
        
        // 🚀 INTELLIGENT SCALE: Ensure visibility while respecting viewport
        const finalScale = Math.max(scaleToFit, Math.min(scaleToMin, 2.5)); // Max 2.5x for quality
        
        const finalSize = {
            width: Math.max(image.width * finalScale, minWidth),
            height: Math.max(image.height * finalScale, minHeight)
        };
        
        // 🎨 SMART CONSTRAINTS: Never exceed viewport but ensure minimum
        finalSize.width = Math.min(finalSize.width, viewport.width);
        finalSize.height = Math.min(finalSize.height, viewport.height);
        
        // Apply optimal size with Apple-style precision
        this.imageElement.style.width = finalSize.width + 'px';
        this.imageElement.style.height = finalSize.height + 'px';
        this.imageElement.style.maxWidth = 'none';
        this.imageElement.style.maxHeight = 'none';
        this.imageElement.style.objectFit = 'contain';
        
        // FORCE VISIBILITY AGAIN
        this.imageElement.style.opacity = '1';
        this.imageElement.style.visibility = 'visible';
        this.imageElement.style.display = 'block';
        this.imageElement.style.zIndex = '10001';
        
        // Center the image
        this.centerImage();
        
        console.log('🎯 PERFECT size calculated:', finalSize, 'Scale:', finalScale.toFixed(2));
        console.log('🎯 Applied styles:', {
            width: this.imageElement.style.width,
            height: this.imageElement.style.height,
            opacity: this.imageElement.style.opacity,
            visibility: this.imageElement.style.visibility
        });
    }
    
    /**
     * 🎯 CENTER IMAGE: Perfect positioning
     */
    centerImage() {
        this.panX = 0;
        this.panY = 0;
        this.imageElement.style.transform = `translate(0, 0) scale(${this.zoomLevel})`;
    }
    
    /**
     * 🔍 ZOOM CONTROLS: Smooth zooming
     */
    zoomIn() {
        this.zoomLevel = Math.min(this.zoomLevel * 1.5, 4);
        this.updateZoom();
    }
    
    zoomOut() {
        this.zoomLevel = Math.max(this.zoomLevel / 1.5, 0.5);
        this.updateZoom();
    }
    
    resetZoom() {
        this.zoomLevel = 1;
        this.centerImage();
        this.updateZoomIndicator();
    }
    
    fitToScreen() {
        this.calculateOptimalSize();
        this.resetZoom();
    }
    
    updateZoom() {
        this.imageElement.style.transform = `translate(${this.panX}px, ${this.panY}px) scale(${this.zoomLevel})`;
        this.updateZoomIndicator();
    }
    
    updateZoomIndicator() {
        const percentage = Math.round(this.zoomLevel * 100);
        this.modal.querySelector('.zoom-indicator').textContent = percentage + '%';
    }
    
    /**
     * 📱 TOUCH CONTROLS: Mobile gestures
     */
    setupTouchControls() {
        let touchStartX, touchStartY, initialPanX, initialPanY;
        
        this.imageElement.addEventListener('touchstart', (e) => {
            if (e.touches.length === 1) {
                // Single touch - pan
                touchStartX = e.touches[0].clientX;
                touchStartY = e.touches[0].clientY;
                initialPanX = this.panX;
                initialPanY = this.panY;
                this.isDragging = true;
            } else if (e.touches.length === 2) {
                // Two finger - zoom
                const touch1 = e.touches[0];
                const touch2 = e.touches[1];
                this.touchStartDistance = Math.hypot(
                    touch2.clientX - touch1.clientX,
                    touch2.clientY - touch1.clientY
                );
            }
        });
        
        this.imageElement.addEventListener('touchmove', (e) => {
            e.preventDefault();
            
            if (e.touches.length === 1 && this.isDragging) {
                // Pan
                const deltaX = e.touches[0].clientX - touchStartX;
                const deltaY = e.touches[0].clientY - touchStartY;
                this.panX = initialPanX + deltaX;
                this.panY = initialPanY + deltaY;
                this.updateZoom();
            } else if (e.touches.length === 2) {
                // Pinch zoom
                const touch1 = e.touches[0];
                const touch2 = e.touches[1];
                const currentDistance = Math.hypot(
                    touch2.clientX - touch1.clientX,
                    touch2.clientY - touch1.clientY
                );
                
                const scale = currentDistance / this.touchStartDistance;
                this.zoomLevel = Math.max(0.5, Math.min(4, this.zoomLevel * scale));
                this.touchStartDistance = currentDistance;
                this.updateZoom();
            }
        });
        
        this.imageElement.addEventListener('touchend', () => {
            this.isDragging = false;
        });
    }
    
    /**
     * ⌨️ KEYBOARD CONTROLS: Pro shortcuts
     */
    setupKeyboardControls() {
        document.addEventListener('keydown', (e) => {
            if (!this.isOpen) return;
            
            switch(e.key) {
                case 'Escape':
                    this.close();
                    break;
                case '+':
                case '=':
                    this.zoomIn();
                    e.preventDefault();
                    break;
                case '-':
                    this.zoomOut();
                    e.preventDefault();
                    break;
                case '0':
                    this.resetZoom();
                    e.preventDefault();
                    break;
                case 'f':
                case 'F':
                    this.toggleFullscreen();
                    break;
            }
        });
    }
    
    /**
     * 🎮 MODAL EVENTS: Interactive controls
     */
    setupModalEvents() {
        // Close button
        this.modal.querySelector('.modal-close').addEventListener('click', () => this.close());
        
        // Backdrop click
        this.modal.querySelector('.modal-backdrop').addEventListener('click', () => this.close());
        
        // Control buttons
        this.modal.querySelector('.zoom-btn').addEventListener('click', () => this.zoomIn());
        this.modal.querySelector('.fit-btn').addEventListener('click', () => this.fitToScreen());
        this.modal.querySelector('.fullscreen-btn').addEventListener('click', () => this.toggleFullscreen());
        
        // Double click to zoom
        this.imageElement.addEventListener('dblclick', () => {
            if (this.zoomLevel === 1) {
                this.zoomIn();
            } else {
                this.resetZoom();
            }
        });
        
        // Mouse wheel zoom
        this.imageElement.addEventListener('wheel', (e) => {
            e.preventDefault();
            if (e.deltaY < 0) {
                this.zoomIn();
            } else {
                this.zoomOut();
            }
        });
    }
    
    /**
     * 🎬 LOADING STATES: Smooth UX
     */
    showLoading() {
        this.modal.querySelector('.loading-spinner').style.display = 'flex';
        this.imageElement.style.opacity = '0';
    }
    
    hideLoading() {
        this.modal.querySelector('.loading-spinner').style.display = 'none';
        this.imageElement.style.opacity = '1';
    }
    
    /**
     * 🖥️ FULLSCREEN: Native support
     */
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            this.modal.requestFullscreen?.() || 
            this.modal.webkitRequestFullscreen?.() || 
            this.modal.msRequestFullscreen?.();
        } else {
            document.exitFullscreen?.() || 
            document.webkitExitFullscreen?.() || 
            document.msExitFullscreen?.();
        }
    }
    
    /**
     * ❌ CLOSE MODAL: Clean cleanup
     */
    close() {
        if (!this.isOpen) return;
        
        this.modal.classList.remove('active');
        document.body.classList.remove('modal-open');
        
        // Reset state
        setTimeout(() => {
            this.isOpen = false;
            this.currentImage = null;
            this.resetZoom();
            this.imageElement.src = '';
        }, 300);
        
        console.log('🖼️ Image modal closed');
    }
    
    /**
     * 🧹 DESTROY: Complete cleanup
     */
    destroy() {
        if (this.modal) {
            this.modal.remove();
        }
        this.isOpen = false;
        console.log('🧹 Quantum Image Modal destroyed');
    }
}

// 🚀 AUTO-INITIALIZATION
document.addEventListener('DOMContentLoaded', () => {
    window.quantumImageModal = new QuantumImageModal();
    console.log('🖼️ Quantum Image Modal: Ready to impress! ✨');
});

// 🎛️ GLOBAL ACCESS
window.openImageModal = (imgSrc, title = '') => {
    const tempImg = document.createElement('img');
    tempImg.src = imgSrc;
    tempImg.alt = title;
    if (window.quantumImageModal) {
        window.quantumImageModal.openImage(tempImg);
    }
};
// 🎛️ GLOBAL ACCESS
window.openImageModal = (imgSrc, title = '') => {
    const tempImg = document.createElement('img');
    tempImg.src = imgSrc;
    tempImg.alt = title;
    if (window.quantumImageModal) {
        window.quantumImageModal.openImage(tempImg);
    }
};

// 🚨 EMERGENCY CLOSE - Fix stuck modals
window.forceCloseModal = () => {
    if (window.quantumImageModal) {
        window.quantumImageModal.close();
        console.log('🚨 Modal forcé à fermer');
    }
    
    // Extra cleanup
    const modals = document.querySelectorAll('.quantum-image-modal');
    modals.forEach(modal => {
        modal.classList.remove('active');
        modal.style.display = 'none';
    });
    
    document.body.classList.remove('modal-open');
    console.log('🧹 Cleanup modal terminé');
};

// 🔧 AUTO-FIX stuck modals
window.addEventListener('load', () => {
    setTimeout(() => {
        const stuckModal = document.querySelector('.quantum-image-modal.active');
        if (stuckModal && !stuckModal.querySelector('.modal-image[src]')) {
            console.log('🚨 Modal coincé détecté, fermeture auto...');
            window.forceCloseModal();
        }
    }, 2000);
});