/**
 * 🔧 SIMPLE IPHONE FIX - État Initial Cohérent
 * 
 * PROBLÈME IDENTIFIÉ via simulation:
 * - État initial incohérent: bouton "Réduire ↑" mais texte déjà caché
 * - Premier toggle marche, suivants ne marchent plus
 * - <PERSON><PERSON> premier toggle, les events ne répondent plus
 * 
 * SOLUTION SIMPLE:
 * - Forcer état initial cohérent à chaque ouverture de modal
 * - Si bouton dit "Réduire ↑" → texte doit être visible
 * - Si bouton dit "En savoir plus ↓" → texte doit être caché
 */

class SimpleIPhoneFix {
    constructor() {
        this.isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        this.debugMode = true;
        
        if (this.isIOS) {
            this.init();
        } else {
            this.log('Non-iOS device - Fix not needed');
        }
    }
    
    log(message) {
        if (this.debugMode) {
            console.log(`🔧 Simple iPhone Fix: ${message}`);
        }
    }
    
    init() {
        this.log('🍎 iPhone detected - Initializing simple fix');
        
        // Observer les modales qui s'ouvrent
        this.observeModals();
        
        // Fix immédiat si modal déjà ouverte
        setTimeout(() => this.fixExistingModals(), 1000);
    }
    
    observeModals() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                if (mutation.type === 'attributes' && 
                    mutation.attributeName === 'style') {
                    const modal = mutation.target;
                    if (this.isModalVisible(modal)) {
                        this.log('Modal opened detected - applying fix');
                        setTimeout(() => this.fixModal(modal), 200);
                    }
                }
            });
        });
        
        // Observer toutes les modales possibles
        const modals = document.querySelectorAll('[class*="modal"]');
        modals.forEach(modal => {
            observer.observe(modal, {
                attributes: true,
                attributeFilter: ['style']
            });
        });
        
        this.log('Modal observer activated');
    }
    
    isModalVisible(element) {
        if (!element || element.nodeType !== 1) return false;
        
        const style = window.getComputedStyle(element);
        const isVisible = style.display !== 'none' && 
                         parseFloat(style.opacity) > 0;
        
        const isModal = element.className.includes('modal');
        return isVisible && isModal;
    }
    
    fixExistingModals() {
        const modals = document.querySelectorAll('[class*="modal"]');
        modals.forEach(modal => {
            if (this.isModalVisible(modal)) {
                this.fixModal(modal);
            }
        });
    }
    
    fixModal(modal) {
        this.log(`Fixing modal: ${modal.className}`);
        
        // Trouver bouton et texte
        const button = this.findButton(modal);
        const text = this.findText(modal);
        
        if (!button || !text) {
            this.log('Button or text not found - skipping fix');
            return;
        }
        
        // Diagnostiquer l'état initial
        const buttonText = button.textContent || button.innerHTML;
        const textStyle = window.getComputedStyle(text);
        const textVisible = textStyle.display !== 'none';
        
        this.log(`Initial state: Button="${buttonText}", Text visible=${textVisible}`);
        
        // CORRIGER l'état incohérent
        if (buttonText.includes('Réduire') && !textVisible) {
            // Bouton dit "Réduire" mais texte caché → montrer le texte
            this.log('❌ INCOHÉRENT: Button dit Réduire mais texte caché → FIX: montrer texte');
            this.showText(text);
        } else if (buttonText.includes('savoir') && textVisible) {
            // Bouton dit "En savoir plus" mais texte visible → cacher le texte
            this.log('❌ INCOHÉRENT: Button dit En savoir plus mais texte visible → FIX: cacher texte');
            this.hideText(text);
        } else {
            this.log('✅ État initial cohérent - pas de correction nécessaire');
        }
    }
    
    findButton(modal) {
        const selectors = [
            '.dovito-expand-btn',
            '.expand-btn', 
            'button[class*="btn"]'
        ];
        
        for (let selector of selectors) {
            const button = modal.querySelector(selector);
            if (button) {
                const text = button.textContent || button.innerHTML;
                if (text.includes('savoir') || text.includes('Réduire') || 
                    text.includes('↑') || text.includes('↓')) {
                    return button;
                }
            }
        }
        
        return null;
    }
    
    findText(modal) {
        const selectors = [
            '.dovito-seo-full',
            '.seo-full',
            '[class*="full"]'
        ];
        
        for (let selector of selectors) {
            const text = modal.querySelector(selector);
            if (text && text.textContent && text.textContent.length > 50) {
                return text;
            }
        }
        
        return null;
    }
    
    showText(text) {
        this.log('🔄 Showing text');
        text.style.display = 'block';
        text.style.opacity = '1';
        text.style.maxHeight = 'none';
        text.style.overflow = 'visible';
        
        // Supprimer classes cachées
        text.classList.remove('collapsed', 'seo-collapsed', 'hidden');
        text.classList.add('expanded', 'seo-expanded', 'visible');
    }
    
    hideText(text) {
        this.log('🔄 Hiding text');
        text.style.display = 'none';
        text.style.opacity = '0';
        text.style.maxHeight = '0';
        text.style.overflow = 'hidden';
        
        // Supprimer classes visibles
        text.classList.remove('expanded', 'seo-expanded', 'visible');
        text.classList.add('collapsed', 'seo-collapsed', 'hidden');
    }
}

// 🚀 INITIALISATION SIMPLE
let simpleIPhoneFix;

document.addEventListener('DOMContentLoaded', () => {
    simpleIPhoneFix = new SimpleIPhoneFix();
});

// Backup avec délai
setTimeout(() => {
    if (!simpleIPhoneFix) {
        simpleIPhoneFix = new SimpleIPhoneFix();
    }
}, 2000);

// Export pour debug
window.simpleIPhoneFix = simpleIPhoneFix;

console.log('🔧 Simple iPhone Fix loaded - État initial cohérent');
