/**
 * 🍎 CORRECTION IPHONE - Boutons "En savoir plus" des cartes de golf
 * Problème : Les boutons .course-expand-btn ne fonctionnent pas sur iPhone Safari
 * Solution : Ajout d'événements tactiles + zone de clic optimisée
 */

(function() {
    'use strict';
    
    // Détecter iPhone/iPad Safari uniquement
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const isSafari = /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);
    
    if (!isIOS || !isSafari) {
        console.log('📱 Non-iPhone Safari détecté - Pas de patch nécessaire');
        return;
    }
    
    console.log('🍎 iPhone Safari détecté - Activation patch boutons golf');
    
    /**
     * 🔧 CORRIGER LES BOUTONS D'EXPANSION DES CARTES DE GOLF
     */
    function fixGolfCardButtons() {
        const expandButtons = document.querySelectorAll('.course-expand-btn');
        
        if (expandButtons.length === 0) {
            console.log('❌ Aucun bouton .course-expand-btn trouvé');
            return;
        }
        
        console.log(`🔧 Correction de ${expandButtons.length} boutons golf`);
        
        expandButtons.forEach((button, index) => {
            // Éviter double correction
            if (button.dataset.iphoneFixed) {
                return;
            }
            
            button.dataset.iphoneFixed = 'true';
            
            // Trouver la carte parente
            const card = button.closest('.course-card');
            if (!card) {
                console.log(`❌ Carte parente non trouvée pour bouton ${index}`);
                return;
            }
            
            // Trouver la description
            const description = card.querySelector('.course-description');
            if (!description) {
                console.log(`❌ Description non trouvée pour bouton ${index}`);
                return;
            }
            
            // État d'expansion
            let isExpanded = false;
            
            // Fonction de toggle
            function toggleDescription(e) {
                e.preventDefault();
                e.stopPropagation();
                
                if (isExpanded) {
                    // Réduire
                    description.style.setProperty('max-height', '100px', 'important');
                    description.style.setProperty('overflow', 'hidden', 'important');
                    description.style.setProperty('-webkit-line-clamp', '3', 'important');
                    description.style.setProperty('display', '-webkit-box', 'important');
                    description.style.setProperty('-webkit-box-orient', 'vertical', 'important');
                    
                    button.setAttribute('aria-label', `Voir plus d'informations sur ${card.querySelector('.course-title')?.textContent || 'ce parcours'}`);
                    button.innerHTML = '<i class="fas fa-expand" aria-hidden="true"></i>';
                    isExpanded = false;
                    
                    console.log(`📦 Description ${index} réduite`);
                } else {
                    // Étendre
                    description.style.setProperty('max-height', 'none', 'important');
                    description.style.setProperty('overflow', 'visible', 'important');
                    description.style.setProperty('-webkit-line-clamp', 'unset', 'important');
                    description.style.setProperty('display', 'block', 'important');
                    description.style.removeProperty('-webkit-box-orient');
                    
                    button.setAttribute('aria-label', `Réduire les informations sur ${card.querySelector('.course-title')?.textContent || 'ce parcours'}`);
                    button.innerHTML = '<i class="fas fa-compress" aria-hidden="true"></i>';
                    isExpanded = true;
                    
                    console.log(`📖 Description ${index} étendue`);
                }
            }
            
            // Optimisations CSS pour iPhone
            button.style.setProperty('min-width', '44px', 'important');
            button.style.setProperty('min-height', '44px', 'important');
            button.style.setProperty('touch-action', 'manipulation', 'important');
            button.style.setProperty('-webkit-tap-highlight-color', 'transparent', 'important');
            button.style.setProperty('-webkit-touch-callout', 'none', 'important');
            button.style.setProperty('cursor', 'pointer', 'important');
            
            // Événements tactiles pour iPhone
            let touchStartTime = 0;
            let touchMoved = false;
            
            button.addEventListener('touchstart', function(e) {
                touchStartTime = Date.now();
                touchMoved = false;
                
                // Feedback visuel
                this.style.setProperty('opacity', '0.7', 'important');
                this.style.setProperty('transform', 'scale(0.95)', 'important');
                
                console.log(`👆 TouchStart sur bouton ${index}`);
            }, { passive: true });
            
            button.addEventListener('touchmove', function(e) {
                touchMoved = true;
                console.log(`👆 TouchMove détecté sur bouton ${index} - scroll en cours`);
            }, { passive: true });
            
            button.addEventListener('touchend', function(e) {
                // Reset feedback visuel
                this.style.setProperty('opacity', '1', 'important');
                this.style.setProperty('transform', 'scale(1)', 'important');
                
                const touchDuration = Date.now() - touchStartTime;
                
                // Vérifier si c'est un tap valide
                if (!touchMoved && touchDuration < 500) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // Délai court pour éviter double déclenchement
                    setTimeout(() => {
                        toggleDescription(e);
                    }, 50);
                    
                    console.log(`👆 TouchEnd valide sur bouton ${index} - toggle déclenché`);
                } else {
                    console.log(`👆 TouchEnd annulé sur bouton ${index} - mouvement: ${touchMoved}, durée: ${touchDuration}ms`);
                }
            }, { passive: false });
            
            button.addEventListener('touchcancel', function(e) {
                // Reset état
                this.style.setProperty('opacity', '1', 'important');
                this.style.setProperty('transform', 'scale(1)', 'important');
                
                console.log(`👆 TouchCancel sur bouton ${index}`);
            }, { passive: true });
            
            // Garder click pour compatibilité desktop
            button.addEventListener('click', function(e) {
                // Éviter double exécution sur mobile
                if (e.detail === 0) return; // Click synthétique après touch
                
                toggleDescription(e);
                console.log(`🖱️ Click desktop sur bouton ${index}`);
            }, { passive: false });
            
            console.log(`✅ Bouton ${index} corrigé pour iPhone`);
        });
    }
    
    /**
     * 🚀 INITIALISATION
     */
    function init() {
        // Attendre le DOM
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', fixGolfCardButtons);
        } else {
            fixGolfCardButtons();
        }
        
        // Fallback avec délai
        setTimeout(fixGolfCardButtons, 1000);
        setTimeout(fixGolfCardButtons, 3000);
        
        // Observer les changements DOM (si contenu chargé dynamiquement)
        const observer = new MutationObserver(function(mutations) {
            let shouldCheck = false;
            
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1 && 
                            (node.classList?.contains('course-card') || 
                             node.querySelector?.('.course-expand-btn'))) {
                            shouldCheck = true;
                        }
                    });
                }
            });
            
            if (shouldCheck) {
                setTimeout(fixGolfCardButtons, 100);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        console.log('🍎 iPhone Golf Cards Fix - Initialisé');
    }
    
    // Démarrer
    init();
    
    // Fonction globale pour test manuel
    window.testGolfButtons = function() {
        console.log('🧪 Test manuel des boutons golf...');
        fixGolfCardButtons();
    };
    
})();