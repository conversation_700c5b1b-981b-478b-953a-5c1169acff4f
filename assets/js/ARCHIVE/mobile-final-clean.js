/**
 * MOBILE FINAL CLEAN - GolfinThaï
 * Script UNIQUE et FINAL pour gérer menu mobile et modales
 * SANS CONFLITS - Remplace tous les autres gestionnaires
 */

class MobileFinalManager {
    constructor() {
        this.isMenuOpen = false;
        this.isModalOpen = false;
        this.scrollPosition = 0;
        
        // Éléments DOM
        this.elements = {
            menuBtn: null,
            menu: null,
            closeBtn: null,
            modal: null,
            modalClose: null
        };
        
        // Désactiver TOUS les autres gestionnaires
        this.disableOtherManagers();
        
        this.init();
    }

    disableOtherManagers() {
        // Désactiver les autres gestionnaires qui pourraient interférer
        if (window.mobileManager) {
            console.log('🚫 Désactivation ancien mobileManager');
            window.mobileManager = null;
        }
        
        if (window.touchManager) {
            console.log('🚫 Désactivation touchManager');
            // Supprimer les callbacks d'images
            if (window.touchManager.removeTap) {
                window.touchManager.removeTap('img[src*="assets/images"]');
                window.touchManager.removeTap('.service-card');
                window.touchManager.removeTap('.course-card');
            }
        }
        
        console.log('✅ Autres gestionnaires désactivés');
    }

    init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => this.setup(), 1000);
            });
        } else {
            setTimeout(() => this.setup(), 1000);
        }
    }

    setup() {
        this.findElements();
        this.bindMenuEvents();
        this.bindModalEvents();
        this.bindImageEvents();
        console.log('✅ Mobile Final Manager initialisé');
    }

    findElements() {
        let attempts = 0;
        const maxAttempts = 50;
        
        const checkElements = () => {
            attempts++;
            this.elements.menuBtn = document.getElementById('mobile-menu-btn');
            this.elements.menu = document.getElementById('mobile-menu');
            this.elements.closeBtn = document.getElementById('close-menu');
            this.elements.modal = document.getElementById('course-modal');
            this.elements.modalClose = this.elements.modal?.querySelector('.modal-close');

            if (!this.elements.menuBtn || !this.elements.menu) {
                if (attempts < maxAttempts) {
                    setTimeout(checkElements, 100);
                    return;
                } else {
                    console.warn('⚠️ Éléments menu non trouvés après 5s');
                    return;
                }
            }
            
            console.log('✅ Éléments DOM trouvés');
        };
        
        checkElements();
    }

    // ===== MENU MOBILE =====
    
    bindMenuEvents() {
        const bindWhenReady = () => {
            if (!this.elements.menuBtn || !this.elements.menu) {
                setTimeout(bindWhenReady, 100);
                return;
            }

            // Éviter les doublons
            if (this.elements.menuBtn.dataset.finalManagerBound) return;
            this.elements.menuBtn.dataset.finalManagerBound = 'true';

            // Bouton hamburger
            this.elements.menuBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('🍔 Menu click (Final Manager)');
                this.toggleMenu();
            });

            // Support tactile
            this.elements.menuBtn.addEventListener('touchend', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('🍔 Menu touch (Final Manager)');
                this.toggleMenu();
            });

            // Bouton fermer
            if (this.elements.closeBtn) {
                this.elements.closeBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.closeMenu();
                });
            }

            // GESTION EXPLICITE DES LIENS DE NAVIGATION MOBILE
            this.bindMobileNavigation();

            // Fermer avec Escape
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.isMenuOpen) {
                    this.closeMenu();
                }
            });

            // Auto-fermeture responsive
            window.addEventListener('resize', () => {
                if (window.innerWidth >= 1024 && this.isMenuOpen) {
                    this.closeMenu();
                }
            });

            console.log('✅ Events menu bindés (Final Manager)');
        };

        bindWhenReady();
    }

    // NOUVELLE MÉTHODE : Gestion explicite navigation mobile
    bindMobileNavigation() {
        const mobileLinks = this.elements.menu?.querySelectorAll('.mobile-menu-link');
        
        if (!mobileLinks) return;

        mobileLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                const href = link.getAttribute('href');
                
                if (href && href.startsWith('#')) {
                    console.log(`📱 Navigation mobile vers: ${href}`);
                    
                    // Fermer le menu avec un délai
                    setTimeout(() => {
                        this.closeMenu();
                    }, 200);
                    
                    // Assurer la navigation smooth vers la section
                    setTimeout(() => {
                        const targetSection = document.querySelector(href);
                        if (targetSection) {
                            console.log(`🎯 Scroll vers section: ${href}`);
                            targetSection.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                            });
                        }
                    }, 300);
                }
            });
        });

        console.log(`✅ ${mobileLinks.length} liens de navigation mobile bindés`);
    }

    toggleMenu() {
        if (this.isMenuOpen) {
            this.closeMenu();
        } else {
            this.openMenu();
        }
    }

    openMenu() {
        if (!this.elements.menu || this.isMenuOpen) return;

        this.scrollPosition = window.pageYOffset;

        this.elements.menu.classList.add('mobile-menu-open');
        this.elements.menu.setAttribute('aria-hidden', 'false');
        this.elements.menuBtn.setAttribute('aria-expanded', 'true');
        
        document.body.classList.add('body-menu-locked');
        document.body.style.top = `-${this.scrollPosition}px`;
        
        this.isMenuOpen = true;
        console.log('✅ Menu ouvert (Final Manager)');
    }

    closeMenu() {
        if (!this.elements.menu || !this.isMenuOpen) return;

        this.elements.menu.classList.remove('mobile-menu-open');
        this.elements.menu.setAttribute('aria-hidden', 'true');
        this.elements.menuBtn.setAttribute('aria-expanded', 'false');
        
        document.body.classList.remove('body-menu-locked');
        document.body.style.top = '';
        
        requestAnimationFrame(() => {
            window.scrollTo(0, this.scrollPosition);
        });
        
        this.isMenuOpen = false;
        console.log('❌ Menu fermé (Final Manager)');
    }

    // ===== MODALES =====
    
    bindModalEvents() {
        // Simple modal doesn't need complex binding
        // Everything is handled by simple-image-modal.js
        console.log('✅ Modal events: Using Simple Modal System');
    }

    bindImageEvents() {
        // DÉTECTION COMPLÈTE - Tous types d'images et conteneurs
        document.addEventListener('click', (e) => {
            console.log('🖱️ Clic détecté (Final Manager):', e.target.tagName, e.target.className);

            // PRIORITÉ ABSOLUE: SI ON EST DANS LE MENU MOBILE, NE RIEN INTERCEPTER
            if (e.target.closest('.mobile-menu-overlay') || 
                e.target.closest('#mobile-menu') ||
                e.target.classList.contains('mobile-menu-link') ||
                e.target.closest('.mobile-menu-link')) {
                console.log('📱 Dans menu mobile - NAVIGATION NATIVE PRIORITAIRE');
                return; // Laisser les liens du menu fonctionner normalement
            }

            // NE PAS intercepter les boutons de langue ET PAGINATION CARROUSEL
            if (e.target.closest('.lang-btn') || 
                e.target.closest('.lang-btn-mobile') ||
                e.target.closest('.lang-option') ||
                e.target.closest('.lang-option-mobile') ||
                e.target.closest('.language-switcher-desktop') ||
                e.target.closest('.language-switcher-mobile') ||
                e.target.closest('.lang-dropdown') ||
                e.target.closest('.lang-dropdown-mobile') ||
                e.target.closest('.pagination-bullet') ||
                e.target.closest('.carousel-pagination') ||
                e.target.classList.contains('pagination-bullet') ||
                e.target.hasAttribute('onclick') ||
                e.target.getAttribute('role') === 'button') {
                console.log('🌍 Bouton langue/pagination - LAISSÉ AU GESTIONNAIRE NORMAL');
                return; // Ne pas traiter, laisser passer
            }

            // Ignorer les autres boutons et liens
            if (e.target.closest('.course-expand-btn') ||
                e.target.closest('button') ||
                e.target.closest('a')) {
                console.log('🚫 Clic sur bouton/lien ignoré');
                return;
            }

            let image = null;
            let foundMethod = '';

            // Méthode 1: Clic direct sur image
            image = e.target.closest('img[src*="assets/images"]');
            if (image) foundMethod = 'direct';

            // Méthode 2: Conteneurs spécifiques
            if (!image) {
                const containers = [
                    '.course-image-container',
                    '.intro-image-container',
                    '.service-image-container',
                    '.founder-image-container',
                    '.image-item'
                ];

                for (const containerClass of containers) {
                    const container = e.target.closest(containerClass);
                    if (container) {
                        image = container.querySelector('img[src*="assets/images"]');
                        if (image) {
                            foundMethod = `conteneur ${containerClass}`;
                            break;
                        }
                    }
                }
            }

            // Méthode 3: Cards (éviter le contenu texte)
            if (!image) {
                const cards = ['.course-card', '.service-card'];
                for (const cardClass of cards) {
                    const card = e.target.closest(cardClass);
                    if (card && !e.target.closest('.course-content, .service-content')) {
                        image = card.querySelector('img[src*="assets/images"]');
                        if (image) {
                            foundMethod = `card ${cardClass}`;
                            break;
                        }
                    }
                }
            }

            // Méthode 4: Images standalone (dans le HTML principal)
            if (!image) {
                const clickedElement = e.target;
                const parentWithImage = clickedElement.closest('div, section, article');
                if (parentWithImage) {
                    const nearbyImage = parentWithImage.querySelector('img[src*="assets/images"]');
                    if (nearbyImage && this.isNearClick(e, nearbyImage)) {
                        image = nearbyImage;
                        foundMethod = 'standalone';
                    }
                }
            }

            if (image) {
                console.log(`🖼️ Image trouvée via ${foundMethod}:`, image.src);

                if (this.isImageClickable(image)) {
                    console.log('✅ Ouverture modal (Final Manager)');
                    e.preventDefault();
                    e.stopPropagation();
                    this.openImageModal(image);
                } else {
                    console.log('❌ Image exclue (Final Manager)');
                }
            } else {
                console.log('ℹ️ Pas d\'image trouvée');
            }
        }, true);

        console.log('✅ Events images bindés - Détection complète (Final Manager)');
    }

    // Vérifier si le clic est proche d'une image
    isNearClick(event, image) {
        const imageRect = image.getBoundingClientRect();
        const clickX = event.clientX;
        const clickY = event.clientY;

        // Zone élargie autour de l'image
        const margin = 50;
        return (
            clickX >= imageRect.left - margin &&
            clickX <= imageRect.right + margin &&
            clickY >= imageRect.top - margin &&
            clickY <= imageRect.bottom + margin
        );
    }

    isImageClickable(image) {
        // Exclure seulement les images spécifiques
        if (image.closest('.hero-carousel') ||
            image.closest('.logo') ||
            image.alt?.toLowerCase().includes('logo') ||
            image.src.includes('logo')) {
            console.log('❌ Image exclue: logo ou carousel');
            return false;
        }

        // Exclure si c'est dans un bouton ou lien direct (pas les conteneurs)
        if (image.closest('button') || image.closest('a')) {
            console.log('❌ Image exclue: dans bouton/lien');
            return false;
        }

        // PERMETTRE TOUTES les autres images assets/images
        if (image.src.includes('assets/images')) {
            console.log('✅ Image autorisée:', image.src);
            return true;
        }

        console.log('❌ Image non assets/images');
        return false;
    }

    openImageModal(imageElement) {
        if (!imageElement) return;

        console.log('🖼️ SIMPLE MODAL - Opening image:', imageElement.src);
        
        // Use the simple modal system
        if (window.openSimpleModal) {
            const title = imageElement.alt || imageElement.title || 'Image';
            window.openSimpleModal(imageElement.src, title);
            console.log('✅ Simple modal opened successfully');
        } else {
            console.error('❌ Simple modal not available yet, retrying...');
            // Retry after 500ms
            setTimeout(() => {
                if (window.openSimpleModal) {
                    window.openSimpleModal(imageElement.src, imageElement.alt || 'Image');
                }
            }, 500);
        }
    }

    // REMOVED: showModal() and closeModal() - Using Simple Modal System
    // All modal functionality now handled by simple-image-modal.js
}

// Initialiser le gestionnaire FINAL
try {
    // Attendre un peu pour que les autres scripts se chargent d'abord
    setTimeout(() => {
        window.mobileFinalManager = new MobileFinalManager();
        console.log('🎯 Mobile Final Manager initialisé avec succès');
    }, 500);
} catch (error) {
    console.error('❌ Erreur Mobile Final Manager:', error);
}
