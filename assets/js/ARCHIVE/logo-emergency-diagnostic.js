/* =============================================
   🚨 LOGO EMERGENCY SCRIPT - DIAGNOSTIC REAL TIME
   Test toutes les possibilités pour réparer le logo
   ============================================= */

(function() {
    'use strict';
    
    console.log('🚨 LOGO EMERGENCY DIAGNOSTIC STARTED');
    
    // Attendre que le DOM soit prêt
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', startDiagnostic);
    } else {
        startDiagnostic();
    }
    
    function startDiagnostic() {
        console.log('🔍 Diagnostic logo en cours...');
        
        // Test 1: Vérifier si le SVG est visible
        const svgLogo = document.querySelector('svg[title="GolfinThaï"]');
        if (svgLogo) {
            console.log('✅ SVG logo trouvé dans le DOM');
            console.log('📐 Taille SVG:', svgLogo.offsetWidth, 'x', svgLogo.offsetHeight);
        } else {
            console.log('❌ SVG logo non trouvé!');
        }
        
        // Test 2: Essayer de charger les images
        const imagePaths = [
            './assets/images/logo-golfinthai.jpg',
            './assets/images/logo-GolfinThaï.svg',
            'assets/images/logo-golfinthai.jpg',
            'assets/images/logo-GolfinThaï.svg'
        ];
        
        imagePaths.forEach((path, index) => {
            testImageLoad(path, index + 1);
        });
        
        // Test 3: Info environnement
        console.log('🌐 URL:', window.location.href);
        console.log('📱 User Agent:', navigator.userAgent);
        console.log('🔧 Base URI:', document.baseURI);
        
        // Test 4: Vérifier s'il y a des erreurs réseau
        window.addEventListener('error', function(e) {
            if (e.target.tagName === 'IMG') {
                console.log('❌ Erreur chargement image:', e.target.src);
            }
        }, true);
    }
    
    function testImageLoad(src, testNumber) {
        const img = new Image();
        
        img.onload = function() {
            console.log(`✅ Test ${testNumber} RÉUSSI:`, src);
            console.log(`📐 Dimensions: ${this.naturalWidth}x${this.naturalHeight}`);
            
            // Si l'image se charge, on peut l'utiliser pour remplacer le SVG
            if (testNumber === 1) { // Premier test réussi
                replaceSvgWithImage(src);
            }
        };
        
        img.onerror = function() {
            console.log(`❌ Test ${testNumber} ÉCHOUÉ:`, src);
        };
        
        img.src = src;
    }
    
    function replaceSvgWithImage(imageSrc) {
        console.log('🔄 Tentative de remplacement SVG par image:', imageSrc);
        
        const svgLogo = document.querySelector('svg[title="GolfinThaï"]');
        if (svgLogo) {
            // Créer un nouveau div avec l'image
            const newLogoDiv = document.createElement('div');
            newLogoDiv.style.cssText = `
                width: 44px !important;
                height: 44px !important;
                border-radius: 50% !important;
                background-image: url('${imageSrc}') !important;
                background-size: contain !important;
                background-position: center !important;
                background-repeat: no-repeat !important;
                background-color: rgba(255, 255, 255, 0.1) !important;
                flex-shrink: 0 !important;
                border: 2px solid rgba(6, 95, 70, 0.3) !important;
                box-shadow: 0 2px 8px rgba(45, 212, 191, 0.15) !important;
            `;
            newLogoDiv.setAttribute('role', 'img');
            newLogoDiv.setAttribute('aria-label', 'Logo GolfinThaï - Voyages Golf Thaïlande');
            newLogoDiv.setAttribute('title', 'GolfinThaï');
            
            // Remplacer le SVG
            svgLogo.parentNode.replaceChild(newLogoDiv, svgLogo);
            console.log('✅ SVG remplacé par image avec succès!');
        }
    }
    
    // Diagnostic après 2 secondes
    setTimeout(() => {
        console.log('📊 RÉSUMÉ DIAGNOSTIC LOGO:');
        console.log('- SVG de secours:', document.querySelector('svg[title="GolfinThaï"]') ? 'Présent' : 'Absent');
        console.log('- Div logo:', document.querySelector('div[title="GolfinThaï"]') ? 'Présent' : 'Absent');
        console.log('📱 Si tu vois toujours un cercle vert = problème de cache navigateur');
    }, 2000);
    
})();

console.log('🚨 Script diagnostic logo chargé - Check console pour résultats');