/**
 * 🍎 UNIVERSAL IPHONE FIX - Modal Image Toggle
 * 
 * PROBLÈME RÉSOLU:
 * - Sur iPhone Safari, certaines images permettent de réduire/agrandir le texte modal
 * - D'autres restent bloquées 
 * - Causé par des conflits entre différents systèmes modal
 * 
 * SOLUTION UNIVERSELLE:
 * - Détecte iPhone/Safari automatiquement
 * - Intercepte TOUTES les modales d'images qui s'ouvrent
 * - Force un système de toggle uniforme et fiable
 * - Compatible avec tous les systèmes existants (Dovito, iOS-fix, etc.)
 * - Fonctionne même si d'autres scripts interfèrent
 */

class UniversaliPhoneFix {
    constructor() {
        this.isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        this.isSafari = /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);
        this.activeModal = null;
        this.fixedButtons = new Set();
        this.debugMode = true; // Activé pour diagnostic
        
        if (this.isIOS && this.isSafari) {
            this.init();
        } else {
            this.log('Non-iPhone Safari détecté - Patch non nécessaire');
        }
    }
    
    log(message) {
        if (this.debugMode) {
            console.log(`🍎 Universal iPhone Fix: ${message}`);
        }
    }
    
    init() {
        this.log('iPhone Safari détecté - Activation patch universel');
        
        // Attendre que le DOM soit prêt
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.startMonitoring());
        } else {
            this.startMonitoring();
        }
    }
    
    startMonitoring() {
        this.log('Démarrage monitoring modal universel');
        
        // Observer pour toutes les modales qui apparaissent
        this.observeModalChanges();
        
        // Vérifier les modales existantes
        this.checkExistingModals();
        
        // Backup: vérification périodique
        setInterval(() => this.checkExistingModals(), 2000);
    }
    
    observeModalChanges() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                // Nouveaux éléments ajoutés
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1) {
                        this.checkElementForModal(node);
                    }
                });
                
                // Changements de style (modal qui s'ouvre)
                if (mutation.type === 'attributes' && 
                    mutation.attributeName === 'style' &&
                    mutation.target.nodeType === 1) {
                    this.checkElementForModal(mutation.target);
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'class']
        });
        
        this.log('Observer modal actif');
    }
    
    checkExistingModals() {
        // Chercher toutes les modales possibles
        const modalSelectors = [
            '.fullscreen-modal',
            '.dovito-fullscreen-modal', 
            '.image-modal',
            '.modal',
            '[class*="modal"]',
            '[id*="modal"]'
        ];
        
        modalSelectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(modal => {
                this.checkElementForModal(modal);
            });
        });
    }
    
    checkElementForModal(element) {
        // Vérifier si c'est une modal visible
        if (this.isModalVisible(element)) {
            this.log(`Modal détectée: ${element.className || element.id || 'sans nom'}`);
            this.fixModalButtons(element);
        }
    }
    
    isModalVisible(element) {
        if (!element || element.nodeType !== 1) return false;
        
        const style = window.getComputedStyle(element);
        const isVisible = style.display !== 'none' && 
                         style.visibility !== 'hidden' && 
                         style.opacity !== '0';
        
        const isModal = element.classList.contains('fullscreen-modal') ||
                       element.classList.contains('dovito-fullscreen-modal') ||
                       element.classList.contains('image-modal') ||
                       element.classList.contains('modal') ||
                       element.className.includes('modal') ||
                       element.id.includes('modal');
        
        return isVisible && isModal;
    }
    
    fixModalButtons(modal) {
        if (this.activeModal === modal) return; // Déjà traité
        
        this.activeModal = modal;
        this.log('Traitement modal pour iPhone...');
        
        // Chercher tous les boutons possibles dans la modal
        const buttonSelectors = [
            '.expand-btn',
            '.dovito-expand-btn',
            '.toggle-btn',
            '.more-btn',
            '[class*="expand"]',
            '[class*="toggle"]',
            'button[class*="btn"]'
        ];
        
        buttonSelectors.forEach(selector => {
            modal.querySelectorAll(selector).forEach(button => {
                this.fixButton(button);
            });
        });
        
        // Chercher le texte à toggler
        const textSelectors = [
            '.seo-full',
            '.dovito-seo-full',
            '.full-text',
            '.expandable-text',
            '[class*="full"]',
            '[class*="text"]'
        ];
        
        let targetText = null;
        textSelectors.forEach(selector => {
            if (!targetText) {
                targetText = modal.querySelector(selector);
            }
        });
        
        if (targetText) {
            this.log(`Texte cible trouvé: ${targetText.className}`);
        }
        
        // 🎯 PROTECTION SPÉCIALE DOVITO MODAL SYSTEM
        this.hookIntoDovitoSystem();
        
        // Nettoyer quand la modal se ferme
        this.observeModalClose(modal);
    }
    
    /**
     * 🎯 HOOK INTO DOVITO MODAL SYSTEM
     * S'assurer que notre fix fonctionne avec le système Dovito existant
     */
    hookIntoDovitoSystem() {
        // Intercepter les fonctions globales du système Dovito
        if (window.dovitoModal && window.dovitoModal.open) {
            const originalOpen = window.dovitoModal.open.bind(window.dovitoModal);
            window.dovitoModal.open = (src, alt) => {
                this.log('🎯 Dovito Modal opening intercepted');
                const result = originalOpen(src, alt);
                
                // Délai pour laisser le modal s'ouvrir, puis appliquer nos fixes
                setTimeout(() => {
                    this.checkExistingModals();
                }, 500);
                
                return result;
            };
            
            this.log('✅ Dovito system hooked');
        }
        
        // Intercepter aussi les fonctions globales simples
        if (window.openSimpleModal) {
            const originalSimpleOpen = window.openSimpleModal;
            window.openSimpleModal = (src, alt) => {
                this.log('🎯 Simple Modal opening intercepted');
                const result = originalSimpleOpen(src, alt);
                
                // Délai pour appliquer nos fixes
                setTimeout(() => {
                    this.checkExistingModals();
                }, 500);
                
                return result;
            };
            
            this.log('✅ Simple Modal system hooked');
        }
    }
    
    fixButton(button) {
        if (this.fixedButtons.has(button)) return; // Déjà traité
        
        this.log(`Correction bouton: ${button.className || button.textContent || 'sans nom'}`);
        
        // Marquer comme traité
        this.fixedButtons.add(button);
        
        // Trouver le texte associé
        const modal = button.closest('[class*="modal"]') || 
                     button.closest('.fullscreen-modal') ||
                     button.closest('.dovito-fullscreen-modal');
        
        if (!modal) {
            this.log('Modal parente non trouvée pour bouton');
            return;
        }
        
        const targetText = this.findTargetText(modal);
        if (!targetText) {
            this.log('Texte cible non trouvé pour bouton');
            return;
        }
        
        // Appliquer le fix iPhone
        this.applyiPhoneFix(button, targetText);
    }
    
    findTargetText(modal) {
        const selectors = [
            '.seo-full',
            '.dovito-seo-full', 
            '.full-text',
            '.expandable-text',
            '[class*="full"]',
            'p[class*="text"]',
            'div[class*="text"]'
        ];
        
        for (let selector of selectors) {
            const element = modal.querySelector(selector);
            if (element) {
                this.log(`Texte trouvé avec sélecteur: ${selector}`);
                return element;
            }
        }
        
        return null;
    }
    
    applyiPhoneFix(button, targetText) {
        this.log('Application fix iPhone sur bouton');
        
        // CSS pour iPhone
        button.style.cssText += `
            min-width: 44px !important;
            min-height: 44px !important;
            touch-action: manipulation !important;
            -webkit-touch-callout: none !important;
            -webkit-tap-highlight-color: transparent !important;
            cursor: pointer !important;
            position: relative !important;
            z-index: 999999 !important;
            padding: 12px 20px !important;
            border-radius: 8px !important;
            font-size: 16px !important;
        `;
        
        // 🔄 FORCER L'ÉTAT INITIAL "ÉTENDU" À CHAQUE OUVERTURE
        // Problème résolu: la modal gardait l'état "réduit" de l'ouverture précédente
        this.forceExpandedState(targetText);
        
        // État initial (maintenant toujours étendu)
        let isExpanded = true; // Force toujours étendu au départ
        this.updateButtonText(button, isExpanded);
        
        // Supprimer tous les anciens event listeners
        this.clearButtonEvents(button);
        
        // Ajouter nouveaux events iPhone-friendly
        this.addTouchEvents(button, targetText);
        
        this.log('Fix iPhone appliqué avec succès');
    }
    
    getTextState(targetText) {
        const style = window.getComputedStyle(targetText);
        return style.display !== 'none' && 
               !targetText.classList.contains('collapsed') &&
               !targetText.classList.contains('seo-collapsed');
    }
    
    /**
     * 🔄 FORCER L'ÉTAT ÉTENDU À CHAQUE OUVERTURE
     * Résout le problème: modal qui garde l'état "réduit" de l'ouverture précédente
     */
    forceExpandedState(targetText) {
        this.log('🔄 Forçage état étendu pour nouvelle ouverture');
        
        // Supprimer toutes les classes "collapsed"
        targetText.classList.remove('collapsed', 'seo-collapsed', 'hidden');
        
        // Ajouter toutes les classes "expanded"
        targetText.classList.add('expanded', 'seo-expanded', 'visible');
        
        // CSS direct pour être sûr
        targetText.style.display = 'block';
        targetText.style.opacity = '1';
        targetText.style.maxHeight = 'none';
        targetText.style.overflow = 'visible';
        
        this.log('✅ État étendu forcé avec succès');
    }
    
    updateButtonText(button, isExpanded) {
        const expandText = 'En savoir plus ↓';
        const collapseText = 'Réduire ↑';
        
        if (isExpanded) {
            button.textContent = collapseText;
            button.innerHTML = collapseText; // Au cas où il y aurait du HTML
        } else {
            button.textContent = expandText;
            button.innerHTML = expandText;
        }
    }
    
    clearButtonEvents(button) {
        // Créer nouveau bouton pour supprimer tous les events
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);
        return newButton;
    }
    
    addTouchEvents(originalButton, targetText) {
        // Le bouton a été remplacé, il faut le récupérer
        const button = this.findReplacedButton(originalButton);
        if (!button) return;
        
        let touchStarted = false;
        let touchStartTime = 0;
        let touchMoved = false;
        
        // Fonction de toggle universelle
        const universalToggle = (e) => {
            if (e) {
                e.preventDefault();
                e.stopPropagation();
            }
            
            const currentState = this.getTextState(targetText);
            
            if (currentState) {
                // Réduire
                this.hideText(targetText);
                this.updateButtonText(button, false);
                this.log('Texte réduit');
            } else {
                // Étendre
                this.showText(targetText);
                this.updateButtonText(button, true);
                this.log('Texte étendu');
            }
        };
        
        // Touch events pour iPhone
        button.addEventListener('touchstart', (e) => {
            touchStarted = true;
            touchStartTime = Date.now();
            touchMoved = false;
            
            // Feedback visuel
            button.style.transform = 'scale(0.95)';
            button.style.opacity = '0.8';
            
            this.log('TouchStart détecté');
        }, { passive: true });
        
        button.addEventListener('touchmove', (e) => {
            touchMoved = true;
            this.log('TouchMove détecté - annulation du tap');
        }, { passive: true });
        
        button.addEventListener('touchend', (e) => {
            // Reset visuel
            button.style.transform = 'scale(1)';
            button.style.opacity = '1';
            
            const touchDuration = Date.now() - touchStartTime;
            
            // Vérifier si c'est un tap valide
            if (touchStarted && !touchMoved && touchDuration < 1000) {
                e.preventDefault();
                e.stopPropagation();
                
                // Petit délai pour le feedback visuel
                setTimeout(() => {
                    universalToggle(e);
                }, 100);
                
                this.log('TouchEnd valide - toggle exécuté');
            } else {
                this.log(`TouchEnd annulé - moved: ${touchMoved}, duration: ${touchDuration}ms`);
            }
            
            touchStarted = false;
        }, { passive: false });
        
        button.addEventListener('touchcancel', (e) => {
            // Reset état
            button.style.transform = 'scale(1)';
            button.style.opacity = '1';
            touchStarted = false;
            this.log('TouchCancel détecté');
        }, { passive: true });
        
        // Click fallback pour desktop (mais éviter double trigger)
        button.addEventListener('click', (e) => {
            if (!touchStarted && e.detail > 0) { // Éviter les clicks synthétiques
                universalToggle(e);
                this.log('Click desktop exécuté');
            }
        }, { passive: false });
        
        this.log('Events iPhone ajoutés au bouton');
    }
    
    findReplacedButton(originalButton) {
        // Trouver le bouton de remplacement dans la modal
        if (this.activeModal) {
            const buttons = this.activeModal.querySelectorAll('button, [class*="btn"]');
            for (let btn of buttons) {
                if (btn.textContent.includes('savoir plus') || 
                    btn.textContent.includes('Réduire') ||
                    btn.classList.toString() === originalButton.classList.toString()) {
                    return btn;
                }
            }
        }
        return null;
    }
    
    hideText(targetText) {
        // Plusieurs méthodes pour cacher le texte
        targetText.style.display = 'none';
        targetText.classList.add('collapsed', 'seo-collapsed', 'hidden');
        targetText.classList.remove('expanded', 'seo-expanded', 'visible');
    }
    
    showText(targetText) {
        // Plusieurs méthodes pour montrer le texte
        targetText.style.display = 'block';
        targetText.classList.add('expanded', 'seo-expanded', 'visible');
        targetText.classList.remove('collapsed', 'seo-collapsed', 'hidden');
    }
    
    observeModalClose(modal) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                if (mutation.type === 'attributes' && 
                    mutation.attributeName === 'style') {
                    const style = window.getComputedStyle(modal);
                    if (style.display === 'none' || style.opacity === '0') {
                        this.cleanupModal(modal);
                        observer.disconnect();
                    }
                }
            });
        });
        
        observer.observe(modal, {
            attributes: true,
            attributeFilter: ['style', 'class']
        });
    }
    
    cleanupModal(modal) {
        this.log('🧹 Nettoyage modal fermée - Reset état texte');
        this.activeModal = null;
        
        // 🔄 RESET COMPLET DE TOUS LES TEXTES DE LA MODAL
        const textElements = modal.querySelectorAll(
            '.seo-full, .dovito-seo-full, .full-text, .expandable-text, [class*="full"], [class*="text"]'
        );
        
        textElements.forEach(textElement => {
            // Supprimer toutes les classes d'état
            textElement.classList.remove('collapsed', 'seo-collapsed', 'hidden', 'expanded', 'seo-expanded', 'visible');
            
            // Reset style inline
            textElement.style.removeProperty('display');
            textElement.style.removeProperty('opacity');
            textElement.style.removeProperty('max-height');
            textElement.style.removeProperty('overflow');
            
            this.log('🔄 Reset état texte element');
        });
        
        // Nettoyer les boutons traités de cette modal
        modal.querySelectorAll('button, [class*="btn"]').forEach(button => {
            this.fixedButtons.delete(button);
        });
        
        this.log('✅ Modal complètement nettoyée');
    }
}

// CSS pour optimisations iPhone
const iPhoneFixCSS = `
<style id="universal-iphone-fix-css">
/* 🍎 UNIVERSAL iPHONE FIX - CSS OPTIMIZATIONS */

/* Base iPhone optimizations */
@supports (-webkit-touch-callout: none) {
    /* Modal optimizations for iPhone */
    [class*="modal"] {
        -webkit-overflow-scrolling: touch !important;
        transform: translate3d(0,0,0) !important;
    }
    
    /* Button optimizations */
    [class*="btn"], button {
        -webkit-touch-callout: none !important;
        -webkit-tap-highlight-color: transparent !important;
        touch-action: manipulation !important;
        outline: none !important;
        border: none !important;
        transition: transform 0.1s ease !important;
    }
    
    /* Expand/Toggle button specific */
    [class*="expand"], [class*="toggle"] {
        min-height: 44px !important;
        min-width: 44px !important;
        padding: 12px 20px !important;
        font-size: 16px !important;
        line-height: 1.2 !important;
        border-radius: 8px !important;
        cursor: pointer !important;
        user-select: none !important;
    }
    
    /* Active state feedback */
    [class*="expand"]:active, 
    [class*="toggle"]:active,
    button:active {
        transform: scale(0.95) !important;
        opacity: 0.8 !important;
    }
    
    /* Text content optimizations */
    [class*="seo-full"], 
    [class*="full-text"],
    [class*="expandable"] {
        transition: all 0.3s ease !important;
        overflow: hidden !important;
    }
    
    /* Hidden state */
    .collapsed, .seo-collapsed, .hidden {
        display: none !important;
        opacity: 0 !important;
        max-height: 0 !important;
    }
    
    /* Visible state */
    .expanded, .seo-expanded, .visible {
        display: block !important;
        opacity: 1 !important;
        max-height: none !important;
    }
}

/* iPhone specific fixes */
@media (max-width: 480px) and (-webkit-min-device-pixel-ratio: 2) {
    [class*="modal"] {
        padding: 10px !important;
    }
    
    [class*="expand"], [class*="toggle"] {
        font-size: 18px !important;
        padding: 15px 25px !important;
        min-height: 50px !important;
        border-radius: 12px !important;
    }
}
</style>
`;

// Injecter le CSS
document.head.insertAdjacentHTML('beforeend', iPhoneFixCSS);

// 🚀 INITIALISATION AUTOMATIQUE
let universaliPhoneFix;

// Init immédiat si DOM prêt
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        universaliPhoneFix = new UniversaliPhoneFix();
    });
} else {
    universaliPhoneFix = new UniversaliPhoneFix();
}

// Backup init avec délai
setTimeout(() => {
    if (!universaliPhoneFix) {
        universaliPhoneFix = new UniversaliPhoneFix();
    }
}, 1000);

// Fonction globale pour test manuel
window.testUniversaliPhoneFix = function() {
    console.log('🧪 Test manuel Universal iPhone Fix...');
    if (universaliPhoneFix) {
        universaliPhoneFix.checkExistingModals();
        console.log('✅ Check des modales existantes effectué');
    } else {
        console.log('❌ Universal iPhone Fix non initialisé');
    }
};

// Export pour debugging
window.universaliPhoneFix = universaliPhoneFix;

console.log('🍎 Universal iPhone Fix chargé - Prêt à corriger TOUTES les modales !');
