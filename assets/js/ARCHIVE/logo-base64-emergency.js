/* =============================================
   🔥 LOGO BASE64 EMERGENCY - ULTIMATE FALLBACK
   Logo en base64 - ne peut JAMAIS échouer
   ============================================= */

(function() {
    'use strict';
    
    // Logo GolfinThaï en base64 (version simplifiée)
    const LOGO_BASE64_SVG = `data:image/svg+xml;base64,${btoa(`
        <svg width="44" height="44" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
            <circle cx="32" cy="32" r="32" fill="#00574B"/>
            <circle cx="32" cy="24" r="6" fill="#FCFCFC"/>
            <path d="M28 30L36 38L34 40L26 32Z" fill="#A3D1C8"/>
            <circle cx="36" cy="38" r="2" fill="#A3D1C8"/>
            <path d="M20 42L32 36L44 42L40 48L24 48Z" fill="#FCFCFC"/>
            <path d="M22 42L32 38L42 42L38 46L26 46Z" fill="#A3D1C8"/>
            <circle cx="32" cy="24" r="1" fill="#00574B"/>
            <circle cx="30" cy="24" r="0.5" fill="#00574B"/>
            <circle cx="34" cy="24" r="0.5" fill="#00574B"/>
            <circle cx="31" cy="22" r="0.5" fill="#00574B"/>
            <circle cx="33" cy="22" r="0.5" fill="#00574B"/>
        </svg>
    `)}`;
    
    // Logo alternatif simple (rond avec initiales)
    const LOGO_BASE64_SIMPLE = `data:image/svg+xml;base64,${btoa(`
        <svg width="44" height="44" viewBox="0 0 44 44" xmlns="http://www.w3.org/2000/svg">
            <circle cx="22" cy="22" r="22" fill="#059669"/>
            <text x="22" y="28" text-anchor="middle" fill="white" font-family="Arial" font-size="16" font-weight="bold">GT</text>
        </svg>
    `)}`;
    
    const isMobile = window.innerWidth <= 1023 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    if (!isMobile) {
        console.log('🖥️ Desktop - Base64 emergency not needed');
        return;
    }
    
    console.log('🔥 MOBILE BASE64 EMERGENCY - Activating ultimate fallback...');
    
    // Attendre que les autres scripts aient eu leur chance
    setTimeout(() => {
        checkForLogoAndActivateBase64();
    }, 5000);
    
    function checkForLogoAndActivateBase64() {
        console.log('🔍 Checking if logo exists and is visible...');
        
        const existingLogos = document.querySelectorAll(`
            .logo-container-bg,
            .nuclear-logo,
            svg[title*="GolfinThaï"],
            [aria-label*="Logo GolfinThaï"]
        `);
        
        let hasVisibleLogo = false;
        
        existingLogos.forEach(logo => {
            const rect = logo.getBoundingClientRect();
            const isVisible = rect.width > 0 && rect.height > 0 && 
                             window.getComputedStyle(logo).visibility !== 'hidden' &&
                             window.getComputedStyle(logo).opacity !== '0';
            
            if (isVisible) {
                // Vérifier si c'est vraiment un logo ou juste un cercle vert
                const bgColor = window.getComputedStyle(logo).backgroundColor;
                const bgImage = window.getComputedStyle(logo).backgroundImage;
                
                // Si c'est juste un cercle vert sans image, ce n'est pas vraiment visible
                const isJustGreenCircle = bgColor.includes('rgb(5, 150, 105)') && 
                                        (bgImage === 'none' || !bgImage.includes('data:') && !bgImage.includes('.jpg') && !bgImage.includes('.svg'));
                
                if (!isJustGreenCircle) {
                    hasVisibleLogo = true;
                    console.log('✅ Visible logo found:', logo);
                }
            }
        });
        
        if (!hasVisibleLogo) {
            console.log('🚨 NO VISIBLE LOGO FOUND - Activating BASE64 emergency!');
            activateBase64Emergency();
        } else {
            console.log('✅ Logo is visible - BASE64 emergency not needed');
        }
    }
    
    function activateBase64Emergency() {
        console.log('💥 BASE64 EMERGENCY ACTIVATED!');
        
        // Détruire tous les logos défaillants
        const failedLogos = document.querySelectorAll(`
            .logo-container-bg,
            .nuclear-logo,
            svg[title*="GolfinThaï"]:not([src*="data:"]),
            [aria-label*="Logo GolfinThaï"]
        `);
        
        failedLogos.forEach(logo => {
            const bgColor = window.getComputedStyle(logo).backgroundColor;
            const bgImage = window.getComputedStyle(logo).backgroundImage;
            const isJustGreenCircle = bgColor.includes('rgb(5, 150, 105)') && 
                                    (bgImage === 'none' || !bgImage.includes('data:'));
            
            if (isJustGreenCircle || logo.offsetWidth === 0) {
                console.log('💥 Destroying failed logo:', logo);
                logo.remove();
            }
        });
        
        // Créer le logo BASE64 INDESTRUCTIBLE
        createBase64Logo();
    }
    
    function createBase64Logo() {
        console.log('🔧 Creating BASE64 logo...');
        
        const logoParent = document.querySelector('.flex.items-center.space-x-3') || 
                          document.querySelector('header nav div') ||
                          document.querySelector('header');
        
        if (!logoParent) {
            console.log('❌ No parent found - Creating floating logo');
            createFloatingBase64Logo();
            return;
        }
        
        const base64Logo = document.createElement('div');
        base64Logo.id = 'base64-emergency-logo-' + Date.now();
        base64Logo.className = 'base64-emergency-logo';
        
        // Attributs
        base64Logo.setAttribute('role', 'img');
        base64Logo.setAttribute('aria-label', 'Logo GolfinThaï - Voyages Golf Thaïlande');
        base64Logo.setAttribute('title', 'GolfinThaï');
        
        // Styles INDESTRUCTIBLES
        base64Logo.style.cssText = `
            width: 44px !important;
            height: 44px !important;
            min-width: 44px !important;
            min-height: 44px !important;
            max-width: 44px !important;
            max-height: 44px !important;
            border-radius: 50% !important;
            background-image: url('${LOGO_BASE64_SVG}') !important;
            background-size: contain !important;
            background-position: center !important;
            background-repeat: no-repeat !important;
            background-color: rgba(255, 255, 255, 0.1) !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: relative !important;
            z-index: 99999 !important;
            flex-shrink: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            border: 2px solid rgba(6, 95, 70, 0.3) !important;
            box-shadow: 0 2px 8px rgba(45, 212, 191, 0.15) !important;
            pointer-events: auto !important;
        `;
        
        // Insérer au début
        logoParent.insertBefore(base64Logo, logoParent.firstChild);
        
        console.log('✅ BASE64 logo created successfully');
        
        // Test si le SVG complexe marche, sinon utiliser le simple
        setTimeout(() => {
            testAndFallbackToSimple(base64Logo);
        }, 1000);
        
        // Protection continue
        protectBase64Logo(base64Logo);
    }
    
    function testAndFallbackToSimple(logoElement) {
        // Vérifier si le logo est vraiment visible
        const rect = logoElement.getBoundingClientRect();
        const isVisible = rect.width >= 40 && rect.height >= 40;
        
        if (!isVisible) {
            console.log('⚠️ Complex SVG failed - Using simple fallback');
            logoElement.style.setProperty('background-image', `url('${LOGO_BASE64_SIMPLE}')`, 'important');
        } else {
            console.log('✅ Complex SVG working correctly');
        }
    }
    
    function createFloatingBase64Logo() {
        console.log('🌊 Creating floating BASE64 logo...');
        
        const floatingLogo = document.createElement('div');
        floatingLogo.style.cssText = `
            position: fixed !important;
            top: 15px !important;
            left: 15px !important;
            width: 50px !important;
            height: 50px !important;
            border-radius: 50% !important;
            background-image: url('${LOGO_BASE64_SVG}') !important;
            background-size: contain !important;
            background-position: center !important;
            background-repeat: no-repeat !important;
            background-color: rgba(255, 255, 255, 0.1) !important;
            z-index: 999999 !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
            border: 3px solid rgba(6, 95, 70, 0.5) !important;
            cursor: pointer !important;
        `;
        
        floatingLogo.setAttribute('title', 'GolfinThaï - Logo d\'urgence');
        floatingLogo.onclick = () => window.location.href = '#home';
        
        document.body.appendChild(floatingLogo);
        
        console.log('🌊 Floating logo created');
    }
    
    function protectBase64Logo(logoElement) {
        // Protection ultime
        const protectionInterval = setInterval(() => {
            if (!document.body.contains(logoElement)) {
                console.log('🛡️ BASE64 logo disappeared - Recreating!');
                createBase64Logo();
                clearInterval(protectionInterval);
                return;
            }
            
            // Vérifier les styles critiques
            const styles = window.getComputedStyle(logoElement);
            if (styles.display === 'none' || styles.visibility === 'hidden' || styles.opacity === '0') {
                console.log('🛡️ BASE64 logo hidden - Forcing visibility!');
                logoElement.style.setProperty('display', 'block', 'important');
                logoElement.style.setProperty('visibility', 'visible', 'important');
                logoElement.style.setProperty('opacity', '1', 'important');
            }
        }, 5000);
        
        // Arrêter la protection après 2 minutes
        setTimeout(() => {
            clearInterval(protectionInterval);
            console.log('🛡️ BASE64 protection ended');
        }, 120000);
    }
    
})();

console.log('🔥 BASE64 EMERGENCY loaded - Logo will DEFINITELY exist!');