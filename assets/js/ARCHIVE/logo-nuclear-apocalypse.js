/* =============================================
   💀 LOGO NUCLEAR APOCALYPSE - SOLUTION RADICALE
   Bypass TOUT le CSS - Force brute JavaScript
   ============================================= */

(function() {
    'use strict';
    
    // Detect mobile
    const isMobile = window.innerWidth <= 1023 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    if (!isMobile) {
        console.log('🖥️ Desktop - Nuclear not needed');
        return;
    }
    
    console.log('💀 MOBILE LOGO NUCLEAR APOCALYPSE - Starting...');
    
    // Wait for DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', nuclearLogoApocalypse);
    } else {
        nuclearLogoApocalypse();
    }
    
    function nuclearLogoApocalypse() {
        console.log('💥 NUCLEAR APOCALYPSE - Destroying all CSS, creating pure JS logo');
        
        // Détruire tous les logos existants
        destroyAllLogos();
        
        // Créer un logo INDESTRUCTIBLE
        createIndestructibleLogo();
        
        // Surveillance continue
        startNuclearSurveillance();
    }
    
    function destroyAllLogos() {
        console.log('💣 Destroying all existing logos...');
        
        // Trouver TOUS les éléments qui pourraient être des logos
        const allPossibleLogos = document.querySelectorAll(`
            .logo-container-bg,
            svg[title*="GolfinThaï"],
            div[title*="GolfinThaï"],
            [aria-label*="Logo GolfinThaï"],
            [role="img"][title*="GolfinThaï"]
        `);
        
        allPossibleLogos.forEach((logo, index) => {
            console.log(`💥 Destroying logo ${index + 1}:`, logo);
            logo.remove();
        });
        
        console.log(`💀 Destroyed ${allPossibleLogos.length} logos`);
    }
    
    function createIndestructibleLogo() {
        console.log('🛠️ Creating INDESTRUCTIBLE logo...');
        
        // Trouver le container parent
        const logoParent = document.querySelector('.flex.items-center.space-x-3');
        
        if (!logoParent) {
            console.log('❌ Logo parent not found - Creating emergency container');
            createEmergencyLogoContainer();
            return;
        }
        
        // Créer le logo INDESTRUCTIBLE
        const nuclearLogo = document.createElement('div');
        
        // ID unique pour le cibler
        nuclearLogo.id = 'nuclear-logo-' + Date.now();
        nuclearLogo.className = 'nuclear-logo';
        
        // Attributs accessibilité
        nuclearLogo.setAttribute('role', 'img');
        nuclearLogo.setAttribute('aria-label', 'Logo GolfinThaï - Voyages Golf Thaïlande');
        nuclearLogo.setAttribute('title', 'GolfinThaï');
        
        // Styles INDESTRUCTIBLES - Inline avec JS
        const nuclearStyles = {
            'width': '44px',
            'height': '44px',
            'min-width': '44px',
            'min-height': '44px',
            'max-width': '44px',
            'max-height': '44px',
            'border-radius': '50%',
            'background-size': 'contain',
            'background-position': 'center',
            'background-repeat': 'no-repeat',
            'background-color': 'rgba(255, 255, 255, 0.1)',
            'display': 'block',
            'visibility': 'visible',
            'opacity': '1',
            'position': 'relative',
            'z-index': '9999',
            'flex-shrink': '0',
            'margin': '0',
            'padding': '0',
            'border': '2px solid rgba(6, 95, 70, 0.3)',
            'box-shadow': '0 2px 8px rgba(45, 212, 191, 0.15)',
            'content': 'none',
            'pointer-events': 'auto'
        };
        
        // Appliquer les styles de force brute
        Object.keys(nuclearStyles).forEach(property => {
            nuclearLogo.style.setProperty(property, nuclearStyles[property], 'important');
        });
        
        // Tenter de charger l'image
        tryLoadImage(nuclearLogo);
        
        // Insérer au début du container
        logoParent.insertBefore(nuclearLogo, logoParent.firstChild);
        
        console.log('💀 Nuclear logo created:', nuclearLogo);
        
        // Protection contre les modifications
        protectNuclearLogo(nuclearLogo);
    }
    
    function tryLoadImage(logoElement) {
        console.log('🖼️ Trying to load image...');
        
        const imagePaths = [
            './assets/images/logo-golfinthai.jpg',
            'assets/images/logo-golfinthai.jpg',
            '../images/logo-golfinthai.jpg'
        ];
        
        let currentPathIndex = 0;
        
        function tryNextPath() {
            if (currentPathIndex >= imagePaths.length) {
                console.log('❌ All image paths failed - Using SVG fallback');
                useSVGFallback(logoElement);
                return;
            }
            
            const currentPath = imagePaths[currentPathIndex];
            console.log(`🔍 Testing path ${currentPathIndex + 1}:`, currentPath);
            
            const testImg = new Image();
            
            testImg.onload = function() {
                console.log(`✅ Image loaded successfully:`, currentPath);
                logoElement.style.setProperty('background-image', `url('${currentPath}')`, 'important');
                logoElement.style.setProperty('background-color', 'transparent', 'important');
            };
            
            testImg.onerror = function() {
                console.log(`❌ Image failed:`, currentPath);
                currentPathIndex++;
                tryNextPath();
            };
            
            testImg.src = currentPath;
        }
        
        tryNextPath();
    }
    
    function useSVGFallback(logoElement) {
        console.log('🎨 Using SVG fallback...');
        
        logoElement.style.setProperty('background-image', 'none', 'important');
        logoElement.style.setProperty('background-color', 'transparent', 'important');
        
        logoElement.innerHTML = `
            <svg width="44" height="44" viewBox="0 0 64 64" style="display: block; border-radius: 50%;">
                <circle cx="32" cy="32" r="32" fill="#00574B"/>
                <circle cx="32" cy="24" r="6" fill="#FCFCFC"/>
                <path d="M28 30L36 38L34 40L26 32Z" fill="#A3D1C8"/>
                <circle cx="36" cy="38" r="2" fill="#A3D1C8"/>
                <path d="M20 42L32 36L44 42L40 48L24 48Z" fill="#FCFCFC"/>
                <path d="M22 42L32 38L42 42L38 46L26 46Z" fill="#A3D1C8"/>
                <circle cx="32" cy="24" r="1" fill="#00574B"/>
                <circle cx="30" cy="24" r="0.5" fill="#00574B"/>
                <circle cx="34" cy="24" r="0.5" fill="#00574B"/>
                <circle cx="31" cy="22" r="0.5" fill="#00574B"/>
                <circle cx="33" cy="22" r="0.5" fill="#00574B"/>
            </svg>
        `;
        
        console.log('✅ SVG fallback applied');
    }
    
    function protectNuclearLogo(logoElement) {
        // Observer pour protéger contre les modifications
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.target === logoElement) {
                    console.log('🛡️ Nuclear logo protection triggered - Re-applying styles');
                    
                    // Re-appliquer les styles si modifiés
                    logoElement.style.setProperty('width', '44px', 'important');
                    logoElement.style.setProperty('height', '44px', 'important');
                    logoElement.style.setProperty('border-radius', '50%', 'important');
                    logoElement.style.setProperty('z-index', '9999', 'important');
                    logoElement.style.setProperty('display', 'block', 'important');
                    logoElement.style.setProperty('visibility', 'visible', 'important');
                    logoElement.style.setProperty('opacity', '1', 'important');
                }
            });
        });
        
        observer.observe(logoElement, {
            attributes: true,
            attributeOldValue: true
        });
        
        console.log('🛡️ Nuclear logo protection activated');
    }
    
    function createEmergencyLogoContainer() {
        console.log('🚨 Creating emergency logo container...');
        
        // Créer un container d'urgence dans le header
        const header = document.querySelector('header');
        if (!header) {
            console.log('❌ No header found - Cannot create emergency logo');
            return;
        }
        
        const emergencyContainer = document.createElement('div');
        emergencyContainer.style.cssText = `
            position: fixed !important;
            top: 20px !important;
            left: 20px !important;
            z-index: 99999 !important;
            display: flex !important;
            align-items: center !important;
            gap: 10px !important;
            background: rgba(255, 255, 255, 0.9) !important;
            padding: 10px !important;
            border-radius: 25px !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
        `;
        
        header.appendChild(emergencyContainer);
        
        // Créer le logo dans ce container
        createIndestructibleLogo();
        
        // Ajouter le texte
        const logoText = document.createElement('span');
        logoText.textContent = 'GolfinThaï';
        logoText.style.cssText = `
            font-weight: bold !important;
            color: #059669 !important;
            font-size: 16px !important;
        `;
        emergencyContainer.appendChild(logoText);
        
        console.log('✅ Emergency logo container created');
    }
    
    function startNuclearSurveillance() {
        console.log('👁️ Starting nuclear surveillance...');
        
        let checkCount = 0;
        const maxChecks = 20;
        
        const surveillance = setInterval(() => {
            checkCount++;
            
            // Vérifier que le logo existe toujours
            const nuclearLogo = document.querySelector('.nuclear-logo');
            
            if (!nuclearLogo) {
                console.log('🚨 Nuclear logo disappeared - Recreating!');
                createIndestructibleLogo();
            } else {
                // Vérifier qu'il est visible
                const rect = nuclearLogo.getBoundingClientRect();
                const isVisible = rect.width > 0 && rect.height > 0;
                
                if (!isVisible) {
                    console.log('👁️ Nuclear logo invisible - Forcing visibility!');
                    nuclearLogo.style.setProperty('display', 'block', 'important');
                    nuclearLogo.style.setProperty('visibility', 'visible', 'important');
                    nuclearLogo.style.setProperty('opacity', '1', 'important');
                }
            }
            
            if (checkCount >= maxChecks) {
                clearInterval(surveillance);
                console.log('👁️ Nuclear surveillance ended');
            }
        }, 3000);
    }
    
})();

console.log('💀 NUCLEAR LOGO APOCALYPSE loaded - Mobile logo WILL exist!');