/**
 * 🔧 QUANTUM MICRO-ADJUSTMENTS TESTER
 * Validates subtitle positioning + weather widget size
 * Precision testing by fréro + QUANTUM TEAM
 */

// 🎯 MICRO-ADJUSTMENTS VALIDATION
window.quantumMicroTest = function() {
    console.log('🔧 RUNNING MICRO-ADJUSTMENTS TEST...\n');
    
    const tests = [];
    let allPassed = true;
    
    // Get elements
    const subtitle = document.querySelector('.text-xs.text-luxury-emerald-light');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const tempElement = document.querySelector('.weather-temp-mobile');
    const logoContainer = document.querySelector('.flex.items-center.space-x-3:first-child');
    
    if (!subtitle || !weatherWidget || !tempElement || !logoContainer) {
        console.log('❌ CRITICAL: Elements not found!');
        return false;
    }
    
    console.log('📝 SUBTITLE "Voyages Golfiques Thaïlande" ANALYSIS:');
    
    // Test 1: Subtitle positioning and visibility
    const subtitleText = subtitle.textContent || '';
    const subtitleRect = subtitle.getBoundingClientRect();
    const logoRect = logoContainer.getBoundingClientRect();
    
    console.log(`  Text: "${subtitleText}"`);
    console.log(`  Position: x=${Math.round(subtitleRect.left)}, width=${Math.round(subtitleRect.width)}px`);
    console.log(`  Logo container: x=${Math.round(logoRect.left)}, width=${Math.round(logoRect.width)}px`);
    
    // Check if "V" is fully visible (not cut off on left)
    const leftMargin = subtitleRect.left - logoRect.left;
    const subtitleWithinContainer = subtitleRect.left >= logoRect.left - 2; // 2px tolerance
    const subtitleNotOverflowing = subtitleRect.right <= logoRect.right + 5; // 5px tolerance
    
    console.log(`  Left margin: ${Math.round(leftMargin)}px`);
    console.log(`  "V" visible: ${subtitleWithinContainer ? '✅' : '❌'}`);
    console.log(`  No right overflow: ${subtitleNotOverflowing ? '✅' : '❌'}`);
    
    tests.push({
        name: '"Voyages" V fully visible',
        passed: subtitleWithinContainer,
        details: `Left margin: ${Math.round(leftMargin)}px`
    });
    
    tests.push({
        name: 'Subtitle contained properly',
        passed: subtitleNotOverflowing,
        details: `Right edge within bounds: ${subtitleNotOverflowing ? 'Yes' : 'No'}`
    });
    
    console.log('\n🌡️ WEATHER WIDGET SIZE ANALYSIS:');
    
    // Test 2: Weather widget size optimization
    const weatherRect = weatherWidget.getBoundingClientRect();
    console.log(`  Widget width: ${Math.round(weatherRect.width)}px`);
    
    // Should be around 85px (reduced from 90px)
    const widgetSizeOptimal = weatherRect.width >= 80 && weatherRect.width <= 90;
    tests.push({
        name: 'Weather widget size optimized',
        passed: widgetSizeOptimal,
        details: `${Math.round(weatherRect.width)}px (target: 80-90px)`
    });
    
    // Test 3: Temperature still fits perfectly
    const tempRect = tempElement.getBoundingClientRect();
    const tempFits = tempRect.width <= weatherRect.width;
    
    console.log(`  Temperature: "${tempElement.textContent}"`);
    console.log(`  Temp width: ${Math.round(tempRect.width)}px in ${Math.round(weatherRect.width)}px container`);
    console.log(`  Fits perfectly: ${tempFits ? '✅' : '❌'}`);
    
    tests.push({
        name: 'Temperature still fits perfectly',
        passed: tempFits,
        details: `${Math.round(tempRect.width)}px in ${Math.round(weatherRect.width)}px`
    });
    
    // Test 4: Overall layout balance
    const totalWidth = logoRect.width + weatherRect.width;
    const screenWidth = window.innerWidth;
    const layoutBalanced = totalWidth <= screenWidth * 0.85; // Should use max 85% of screen
    
    console.log(`\n📏 LAYOUT BALANCE:`);
    console.log(`  Logo + Weather: ${Math.round(totalWidth)}px of ${screenWidth}px screen`);
    console.log(`  Layout balanced: ${layoutBalanced ? '✅' : '❌'}`);
    
    tests.push({
        name: 'Overall layout balanced',
        passed: layoutBalanced,
        details: `${Math.round(totalWidth)}px used of ${screenWidth}px available`
    });
    
    // Test 5: Micro-positioning CSS applied
    const subtitleStyles = getComputedStyle(subtitle);
    const hasLeftShift = subtitleStyles.left && subtitleStyles.left !== 'auto' && subtitleStyles.left !== '0px';
    
    tests.push({
        name: 'Micro-positioning CSS applied',
        passed: hasLeftShift,
        details: `Left shift: ${subtitleStyles.left || 'none'}`
    });
    
    // Results summary
    console.log(`\n🧪 MICRO-ADJUSTMENTS TEST RESULTS:`);
    tests.forEach((test, i) => {
        const status = test.passed ? '✅' : '❌';
        console.log(`  ${i + 1}. ${status} ${test.name} - ${test.details}`);
        if (!test.passed) allPassed = false;
    });
    
    console.log(`\n${allPassed ? '🎉' : '⚠️'} OVERALL RESULT: ${allPassed ? 'MICRO-ADJUSTMENTS SUCCESS!' : 'Some fine-tuning needed'}`);
    
    if (allPassed) {
        console.log('🔧 PERFECT! "Voyages" fully visible + weather widget optimized! 🏆');
        console.log('💎 Fréro\'s feedback = implemented with precision!');
    } else {
        console.log('🔧 Run quantumMicroEmergencyFix() for additional fine-tuning');
    }
    
    return allPassed;
};

// 🚨 EMERGENCY MICRO-FIX
window.quantumMicroEmergencyFix = function() {
    console.log('🚨 APPLYING EMERGENCY MICRO-ADJUSTMENTS...');
    
    const subtitle = document.querySelector('.text-xs.text-luxury-emerald-light');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    
    if (!subtitle || !weatherWidget) {
        console.log('❌ Elements not found for emergency micro-fix');
        return;
    }
    
    // Force subtitle positioning
    subtitle.style.position = 'relative';
    subtitle.style.left = '3px'; // Bit more shift
    subtitle.style.paddingLeft = '3px';
    subtitle.style.paddingRight = '2px';
    subtitle.style.maxWidth = '94px';
    subtitle.style.overflow = 'visible';
    subtitle.style.whiteSpace = 'nowrap';
    subtitle.style.textAlign = 'center';
    
    // Force weather widget size
    weatherWidget.style.width = '83px';
    weatherWidget.style.minWidth = '83px';
    weatherWidget.style.maxWidth = '86px';
    weatherWidget.style.overflow = 'visible';
    
    console.log('✅ Emergency micro-adjustments applied!');
    console.log('🔧 Testing in 1 second...');
    
    setTimeout(() => {
        window.quantumMicroTest();
    }, 1000);
};

// 🎨 MICRO VISUAL DEBUG
window.quantumMicroVisualDebug = function() {
    console.log('🎨 MICRO-ADJUSTMENTS VISUAL DEBUG...');
    
    const subtitle = document.querySelector('.text-xs.text-luxury-emerald-light');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const logoContainer = document.querySelector('.flex.items-center.space-x-3:first-child');
    
    if (!subtitle || !weatherWidget || !logoContainer) {
        console.log('❌ Elements not found for visual debug');
        return;
    }
    
    // Add visual indicators
    logoContainer.style.border = '1px dashed #FF6B6B';
    logoContainer.style.backgroundColor = 'rgba(255, 107, 107, 0.05)';
    
    subtitle.style.border = '1px solid #FFA500';
    subtitle.style.backgroundColor = 'rgba(255, 165, 0, 0.15)';
    
    weatherWidget.style.border = '1px solid #4ECDC4';
    weatherWidget.style.backgroundColor = 'rgba(78, 205, 196, 0.1)';
    
    console.log('🎨 Visual debug active for 8 seconds:');
    console.log('🔴 Red dashed = Logo container');
    console.log('🟠 Orange solid = "Voyages" subtitle (should be within red area)');
    console.log('🟢 Teal solid = Weather widget (should be compact but functional)');
    
    setTimeout(() => {
        logoContainer.style.border = '';
        logoContainer.style.backgroundColor = '';
        subtitle.style.border = '';
        subtitle.style.backgroundColor = '';
        weatherWidget.style.border = '';
        weatherWidget.style.backgroundColor = '';
        console.log('🧹 Micro visual debug cleaned up');
    }, 8000);
};

// 📏 MEASURE SUBTITLE POSITIONING
window.measureSubtitlePosition = function() {
    console.log('📏 MEASURING SUBTITLE PRECISE POSITIONING...');
    
    const subtitle = document.querySelector('.text-xs.text-luxury-emerald-light');
    const logoContainer = document.querySelector('.flex.items-center.space-x-3:first-child');
    
    if (!subtitle || !logoContainer) {
        console.log('❌ Elements not found');
        return;
    }
    
    const subtitleRect = subtitle.getBoundingClientRect();
    const logoRect = logoContainer.getBoundingClientRect();
    const screenWidth = window.innerWidth;
    
    console.log('📊 PRECISE MEASUREMENTS:');
    console.log(`Screen width: ${screenWidth}px`);
    console.log(`Logo container: ${Math.round(logoRect.left)}px to ${Math.round(logoRect.right)}px (${Math.round(logoRect.width)}px)`);
    console.log(`Subtitle: ${Math.round(subtitleRect.left)}px to ${Math.round(subtitleRect.right)}px (${Math.round(subtitleRect.width)}px)`);
    console.log(`Left edge clearance: ${Math.round(subtitleRect.left)}px`);
    console.log(`Right edge clearance: ${Math.round(screenWidth - subtitleRect.right)}px`);
    
    // Check if "V" is visible
    const vVisible = subtitleRect.left >= 0;
    console.log(`"V" fully visible: ${vVisible ? '✅' : '❌'}`);
    
    return {
        screenWidth,
        logoWidth: Math.round(logoRect.width),
        subtitleWidth: Math.round(subtitleRect.width),
        leftClearance: Math.round(subtitleRect.left),
        rightClearance: Math.round(screenWidth - subtitleRect.right),
        vVisible
    };
};

// 🚀 AUTO-TEST ON LOAD
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        console.log('\n🔧 QUANTUM MICRO-ADJUSTMENTS TESTER LOADED!\n');
        console.log('Available commands:');
        console.log('  🧪 quantumMicroTest() - Test micro-adjustments');
        console.log('  🚨 quantumMicroEmergencyFix() - Emergency fine-tuning');
        console.log('  🎨 quantumMicroVisualDebug() - Visual boundaries (8s)');
        console.log('  📏 measureSubtitlePosition() - Precise measurements');
        console.log('\n🎯 Running auto-test in 4 seconds...\n');
        
        setTimeout(() => {
            window.quantumMicroTest();
        }, 4000);
        
    }, 2000);
});

console.log('🔧 QUANTUM MICRO-ADJUSTMENTS TESTER INITIALIZED!');