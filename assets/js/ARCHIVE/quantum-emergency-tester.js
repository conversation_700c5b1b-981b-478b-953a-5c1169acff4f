/**
 * 🚨 QUANTUM EMERGENCY FIX TESTER
 * Critical validation for urgent repairs:
 * - "GolfinThaï" text visibility (not behind widget)
 * - Weather widget ultra-compact (70px)
 */

// 🚨 EMERGENCY VALIDATION
window.quantumEmergencyTest = function() {
    console.log('🚨 RUNNING EMERGENCY FIX VALIDATION...\n');
    
    const tests = [];
    let allPassed = true;
    let criticalIssues = [];
    
    // Get critical elements
    const logoContainer = document.querySelector('.flex.items-center.space-x-3:first-child');
    const logoTitle = document.querySelector('.header-logo-gradient');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const tempElement = document.querySelector('.weather-temp-mobile');
    
    if (!logoContainer || !logoTitle || !weatherWidget || !tempElement) {
        console.log('❌ CRITICAL: Essential elements not found!');
        return false;
    }
    
    console.log('🚨 CRITICAL ISSUE #1: "GolfinThaï" TEXT VISIBILITY');
    
    // Test 1: "GolfinThaï" text must be visible and accessible
    const logoText = logoTitle.textContent || '';
    const logoComplete = logoText.includes('GolfinThaï');
    
    const logoStyles = getComputedStyle(logoTitle);
    const logoVisible = logoStyles.visibility !== 'hidden' && logoStyles.display !== 'none';
    const logoOpacity = parseFloat(logoStyles.opacity) > 0.5;
    const logoZIndex = parseInt(logoStyles.zIndex) > 1000;
    
    console.log(`  Text content: "${logoText}"`);
    console.log(`  Text complete: ${logoComplete ? '✅' : '❌'}`);
    console.log(`  Visibility: ${logoVisible ? '✅' : '❌'}`);
    console.log(`  Opacity: ${logoStyles.opacity} ${logoOpacity ? '✅' : '❌'}`);
    console.log(`  Z-index: ${logoStyles.zIndex} ${logoZIndex ? '✅' : '❌'}`);
    
    const logoTextOK = logoComplete && logoVisible && logoOpacity && logoZIndex;
    tests.push({
        name: '"GolfinThaï" text fully visible',
        passed: logoTextOK,
        critical: true,
        details: logoTextOK ? 'Text visible and accessible ✅' : 'TEXT HIDDEN OR BEHIND WIDGET ❌'
    });
    
    if (!logoTextOK) {
        criticalIssues.push('LOGO TEXT NOT VISIBLE');
    }
    
    // Test 2: Logo text positioning (not behind weather widget)
    const logoRect = logoTitle.getBoundingClientRect();
    const weatherRect = weatherWidget.getBoundingClientRect();
    
    // Check if logo text overlaps with weather widget
    const noOverlap = logoRect.right <= weatherRect.left + 5; // 5px tolerance
    const logoLeftOfWeather = logoRect.left < weatherRect.left;
    
    console.log(`  Logo text position: x=${Math.round(logoRect.left)}-${Math.round(logoRect.right)}`);
    console.log(`  Weather position: x=${Math.round(weatherRect.left)}-${Math.round(weatherRect.right)}`);
    console.log(`  No overlap: ${noOverlap ? '✅' : '❌'}`);
    console.log(`  Logo left of weather: ${logoLeftOfWeather ? '✅' : '❌'}`);
    
    const positioningOK = noOverlap && logoLeftOfWeather;
    tests.push({
        name: '"GolfinThaï" not behind weather widget',
        passed: positioningOK,
        critical: true,
        details: positioningOK ? 'Proper positioning ✅' : 'OVERLAPPING WITH WEATHER ❌'
    });
    
    if (!positioningOK) {
        criticalIssues.push('LOGO TEXT BEHIND WEATHER WIDGET');
    }
    
    console.log('\n🚨 CRITICAL ISSUE #2: WEATHER WIDGET SIZE');
    
    // Test 3: Weather widget must be ultra-compact (~70px)
    const weatherWidth = Math.round(weatherRect.width);
    const weatherUltraCompact = weatherWidth >= 68 && weatherWidth <= 78;
    
    console.log(`  Weather widget width: ${weatherWidth}px (target: ~70px)`);
    console.log(`  Ultra-compact achieved: ${weatherUltraCompact ? '✅' : '❌'}`);
    
    tests.push({
        name: 'Weather widget ultra-compact (70px)',
        passed: weatherUltraCompact,
        critical: true,
        details: weatherUltraCompact ? `${weatherWidth}px - Perfect! ✅` : `${weatherWidth}px - STILL TOO BIG ❌`
    });
    
    if (!weatherUltraCompact) {
        criticalIssues.push('WEATHER WIDGET STILL TOO BIG');
    }
    
    // Test 4: Temperature still fits in ultra-compact widget
    const tempText = tempElement.textContent || '--°';
    const tempRect = tempElement.getBoundingClientRect();
    const tempFitsInCompact = tempRect.width <= weatherRect.width - 3; // 3px tight margin
    
    console.log(`  Temperature: "${tempText}"`);
    console.log(`  Temp fits in ultra-compact: ${tempFitsInCompact ? '✅' : '❌'}`);
    console.log(`  Space: ${Math.round(tempRect.width)}px in ${weatherWidth}px`);
    
    tests.push({
        name: 'Temperature fits in ultra-compact widget',
        passed: tempFitsInCompact,
        critical: false,
        details: `${Math.round(tempRect.width)}px in ${weatherWidth}px`
    });
    
    // Test 5: Layout efficiency after emergency fixes
    const logoContainerRect = logoContainer.getBoundingClientRect();
    const totalWidth = logoContainerRect.width + weatherRect.width;
    const screenWidth = window.innerWidth;
    const efficiency = Math.round((totalWidth / screenWidth) * 100);
    
    console.log(`\n📊 EMERGENCY LAYOUT ANALYSIS:`);
    console.log(`  Logo container: ${Math.round(logoContainerRect.width)}px`);
    console.log(`  Weather widget: ${weatherWidth}px`);
    console.log(`  Combined: ${Math.round(totalWidth)}px`);
    console.log(`  Screen efficiency: ${efficiency}%`);
    
    const efficiencyOK = efficiency >= 50 && efficiency <= 70; // Should be reasonable
    tests.push({
        name: 'Layout efficiency reasonable',
        passed: efficiencyOK,
        critical: false,
        details: `${efficiency}% screen usage`
    });
    
    // Results summary
    console.log(`\n🧪 EMERGENCY FIX TEST RESULTS:`);
    tests.forEach((test, i) => {
        const status = test.passed ? '✅' : (test.critical ? '🚨' : '❌');
        const priority = test.critical ? ' [CRITICAL]' : '';
        console.log(`  ${i + 1}. ${status} ${test.name}${priority} - ${test.details}`);
        if (!test.passed) allPassed = false;
    });
    
    // Critical issues summary
    if (criticalIssues.length > 0) {
        console.log(`\n🚨 CRITICAL ISSUES DETECTED:`);
        criticalIssues.forEach((issue, i) => {
            console.log(`  ${i + 1}. ${issue}`);
        });
    }
    
    console.log(`\n${allPassed ? '🎉' : '🚨'} OVERALL RESULT: ${allPassed ? 'EMERGENCY FIXES SUCCESSFUL!' : 'CRITICAL ISSUES REMAIN'}`);
    
    if (allPassed) {
        console.log('✅ "GolfinThaï" is visible and accessible!');
        console.log('✅ Weather widget is ultra-compact as requested!');
        console.log('🎯 Fréro\'s issues have been resolved!');
    } else {
        console.log('🔧 Run quantumEmergencyRepair() for additional fixes');
        
        if (criticalIssues.length > 0) {
            console.log('⚠️ MANUAL INSPECTION RECOMMENDED - Check browser visually');
        }
    }
    
    return allPassed;
};

// 🔧 EMERGENCY REPAIR FUNCTION
window.quantumEmergencyRepair = function() {
    console.log('🔧 APPLYING ADDITIONAL EMERGENCY REPAIRS...');
    
    const logoTitle = document.querySelector('.header-logo-gradient');
    const logoContainer = document.querySelector('.flex.items-center.space-x-3:first-child');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const tempElement = document.querySelector('.weather-temp-mobile');
    
    if (!logoTitle || !logoContainer || !weatherWidget || !tempElement) {
        console.log('❌ Elements not found for emergency repair');
        return;
    }
    
    console.log('🚨 Forcing critical visibility fixes...');
    
    // FORCE logo text visibility
    logoTitle.style.zIndex = '2000';
    logoTitle.style.position = 'relative';
    logoTitle.style.display = 'block';
    logoTitle.style.visibility = 'visible';
    logoTitle.style.opacity = '1';
    logoTitle.style.overflow = 'visible';
    logoTitle.style.maxWidth = '60px';
    logoTitle.style.fontSize = '0.8rem';
    logoTitle.style.fontWeight = '700';
    logoTitle.style.whiteSpace = 'nowrap';
    logoTitle.style.textOverflow = 'clip';
    
    // FORCE logo container layout
    logoContainer.style.display = 'flex';
    logoContainer.style.flexDirection = 'row';
    logoContainer.style.alignItems = 'center';
    logoContainer.style.gap = '0.3rem';
    logoContainer.style.width = '100px';
    logoContainer.style.maxWidth = '100px';
    logoContainer.style.overflow = 'visible';
    logoContainer.style.zIndex = '1500';
    
    // FORCE ultra-compact weather widget
    weatherWidget.style.width = '68px';
    weatherWidget.style.minWidth = '68px';
    weatherWidget.style.maxWidth = '72px';
    weatherWidget.style.zIndex = '500';
    weatherWidget.style.overflow = 'visible';
    weatherWidget.style.padding = '0.3rem 0.15rem';
    
    // FORCE temperature fit
    tempElement.style.fontSize = '0.8rem';
    tempElement.style.fontWeight = '600';
    tempElement.style.letterSpacing = '0.1px';
    tempElement.style.overflow = 'visible';
    tempElement.style.whiteSpace = 'nowrap';
    tempElement.style.textOverflow = 'clip';
    tempElement.style.padding = '0';
    
    console.log('✅ Emergency repairs applied!');
    console.log('🧪 Testing in 1 second...');
    
    setTimeout(() => {
        window.quantumEmergencyTest();
    }, 1000);
};

// 🎨 EMERGENCY VISUAL DEBUG
window.quantumEmergencyVisualDebug = function() {
    console.log('🎨 EMERGENCY VISUAL DEBUG - HIGHLIGHT CRITICAL AREAS...');
    
    const logoContainer = document.querySelector('.flex.items-center.space-x-3:first-child');
    const logoTitle = document.querySelector('.header-logo-gradient');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    
    if (!logoContainer || !logoTitle || !weatherWidget) {
        console.log('❌ Elements not found for visual debug');
        return;
    }
    
    // Highlight critical areas with bright colors
    logoContainer.style.border = '3px solid #FF0000';
    logoContainer.style.backgroundColor = 'rgba(255, 0, 0, 0.1)';
    
    logoTitle.style.border = '2px solid #00FF00';
    logoTitle.style.backgroundColor = 'rgba(0, 255, 0, 0.2)';
    logoTitle.style.textShadow = '0 0 5px #00FF00';
    
    weatherWidget.style.border = '3px solid #0000FF';
    weatherWidget.style.backgroundColor = 'rgba(0, 0, 255, 0.1)';
    
    console.log('🎨 Emergency visual debug active for 15 seconds:');
    console.log('🔴 RED = Logo container (should contain logo + text)');
    console.log('🟢 GREEN = "GolfinThaï" text (MUST BE VISIBLE AND READABLE)');
    console.log('🔵 BLUE = Weather widget (should be ~70px width)');
    console.log('👁️ MANUALLY VERIFY: Can you see green "GolfinThaï" text clearly?');
    
    setTimeout(() => {
        logoContainer.style.border = '';
        logoContainer.style.backgroundColor = '';
        logoTitle.style.border = '';
        logoTitle.style.backgroundColor = '';
        logoTitle.style.textShadow = '';
        weatherWidget.style.border = '';
        weatherWidget.style.backgroundColor = '';
        console.log('🧹 Emergency visual debug cleaned up');
    }, 15000);
};

// 📏 EMERGENCY MEASUREMENTS
window.measureEmergencyLayout = function() {
    console.log('📏 EMERGENCY LAYOUT MEASUREMENTS...\n');
    
    const logoContainer = document.querySelector('.flex.items-center.space-x-3:first-child');
    const logoTitle = document.querySelector('.header-logo-gradient');
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    
    if (!logoContainer || !logoTitle || !weatherWidget) {
        console.log('❌ Elements not found for measurements');
        return;
    }
    
    const logoContainerRect = logoContainer.getBoundingClientRect();
    const logoTitleRect = logoTitle.getBoundingClientRect();
    const weatherRect = weatherWidget.getBoundingClientRect();
    const screenWidth = window.innerWidth;
    
    console.log('📊 CRITICAL MEASUREMENTS:');
    console.log(`Screen width: ${screenWidth}px`);
    console.log(`Logo container: ${Math.round(logoContainerRect.width)}px (x=${Math.round(logoContainerRect.left)}-${Math.round(logoContainerRect.right)})`);
    console.log(`"GolfinThaï" text: ${Math.round(logoTitleRect.width)}px (x=${Math.round(logoTitleRect.left)}-${Math.round(logoTitleRect.right)})`);
    console.log(`Weather widget: ${Math.round(weatherRect.width)}px (x=${Math.round(weatherRect.left)}-${Math.round(weatherRect.right)})`);
    
    // Check overlaps
    const textWeatherGap = weatherRect.left - logoTitleRect.right;
    const overlap = textWeatherGap < 0;
    
    console.log(`\n🔍 OVERLAP ANALYSIS:`);
    console.log(`Gap between text and weather: ${Math.round(textWeatherGap)}px`);
    console.log(`Overlap detected: ${overlap ? '🚨 YES - CRITICAL ISSUE!' : '✅ NO - Good separation'}`);
    
    // Visibility check
    const logoStyles = getComputedStyle(logoTitle);
    console.log(`\n👁️ VISIBILITY CHECK:`);
    console.log(`Text visibility: ${logoStyles.visibility}`);
    console.log(`Text display: ${logoStyles.display}`);
    console.log(`Text opacity: ${logoStyles.opacity}`);
    console.log(`Text z-index: ${logoStyles.zIndex}`);
    
    return {
        logoContainer: Math.round(logoContainerRect.width),
        logoText: Math.round(logoTitleRect.width),
        weather: Math.round(weatherRect.width),
        gap: Math.round(textWeatherGap),
        overlap,
        visible: logoStyles.visibility === 'visible' && logoStyles.display !== 'none'
    };
};

// 🚀 AUTO-TEST ON LOAD
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        console.log('\n🚨 QUANTUM EMERGENCY FIX TESTER LOADED!\n');
        console.log('Emergency validation for critical issues:');
        console.log('  🎯 "GolfinThaï" text visibility (not behind widget)');
        console.log('  🎯 Weather widget ultra-compact (70px)');
        console.log('\nAvailable emergency commands:');
        console.log('  🧪 quantumEmergencyTest() - Validate emergency fixes');
        console.log('  🔧 quantumEmergencyRepair() - Additional repairs');
        console.log('  🎨 quantumEmergencyVisualDebug() - Highlight critical areas (15s)');
        console.log('  📏 measureEmergencyLayout() - Critical measurements');
        console.log('\n🚨 Running emergency validation in 2 seconds...\n');
        
        setTimeout(() => {
            window.quantumEmergencyTest();
        }, 2000);
        
    }, 1500);
});

console.log('🚨 QUANTUM EMERGENCY FIX TESTER INITIALIZED!');