/**
 * 🍎 iPhone EXPAND FORCE PATCHER
 * Solution ultra-agressive pour iPhone UNIQUEMENT
 * Force l'expansion "En savoir plus" sans toucher aux CSS existants
 * Méthode: DOM manipulation directe + event override
 */

class iPhoneExpandForcePatcher {
    constructor() {
        this.isIPhone = this.detectIPhone();
        this.patches = new Map();
        this.debugMode = true;
        
        if (this.isIPhone) {
            console.log('🍎 iPhone détecté - Activation force patcher');
            this.init();
        } else {
            console.log('📱 Non-iPhone - Patcher inactif');
        }
    }
    
    /**
     * 🔍 DÉTECTER iPhone/iPad
     */
    detectIPhone() {
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        const isSafari = /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);
        const hasTouch = 'ontouchstart' in window;
        
        return isIOS && hasTouch;
    }
    
    /**
     * 🚀 INITIALISATION
     */
    init() {
        // Attendre que tout soit chargé
        document.addEventListener('DOMContentLoaded', () => {
            this.startPatching();
        });
        
        // Fallback si DOMContentLoaded déjà passé
        if (document.readyState !== 'loading') {
            this.startPatching();
        }
        
        // Activation retardée pour être sûr
        setTimeout(() => this.startPatching(), 2000);
    }
    
    /**
     * 🔧 DÉMARRER LE PATCHING
     */
    startPatching() {
        console.log('🔧 iPhone Patcher - Démarrage surveillance');
        
        // Observer les modales qui s'ouvrent
        this.observeModals();
        
        // Vérifier les modales déjà ouvertes
        this.patchExistingModals();
        
        // Check périodique pour être sûr
        setInterval(() => this.patchExistingModals(), 1000);
    }
    
    /**
     * 👁️ OBSERVER LES MODALES
     */
    observeModals() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                // Détecter nouvelles modales
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === 1) {
                            this.checkForModal(node);
                        }
                    });
                }
                
                // Détecter modales qui deviennent visibles
                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                    const element = mutation.target;
                    if (this.isModal(element) && this.isModalVisible(element)) {
                        this.patchModal(element);
                    }
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'class']
        });
        
        console.log('👁️ Observer actif pour détection modales');
    }
    
    /**
     * 🔍 VÉRIFIER SI C'EST UNE MODAL
     */
    isModal(element) {
        if (!element || !element.classList) return false;
        
        return element.classList.contains('fullscreen-modal') ||
               element.classList.contains('modal') ||
               element.classList.contains('image-modal') ||
               element.querySelector('.seo-panel, .expand-btn');
    }
    
    /**
     * 🔍 VÉRIFIER SI MODAL VISIBLE
     */
    isModalVisible(element) {
        if (!element) return false;
        
        const style = getComputedStyle(element);
        return style.display !== 'none' && 
               parseFloat(style.opacity) > 0 &&
               (style.display === 'flex' || style.display === 'block');
    }
    
    /**
     * 🔍 CHERCHER MODALES DANS UN ÉLÉMENT
     */
    checkForModal(element) {
        if (this.isModal(element)) {
            this.patchModal(element);
        }
        
        // Chercher dans les enfants
        const modals = element.querySelectorAll ? 
            element.querySelectorAll('.fullscreen-modal, .modal, .image-modal, [class*="modal"]') : [];
        
        modals.forEach(modal => {
            if (this.isModalVisible(modal)) {
                this.patchModal(modal);
            }
        });
    }
    
    /**
     * 🛠️ PATCHER MODALES EXISTANTES
     */
    patchExistingModals() {
        const selectors = [
            '.fullscreen-modal',
            '.modal',
            '.image-modal',
            '[class*="modal"]'
        ];
        
        selectors.forEach(selector => {
            const modals = document.querySelectorAll(selector);
            modals.forEach(modal => {
                if (this.isModalVisible(modal)) {
                    this.patchModal(modal);
                }
            });
        });
    }
    
    /**
     * 🔧 PATCHER UNE MODAL SPÉCIFIQUE
     */
    patchModal(modal) {
        const modalId = this.getModalId(modal);
        
        // Éviter double patching
        if (this.patches.has(modalId)) {
            return;
        }
        
        console.log('🔧 Patching modal:', modalId);
        
        // Trouver le bouton expand
        const expandBtn = this.findExpandButton(modal);
        if (!expandBtn) {
            console.log('❌ Pas de bouton expand trouvé dans modal');
            return;
        }
        
        // Trouver le texte à agrandir
        const textElement = this.findTextElement(modal);
        if (!textElement) {
            console.log('❌ Pas de texte à agrandir trouvé dans modal');
            return;
        }
        
        // PATCH AGGRESSIF
        this.forceExpandFunctionality(expandBtn, textElement, modalId);
        
        this.patches.set(modalId, { modal, expandBtn, textElement });
        console.log('✅ Modal patchée avec succès');
    }
    
    /**
     * 🆔 OBTENIR ID UNIQUE MODAL
     */
    getModalId(modal) {
        return modal.id || `modal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * 🔍 TROUVER BOUTON EXPAND
     */
    findExpandButton(modal) {
        const selectors = [
            '.expand-btn',
            '.expand-button',
            'button[class*="expand"]',
            'button[onclick*="expand"]',
            'button:contains("En savoir plus")',
            'button:contains("Réduire")'
        ];
        
        for (const selector of selectors) {
            const btn = modal.querySelector(selector);
            if (btn) return btn;
        }
        
        // Recherche par texte
        const buttons = modal.querySelectorAll('button');
        for (const btn of buttons) {
            const text = btn.textContent.toLowerCase();
            if (text.includes('savoir') || text.includes('plus') || text.includes('réduire')) {
                return btn;
            }
        }
        
        return null;
    }
    
    /**
     * 🔍 TROUVER ÉLÉMENT TEXTE
     */
    findTextElement(modal) {
        const selectors = [
            '.seo-full',
            '.full-text',
            '.modal-full-text',
            '.expand-text',
            '[class*="full"]',
            '[class*="expand"]'
        ];
        
        for (const selector of selectors) {
            const element = modal.querySelector(selector);
            if (element) return element;
        }
        
        return null;
    }
    
    /**
     * 💥 FORCER LA FONCTIONNALITÉ EXPAND
     */
    forceExpandFunctionality(expandBtn, textElement, modalId) {
        console.log('💥 Force patching button functionality');
        
        // État d'expansion
        let isExpanded = false;
        
        // Fonction d'expansion ULTRA-AGRESSIVE
        const forceExpand = () => {
            console.log('🚀 FORCE EXPAND');
            
            // Méthode 1: Style inline forcé
            textElement.style.setProperty('display', 'block', 'important');
            textElement.style.setProperty('opacity', '1', 'important');
            textElement.style.setProperty('max-height', '2000px', 'important');
            textElement.style.setProperty('height', 'auto', 'important');
            textElement.style.setProperty('overflow', 'visible', 'important');
            textElement.style.setProperty('visibility', 'visible', 'important');
            textElement.style.setProperty('position', 'relative', 'important');
            textElement.style.setProperty('z-index', '999999', 'important');
            
            // Méthode 2: Classes CSS
            textElement.classList.remove('hidden', 'collapsed', 'seo-collapsed');
            textElement.classList.add('expanded', 'seo-expanded', 'visible');
            
            // Méthode 3: Attributs
            textElement.removeAttribute('hidden');
            textElement.setAttribute('aria-expanded', 'true');
            
            // Méthode 4: Force repaint
            textElement.offsetHeight;
            
            // Update button
            expandBtn.textContent = 'Réduire ↑';
            isExpanded = true;
            
            console.log('✅ Force expand applied');
        };
        
        // Fonction de collapse
        const forceCollapse = () => {
            console.log('🔻 FORCE COLLAPSE');
            
            textElement.style.setProperty('display', 'none', 'important');
            textElement.style.setProperty('opacity', '0', 'important');
            textElement.style.setProperty('max-height', '0', 'important');
            textElement.style.setProperty('overflow', 'hidden', 'important');
            
            textElement.classList.remove('expanded', 'seo-expanded', 'visible');
            textElement.classList.add('hidden', 'collapsed', 'seo-collapsed');
            
            expandBtn.textContent = 'En savoir plus ↓';
            isExpanded = false;
            
            console.log('✅ Force collapse applied');
        };
        
        // REMPLACER COMPLÈTEMENT les event listeners
        const newBtn = expandBtn.cloneNode(true);
        expandBtn.parentNode.replaceChild(newBtn, expandBtn);
        
        // Toggle function
        const toggle = (e) => {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();
            
            console.log('🍎 iPhone toggle triggered, expanded:', isExpanded);
            
            if (isExpanded) {
                forceCollapse();
            } else {
                forceExpand();
            }
        };
        
        // TOUS LES EVENTS POSSIBLES
        newBtn.addEventListener('touchstart', (e) => {
            newBtn.style.transform = 'scale(0.95)';
        }, { passive: true });
        
        newBtn.addEventListener('touchend', (e) => {
            newBtn.style.transform = '';
            setTimeout(() => toggle(e), 10);
        }, { passive: false });
        
        newBtn.addEventListener('click', toggle, { passive: false });
        newBtn.addEventListener('tap', toggle, { passive: false });
        newBtn.addEventListener('mouseup', toggle, { passive: false });
        
        // Style touch optimisé
        newBtn.style.setProperty('touch-action', 'manipulation', 'important');
        newBtn.style.setProperty('-webkit-tap-highlight-color', 'transparent', 'important');
        newBtn.style.setProperty('min-height', '44px', 'important');
        newBtn.style.setProperty('min-width', '44px', 'important');
        
        console.log('🍎 Button patchée avec events iPhone');
        
        // Test automatique après 500ms
        setTimeout(() => {
            console.log('🧪 Test automatique de l\'expansion...');
            if (!isExpanded) {
                forceExpand();
                setTimeout(() => forceCollapse(), 1000);
            }
        }, 500);
    }
    
    /**
     * 🧪 TEST MANUEL
     */
    testExpand() {
        console.log('🧪 Test manuel iPhone expand');
        
        this.patches.forEach((patch, modalId) => {
            console.log(`Testing modal ${modalId}`);
            
            const { textElement } = patch;
            
            // Force expand
            textElement.style.setProperty('display', 'block', 'important');
            textElement.style.setProperty('opacity', '1', 'important');
            textElement.style.setProperty('max-height', '2000px', 'important');
            textElement.style.setProperty('visibility', 'visible', 'important');
            
            console.log('✅ Test expand applied');
        });
    }
}

// 🚀 ACTIVATION iPhone UNIQUEMENT
let iPhonePatcher;

// Fonction globale de test
window.testIPhoneExpand = () => {
    if (iPhonePatcher) {
        iPhonePatcher.testExpand();
    } else {
        console.log('❌ iPhone patcher not active');
    }
};

// Initialisation
function initIPhonePatcher() {
    iPhonePatcher = new iPhoneExpandForcePatcher();
    window.iPhonePatcher = iPhonePatcher;
}

// Démarrer immédiatement
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initIPhonePatcher);
} else {
    initIPhonePatcher();
}

// Fallback
setTimeout(initIPhonePatcher, 100);

console.log('🍎 iPhone Expand Force Patcher loaded');

/**
 * ✅ STRATÉGIE:
 * 
 * 1. 🎯 Détection iPhone/Safari uniquement
 * 2. 👁️ Observer toutes les modales qui s'ouvrent
 * 3. 🔧 Patcher chaque bouton "En savoir plus" trouvé
 * 4. 💥 Remplacer complètement la logique du bouton
 * 5. 🚀 Force l'expansion par style inline (priorité max)
 * 6. 🧪 Test automatique + fonction test manuelle
 * 
 * Cette fois, je ne touche à AUCUN CSS existant !
 * Je force juste le comportement sur iPhone uniquement.
 */