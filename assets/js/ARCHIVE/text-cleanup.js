/**
 * 🧹 TEXT CLEANUP SCRIPT - REMOVE ALL "LIRE LA SUITE" CHAOS
 * Execute immediately to clean up any existing mess
 */

(function() {
    'use strict';
    
    console.log('🧹 Text Cleanup: Starting immediate cleanup...');
    
    /**
     * 🚫 REMOVE ALL READ MORE BUTTONS
     */
    function removeReadMoreButtons() {
        const selectors = [
            '.read-more-btn',
            '.read-less-btn',
            '.smart-read-more',
            '.smart-read-less',
            '[class*="read-more"]',
            '[class*="read-less"]',
            'button[data-action="read-more"]',
            'button[data-action="read-less"]'
        ];
        
        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                el.remove();
                console.log('🗑️ Removed button:', selector);
            });
        });
    }
    
    /**
     * 📖 RESTORE ALL TEXT CONTENT
     */
    function restoreFullText() {
        const managedElements = document.querySelectorAll('[data-smart-managed]');
        
        managedElements.forEach(element => {
            // Remove smart text management
            element.removeAttribute('data-smart-managed');
            
            // Reset styles
            element.style.maxHeight = '';
            element.style.overflow = '';
            element.style.textOverflow = '';
            element.style.webkitLineClamp = '';
            element.style.lineClamp = '';
            
            // Remove truncation classes
            element.classList.remove(
                'text-truncated', 
                'smart-truncated', 
                'truncated', 
                'collapsed', 
                'smart-collapsed'
            );
            
            console.log('📖 Restored full text for element');
        });
    }
    
    /**
     * 🎯 FORCE SHOW ALL PARAGRAPHS
     */
    function showAllParagraphs() {
        const hiddenElements = document.querySelectorAll('.hidden-paragraph, .truncated-content');
        
        hiddenElements.forEach(element => {
            element.style.display = 'block';
            element.style.opacity = '1';
            element.style.visibility = 'visible';
            element.style.maxHeight = 'none';
            
            element.classList.remove('hidden-paragraph', 'truncated-content');
            
            console.log('👁️ Showed hidden element');
        });
    }
    
    /**
     * 🚀 EXECUTE CLEANUP
     */
    function executeCleanup() {
        removeReadMoreButtons();
        restoreFullText();
        showAllParagraphs();
        
        console.log('✅ Text cleanup completed - All text is now visible!');
    }
    
    // Execute immediately
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', executeCleanup);
    } else {
        executeCleanup();
    }
    
    // Execute again after 1 second to catch any delayed elements
    setTimeout(executeCleanup, 1000);
    
    // Execute again after 3 seconds for any async content
    setTimeout(executeCleanup, 3000);
    
    console.log('🧹 Text Cleanup Script: Active and monitoring');
    
})();
