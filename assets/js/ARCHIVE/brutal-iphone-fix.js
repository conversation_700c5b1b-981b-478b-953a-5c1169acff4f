/**
 * 🔥 BRUTAL IPHONE FIX - Contrôle Total Bouton
 * 
 * PROBLÈME FINAL identifié:
 * - <PERSON>tat cohérent ✅
 * - Fix appliqué ✅ 
 * - MAIS clics ne déclenchent pas le toggle ❌
 * - Conflit entre events Dovito vs Final Fix
 * 
 * SOLUTION BRUTALE:
 * - SUPPRIME tous les event listeners existants du bouton
 * - REMPLACE le bouton complètement pour nettoyer les events
 * - AJOUTE seulement NOS events touch qui marchent
 * - CONTRÔLE TOTAL = AUCUN CONFLIT
 */

class BrutalIPhoneFix {
    constructor() {
        this.isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        this.isSafari = /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);
        this.processedModals = new Set();
        
        if (this.isIOS && this.isSafari) {
            this.init();
            console.log('🔥 Brutal iPhone Fix: iPhone Safari - MODE BRUTAL activé');
        } else {
            console.log('🔥 Brutal iPhone Fix: Non-iPhone - pas nécessaire');
        }
    }
    
    init() {
        // Observer AGRESSIF des modales
        this.brutalObserver();
        
        // Fix immédiat si modal déjà ouverte
        setTimeout(() => this.brutalFixAll(), 1000);
        
        // Backup périodique pour être SÛR
        setInterval(() => this.brutalFixAll(), 3000);
    }
    
    brutalObserver() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                if (mutation.type === 'attributes' && 
                    mutation.attributeName === 'style') {
                    const modal = mutation.target;
                    if (this.isModalVisible(modal) && !this.processedModals.has(modal)) {
                        console.log('🔥 Brutal iPhone Fix: NOUVELLE modal détectée - BRUTAL FIX');
                        setTimeout(() => this.brutalFixModal(modal), 100);
                    }
                }
            });
        });
        
        // Observer TOUT
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'class']
        });
        
        console.log('🔥 Brutal Observer: ACTIF - mode surveillance agressive');
    }
    
    isModalVisible(element) {
        if (!element || element.nodeType !== 1) return false;
        
        const style = window.getComputedStyle(element);
        const isVisible = style.display !== 'none' && 
                         parseFloat(style.opacity) > 0;
        
        const isModal = element.className.includes('modal');
        return isVisible && isModal;
    }
    
    brutalFixAll() {
        const modals = document.querySelectorAll('[class*="modal"]');
        modals.forEach(modal => {
            if (this.isModalVisible(modal) && !this.processedModals.has(modal)) {
                this.brutalFixModal(modal);
            }
        });
    }
    
    brutalFixModal(modal) {
        console.log(`🔥 Brutal iPhone Fix: TRAITEMENT BRUTAL modal ${modal.className}`);
        
        // Marquer comme traité pour éviter double traitement
        this.processedModals.add(modal);
        
        // Trouver bouton et texte
        const button = this.findTargetButton(modal);
        const text = this.findTargetText(modal);
        
        if (!button || !text) {
            console.log('🔥 Brutal iPhone Fix: Bouton ou texte INTROUVABLE - abandon');
            return;
        }
        
        console.log('🔥 Brutal iPhone Fix: Bouton ET texte trouvés - REPLACEMENT BRUTAL');
        
        // REMPLACEMENT BRUTAL du bouton
        this.brutalReplaceButton(button, text);
    }
    
    findTargetButton(modal) {
        // Chercher TOUS les boutons
        const allButtons = modal.querySelectorAll('button, [class*="btn"], [onclick]');
        
        for (let button of allButtons) {
            const text = button.textContent || button.innerHTML || '';
            if (text.includes('savoir') || text.includes('Réduire') || 
                text.includes('↑') || text.includes('↓')) {
                console.log(`🔥 Brutal: BOUTON CIBLE trouvé: "${text}"`);
                return button;
            }
        }
        
        console.log('🔥 Brutal: AUCUN bouton cible trouvé');
        return null;
    }
    
    findTargetText(modal) {
        const selectors = [
            '.dovito-seo-full',
            '.seo-full',
            '[class*="seo-full"]',
            '[class*="full"]'
        ];
        
        for (let selector of selectors) {
            const elements = modal.querySelectorAll(selector);
            for (let element of elements) {
                if (element.textContent && element.textContent.length > 30) {
                    console.log(`🔥 Brutal: TEXTE CIBLE trouvé: ${selector}`);
                    return element;
                }
            }
        }
        
        console.log('🔥 Brutal: AUCUN texte cible trouvé');
        return null;
    }
    
    brutalReplaceButton(oldButton, targetText) {
        console.log('🔥 Brutal: REPLACEMENT TOTAL du bouton - suppression de TOUS les events');
        
        // Créer NOUVEAU bouton propre
        const newButton = document.createElement('button');
        newButton.className = oldButton.className;
        newButton.textContent = oldButton.textContent;
        newButton.innerHTML = oldButton.innerHTML;
        
        // CSS iPhone OPTIMAL
        newButton.style.cssText = `
            background: linear-gradient(135deg, #00ff88, #00aaff) !important;
            color: black !important;
            border: none !important;
            padding: 18px 30px !important;
            border-radius: 12px !important;
            font-size: 18px !important;
            font-weight: bold !important;
            cursor: pointer !important;
            -webkit-touch-callout: none !important;
            -webkit-tap-highlight-color: transparent !important;
            touch-action: manipulation !important;
            user-select: none !important;
            min-width: 200px !important;
            min-height: 60px !important;
            box-shadow: 0 6px 20px rgba(0, 255, 136, 0.4) !important;
            position: relative !important;
            z-index: 100001 !important;
            display: block !important;
            margin: 0 auto !important;
        `;
        
        // REMPLACER dans le DOM
        oldButton.parentNode.replaceChild(newButton, oldButton);
        
        console.log('🔥 Brutal: Bouton REMPLACÉ - ajout events BRUTAUX');
        
        // AJOUTER events BRUTAUX
        this.addBrutalEvents(newButton, targetText);
    }
    
    addBrutalEvents(button, text) {
        let touchInProgress = false;
        let touchStartTime = 0;
        let hasMoved = false;
        
        // Fonction toggle BRUTALE et SIMPLE
        const brutalToggle = () => {
            console.log('🔥 Brutal: TOGGLE BRUTAL déclenché !');
            
            const currentText = button.textContent || button.innerHTML;
            const isCurrentlyVisible = window.getComputedStyle(text).display !== 'none';
            
            console.log(`🔥 État avant: "${currentText}", visible=${isCurrentlyVisible}`);
            
            if (currentText.includes('Réduire')) {
                // CACHER
                text.style.setProperty('display', 'none', 'important');
                text.style.setProperty('opacity', '0', 'important');
                text.style.setProperty('max-height', '0', 'important');
                
                button.textContent = 'En savoir plus ↓';
                button.innerHTML = 'En savoir plus ↓';
                
                console.log('🔥 BRUTAL ACTION: Texte CACHÉ → "En savoir plus ↓"');
            } else {
                // MONTRER
                text.style.setProperty('display', 'block', 'important');
                text.style.setProperty('opacity', '1', 'important');
                text.style.setProperty('max-height', 'none', 'important');
                text.style.setProperty('overflow', 'visible', 'important');
                
                button.textContent = 'Réduire ↑';
                button.innerHTML = 'Réduire ↑';
                
                console.log('🔥 BRUTAL ACTION: Texte VISIBLE → "Réduire ↑"');
            }
        };
        
        // TOUCH EVENTS BRUTAUX
        button.addEventListener('touchstart', (e) => {
            touchInProgress = true;
            touchStartTime = Date.now();
            hasMoved = false;
            
            // Feedback visuel BRUTAL
            button.style.setProperty('transform', 'scale(0.95)', 'important');
            button.style.setProperty('box-shadow', '0 4px 15px rgba(0, 255, 136, 0.7)', 'important');
            
            console.log('🔥 Brutal: TouchStart BRUTAL');
        }, { passive: false, capture: true });
        
        button.addEventListener('touchmove', (e) => {
            hasMoved = true;
            console.log('🔥 Brutal: TouchMove - MOUVEMENT détecté');
        }, { passive: false, capture: true });
        
        button.addEventListener('touchend', (e) => {
            e.preventDefault();
            e.stopImmediatePropagation();
            
            // Reset visuel
            button.style.setProperty('transform', 'scale(1)', 'important');
            button.style.setProperty('box-shadow', '0 6px 20px rgba(0, 255, 136, 0.4)', 'important');
            
            const duration = Date.now() - touchStartTime;
            
            if (touchInProgress && !hasMoved && duration < 800) {
                console.log('🔥 Brutal: TouchEnd VALIDE - EXÉCUTION TOGGLE BRUTAL !');
                setTimeout(() => brutalToggle(), 10);
            } else {
                console.log(`🔥 Brutal: TouchEnd REJETÉ - moved:${hasMoved}, duration:${duration}ms`);
            }
            
            touchInProgress = false;
        }, { passive: false, capture: true });
        
        // CLICK BRUTAL pour desktop
        button.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopImmediatePropagation();
            
            if (!touchInProgress) {
                console.log('🔥 Brutal: Click BRUTAL desktop');
                brutalToggle();
            }
        }, { passive: false, capture: true });
        
        console.log('🔥 Brutal: Events BRUTAUX ajoutés - CONTRÔLE TOTAL acquis');
    }
}

// 🚀 INITIALISATION BRUTALE
let brutalIPhoneFix;

document.addEventListener('DOMContentLoaded', () => {
    brutalIPhoneFix = new BrutalIPhoneFix();
});

// Backup BRUTAL
setTimeout(() => {
    if (!brutalIPhoneFix) {
        brutalIPhoneFix = new BrutalIPhoneFix();
    }
}, 1500);

// Backup BRUTAL supplémentaire
setTimeout(() => {
    if (brutalIPhoneFix && brutalIPhoneFix.brutalFixAll) {
        brutalIPhoneFix.brutalFixAll();
    }
}, 5000);

// Export pour debug
window.brutalIPhoneFix = brutalIPhoneFix;

console.log('🔥 BRUTAL iPhone Fix chargé - MODE BRUTAL activé');
