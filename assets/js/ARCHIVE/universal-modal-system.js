/**
 * 🌍 UNIVERSAL MODAL SYSTEM - JAVASCRIPT
 * Compatible iPhone, Android, Desktop, TOUT
 * Ultra-simple, zero dépendance, zero conflit
 */

class UniversalModal {
    constructor() {
        this.modal = null;
        this.isOpen = false;
        this.golfData = this.initGolfData();
        
        this.init();
    }
    
    /**
     * 🏌️ DONNÉES GOLF SIMPLIFIÉES
     */
    initGolfData() {
        return {
            'RedMountain': {
                title: 'Red Mountain Golf Club Phuket',
                short: 'Parcours le plus spectaculaire d\'Asie sculpté dans une ancienne mine d\'étain.',
                full: 'Red Mountain Golf Club à Phuket est reconnu comme le parcours le plus spectaculaire d\'Asie. Sculpté dans une ancienne mine d\'étain, ce golf unique offre des formations rocheuses rouges iconiques et un dénivelé de 50 mètres au légendaire trou 17 "Drop Shot". L\'expérience de jeu est révolutionnaire avec des vues panoramiques exceptionnelles sur l\'île de Phuket.'
            },
            'Aquella': {
                title: 'Aquella Golf & Country Club',
                short: 'Élu Meilleur Golf de Luxe de Thaïlande 2024 avec 2,5 km de plage privée.',
                full: 'Aquella Golf & Country Club en Thaïlande a été élu Meilleur Golf de Luxe 2024. Ce parcours championship de 18 trous propose une expérience unique avec 2,5 kilomètres de plage privée exclusive, des tunnels de bambou spectaculaires et une vue imprenable sur la mer d\'Andaman. Le resort 5 étoiles offre un hébergement de luxe exceptionnel.'
            },
            'BlackMountain': {
                title: 'Black Mountain Golf Club Hua Hin',
                short: '#59 Golf Digest Top 100 mondial, seul parcours thaïlandais dans ce classement.',
                full: 'Black Mountain Golf Club à Hua Hin est classé #59 au Golf Digest Top 100 mondial, étant le seul parcours thaïlandais dans ce prestigieux classement. Ce parcours de 27 trous championship conçu par Phil Ryan s\'étend dans un cadre montagneux spectaculaire avec une academy professionnelle de renommée internationale.'
            },
            'Santiburi': {
                title: 'Santiburi Samui Country Club',
                short: 'Seul parcours 18 trous de Koh Samui dans un resort 5 étoiles.',
                full: 'Santiburi Samui Country Club est le seul parcours de golf 18 trous de l\'île de Koh Samui. Niché dans un resort 5 étoiles avec plage privée, ce golf offre des dénivelés spectaculaires jusqu\'à 180 mètres d\'altitude avec des panoramas époustouflants sur le Golfe de Thaïlande et la plage Mae Nam.'
            },
            'ChiangMai': {
                title: 'Chiang Mai Highlands Golf & Spa',
                short: 'Golf montagnard à altitude élevée avec climat frais unique en Thaïlande.',
                full: 'Chiang Mai Highlands Golf & Spa Resort propose une expérience golfique unique en Thaïlande grâce à son altitude élevée offrant un climat frais. Ce parcours de 27 trous par Schmidt-Curley Design est situé sur un site spirituel historique avec un spa intégré primé et des vues panoramiques à 360° sur les montagnes du Nord.'
            },
            'BlueCanyon': {
                title: 'Blue Canyon Country Club Phuket',
                short: 'Seul parcours triple-hôte du Johnnie Walker Classic au monde.',
                full: 'Blue Canyon Country Club à Phuket détient le record unique d\'être le seul parcours au monde à avoir accueilli trois fois le Johnnie Walker Classic. Ses deux parcours championship Canyon et Lakes ont vu Tiger Woods et Greg Norman écrire l\'histoire du golf international, notamment au célèbre trou 13 "Tiger Hole".'
            }
        };
    }
    
    /**
     * 🚀 INITIALISATION
     */
    init() {
        console.log('🌍 Universal Modal System - Initializing');
        
        this.createModal();
        this.bindEvents();
        
        // Global function pour compatibilité
        window.openUniversalModal = (src, alt) => this.open(src, alt);
        window.closeUniversalModal = () => this.close();
        
        console.log('✅ Universal Modal ready');
    }
    
    /**
     * 🎨 CRÉER LA MODAL
     */
    createModal() {
        // Créer l'élément modal
        this.modal = document.createElement('div');
        this.modal.className = 'universal-modal';
        
        this.modal.innerHTML = `
            <!-- Image -->
            <img class="universal-modal-image" src="" alt="">
            
            <!-- Bouton fermer -->
            <button class="universal-close" aria-label="Fermer">×</button>
            
            <!-- Panneau texte -->
            <div class="universal-text-panel">
                <h3 class="universal-title">Titre du golf</h3>
                <p class="universal-short">Description courte</p>
                <div class="universal-full">Description complète</div>
                <button class="universal-expand-btn">En savoir plus ↓</button>
            </div>
        `;
        
        document.body.appendChild(this.modal);
        console.log('🎨 Modal created');
    }
    
    /**
     * 🎛️ BIND EVENTS
     */
    bindEvents() {
        // Click sur background pour fermer
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.close();
            }
        });
        
        // Bouton fermer
        const closeBtn = this.modal.querySelector('.universal-close');
        closeBtn.addEventListener('click', () => this.close());
        
        // Bouton expand - UNIVERSEL
        const expandBtn = this.modal.querySelector('.universal-expand-btn');
        this.bindExpandButton(expandBtn);
        
        // Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });
        
        console.log('🎛️ Events bound');
    }
    
    /**
     * 🚀 BIND EXPAND BUTTON - Compatible TOUS appareils
     */
    bindExpandButton(btn) {
        const fullText = this.modal.querySelector('.universal-full');
        let isExpanded = false;
        
        // Fonction toggle universelle
        const toggle = (e) => {
            e.preventDefault();
            e.stopPropagation();
            
            console.log('🚀 Toggle text, expanded:', isExpanded);
            
            if (isExpanded) {
                // Collapse
                fullText.classList.remove('expanded');
                btn.textContent = 'En savoir plus ↓';
                isExpanded = false;
                console.log('📄 Text collapsed');
            } else {
                // Expand
                fullText.classList.add('expanded');
                btn.textContent = 'Réduire ↑';
                isExpanded = true;
                console.log('📄 Text expanded');
            }
        };
        
        // Touch events (iOS/Android)
        btn.addEventListener('touchstart', (e) => {
            btn.style.transform = 'scale(0.95)';
        }, { passive: true });
        
        btn.addEventListener('touchend', (e) => {
            btn.style.transform = '';
            toggle(e);
        }, { passive: false });
        
        // Click events (Desktop + fallback)
        btn.addEventListener('click', (e) => {
            // Éviter double trigger sur mobile
            if (e.type === 'click' && 'ontouchstart' in window) {
                return;
            }
            toggle(e);
        });
        
        // Mouse events (Desktop)
        btn.addEventListener('mousedown', () => {
            if (!('ontouchstart' in window)) {
                btn.style.transform = 'scale(0.95)';
            }
        });
        
        btn.addEventListener('mouseup', () => {
            if (!('ontouchstart' in window)) {
                btn.style.transform = '';
            }
        });
        
        console.log('🚀 Expand button bound with universal events');
    }
    
    /**
     * 🔍 IDENTIFIER GOLF
     */
    identifyGolf(imageSrc) {
        const src = imageSrc.toLowerCase();
        
        if (src.includes('redmountain')) return 'RedMountain';
        if (src.includes('aquella')) return 'Aquella';
        if (src.includes('blackmountain')) return 'BlackMountain';
        if (src.includes('santiburi')) return 'Santiburi';
        if (src.includes('chiangmai')) return 'ChiangMai';
        if (src.includes('blue-canyon') || src.includes('bluecanyon')) return 'BlueCanyon';
        
        return null;
    }
    
    /**
     * 📝 POPULATE CONTENT
     */
    populateContent(golfInfo, imageAlt) {
        const title = this.modal.querySelector('.universal-title');
        const short = this.modal.querySelector('.universal-short');
        const full = this.modal.querySelector('.universal-full');
        const expandBtn = this.modal.querySelector('.universal-expand-btn');
        
        if (golfInfo) {
            title.textContent = golfInfo.title;
            short.textContent = golfInfo.short;
            full.textContent = golfInfo.full;
        } else {
            // Default content
            title.textContent = imageAlt || 'Golf en Thaïlande';
            short.textContent = 'Découvrez nos destinations golf premium en Thaïlande.';
            full.textContent = 'GolfinThaï vous propose des séjours golf d\'exception en Thaïlande sur les plus beaux parcours d\'Asie. Expertise locale et service personnalisé pour une expérience unique.';
        }
        
        // Reset button state
        expandBtn.textContent = 'En savoir plus ↓';
        full.classList.remove('expanded');
        
        console.log('📝 Content populated for:', golfInfo ? golfInfo.title : 'Default');
    }
    
    /**
     * 🖼️ OUVRIR MODAL
     */
    open(imageSrc, imageAlt = '') {
        if (!imageSrc) {
            console.error('❌ No image source provided');
            return;
        }
        
        console.log('🖼️ Opening modal with:', imageSrc);
        
        // Set image
        const img = this.modal.querySelector('.universal-modal-image');
        img.src = imageSrc;
        img.alt = imageAlt || 'Golf en Thaïlande';
        
        // Populate content
        const golfKey = this.identifyGolf(imageSrc);
        const golfInfo = golfKey ? this.golfData[golfKey] : null;
        this.populateContent(golfInfo, imageAlt);
        
        // Show modal
        this.modal.classList.add('visible');
        document.body.classList.add('universal-modal-open');
        
        this.isOpen = true;
        
        // Auto-focus pour accessibility (desktop)
        if (!('ontouchstart' in window)) {
            const closeBtn = this.modal.querySelector('.universal-close');
            closeBtn.focus();
        }
        
        console.log('✅ Modal opened');
    }
    
    /**
     * ❌ FERMER MODAL
     */
    close() {
        console.log('❌ Closing modal');
        
        this.modal.classList.remove('visible');
        document.body.classList.remove('universal-modal-open');
        
        this.isOpen = false;
        
        // Clear image après animation
        setTimeout(() => {
            const img = this.modal.querySelector('.universal-modal-image');
            img.src = '';
        }, 300);
        
        console.log('✅ Modal closed');
    }
    
    /**
     * 🔧 DEBUG
     */
    debug() {
        console.log('🔧 Universal Modal Debug Info:');
        console.log('Modal element:', this.modal);
        console.log('Is open:', this.isOpen);
        console.log('Golf data:', this.golfData);
        
        // Test all golf data
        Object.keys(this.golfData).forEach(key => {
            console.log(`Golf ${key}:`, this.golfData[key]);
        });
    }
}

// 🚀 INITIALISATION AUTOMATIQUE
let universalModal;

// Initialiser dès que possible
function initUniversalModal() {
    if (!universalModal) {
        universalModal = new UniversalModal();
        window.universalModal = universalModal;
        console.log('🌍 Universal Modal System initialized');
    }
}

// DOMContentLoaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initUniversalModal);
} else {
    initUniversalModal();
}

// Fallback si DOMContentLoaded déjà passé
setTimeout(() => {
    if (!universalModal) {
        initUniversalModal();
    }
}, 100);

// 🌍 FONCTIONS GLOBALES pour compatibilité
window.openSimpleModal = (src, alt) => {
    if (window.universalModal) {
        window.universalModal.open(src, alt);
    } else {
        console.warn('⚠️ Universal Modal not ready yet');
        setTimeout(() => window.openSimpleModal(src, alt), 100);
    }
};

window.closeSimpleModal = () => {
    if (window.universalModal) {
        window.universalModal.close();
    }
};

// 🧪 DEBUG GLOBAL
window.debugUniversalModal = () => {
    if (window.universalModal) {
        window.universalModal.debug();
    }
};

console.log('🌍 Universal Modal System loaded - Compatible ALL devices');

/**
 * ✅ FEATURES IMPLÉMENTÉES:
 * 
 * 1. 🎯 Ultra-simple - Un seul CSS, un seul JS
 * 2. 🌍 Compatible TOUT - iPhone, Android, Desktop
 * 3. 👆 Touch events optimisés - touchstart/touchend + click fallback
 * 4. 📱 Responsive design - Mobile-first approach
 * 5. ♿ Accessibility - Keyboard navigation, focus management
 * 6. 🚀 Performance - Hardware acceleration, optimized animations
 * 7. 🎨 Modern design - Backdrop-filter, smooth transitions
 * 8. 🔧 Debug tools - Console logging, debug function
 * 9. 📄 Golf data integration - Auto-detection par image src
 * 10. 🛡️ Error handling - Fallbacks et timeouts
 * 
 * RÉSULTAT: Modal qui marche sur TOUT! 🎯
 */