/**
 * 🚨 iOS TEXT EXPANSION NUCLEAR JAVASCRIPT
 * Solution ultra-agressive pour forcer l'expansion sur iPhone
 * Bypass TOUS les CSS conflictuels
 */

class iOSTextExpansionNuclear {
    constructor() {
        this.expandedStates = new Map(); // Track des états
        this.observerActive = false;
        this.debugMode = true; // Pour debug
        
        this.init();
    }
    
    init() {
        console.log('🚨 iOS Nuclear Text Expansion - Mode ULTRA AGGRESSIF');
        
        // Attendre que tout soit chargé
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.activate());
        } else {
            this.activate();
        }
        
        // Activation retardée pour être sûr
        setTimeout(() => this.activate(), 1000);
        setTimeout(() => this.activate(), 3000);
    }
    
    /**
     * 🔥 ACTIVATION PRINCIPALE
     */
    activate() {
        console.log('🔥 Nuclear activation - Scanning for modals');
        
        // 1. Injecter CSS nuclear si pas déjà fait
        this.injectNuclearCSS();
        
        // 2. Setup observer pour détecter modales
        this.setupModalObserver();
        
        // 3. Fix modales existantes
        this.fixExistingModals();
        
        console.log('✅ Nuclear mode activated');
    }
    
    /**
     * 🧨 INJECTION CSS NUCLEAR
     */
    injectNuclearCSS() {
        if (document.getElementById('ios-nuclear-css')) return;
        
        const style = document.createElement('style');
        style.id = 'ios-nuclear-css';
        style.textContent = `
            /* 🚨 CSS NUCLEAR INLINE - MAXIMUM PRIORITY */
            .seo-full-nuclear-expanded {
                display: block !important;
                opacity: 1 !important;
                max-height: 2000px !important;
                height: auto !important;
                overflow: visible !important;
                visibility: visible !important;
                position: relative !important;
                z-index: 999999 !important;
                margin: 8px 0 12px 0 !important;
                padding: 8px 0 !important;
                color: rgba(255, 255, 255, 0.8) !important;
                font-size: 0.9rem !important;
                line-height: 1.5 !important;
                transition: all 0.3s ease !important;
                animation: nuclearSlideDown 0.3s ease-out !important;
            }
            
            .seo-full-nuclear-collapsed {
                display: none !important;
                opacity: 0 !important;
                max-height: 0 !important;
                overflow: hidden !important;
                margin: 0 !important;
                padding: 0 !important;
                animation: nuclearSlideUp 0.3s ease-in !important;
            }
            
            @keyframes nuclearSlideDown {
                0% { opacity: 0; max-height: 0; transform: translateY(-5px); }
                100% { opacity: 1; max-height: 2000px; transform: translateY(0); }
            }
            
            @keyframes nuclearSlideUp {
                0% { opacity: 1; max-height: 2000px; transform: translateY(0); }
                100% { opacity: 0; max-height: 0; transform: translateY(-5px); }
            }
            
            .expand-btn-nuclear {
                display: inline-block !important;
                min-height: 44px !important;
                min-width: 120px !important;
                padding: 10px 16px !important;
                background: linear-gradient(135deg, #00ff88, #00aaff) !important;
                color: black !important;
                border: none !important;
                border-radius: 8px !important;
                font-size: 0.85rem !important;
                font-weight: 600 !important;
                cursor: pointer !important;
                touch-action: manipulation !important;
                -webkit-tap-highlight-color: transparent !important;
                z-index: 1000000 !important;
                position: relative !important;
                transition: all 0.2s ease !important;
            }
            
            .expand-btn-nuclear:active {
                transform: scale(0.95) !important;
                background: linear-gradient(135deg, #00cc66, #0088cc) !important;
            }
        `;
        
        document.head.appendChild(style);
        console.log('💉 Nuclear CSS injected');
    }
    
    /**
     * 👁️ SETUP MODAL OBSERVER
     */
    setupModalObserver() {
        if (this.observerActive) return;
        
        // Observer pour nouvelles modales
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                    const element = mutation.target;
                    if (this.isModal(element) && this.isModalVisible(element)) {
                        console.log('👁️ Modal detected by observer');
                        setTimeout(() => this.fixModal(element), 100);
                    }
                }
                
                // Observer pour nouveaux nœuds
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === 1) { // Element node
                            const modals = node.querySelectorAll ? 
                                node.querySelectorAll('.fullscreen-modal, .simple-image-modal, .modal, .image-modal') : [];
                            modals.forEach(modal => {
                                if (this.isModalVisible(modal)) {
                                    console.log('👁️ New modal node detected');
                                    setTimeout(() => this.fixModal(modal), 100);
                                }
                            });
                        }
                    });
                }
            });
        });
        
        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['style', 'class'],
            childList: true,
            subtree: true
        });
        
        this.observerActive = true;
        console.log('👁️ Modal observer setup');
    }
    
    /**
     * 🔍 DÉTECTION MODAL
     */
    isModal(element) {
        return element && (
            element.classList.contains('fullscreen-modal') ||
            element.classList.contains('simple-image-modal') ||
            element.classList.contains('modal') ||
            element.classList.contains('image-modal')
        );
    }
    
    isModalVisible(element) {
        if (!element) return false;
        const style = getComputedStyle(element);
        return style.display !== 'none' && 
               (style.display === 'flex' || style.display === 'block') &&
               parseFloat(style.opacity) > 0;
    }
    
    /**
     * 🛠️ FIX MODALES EXISTANTES
     */
    fixExistingModals() {
        const selectors = [
            '.fullscreen-modal',
            '.simple-image-modal', 
            '.modal',
            '.image-modal'
        ];
        
        selectors.forEach(selector => {
            const modals = document.querySelectorAll(selector);
            modals.forEach(modal => {
                if (this.isModalVisible(modal)) {
                    console.log(`🛠️ Fixing existing modal: ${selector}`);
                    this.fixModal(modal);
                }
            });
        });
    }
    
    /**
     * 🔧 FIX MODAL SPÉCIFIQUE
     */
    fixModal(modal) {
        if (!modal) return;
        
        console.log('🔧 Fixing modal:', modal);
        
        // 1. Fix le SEO panel
        const seoPanel = modal.querySelector('.seo-panel');
        if (seoPanel) {
            this.fixSeoPanel(seoPanel);
        }
        
        // 2. Fix les boutons expand
        const expandBtns = modal.querySelectorAll('.expand-btn');
        expandBtns.forEach(btn => this.fixExpandButton(btn));
        
        // 3. Fix les textes seo-full
        const seoFulTexts = modal.querySelectorAll('.seo-full');
        seoFulTexts.forEach(text => this.fixSeoFullText(text));
        
        console.log('✅ Modal fixed');
    }
    
    /**
     * 📝 FIX SEO PANEL
     */
    fixSeoPanel(seoPanel) {
        // Force le style inline pour override tout
        seoPanel.style.setProperty('position', 'fixed', 'important');
        seoPanel.style.setProperty('bottom', '20px', 'important');
        seoPanel.style.setProperty('left', '50%', 'important');
        seoPanel.style.setProperty('transform', 'translateX(-50%)', 'important');
        seoPanel.style.setProperty('z-index', '999999', 'important');
        seoPanel.style.setProperty('max-height', 'none', 'important');
        seoPanel.style.setProperty('overflow', 'visible', 'important');
        seoPanel.style.setProperty('width', '90%', 'important');
        seoPanel.style.setProperty('max-width', '600px', 'important');
        
        console.log('📝 SEO panel fixed');
    }
    
    /**
     * 🚀 FIX EXPAND BUTTON
     */
    fixExpandButton(btn) {
        if (!btn) return;
        
        // Ajouter classe nuclear
        btn.classList.add('expand-btn-nuclear');
        
        // Nettoyer tous les event listeners existants
        const newBtn = btn.cloneNode(true);
        btn.parentNode.replaceChild(newBtn, btn);
        
        // Style inline pour override
        newBtn.style.setProperty('display', 'inline-block', 'important');
        newBtn.style.setProperty('min-height', '44px', 'important');
        newBtn.style.setProperty('z-index', '1000000', 'important');
        newBtn.style.setProperty('position', 'relative', 'important');
        
        // Event listeners nuclear
        this.addNuclearEventListeners(newBtn);
        
        console.log('🚀 Expand button fixed');
    }
    
    /**
     * 📄 FIX SEO FULL TEXT
     */
    fixSeoFullText(seoText) {
        if (!seoText) return;
        
        // Force les propriétés critiques
        seoText.style.setProperty('width', '100%', 'important');
        seoText.style.setProperty('max-width', 'none', 'important');
        seoText.style.setProperty('position', 'relative', 'important');
        seoText.style.setProperty('z-index', '999998', 'important');
        
        // État initial collapsed
        if (!seoText.classList.contains('seo-expanded')) {
            seoText.classList.add('seo-full-nuclear-collapsed');
            seoText.classList.remove('seo-full-nuclear-expanded');
        }
        
        console.log('📄 SEO full text fixed');
    }
    
    /**
     * 🎯 EVENT LISTENERS NUCLEAR
     */
    addNuclearEventListeners(btn) {
        const modal = btn.closest('.fullscreen-modal, .simple-image-modal, .modal, .image-modal');
        if (!modal) return;
        
        const seoFull = modal.querySelector('.seo-full');
        if (!seoFull) return;
        
        let isExpanded = false;
        
        // Fonction de toggle nuclear
        const nuclearToggle = (e) => {
            e.preventDefault();
            e.stopPropagation();
            
            console.log('🎯 Nuclear toggle triggered, isExpanded:', isExpanded);
            
            if (isExpanded) {
                // COLLAPSE
                this.nuclearCollapse(seoFull, btn);
                isExpanded = false;
            } else {
                // EXPAND  
                this.nuclearExpand(seoFull, btn);
                isExpanded = true;
            }
        };
        
        // Touch events
        btn.addEventListener('touchstart', (e) => {
            btn.style.transform = 'scale(0.95)';
        }, { passive: true });
        
        btn.addEventListener('touchend', (e) => {
            btn.style.transform = 'scale(1)';
            nuclearToggle(e);
        }, { passive: false });
        
        // Click fallback
        btn.addEventListener('click', nuclearToggle, { passive: false });
        
        console.log('🎯 Nuclear event listeners added');
    }
    
    /**
     * 💥 NUCLEAR EXPAND
     */
    nuclearExpand(seoFull, btn) {
        console.log('💥 NUCLEAR EXPAND');
        
        // Supprimer toutes les classes restrictives
        seoFull.classList.remove('seo-full-nuclear-collapsed', 'seo-collapsed');
        
        // Force display avec style inline (plus prioritaire que CSS)
        seoFull.style.setProperty('display', 'block', 'important');
        seoFull.style.setProperty('opacity', '1', 'important');
        seoFull.style.setProperty('max-height', '2000px', 'important');
        seoFull.style.setProperty('height', 'auto', 'important');
        seoFull.style.setProperty('overflow', 'visible', 'important');
        seoFull.style.setProperty('visibility', 'visible', 'important');
        seoFull.style.setProperty('margin', '8px 0 12px 0', 'important');
        seoFull.style.setProperty('padding', '8px 0', 'important');
        seoFull.style.setProperty('z-index', '999998', 'important');
        seoFull.style.setProperty('position', 'relative', 'important');
        
        // Ajouter classe expanded
        seoFull.classList.add('seo-full-nuclear-expanded', 'seo-expanded');
        
        // Update button text
        btn.textContent = 'Réduire ↑';
        
        // Force repaint
        seoFull.offsetHeight;
        
        console.log('✅ Nuclear expand completed');
        
        // Debug
        if (this.debugMode) {
            console.log('📊 SEO Full computed style:', getComputedStyle(seoFull));
        }
    }
    
    /**
     * 💥 NUCLEAR COLLAPSE
     */
    nuclearCollapse(seoFull, btn) {
        console.log('💥 NUCLEAR COLLAPSE');
        
        // Supprimer classes expanded
        seoFull.classList.remove('seo-full-nuclear-expanded', 'seo-expanded');
        
        // Force hide avec style inline
        seoFull.style.setProperty('display', 'none', 'important');
        seoFull.style.setProperty('opacity', '0', 'important');
        seoFull.style.setProperty('max-height', '0', 'important');
        seoFull.style.setProperty('overflow', 'hidden', 'important');
        seoFull.style.setProperty('margin', '0', 'important');
        seoFull.style.setProperty('padding', '0', 'important');
        
        // Ajouter classe collapsed
        seoFull.classList.add('seo-full-nuclear-collapsed');
        
        // Update button text
        btn.textContent = 'En savoir plus ↓';
        
        console.log('✅ Nuclear collapse completed');
    }
    
    /**
     * 🧪 MÉTHODE DEBUG
     */
    debugModal(modal) {
        if (!this.debugMode) return;
        
        console.log('🧪 DEBUG Modal:', modal);
        
        const seoPanel = modal.querySelector('.seo-panel');
        const expandBtn = modal.querySelector('.expand-btn');
        const seoFull = modal.querySelector('.seo-full');
        
        if (seoPanel) {
            console.log('📊 SEO Panel style:', getComputedStyle(seoPanel));
        }
        if (expandBtn) {
            console.log('📊 Expand Button style:', getComputedStyle(expandBtn));
        }
        if (seoFull) {
            console.log('📊 SEO Full style:', getComputedStyle(seoFull));
        }
    }
}

// 🚨 FONCTION GLOBALE pour test manuel
window.nuclearExpandTest = () => {
    console.log('🚨 NUCLEAR EXPAND TEST');
    const modal = document.querySelector('.fullscreen-modal[style*="flex"], .simple-image-modal[style*="flex"]');
    if (modal) {
        const seoFull = modal.querySelector('.seo-full');
        const btn = modal.querySelector('.expand-btn');
        if (seoFull && btn) {
            if (window.iOSNuclear) {
                window.iOSNuclear.nuclearExpand(seoFull, btn);
            }
        }
    }
};

// 🚀 ACTIVATION NUCLEAR
let iOSNuclear;

// Activation immédiate
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        iOSNuclear = new iOSTextExpansionNuclear();
        window.iOSNuclear = iOSNuclear;
    });
} else {
    iOSNuclear = new iOSTextExpansionNuclear();
    window.iOSNuclear = iOSNuclear;
}

// Activation retardée de sécurité
setTimeout(() => {
    if (!window.iOSNuclear) {
        iOSNuclear = new iOSTextExpansionNuclear();
        window.iOSNuclear = iOSNuclear;
    }
}, 2000);

console.log('🚨 iOS Nuclear Text Expansion loaded - Mode ULTRA AGGRESSIF ready');
