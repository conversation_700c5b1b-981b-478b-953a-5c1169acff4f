/**
 * 🚀 SIMPLE IMAGE MODAL - NO BULLSHIT VERSION
 * Fonctionne ou je rends mon badge de dev
 */

class SimpleImageModal {
    constructor() {
        this.isOpen = false;
        this.modal = null;
        
        this.init();
    }
    
    init() {
        console.log('🚀 Simple Image Modal - NO BULLSHIT VERSION');
        this.createModal();
        this.bindEvents();
        console.log('✅ Simple Image Modal: Ready to WORK!');
    }
    
    /**
     * 🎨 CREATE MODAL: Ultra simple
     */
    createModal() {
        this.modal = document.createElement('div');
        this.modal.className = 'simple-image-modal';
        this.modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 99999;
            display: none;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        `;
        
        this.modal.innerHTML = `
            <div style="
                position: relative;
                max-width: 90vw;
                max-height: 90vh;
                display: flex;
                align-items: center;
                justify-content: center;
            ">
                <img class="simple-modal-image" src="" alt="" style="
                    max-width: 100%;
                    max-height: 100%;
                    width: auto;
                    height: auto;
                    min-width: 400px;
                    min-height: 300px;
                    object-fit: contain;
                    border-radius: 12px;
                    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
                    background: white;
                ">
                <button class="simple-modal-close" style="
                    position: absolute;
                    top: -40px;
                    right: 0;
                    background: rgba(255, 255, 255, 0.9);
                    border: none;
                    border-radius: 50%;
                    width: 40px;
                    height: 40px;
                    font-size: 20px;
                    cursor: pointer;
                    color: #333;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                ">×</button>
            </div>
        `;
        
        document.body.appendChild(this.modal);
        console.log('✅ Simple modal created');
    }
    
    /**
     * 🎛️ BIND EVENTS: Ultra simple
     */
    bindEvents() {
        // Close button
        const closeBtn = this.modal.querySelector('.simple-modal-close');
        closeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.close();
        });
        
        // Background click to close
        this.modal.addEventListener('click', () => {
            this.close();
        });
        
        // Don't close when clicking image
        const img = this.modal.querySelector('.simple-modal-image');
        img.addEventListener('click', (e) => {
            e.stopPropagation();
        });
        
        // Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });
        
        console.log('✅ Simple events bound');
    }
    
    /**
     * 🖼️ OPEN IMAGE: ULTRA SIMPLE
     */
    open(imageSrc, imageAlt = '') {
        console.log('🖼️ Opening image:', imageSrc);
        
        const img = this.modal.querySelector('.simple-modal-image');
        img.src = imageSrc;
        img.alt = imageAlt;
        
        // Show modal
        this.modal.style.display = 'flex';
        
        // Lock body scroll
        document.body.style.overflow = 'hidden';
        
        this.isOpen = true;
        
        console.log('✅ Simple modal opened');
    }
    
    /**
     * ❌ CLOSE: ULTRA SIMPLE
     */
    close() {
        console.log('❌ Closing simple modal');
        
        // Hide modal
        this.modal.style.display = 'none';
        
        // Unlock body scroll
        document.body.style.overflow = '';
        
        // Clear image
        const img = this.modal.querySelector('.simple-modal-image');
        img.src = '';
        
        this.isOpen = false;
        
        console.log('✅ Simple modal closed');
    }
}

// 🚀 INIT IMMEDIATELY
let simpleImageModal;

document.addEventListener('DOMContentLoaded', () => {
    simpleImageModal = new SimpleImageModal();
    
    // Global function for easy access
    window.openSimpleModal = (src, alt) => {
        if (simpleImageModal) {
            simpleImageModal.open(src, alt);
        }
    };
    
    window.closeSimpleModal = () => {
        if (simpleImageModal) {
            simpleImageModal.close();
        }
    };
    
    console.log('🚀 Simple Image Modal: READY TO WORK! NO BULLSHIT!');
});
