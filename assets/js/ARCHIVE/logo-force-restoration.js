/* =============================================
   🚑 LOGO FORCE RESTORATION - EMERGENCY
   Force le logo à apparaître immédiatement
   ============================================= */

document.addEventListener('DOMContentLoaded', function() {
    console.log('🚑 LOGO FORCE RESTORATION - EMERGENCY MODE');
    
    forceRestoreLogo();
    
    // Re-check toutes les 2 secondes
    const forceInterval = setInterval(() => {
        const logo = document.querySelector('.logo-container-bg');
        if (!logo || logo.offsetWidth === 0) {
            console.log('🚨 Logo invisible - Force restoration!');
            forceRestoreLogo();
        } else {
            console.log('✅ Logo visible - Stopping force checks');
            clearInterval(forceInterval);
        }
    }, 2000);
    
    // Arrêter après 20 secondes
    setTimeout(() => {
        clearInterval(forceInterval);
        console.log('🛑 Force restoration ended');
    }, 20000);
});

function forceRestoreLogo() {
    // Trouver le container parent
    const logoParent = document.querySelector('.flex.items-center.space-x-3');
    
    if (!logoParent) {
        console.log('❌ Logo parent not found');
        return;
    }
    
    // Supprimer tous les logos existants
    const existingLogos = logoParent.querySelectorAll('.logo-container-bg, svg[title*="GolfinThaï"], div[title*="GolfinThaï"]');
    existingLogos.forEach(logo => {
        console.log('🗑️ Removing existing logo element');
        logo.remove();
    });
    
    // Créer un nouveau logo propre
    const newLogo = document.createElement('div');
    newLogo.className = 'logo-container-bg';
    newLogo.setAttribute('role', 'img');
    newLogo.setAttribute('aria-label', 'Logo GolfinThaï - Voyages Golf Thaïlande');
    newLogo.setAttribute('title', 'GolfinThaï');
    newLogo.setAttribute('itemprop', 'logo');
    
    // Styles forcés qui ne peuvent pas être overridés
    newLogo.style.cssText = `
        width: 44px !important;
        height: 44px !important;
        min-width: 44px !important;
        min-height: 44px !important;
        border-radius: 50% !important;
        background-image: url('./assets/images/logo-golfinthai.jpg') !important;
        background-size: contain !important;
        background-position: center !important;
        background-repeat: no-repeat !important;
        background-color: rgba(255, 255, 255, 0.1) !important;
        flex-shrink: 0 !important;
        border: 2px solid rgba(6, 95, 70, 0.3) !important;
        box-shadow: 0 2px 8px rgba(45, 212, 191, 0.15) !important;
        margin: 0 !important;
        padding: 0 !important;
        position: relative !important;
        z-index: 1001 !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        background-color: #059669 !important;
    `;
    
    // L'insérer au début du container
    logoParent.insertBefore(newLogo, logoParent.firstChild);
    
    console.log('✅ NEW LOGO FORCE CREATED:', newLogo);
    console.log('📐 Logo dimensions:', newLogo.offsetWidth, 'x', newLogo.offsetHeight);
    
    // Vérifier que l'image se charge
    const testImg = new Image();
    testImg.onload = () => {
        console.log('✅ Logo image loaded successfully');
    };
    testImg.onerror = () => {
        console.log('❌ Logo image failed - Using fallback');
        // Fallback avec SVG inline
        newLogo.innerHTML = `
            <svg width="44" height="44" viewBox="0 0 64 64" style="border-radius: 50%;">
                <circle cx="32" cy="32" r="32" fill="#00574B"/>
                <circle cx="32" cy="24" r="6" fill="#FCFCFC"/>
                <path d="M28 30L36 38L34 40L26 32Z" fill="#A3D1C8"/>
                <circle cx="36" cy="38" r="2" fill="#A3D1C8"/>
                <path d="M20 42L32 36L44 42L40 48L24 48Z" fill="#FCFCFC"/>
                <path d="M22 42L32 38L42 42L38 46L26 46Z" fill="#A3D1C8"/>
            </svg>
        `;
        newLogo.style.backgroundImage = 'none';
    };
    testImg.src = './assets/images/logo-golfinthai.jpg';
}

console.log('🚑 LOGO FORCE RESTORATION loaded - Logo WILL appear!');