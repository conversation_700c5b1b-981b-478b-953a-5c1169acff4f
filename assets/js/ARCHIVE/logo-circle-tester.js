/**
 * 🚀 LOGO PERFECT CIRCLE TESTER
 * Test and validate the logo circle fix
 */

// 🎯 TEST LOGO CIRCLE
window.testLogoCircle = function() {
    console.log('🎯 Testing logo circle fix...');
    
    const logoContainer = document.querySelector('.logo-container-bg');
    
    if (!logoContainer) {
        console.log('❌ Logo container not found');
        return;
    }
    
    // Get computed styles
    const styles = getComputedStyle(logoContainer);
    const width = styles.width;
    const height = styles.height;
    const borderRadius = styles.borderRadius;
    const backgroundImage = styles.backgroundImage;
    
    console.log('📊 Logo container info:');
    console.log('Width:', width);
    console.log('Height:', height);
    console.log('Border radius:', borderRadius);
    console.log('Background image:', backgroundImage);
    
    // Check if it's a perfect circle
    const widthNum = parseFloat(width);
    const heightNum = parseFloat(height);
    const isSquare = Math.abs(widthNum - heightNum) < 1;
    const isCircle = borderRadius === '50%' || parseFloat(borderRadius) >= widthNum / 2;
    
    console.log('✅ Is square:', isSquare);
    console.log('✅ Is circle:', isCircle);
    console.log('✅ Has background image:', backgroundImage !== 'none');
    
    if (isSquare && isCircle && backgroundImage !== 'none') {
        console.log('🎉 Logo circle fix SUCCESS!');
    } else {
        console.log('❌ Logo circle fix needs adjustment');
    }
};

// 🔧 FORCE LOGO CIRCLE (Emergency fix)
window.forceLogoCircle = function() {
    console.log('🔧 Force applying logo circle fix...');
    
    const logoContainer = document.querySelector('.logo-container-bg');
    
    if (!logoContainer) {
        console.log('❌ Logo container not found');
        return;
    }
    
    // Apply optimized styles - Desktop: cover, Mobile: handled by CSS
    logoContainer.style.width = '64px';
    logoContainer.style.height = '64px';
    logoContainer.style.borderRadius = '50%';
    logoContainer.style.backgroundImage = 'url("./assets/images/logo-golfinthai.jpg")';
    logoContainer.style.backgroundSize = 'cover';  // CSS will override on mobile
    logoContainer.style.backgroundPosition = 'center';
    logoContainer.style.backgroundRepeat = 'no-repeat';
    logoContainer.style.border = '2px solid #A3D1C8';
    logoContainer.style.flexShrink = '0';
    logoContainer.style.overflow = 'hidden';
    
    console.log('✅ Force fix applied!');
    
    setTimeout(() => {
        window.testLogoCircle();
    }, 500);
};

// 🎨 LOGO CIRCLE VISUAL DEBUG
window.debugLogoCircle = function() {
    console.log('🎨 Adding visual debug to logo...');
    
    const logoContainer = document.querySelector('.logo-container-bg');
    
    if (!logoContainer) {
        console.log('❌ Logo container not found');
        return;
    }
    
    // Add visual debug border
    logoContainer.style.border = '3px solid red';
    logoContainer.style.boxShadow = '0 0 0 1px yellow';
    
    console.log('✅ Visual debug applied - red border should be perfectly circular');
    
    setTimeout(() => {
        // Remove debug after 5 seconds
        logoContainer.style.border = '2px solid #A3D1C8';
        logoContainer.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)';
        console.log('🧹 Visual debug removed');
    }, 5000);
};

// Auto-test on load
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        window.testLogoCircle();
    }, 1000);
});

console.log(`
🎯 LOGO CIRCLE TESTER LOADED:
- window.testLogoCircle() - Test current logo circle
- window.forceLogoCircle() - Force fix if broken
- window.debugLogoCircle() - Visual debug (red border for 5s)
`);
