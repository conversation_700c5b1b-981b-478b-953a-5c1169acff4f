/**
 * 🍎 FIX FINAL IPHONE - Events Touch Fonctionnels
 * 
 * PROBLÈME: 
 * - État initial maintenant cohérent ✅
 * - MAIS clics iPhone ne déclenchent pas le toggle ❌
 * - Système Dovito events touch ne marchent pas sur iPhone réel
 * 
 * SOLUTION FINALE:
 * - Ajouter events touch iPhone SIMPLES et FONCTIONNELS
 * - Override/complément du système Dovito pour iPhone
 * - Events click + touch qui marchent vraiment
 */

class FinalIPhoneFix {
    constructor() {
        this.isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        this.isSafari = /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);
        this.fixedButtons = new Set();
        
        if (this.isIOS && this.isSafari) {
            this.init();
            console.log('🍎 Final iPhone Fix: iPhone Safari détecté - activation');
        } else {
            console.log('🍎 Final iPhone Fix: Non-iPhone Safari - pas nécessaire');
        }
    }
    
    init() {
        // Observer les modales qui s'ouvrent
        this.observeModals();
        
        // Fix immédiat si modal déjà ouverte
        setTimeout(() => this.fixExistingModals(), 1000);
    }
    
    observeModals() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                if (mutation.type === 'attributes' && 
                    mutation.attributeName === 'style') {
                    const modal = mutation.target;
                    if (this.isModalVisible(modal)) {
                        console.log('🍎 Final iPhone Fix: Modal ouverte - fix des events');
                        setTimeout(() => this.fixModalButton(modal), 300);
                    }
                }
            });
        });
        
        // Observer toutes les modales possibles
        const modals = document.querySelectorAll('[class*="modal"]');
        modals.forEach(modal => {
            observer.observe(modal, {
                attributes: true,
                attributeFilter: ['style']
            });
        });
    }
    
    isModalVisible(element) {
        if (!element || element.nodeType !== 1) return false;
        
        const style = window.getComputedStyle(element);
        const isVisible = style.display !== 'none' && 
                         parseFloat(style.opacity) > 0;
        
        const isModal = element.className.includes('modal');
        return isVisible && isModal;
    }
    
    fixExistingModals() {
        const modals = document.querySelectorAll('[class*="modal"]');
        modals.forEach(modal => {
            if (this.isModalVisible(modal)) {
                this.fixModalButton(modal);
            }
        });
    }
    
    fixModalButton(modal) {
        console.log(`🍎 Final iPhone Fix: Traitement modal ${modal.className}`);
        
        // Trouver le bouton
        const button = this.findButton(modal);
        const text = this.findText(modal);
        
        if (!button || !text) {
            console.log('🍎 Final iPhone Fix: Bouton ou texte non trouvé');
            return;
        }
        
        // Éviter double traitement
        if (this.fixedButtons.has(button)) {
            console.log('🍎 Final iPhone Fix: Bouton déjà traité');
            return;
        }
        
        console.log('🍎 Final iPhone Fix: Ajout events touch iPhone');
        this.addIPhoneEvents(button, text);
        this.fixedButtons.add(button);
    }
    
    findButton(modal) {
        const selectors = [
            '.dovito-expand-btn',
            '.expand-btn', 
            'button'
        ];
        
        for (let selector of selectors) {
            const buttons = modal.querySelectorAll(selector);
            for (let button of buttons) {
                const text = button.textContent || button.innerHTML;
                if (text.includes('savoir') || text.includes('Réduire') || 
                    text.includes('↑') || text.includes('↓')) {
                    return button;
                }
            }
        }
        
        return null;
    }
    
    findText(modal) {
        const selectors = [
            '.dovito-seo-full',
            '.seo-full',
            '[class*="full"]'
        ];
        
        for (let selector of selectors) {
            const text = modal.querySelector(selector);
            if (text && text.textContent && text.textContent.length > 50) {
                return text;
            }
        }
        
        return null;
    }
    
    addIPhoneEvents(button, text) {
        // CSS pour iPhone
        button.style.cssText += `
            -webkit-touch-callout: none !important;
            -webkit-tap-highlight-color: transparent !important;
            touch-action: manipulation !important;
            cursor: pointer !important;
            min-width: 44px !important;
            min-height: 44px !important;
        `;
        
        let touchStarted = false;
        let touchStartTime = 0;
        let touchMoved = false;
        
        // Fonction de toggle simple
        const simpleToggle = (e) => {
            e.preventDefault();
            e.stopPropagation();
            
            console.log('🍎 Final iPhone Fix: Toggle déclenché');
            
            const currentButtonText = button.textContent || button.innerHTML;
            const currentTextVisible = window.getComputedStyle(text).display !== 'none';
            
            console.log(`🍎 État avant: Bouton="${currentButtonText}", Texte visible=${currentTextVisible}`);
            
            if (currentButtonText.includes('Réduire')) {
                // Cacher le texte
                text.style.display = 'none';
                text.style.opacity = '0';
                text.style.maxHeight = '0';
                
                button.textContent = 'En savoir plus ↓';
                button.innerHTML = 'En savoir plus ↓';
                
                console.log('🍎 Action: Texte CACHÉ, bouton → "En savoir plus ↓"');
            } else {
                // Montrer le texte
                text.style.display = 'block';
                text.style.opacity = '1';
                text.style.maxHeight = 'none';
                text.style.overflow = 'visible';
                
                button.textContent = 'Réduire ↑';
                button.innerHTML = 'Réduire ↑';
                
                console.log('🍎 Action: Texte VISIBLE, bouton → "Réduire ↑"');
            }
        };
        
        // Events iPhone
        button.addEventListener('touchstart', (e) => {
            touchStarted = true;
            touchStartTime = Date.now();
            touchMoved = false;
            
            // Feedback visuel
            button.style.transform = 'scale(0.95)';
            button.style.opacity = '0.8';
            
            console.log('🍎 Final iPhone Fix: TouchStart');
        }, { passive: false });
        
        button.addEventListener('touchmove', (e) => {
            touchMoved = true;
            console.log('🍎 Final iPhone Fix: TouchMove - annulation');
        }, { passive: false });
        
        button.addEventListener('touchend', (e) => {
            // Reset visuel
            button.style.transform = 'scale(1)';
            button.style.opacity = '1';
            
            const touchDuration = Date.now() - touchStartTime;
            
            if (touchStarted && !touchMoved && touchDuration < 500) {
                console.log('🍎 Final iPhone Fix: TouchEnd VALIDE - exécution toggle');
                setTimeout(() => simpleToggle(e), 50);
            } else {
                console.log(`🍎 Final iPhone Fix: TouchEnd ANNULÉ - moved:${touchMoved}, duration:${touchDuration}ms`);
            }
            
            touchStarted = false;
        }, { passive: false });
        
        // Click fallback pour desktop
        button.addEventListener('click', (e) => {
            if (!touchStarted) {
                console.log('🍎 Final iPhone Fix: Click desktop');
                simpleToggle(e);
            }
        }, { passive: false });
        
        console.log('🍎 Final iPhone Fix: Events touch ajoutés avec succès');
    }
}

// 🚀 INITIALISATION
let finalIPhoneFix;

document.addEventListener('DOMContentLoaded', () => {
    finalIPhoneFix = new FinalIPhoneFix();
});

// Backup
setTimeout(() => {
    if (!finalIPhoneFix) {
        finalIPhoneFix = new FinalIPhoneFix();
    }
}, 2000);

// Export pour debug
window.finalIPhoneFix = finalIPhoneFix;

console.log('🍎 Final iPhone Fix chargé - Events touch fonctionnels');
