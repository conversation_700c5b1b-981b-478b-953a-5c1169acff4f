/**
 * 🍎 IPHONE MODAL FIX - DEVS PROTOCOL ELITE SOLUTION
 * Fix pour modal images sur iOS Safari
 */

class iPhoneModalFix {
    constructor() {
        this.isiPhone = /iPhone|iPad|iPod/.test(navigator.userAgent);
        this.isiOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
        this.isTouch = 'ontouchstart' in window;
        
        if (this.isiOS || this.isiPhone) {
            console.log('🍎 iPhone/iOS detected - Applying fixes');
            this.init();
        }
    }
    
    init() {
        this.fixTouchEvents();
        this.fixModalPositioning();
        this.fixImageExpansion();
        console.log('✅ iPhone modal fixes applied');
    }
    
    /**
     * 🔧 FIX TOUCH EVENTS
     */
    fixTouchEvents() {
        // Override modal bindings for iOS
        document.addEventListener('DOMContentLoaded', () => {
            // Wait for modal to be created
            setTimeout(() => {
                const modal = document.querySelector('.fullscreen-modal');
                if (!modal) return;
                
                // Remove existing click events
                modal.replaceWith(modal.cloneNode(true));
                const newModal = document.querySelector('.fullscreen-modal');
                
                // iOS-specific touch events
                newModal.addEventListener('touchstart', (e) => {
                    if (e.target === newModal) {
                        this.closeModal();
                    }
                }, { passive: true });
                
                newModal.addEventListener('click', (e) => {
                    if (e.target === newModal) {
                        this.closeModal();
                    }
                });
                
                // Prevent image and panel from closing
                const img = newModal.querySelector('.modal-image');
                const panel = newModal.querySelector('.seo-panel');
                
                if (img) {
                    img.addEventListener('touchstart', (e) => e.stopPropagation(), { passive: true });
                    img.addEventListener('click', (e) => e.stopPropagation());
                }
                
                if (panel) {
                    panel.addEventListener('touchstart', (e) => e.stopPropagation(), { passive: true });
                    panel.addEventListener('click', (e) => e.stopPropagation());
                }
                
                console.log('✅ iOS touch events fixed');
            }, 1000);
        });
    }
    
    /**
     * 🎯 FIX MODAL POSITIONING
     */
    fixModalPositioning() {
        const style = document.createElement('style');
        style.textContent = `
            @media screen and (-webkit-min-device-pixel-ratio: 0) {
                .fullscreen-modal {
                    position: fixed !important;
                    top: 0 !important;
                    left: 0 !important;
                    width: 100vw !important;
                    height: 100vh !important;
                    height: 100dvh !important; /* iOS Safari fix */
                    z-index: 999999 !important;
                    transform: none !important;
                    -webkit-transform: none !important;
                }
                
                .modal-image {
                    max-width: 95vw !important;
                    max-height: 75vh !important;
                    max-height: 75dvh !important; /* iOS Safari fix */
                    width: auto !important;
                    height: auto !important;
                    object-fit: contain !important;
                    -webkit-touch-callout: none !important;
                    user-select: none !important;
                    -webkit-user-select: none !important;
                }
                
                .seo-panel {
                    position: fixed !important;
                    bottom: env(safe-area-inset-bottom, 20px) !important;
                    left: 50% !important;
                    transform: translateX(-50%) !important;
                    -webkit-transform: translateX(-50%) !important;
                    z-index: 1000000 !important;
                }
            }
            
            /* Spécifique iPhone en portrait */
            @media only screen 
                and (device-width: 375px) 
                and (device-height: 812px) 
                and (-webkit-device-pixel-ratio: 3)
                and (orientation: portrait) {
                .modal-image {
                    max-height: 70vh !important;
                }
                .seo-panel {
                    bottom: 30px !important;
                    width: 90% !important;
                }
            }
            
            /* Spécifique iPhone en landscape */
            @media only screen 
                and (max-height: 500px) 
                and (orientation: landscape) {
                .modal-image {
                    max-height: 60vh !important;
                }
                .seo-panel {
                    bottom: 15px !important;
                    font-size: 0.8rem !important;
                }
            }
        `;
        document.head.appendChild(style);
        console.log('✅ iOS positioning fixed');
    }
    
    /**
     * 🔍 FIX IMAGE EXPANSION
     */
    fixImageExpansion() {
        // Override course card clicks for iOS
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                const courseCards = document.querySelectorAll('.course-card');
                const expandBtns = document.querySelectorAll('.course-expand-btn');
                
                // Fix expand buttons
                expandBtns.forEach(btn => {
                    const handleExpand = (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        
                        const card = btn.closest('.course-card');
                        const img = card?.querySelector('.course-image');
                        
                        if (img && img.src) {
                            console.log('🍎 iOS: Opening modal for', img.src);
                            
                            if (window.fullscreenModal) {
                                window.fullscreenModal.open(img.src, img.alt);
                            } else if (window.openSimpleModal) {
                                window.openSimpleModal(img.src, img.alt);
                            }
                        }
                    };
                    
                    // Remove existing events
                    btn.replaceWith(btn.cloneNode(true));
                    const newBtn = card.querySelector('.course-expand-btn');
                    
                    // Add iOS-specific events
                    newBtn.addEventListener('touchstart', handleExpand, { passive: false });
                    newBtn.addEventListener('click', handleExpand);
                });
                
                // Also fix direct image clicks
                const courseImages = document.querySelectorAll('.course-image');
                courseImages.forEach(img => {
                    const handleImageClick = (e) => {
                        e.preventDefault();
                        console.log('🍎 iOS: Image clicked directly', img.src);
                        
                        if (window.fullscreenModal) {
                            window.fullscreenModal.open(img.src, img.alt);
                        } else if (window.openSimpleModal) {
                            window.openSimpleModal(img.src, img.alt);
                        }
                    };
                    
                    img.addEventListener('touchstart', handleImageClick, { passive: false });
                    img.addEventListener('click', handleImageClick);
                });
                
                console.log('✅ iOS image expansion fixed');
            }, 1500);
        });
    }
    
    /**
     * ❌ CLOSE MODAL HELPER
     */
    closeModal() {
        if (window.fullscreenModal) {
            window.fullscreenModal.close();
        } else if (window.closeSimpleModal) {
            window.closeSimpleModal();
        }
    }
}

// 🚀 INIT IPHONE FIX
if (/iPhone|iPad|iPod/.test(navigator.userAgent)) {
    console.log('🍎 iPhone detected - Loading fixes...');
    new iPhoneModalFix();
}
