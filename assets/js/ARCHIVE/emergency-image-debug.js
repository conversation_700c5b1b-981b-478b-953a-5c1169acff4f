/**
 * 🚀 SIMPLE IMAGE MODAL TESTER
 * Test the simple modal system
 */

// 🚀 SIMPLE TEST FUNCTION
window.testSimpleModal = function(imageSrc = './assets/images/acceuil1.jpg') {
    console.log('🚀 TESTING SIMPLE MODAL WITH:', imageSrc);
    
    if (window.openSimpleModal) {
        window.openSimpleModal(imageSrc, 'Test Image');
        console.log('✅ Simple modal opened');
    } else {
        console.log('❌ Simple modal not ready yet, retrying...');
        setTimeout(() => {
            if (window.openSimpleModal) {
                window.openSimpleModal(imageSrc, 'Test Image');
            }
        }, 1000);
    }
};

// 🚀 FORCE CLOSE FUNCTION
window.forceCloseSimpleModal = function() {
    if (window.closeSimpleModal) {
        window.closeSimpleModal();
        console.log('✅ Simple modal force closed');
    } else {
        // Fallback: find modal and hide it
        const modal = document.querySelector('.simple-image-modal');
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = '';
            console.log('✅ Modal force closed via DOM');
        }
    }
};

console.log(`
🚀 SIMPLE MODAL TESTER LOADED:
- window.testSimpleModal() - Test with default image
- window.testSimpleModal('path/to/image.jpg') - Test with custom image
- window.forceCloseSimpleModal() - Force close modal
`);

