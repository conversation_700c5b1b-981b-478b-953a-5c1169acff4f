/**
 * 🔧 COURSE EXPAND BUTTONS FIX - DEVS PROTOCOL
 * Ensure image modal works on all devices
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Fixing course expand buttons...');
    
    // Wait for everything to load
    setTimeout(() => {
        const expandButtons = document.querySelectorAll('.course-expand-btn');
        
        expandButtons.forEach((btn, index) => {
            const card = btn.closest('.course-card');
            const img = card?.querySelector('.course-image');
            
            if (img) {
                const handleClick = (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    console.log(`🖼️ Opening image ${index + 1}:`, img.src);
                    
                    // Try multiple modal methods for compatibility
                    if (window.fullscreenModal && window.fullscreenModal.open) {
                        window.fullscreenModal.open(img.src, img.alt);
                    } else if (window.openSimpleModal) {
                        window.openSimpleModal(img.src, img.alt);
                    } else {
                        console.warn('⚠️ No modal system found');
                        // Fallback: open in new tab
                        window.open(img.src, '_blank');
                    }
                };
                
                // Add multiple event types for cross-device compatibility
                btn.addEventListener('click', handleClick);
                btn.addEventListener('touchend', handleClick);
                
                // Also add click to the image itself
                img.style.cursor = 'pointer';
                img.addEventListener('click', handleClick);
                img.addEventListener('touchend', handleClick);
                
                console.log(`✅ Button ${index + 1} fixed for:`, img.src);
            }
        });
        
        console.log(`🎯 Fixed ${expandButtons.length} expand buttons`);
    }, 2000);
});
