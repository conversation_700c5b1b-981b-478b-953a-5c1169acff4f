/**
 * 🎯 SOLUTION DOVITO - LÉGENDE COMPLÈTE PAR DÉFAUT
 * Idée brillante : Afficher le texte complet d'abord, bouton pour réduire
 * Fix définitif pour les problèmes iPhone avec boutons d'expansion
 */

class DovitoLegendSolution {
    constructor() {
        this.isEnabled = this.shouldEnable();
        this.init();
    }
    
    /**
     * 🔍 Détecter si on doit activer la solution Dovito
     * (iPhone, ou tous les appareils selon préférence)
     */
    shouldEnable() {
        // Pour l'instant, activer pour tous les appareils
        // Car l'UX est meilleure même sur desktop
        return true;
        
        // Si on veut seulement sur iPhone/iPad :
        // return /iPad|iPhone|iPod/.test(navigator.userAgent);
    }
    
    init() {
        if (!this.isEnabled) return;
        
        console.log('🎯 Solution Dovito activée - Légende complète par défaut');
        
        // Attendre que le DOM soit prêt
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.applyDovitoSolution());
        } else {
            this.applyDovitoSolution();
        }
        
        // Observer les nouvelles modales qui s'ouvrent
        this.observeModalChanges();
    }
    
    /**
     * 🚀 Appliquer la solution Dovito à toutes les modales existantes
     */
    applyDovitoSolution() {
        // Modifier le système modal existant
        this.patchExistingModalSystem();
        
        // Appliquer aux modales déjà présentes
        setTimeout(() => {
            this.processExistingModals();
        }, 500);
    }
    
    /**
     * 🔧 Modifier le système modal existant pour la solution Dovito
     */
    patchExistingModalSystem() {
        // Vérifier si le système modal existe
        if (typeof window.fullscreenModal === 'undefined') {
            console.log('⚠️ Système modal non trouvé, on attend...');
            setTimeout(() => this.patchExistingModalSystem(), 1000);
            return;
        }
        
        // Sauvegarder la méthode originale
        if (window.fullscreenModal && !window.fullscreenModal._originalPopulateSEO) {
            const modal = window.fullscreenModal;
            modal._originalPopulateSEO = modal.populateSEOContent;
            
            // Remplacer par la version Dovito
            modal.populateSEOContent = this.createDovitoPopulateSEO(modal._originalPopulateSEO.bind(modal));
            
            console.log('✅ Système modal patché avec solution Dovito');
        }
    }
    
    /**
     * 🎨 Créer la version Dovito de populateSEOContent
     */
    createDovitoPopulateSEO(originalMethod) {
        return function(golfInfo) {
            // Appeler la méthode originale
            originalMethod(golfInfo);
            
            // Appliquer la solution Dovito
            const fullText = this.modal.querySelector('.seo-full');
            const expandBtn = this.modal.querySelector('.expand-btn');
            
            if (fullText && expandBtn) {
                // DOVITO SOLUTION: Afficher le texte complet par défaut
                fullText.style.display = 'block';
                expandBtn.textContent = 'Réduire ↑';
                
                // Modifier le comportement du bouton
                expandBtn.onclick = (e) => {
                    e.stopPropagation();
                    const isExpanded = fullText.style.display === 'block';
                    
                    if (isExpanded) {
                        // Réduire (cacher le texte complet)
                        fullText.style.display = 'none';
                        expandBtn.textContent = 'En savoir plus ↓';
                    } else {
                        // Étendre (montrer le texte complet)
                        fullText.style.display = 'block';
                        expandBtn.textContent = 'Réduire ↑';
                    }
                };
                
                // Style amélioré pour iPhone
                expandBtn.style.cssText += `
                    -webkit-appearance: none;
                    -webkit-tap-highlight-color: transparent;
                    touch-action: manipulation;
                    user-select: none;
                `;
            }
        };
    }
    
    /**
     * 🔍 Traiter les modales existantes
     */
    processExistingModals() {
        const existingModals = document.querySelectorAll('.fullscreen-modal');
        
        existingModals.forEach(modal => {
            const fullText = modal.querySelector('.seo-full');
            const expandBtn = modal.querySelector('.expand-btn');
            
            if (fullText && expandBtn && !modal.dataset.dovitoProcessed) {
                this.applyDovitoToModal(modal, fullText, expandBtn);
                modal.dataset.dovitoProcessed = 'true';
            }
        });
    }
    
    /**
     * 🎯 Appliquer la solution Dovito à une modal spécifique
     */
    applyDovitoToModal(modal, fullText, expandBtn) {
        // Afficher le texte complet par défaut
        fullText.style.display = 'block';
        expandBtn.textContent = 'Réduire ↑';
        
        // Supprimer les anciens event listeners
        const newBtn = expandBtn.cloneNode(true);
        expandBtn.parentNode.replaceChild(newBtn, expandBtn);
        
        // Nouveau comportement Dovito
        newBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            
            const isExpanded = fullText.style.display === 'block';
            
            if (isExpanded) {
                fullText.style.display = 'none';
                newBtn.textContent = 'En savoir plus ↓';
            } else {
                fullText.style.display = 'block';
                newBtn.textContent = 'Réduire ↑';
            }
        });
        
        // Support tactile amélioré pour iPhone
        newBtn.addEventListener('touchstart', (e) => {
            newBtn.style.opacity = '0.7';
        }, { passive: true });
        
        newBtn.addEventListener('touchend', (e) => {
            newBtn.style.opacity = '1';
        }, { passive: true });
        
        // Style iPhone optimisé
        newBtn.style.cssText += `
            -webkit-appearance: none;
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
            user-select: none;
            font-size: 0.85rem;
            padding: 8px 14px;
            border-radius: 8px;
        `;
        
        console.log('✅ Solution Dovito appliquée à modal');
    }
    
    /**
     * 👁️ Observer les changements de modales
     */
    observeModalChanges() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                    const element = mutation.target;
                    if (element.classList.contains('fullscreen-modal')) {
                        const style = getComputedStyle(element);
                        if (style.display === 'flex' && parseFloat(style.opacity) > 0) {
                            // Modal qui s'ouvre
                            setTimeout(() => {
                                this.processExistingModals();
                            }, 100);
                        }
                    }
                }
            });
        });
        
        observer.observe(document.body, {
            attributes: true,
            subtree: true,
            attributeFilter: ['style']
        });
    }
}

/**
 * 🎯 ACTIVATION AUTOMATIQUE
 */
(function() {
    'use strict';
    
    // Attendre que tout soit chargé
    function initDovitoSolution() {
        window.dovitoSolution = new DovitoLegendSolution();
    }
    
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initDovitoSolution);
    } else {
        initDovitoSolution();
    }
    
    // Fallback pour s'assurer que ça marche
    setTimeout(initDovitoSolution, 2000);
    
})();
