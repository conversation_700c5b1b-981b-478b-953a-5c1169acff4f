/**
 * 🔥 QUANTUM MOBILE TEMPERATURE TESTER
 * Tests NUCLEAR PRECISION fix for temperature display
 * <PERSON> (UX) + <PERSON><PERSON> (CSS) + <PERSON> (Performance) = PERFECTION
 */

// 🎯 ULTIMATE TEMPERATURE TEST SUITE
window.quantumTemperatureTest = function() {
    console.log('🔥 RUNNING QUANTUM TEMPERATURE TEST SUITE...\n');
    
    const tests = [];
    let allPassed = true;
    
    // Test 1: Weather widget exists and has correct dimensions
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const tempElement = document.querySelector('.weather-temp-mobile');
    
    if (!weatherWidget || !tempElement) {
        console.log('❌ CRITICAL: Weather elements not found!');
        return false;
    }
    
    const widgetRect = weatherWidget.getBoundingClientRect();
    const tempRect = tempElement.getBoundingClientRect();
    
    console.log('📊 WIDGET DIMENSIONS:');
    console.log(`  Width: ${Math.round(widgetRect.width)}px (target: 80px+)`);
    console.log(`  Height: ${Math.round(widgetRect.height)}px`);
    console.log(`  Position: x=${Math.round(widgetRect.left)}, y=${Math.round(widgetRect.top)}`);
    
    // Test 2: Temperature text fitting
    const tempText = tempElement.textContent || '--°';
    console.log(`\n🌡️ TEMPERATURE ANALYSIS:`);
    console.log(`  Text: "${tempText}"`);
    console.log(`  Text width: ${Math.round(tempRect.width)}px`);
    console.log(`  Container width: ${Math.round(widgetRect.width)}px`);
    
    const temperatureFits = tempRect.width <= widgetRect.width;
    tests.push({
        name: 'Temperature fits in container',
        passed: temperatureFits,
        details: `${Math.round(tempRect.width)}px text in ${Math.round(widgetRect.width)}px container`
    });
    
    // Test 3: No overflow hidden on critical elements
    const widgetStyles = getComputedStyle(weatherWidget);
    const tempStyles = getComputedStyle(tempElement);
    
    const noOverflowHidden = widgetStyles.overflow !== 'hidden' && tempStyles.overflow !== 'hidden';
    tests.push({
        name: 'No overflow hidden',
        passed: noOverflowHidden,
        details: `Widget: ${widgetStyles.overflow}, Temp: ${tempStyles.overflow}`
    });
    
    // Test 4: Font size adequate for readability
    const fontSize = parseFloat(tempStyles.fontSize);
    const fontSizeOK = fontSize >= 12; // At least 0.75rem
    tests.push({
        name: 'Font size readable',
        passed: fontSizeOK,
        details: `${fontSize}px (min: 12px)`
    });
    
    // Test 5: Element spacing check
    const logo = document.querySelector('.flex.items-center.space-x-3:first-child');
    const controls = document.querySelector('.lg\\:hidden.flex.items-center.space-x-3');
    
    if (logo && controls) {
        const logoRect = logo.getBoundingClientRect();
        const controlsRect = controls.getBoundingClientRect();
        
        const logoWeatherGap = widgetRect.left - logoRect.right;
        const weatherControlsGap = controlsRect.left - widgetRect.right;
        
        console.log(`\n📏 SPACING ANALYSIS:`);
        console.log(`  Logo → Weather gap: ${Math.round(logoWeatherGap)}px`);
        console.log(`  Weather → Controls gap: ${Math.round(weatherControlsGap)}px`);
        
        const spacingOK = logoWeatherGap >= -5 && weatherControlsGap >= -5; // 5px tolerance
        tests.push({
            name: 'Element spacing OK',
            passed: spacingOK,
            details: `Gaps: ${Math.round(logoWeatherGap)}px, ${Math.round(weatherControlsGap)}px`
        });
    }
    
    // Test 6: Container width meets target
    const widthTarget = widgetRect.width >= 80;
    tests.push({
        name: 'Widget width meets target',
        passed: widthTarget,
        details: `${Math.round(widgetRect.width)}px (target: 80px+)`
    });
    
    // Test 7: Temperature never cuts (ultimate test)
    const measureSpan = document.createElement('span');
    measureSpan.style.visibility = 'hidden';
    measureSpan.style.position = 'absolute';
    measureSpan.style.whiteSpace = 'nowrap';
    measureSpan.style.fontSize = tempStyles.fontSize;
    measureSpan.style.fontWeight = tempStyles.fontWeight;
    measureSpan.style.fontFamily = tempStyles.fontFamily;
    measureSpan.textContent = tempText;
    
    document.body.appendChild(measureSpan);
    const actualTextWidth = measureSpan.getBoundingClientRect().width;
    document.body.removeChild(measureSpan);
    
    const neverCuts = actualTextWidth <= widgetRect.width + 5; // 5px tolerance
    tests.push({
        name: 'Temperature NEVER cuts',
        passed: neverCuts,
        details: `Text: ${Math.round(actualTextWidth)}px, Container: ${Math.round(widgetRect.width)}px`
    });
    
    // Results summary
    console.log(`\n🧪 TEST RESULTS SUMMARY:`);
    tests.forEach((test, i) => {
        const status = test.passed ? '✅' : '❌';
        console.log(`  ${i + 1}. ${status} ${test.name} - ${test.details}`);
        if (!test.passed) allPassed = false;
    });
    
    console.log(`\n${allPassed ? '🎉' : '⚠️'} OVERALL RESULT: ${allPassed ? 'ALL TESTS PASSED! TEMPERATURE WILL NEVER CUT!' : 'Some issues detected'}`);
    
    if (allPassed) {
        console.log('🔥 QUANTUM FIX SUCCESS! Your clients will say "PUTAIN c\'est beau!" 🏆');
    } else {
        console.log('🔧 Run quantumEmergencyFix() for additional adjustments');
    }
    
    return allPassed;
};

// 🚨 EMERGENCY FIX - If quantum test fails
window.quantumEmergencyFix = function() {
    console.log('🚨 APPLYING EMERGENCY QUANTUM ADJUSTMENTS...');
    
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const tempElement = document.querySelector('.weather-temp-mobile');
    
    if (!weatherWidget || !tempElement) {
        console.log('❌ Elements not found for emergency fix');
        return;
    }
    
    // Nuclear expansion
    weatherWidget.style.minWidth = '85px';
    weatherWidget.style.maxWidth = '90px';
    weatherWidget.style.width = '85px';
    weatherWidget.style.overflow = 'visible';
    weatherWidget.style.whiteSpace = 'nowrap';
    
    tempElement.style.overflow = 'visible';
    tempElement.style.whiteSpace = 'nowrap';
    tempElement.style.textOverflow = 'clip';
    tempElement.style.maxWidth = 'none';
    tempElement.style.width = 'auto';
    tempElement.style.fontSize = '0.8rem';
    tempElement.style.fontWeight = '700';
    
    console.log('✅ Emergency fix applied! Testing in 1 second...');
    
    setTimeout(() => {
        window.quantumTemperatureTest();
    }, 1000);
};

// 🎨 VISUAL DEBUG - Show temperature boundaries
window.quantumVisualDebug = function() {
    console.log('🎨 QUANTUM VISUAL DEBUG ACTIVATED...');
    
    const weatherWidget = document.querySelector('.weather-widget-mobile');
    const tempElement = document.querySelector('.weather-temp-mobile');
    
    if (!weatherWidget || !tempElement) {
        console.log('❌ Elements not found for visual debug');
        return;
    }
    
    // Add colored borders with different colors for each element
    weatherWidget.style.border = '2px solid #FF6B6B';
    weatherWidget.style.backgroundColor = 'rgba(255, 107, 107, 0.1)';
    
    tempElement.style.border = '1px solid #4ECDC4';
    tempElement.style.backgroundColor = 'rgba(78, 205, 196, 0.2)';
    
    // Add labels
    const widgetLabel = document.createElement('div');
    widgetLabel.textContent = 'Widget Container';
    widgetLabel.style.position = 'absolute';
    widgetLabel.style.top = '-20px';
    widgetLabel.style.left = '0';
    widgetLabel.style.fontSize = '10px';
    widgetLabel.style.color = '#FF6B6B';
    widgetLabel.style.fontWeight = 'bold';
    widgetLabel.style.zIndex = '9999';
    weatherWidget.style.position = 'relative';
    weatherWidget.appendChild(widgetLabel);
    
    const tempLabel = document.createElement('div');
    tempLabel.textContent = 'Temperature Text';
    tempLabel.style.position = 'absolute';
    tempLabel.style.bottom = '-20px';
    tempLabel.style.left = '0';
    tempLabel.style.fontSize = '10px';
    tempLabel.style.color = '#4ECDC4';
    tempLabel.style.fontWeight = 'bold';
    tempLabel.style.zIndex = '9999';
    tempElement.style.position = 'relative';
    tempElement.appendChild(tempLabel);
    
    console.log('🎨 Visual debug active:');
    console.log('🔴 Red border = Widget container');
    console.log('🟢 Teal border = Temperature text');
    console.log('📏 Text should NEVER overflow red container');
    
    setTimeout(() => {
        weatherWidget.style.border = '';
        weatherWidget.style.backgroundColor = '';
        tempElement.style.border = '';
        tempElement.style.backgroundColor = '';
        if (widgetLabel.parentNode) widgetLabel.parentNode.removeChild(widgetLabel);
        if (tempLabel.parentNode) tempLabel.parentNode.removeChild(tempLabel);
        console.log('🧹 Visual debug cleaned up');
    }, 10000);
};

// 📱 RESPONSIVE TEST - Test on multiple screen sizes
window.quantumResponsiveTest = function() {
    console.log('📱 QUANTUM RESPONSIVE TEST...');
    
    const screenSizes = [
        { width: 320, name: 'iPhone SE' },
        { width: 375, name: 'iPhone 12 Mini' },
        { width: 390, name: 'iPhone 12 Pro' },
        { width: 414, name: 'iPhone 12 Pro Max' },
        { width: 768, name: 'iPad Portrait' }
    ];
    
    const originalWidth = window.innerWidth;
    let currentTest = 0;
    
    function testNextSize() {
        if (currentTest >= screenSizes.length) {
            console.log('📱 Responsive test completed!');
            console.log('💡 Manually resize your browser to test different widths');
            return;
        }
        
        const size = screenSizes[currentTest];
        console.log(`\n📱 Testing ${size.name} (${size.width}px):`);
        
        // Can't actually resize window in this context, but show what to test
        console.log(`   💡 Manually resize to ${size.width}px width and run quantumTemperatureTest()`);
        
        currentTest++;
        setTimeout(testNextSize, 1000);
    }
    
    testNextSize();
};

// 🚀 AUTO-TEST ON LOAD
document.addEventListener('DOMContentLoaded', () => {
    // Wait for everything to load
    setTimeout(() => {
        console.log('\n🔥 QUANTUM MOBILE TEMPERATURE TESTER LOADED!\n');
        console.log('Available commands:');
        console.log('  🧪 quantumTemperatureTest() - Run complete test suite');
        console.log('  🚨 quantumEmergencyFix() - Apply emergency adjustments');
        console.log('  🎨 quantumVisualDebug() - Show visual boundaries (10s)');
        console.log('  📱 quantumResponsiveTest() - Test multiple screen sizes');
        console.log('\n🎯 Running auto-test in 2 seconds...\n');
        
        setTimeout(() => {
            window.quantumTemperatureTest();
        }, 2000);
        
    }, 1000);
});

console.log('🔥 QUANTUM MOBILE TEMPERATURE TESTER INITIALIZED!');