#!/bin/bash

# 🚀 GolfinThaï - Script de Migration vers Architecture Clean
# Remplace progressivement l'ancien chaos par la nouvelle architecture

echo "🚀 Migration GolfinThaï vers Architecture Clean Silicon Valley"
echo "============================================================="

# Créer un backup de l'ancien système
echo "📦 Création du backup..."
mkdir -p backup-$(date +%Y%m%d-%H%M%S)
cp index.html backup-$(date +%Y%m%d-%H%M%S)/
cp -r assets/css backup-$(date +%Y%m%d-%H%M%S)/css-old
cp -r assets/js backup-$(date +%Y%m%d-%H%M%S)/js-old

echo "✅ Backup créé avec succès"

# Phase 1: Tester la nouvelle architecture
echo ""
echo "🧪 Phase 1: Test de la nouvelle architecture"
echo "Ouvrez index-final.html dans votre navigateur pour tester"
echo "Comparez avec index.html (version actuelle)"
echo ""
read -p "La nouvelle version fonctionne-t-elle correctement? (y/n): " confirm

if [ "$confirm" != "y" ]; then
    echo "❌ Migration annulée. Corrigez les problèmes et relancez."
    exit 1
fi

# Phase 2: Migration progressive
echo ""
echo "🔄 Phase 2: Migration progressive"

# Sauvegarder l'ancien index.html
mv index.html index-old.html
echo "✅ Ancien index.html sauvegardé vers index-old.html"

# Remplacer par la nouvelle version
cp index-final.html index.html
echo "✅ Nouveau index.html activé"

# Créer un dossier pour les anciens fichiers CSS
mkdir -p assets/css/old
mv assets/css/*.css assets/css/old/ 2>/dev/null || true
echo "✅ Anciens fichiers CSS déplacés vers assets/css/old/"

# Créer un dossier pour les anciens fichiers JS
mkdir -p assets/js/old
mv assets/js/*.js assets/js/old/ 2>/dev/null || true
echo "✅ Anciens fichiers JS déplacés vers assets/js/old/"

# Garder les fichiers clean
cp -r assets/css/clean/* assets/css/ 2>/dev/null || true
cp -r assets/js/clean/* assets/js/ 2>/dev/null || true

echo ""
echo "🎉 Migration terminée avec succès!"
echo ""
echo "📋 Résumé des changements:"
echo "- ✅ index.html -> Architecture clean activée"
echo "- ✅ CSS -> 6 fichiers propres au lieu de 50+ chaotiques"
echo "- ✅ JS -> 3 fichiers modulaires au lieu de dizaines"
echo "- ✅ Anciens fichiers sauvegardés dans /old/"
echo ""
echo "🧪 Tests recommandés:"
echo "1. Navigation mobile (menu hamburger)"
echo "2. Carrousel hero (autoplay + pagination)"
echo "3. Changement de langue (FR/EN)"
echo "4. Modales d'images (clic sur images)"
echo "5. Formulaire de contact"
echo "6. Responsive design (mobile/desktop)"
echo ""
echo "🔧 En cas de problème:"
echo "- Restaurer: mv index-old.html index.html"
echo "- Logs: Ouvrir la console développeur"
echo "- Support: Vérifier ARCHITECTURE-CLEAN.md"
echo ""
echo "🚀 Votre site est maintenant propre et maintenable!"