# Netlify redirects file
# https://docs.netlify.com/routing/redirects/

# SPA fallback - redirect all routes to index.html for client-side routing
/*    /index.html   200

# Security headers
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()

# Cache headers for static assets
/assets/*
  Cache-Control: public, max-age=********, immutable

# Cache headers for images
*.jpg
  Cache-Control: public, max-age=********
*.jpeg
  Cache-Control: public, max-age=********
*.png
  Cache-Control: public, max-age=********
*.webp
  Cache-Control: public, max-age=********
*.svg
  Cache-Control: public, max-age=********

# Service worker
/sw.js
  Cache-Control: public, max-age=0, must-revalidate

# Manifest
/manifest.json
  Cache-Control: public, max-age=86400
