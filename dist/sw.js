const CACHE_NAME='golfinthai-v2.0.0',STATIC_CACHE=`${CACHE_NAME}-static`,DYNAMIC_CACHE=`${CACHE_NAME}-dynamic`,IMAGE_CACHE=`${CACHE_NAME}-images`,STATIC_ASSETS=['/','/index.html','/manifest.json','/favicon.ico','/favicon.svg'],IMAGE_EXTENSIONS=['.jpg','.jpeg','.png','.webp','.avif','.svg'],API_ENDPOINTS=['/data/courses.json','/data/translations.json'];self.addEventListener('install',e=>{console.log('Service Worker: Installing...'),e.waitUntil(caches.open(STATIC_CACHE).then(cache=>(console.log('Service Worker: Caching static assets'),cache.addAll(STATIC_ASSETS))).then(()=>(console.log('Service Worker: Installed successfully'),self.skipWaiting())).catch(error=>{console.error('Service Worker: Installation failed',error)}))});self.addEventListener('activate',e=>{console.log('Service Worker: Activating...'),e.waitUntil(caches.keys().then(cacheNames=>Promise.all(cacheNames.map(cacheName=>{if(cacheName!==STATIC_CACHE&&cacheName!==DYNAMIC_CACHE&&cacheName!==IMAGE_CACHE)return console.log('Service Worker: Deleting old cache',cacheName),caches.delete(cacheName)}))).then(()=>(console.log('Service Worker: Activated successfully'),self.clients.claim())))});self.addEventListener('fetch',e=>{const{request}=e,url=new URL(request.url);if('GET'===request.method&&url.protocol.startsWith('http'))e.respondWith(handleRequest(request))});async function handleRequest(request){const url=new URL(request.url);try{if(STATIC_ASSETS.some(asset=>url.pathname===asset))return await cacheFirst(request,STATIC_CACHE);if(isImageRequest(request))return await cacheFirstWithFallback(request,IMAGE_CACHE);if(API_ENDPOINTS.some(endpoint=>url.pathname.includes(endpoint)))return await networkFirstWithCache(request,DYNAMIC_CACHE);if(request.headers.get('accept')?.includes('text/html'))return await networkFirstWithCache(request,DYNAMIC_CACHE);if(isCSSOrJSRequest(request))return await cacheFirst(request,STATIC_CACHE);return await networkFirst(request)}catch(error){if(console.error('Service Worker: Request failed',error),request.headers.get('accept')?.includes('text/html'))return await getOfflineFallback();throw error}}async function cacheFirst(request,cacheName){const cache=await caches.open(cacheName),cachedResponse=await cache.match(request);if(cachedResponse)return cachedResponse;const networkResponse=await fetch(request);return networkResponse.ok&&cache.put(request,networkResponse.clone()),networkResponse}async function cacheFirstWithFallback(request,cacheName){try{return await cacheFirst(request,cacheName)}catch(error){return new Response('<svg xmlns="http://www.w3.org/2000/svg" width="400" height="300" viewBox="0 0 400 300"><rect width="400" height="300" fill="#f3f4f6"/><text x="200" y="150" text-anchor="middle" fill="#9ca3af" font-family="Arial, sans-serif" font-size="16">Image non disponible</text></svg>',{headers:{'Content-Type':'image/svg+xml','Cache-Control':'no-cache'}})}}async function networkFirstWithCache(request,cacheName){const cache=await caches.open(cacheName);try{const networkResponse=await fetch(request);return networkResponse.ok&&cache.put(request,networkResponse.clone()),networkResponse}catch(error){const cachedResponse=await cache.match(request);if(cachedResponse)return cachedResponse;throw error}}async function networkFirst(request){return await fetch(request)}async function getOfflineFallback(){const cache=await caches.open(STATIC_CACHE);return await cache.match('/')||new Response('<!DOCTYPE html><html lang="fr"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>GolfinThaï - Hors ligne</title><style>body{font-family:Arial,sans-serif;text-align:center;padding:50px}.offline{color:#666}.retry-btn{background:#16a34a;color:white;border:none;padding:10px 20px;border-radius:5px;cursor:pointer;margin-top:20px}</style></head><body><div class="offline"><h1>Vous êtes hors ligne</h1><p>Vérifiez votre connexion internet et réessayez.</p><button class="retry-btn" onclick="location.reload()">Réessayer</button></div></body></html>',{headers:{'Content-Type':'text/html','Cache-Control':'no-cache'}})}function isImageRequest(request){const url=new URL(request.url);return IMAGE_EXTENSIONS.some(ext=>url.pathname.toLowerCase().includes(ext))}function isCSSOrJSRequest(request){const url=new URL(request.url);return url.pathname.includes('.css')||url.pathname.includes('.js')}self.addEventListener('sync',e=>{'contact-form'===e.tag&&e.waitUntil(syncContactForm())});async function syncContactForm(){console.log('Service Worker: Syncing contact form submissions')}self.addEventListener('push',e=>{if(!e.data)return;const data=e.data.json(),options={body:data.body,icon:'/favicon.svg',badge:'/favicon.svg',vibrate:[200,100,200],data:data.data||{},actions:data.actions||[]};e.waitUntil(self.registration.showNotification(data.title,options))});self.addEventListener('notificationclick',e=>{e.notification.close(),e.waitUntil(clients.openWindow(e.notification.data.url||'/'))});console.log('Service Worker: Loaded successfully');
