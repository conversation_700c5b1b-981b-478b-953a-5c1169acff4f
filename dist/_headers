# Netlify headers file
# https://docs.netlify.com/routing/headers/

/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https:; media-src 'self'; object-src 'none'; frame-src 'none'; base-uri 'self'; form-action 'self';

/assets/*
  Cache-Control: public, max-age=********, immutable

*.js
  Content-Type: application/javascript; charset=utf-8
  Cache-Control: public, max-age=********, immutable

*.css
  Content-Type: text/css; charset=utf-8
  Cache-Control: public, max-age=********, immutable

*.woff2
  Content-Type: font/woff2
  Cache-Control: public, max-age=********, immutable

*.woff
  Content-Type: font/woff
  Cache-Control: public, max-age=********, immutable

*.ttf
  Content-Type: font/ttf
  Cache-Control: public, max-age=********, immutable

*.jpg
  Content-Type: image/jpeg
  Cache-Control: public, max-age=********

*.jpeg
  Content-Type: image/jpeg
  Cache-Control: public, max-age=********

*.png
  Content-Type: image/png
  Cache-Control: public, max-age=********

*.webp
  Content-Type: image/webp
  Cache-Control: public, max-age=********

*.svg
  Content-Type: image/svg+xml
  Cache-Control: public, max-age=********

/sw.js
  Content-Type: application/javascript; charset=utf-8
  Cache-Control: public, max-age=0, must-revalidate

/manifest.json
  Content-Type: application/manifest+json; charset=utf-8
  Cache-Control: public, max-age=86400
