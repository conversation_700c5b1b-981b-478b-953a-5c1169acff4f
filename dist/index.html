<!DOCTYPE html>
<html lang="fr" prefix="og: https://ogp.me/ns#">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="GolfinThaï - Découvrez les plus beaux parcours de golf de Thaïlande. Réservez votre séjour golf avec nos experts locaux.">
    <meta name="keywords" content="golf, thaïlande, parcours, réservation, séjour, bangkok, phuket, chiang mai">
    <meta name="author" content="GolfinThaï">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://golfinthai.com/">
    <meta property="og:title" content="GolfinThaï - Golf en Thaïlande">
    <meta property="og:description" content="Découvrez les plus beaux parcours de golf de Thaïlande avec GolfinThaï">
    <meta property="og:image" content="./assets/images/hero-golf-thailand.jpg">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://golfinthai.com/">
    <meta property="twitter:title" content="GolfinThaï - Golf en Thaïlande">
    <meta property="twitter:description" content="Découvrez les plus beaux parcours de golf de Thaïlande avec GolfinThaï">
    <meta property="twitter:image" content="./assets/images/hero-golf-thailand.jpg">
    
    <title>GolfinThaï - Golf en Thaïlande | Parcours Premium & Séjours</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="./favicon.svg">
    <link rel="icon" type="image/x-icon" href="./favicon.ico">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="./manifest.json">
    <meta name="theme-color" content="#16a34a">
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>
    
    <!-- Critical CSS (inlined for performance) -->
    <style>
        :root{--color-primary-50:#f0fdf4;--color-primary-100:#dcfce7;--color-primary-200:#bbf7d0;--color-primary-300:#86efac;--color-primary-400:#4ade80;--color-primary-500:#22c55e;--color-primary-600:#16a34a;--color-primary-700:#15803d;--color-primary-800:#166534;--color-primary-900:#14532d;--color-secondary-50:#fffbeb;--color-secondary-100:#fef3c7;--color-secondary-200:#fde68a;--color-secondary-300:#fcd34d;--color-secondary-400:#fbbf24;--color-secondary-500:#f59e0b;--color-secondary-600:#d97706;--color-secondary-700:#b45309;--color-secondary-800:#92400e;--color-secondary-900:#78350f;--color-neutral-50:#fafafa;--color-neutral-100:#f5f5f5;--color-neutral-200:#e5e5e5;--color-neutral-300:#d4d4d4;--color-neutral-400:#a3a3a3;--color-neutral-500:#737373;--color-neutral-600:#525252;--color-neutral-700:#404040;--color-neutral-800:#262626;--color-neutral-900:#171717;--color-success:var(--color-primary-600);--color-warning:var(--color-secondary-500);--color-error:#dc2626;--color-info:#2563eb;--color-background:#ffffff;--color-surface:var(--color-neutral-50);--color-surface-elevated:#ffffff;--color-text-primary:var(--color-neutral-900);--color-text-secondary:var(--color-neutral-600);--color-text-muted:var(--color-neutral-400);--color-text-inverse:#ffffff;--font-primary:'Poppins',-apple-system,BlinkMacSystemFont,'Segoe UI',sans-serif;--font-heading:'Playfair Display',Georgia,serif;--font-mono:'SF Mono',Monaco,'Cascadia Code',monospace;--text-xs:clamp(0.75rem,0.7rem + 0.25vw,0.875rem);--text-sm:clamp(0.875rem,0.8rem + 0.375vw,1rem);--text-base:clamp(1rem,0.9rem + 0.5vw,1.125rem);--text-lg:clamp(1.125rem,1rem + 0.625vw,1.25rem);--text-xl:clamp(1.25rem,1.1rem + 0.75vw,1.5rem);--text-2xl:clamp(1.5rem,1.3rem + 1vw,1.875rem);--text-3xl:clamp(1.875rem,1.6rem + 1.375vw,2.25rem);--text-4xl:clamp(2.25rem,1.9rem + 1.75vw,3rem);--text-5xl:clamp(3rem,2.5rem + 2.5vw,4rem);--leading-tight:1.25;--leading-normal:1.5;--leading-relaxed:1.75;--font-light:300;--font-normal:400;--font-medium:500;--font-semibold:600;--font-bold:700;--space-0:0;--space-1:0.25rem;--space-2:0.5rem;--space-3:0.75rem;--space-4:1rem;--space-5:1.25rem;--space-6:1.5rem;--space-8:2rem;--space-10:2.5rem;--space-12:3rem;--space-16:4rem;--space-20:5rem;--space-24:6rem;--space-32:8rem;--shadow-sm:0 1px 2px 0 rgb(0 0 0 / 0.05);--shadow-base:0 1px 3px 0 rgb(0 0 0 / 0.1),0 1px 2px -1px rgb(0 0 0 / 0.1);--shadow-md:0 4px 6px -1px rgb(0 0 0 / 0.1),0 2px 4px -2px rgb(0 0 0 / 0.1);--shadow-lg:0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);--shadow-xl:0 20px 25px -5px rgb(0 0 0 / 0.1),0 8px 10px -6px rgb(0 0 0 / 0.1);--shadow-2xl:0 25px 50px -12px rgb(0 0 0 / 0.25);--radius-none:0;--radius-sm:0.125rem;--radius-base:0.25rem;--radius-md:0.375rem;--radius-lg:0.5rem;--radius-xl:0.75rem;--radius-2xl:1rem;--radius-full:9999px;--transition-fast:150ms ease-in-out;--transition-base:250ms ease-in-out;--transition-slow:350ms ease-in-out;--ease-in-out:cubic-bezier(0.4,0,0.2,1);--ease-out:cubic-bezier(0,0,0.2,1);--ease-in:cubic-bezier(0.4,0,1,1);--z-dropdown:1000;--z-sticky:1020;--z-fixed:1030;--z-modal-backdrop:1040;--z-modal:1050;--z-popover:1060;--z-tooltip:1070;--z-toast:1080}*,*::before,*::after{box-sizing:border-box}*{margin:0}html,body{height:100%}body{line-height:1.5;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeSpeed;font-family:var(--font-primary);font-size:var(--text-base);color:var(--color-text-primary);background-color:var(--color-background)}img,picture,video,canvas,svg{display:block;max-width:100%;height:auto}input,button,textarea,select{font:inherit}p,h1,h2,h3,h4,h5,h6{overflow-wrap:break-word}#root,#__next{isolation:isolate}.loading{display:flex;align-items:center;justify-content:center;min-height:100vh;font-size:1.125rem;background:var(--color-background);color:var(--color-text-primary)}.header{position:sticky;top:0;z-index:var(--z-sticky);background-color:var(--color-background);border-bottom:1px solid var(--color-neutral-200);backdrop-filter:blur(8px);transition:all var(--transition-base)}.header.scrolled{box-shadow:var(--shadow-md);background-color:rgba(255,255,255,0.95)}.header-container{width:100%;max-width:1280px;margin-left:auto;margin-right:auto;padding-left:var(--space-4);padding-right:var(--space-4);display:flex;align-items:center;justify-content:space-between;min-height:70px;padding-top:var(--space-3);padding-bottom:var(--space-3)}@media (min-width:768px){.header-container{min-height:80px;padding-top:var(--space-4);padding-bottom:var(--space-4);padding-left:var(--space-6);padding-right:var(--space-6)}}@media (min-width:1024px){.header-container{padding-left:var(--space-8);padding-right:var(--space-8)}}.header-logo{display:flex;align-items:center;gap:var(--space-3);text-decoration:none;color:var(--color-text-primary);font-weight:var(--font-semibold);transition:opacity var(--transition-fast)}.header-logo:hover{opacity:0.8}.header-logo img{height:40px;width:auto}@media (min-width:768px){.header-logo img{height:48px}}.logo-text{font-family:var(--font-heading);font-size:var(--text-lg);font-weight:var(--font-bold)}@media (min-width:768px){.logo-text{font-size:var(--text-xl)}}.mobile-menu-button{display:flex;align-items:center;justify-content:center;width:44px;height:44px;background-color:transparent;border:2px solid var(--color-neutral-300);border-radius:var(--radius-lg);cursor:pointer;transition:all var(--transition-fast)}@media (min-width:1024px){.mobile-menu-button{display:none}}.mobile-menu-button:hover{background-color:var(--color-neutral-100);border-color:var(--color-neutral-400)}.hamburger{position:relative;width:20px;height:16px}.hamburger span{position:absolute;left:0;width:100%;height:2px;background-color:currentColor;border-radius:var(--radius-full);transition:all var(--transition-base)}.hamburger span:nth-child(1){top:0}.hamburger span:nth-child(2){top:7px}.hamburger span:nth-child(3){top:14px}.carousel{position:relative;overflow:hidden;border-radius:var(--radius-xl);background-color:var(--color-surface);box-shadow:var(--shadow-base)}.carousel-hero{height:60vh;min-height:400px}@media (min-width:768px){.carousel-hero{height:70vh;min-height:500px}}.carousel-slide{flex:0 0 100%;width:100%;height:100%;position:relative;opacity:0;transition:opacity var(--transition-base)}.carousel-slide.active{opacity:1}.carousel-slide img{width:100%;height:100%;object-fit:cover;display:block}.slide-content{position:absolute;bottom:0;left:0;right:0;background:linear-gradient(transparent,rgba(0,0,0,0.7));color:white;padding:var(--space-6) var(--space-4) var(--space-4);text-align:center}.slide-content h1{margin:0 0 var(--space-4) 0;font-size:var(--text-2xl);font-weight:var(--font-bold);font-family:var(--font-heading)}@media (min-width:768px){.slide-content h1{font-size:var(--text-4xl)}}.slide-content p{margin:0;font-size:var(--text-base);opacity:0.9;max-width:600px;margin:0 auto}@media (min-width:768px){.slide-content p{font-size:var(--text-lg)}}
    </style>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Main stylesheet -->
    <link rel="stylesheet" href="./assets/css/main.min.css">
</head>
<body>
    <!-- Loading indicator -->
    <div id="loading" class="loading">
        <div>Chargement de GolfinThaï...</div>
    </div>
    
    <!-- Main application container -->
    <div id="app" style="display: none;">
        <!-- Header -->
        <header class="header" id="header">
            <div class="header-container">
                <!-- Logo -->
                <a href="#home" class="header-logo">
                    <img src="./assets/images/logo-golfinthai.jpg" alt="GolfinThaï Logo" width="48" height="48">
                    <span class="logo-text">GolfinThaï</span>
                </a>
                
                <!-- Desktop Navigation -->
                <nav class="header-nav" aria-label="Navigation principale">
                    <ul class="nav-list">
                        <li><a href="#home" class="nav-link active" data-translate="nav.home">Accueil</a></li>
                        <li><a href="#courses" class="nav-link" data-translate="nav.courses">Parcours</a></li>
                        <li><a href="#destinations" class="nav-link" data-translate="nav.destinations">Destinations</a></li>
                        <li><a href="#services" class="nav-link" data-translate="nav.services">Services</a></li>
                        <li><a href="#contact" class="nav-link" data-translate="nav.contact">Contact</a></li>
                    </ul>
                </nav>
                
                <!-- Header Actions -->
                <div class="header-actions">
                    <!-- Weather Widget -->
                    <div class="header-weather" id="weather-widget">
                        <span class="weather-icon">🌤️</span>
                        <span class="weather-temp" id="weather-temp">--°C</span>
                        <span class="weather-location">Bangkok</span>
                    </div>
                    
                    <!-- Language Switcher -->
                    <div class="language-switcher">
                        <button class="language-button" aria-expanded="false" aria-haspopup="true">
                            <img src="./assets/images/flags/fr.svg" alt="Français" class="flag">
                            <span class="language-code">FR</span>
                            <i class="fas fa-chevron-down chevron"></i>
                        </button>
                        <div class="language-dropdown" role="menu">
                            <div class="language-option active" role="menuitem" data-lang="fr">
                                <img src="./assets/images/flags/fr.svg" alt="Français" class="flag">
                                <span class="language-name">Français</span>
                            </div>
                            <div class="language-option" role="menuitem" data-lang="en">
                                <img src="./assets/images/flags/en.svg" alt="English" class="flag">
                                <span class="language-name">English</span>
                            </div>
                            <div class="language-option" role="menuitem" data-lang="th">
                                <img src="./assets/images/flags/th.svg" alt="ไทย" class="flag">
                                <span class="language-name">ไทย</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Mobile Menu Button -->
                    <button class="mobile-menu-button" id="mobile-menu-btn" aria-expanded="false" aria-controls="mobile-menu-overlay" aria-label="Ouvrir le menu">
                        <div class="hamburger">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- Mobile Menu -->
        <div class="mobile-menu-overlay" id="mobile-menu-overlay">
            <div class="mobile-menu-content" id="mobile-menu-content">
                <div class="mobile-menu-header">
                    <span class="mobile-menu-title">Menu</span>
                    <button class="mobile-menu-close" id="mobile-menu-close" aria-label="Fermer le menu">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <nav class="mobile-menu-nav" aria-label="Navigation mobile">
                    <ul class="mobile-nav-list">
                        <li><a href="#home" class="mobile-nav-link active">
                            <i class="fas fa-home nav-icon"></i>
                            <span data-translate="nav.home">Accueil</span>
                        </a></li>
                        <li><a href="#courses" class="mobile-nav-link">
                            <i class="fas fa-golf-ball nav-icon"></i>
                            <span data-translate="nav.courses">Parcours</span>
                        </a></li>
                        <li><a href="#destinations" class="mobile-nav-link">
                            <i class="fas fa-map-marker-alt nav-icon"></i>
                            <span data-translate="nav.destinations">Destinations</span>
                        </a></li>
                        <li><a href="#services" class="mobile-nav-link">
                            <i class="fas fa-concierge-bell nav-icon"></i>
                            <span data-translate="nav.services">Services</span>
                        </a></li>
                        <li><a href="#contact" class="mobile-nav-link">
                            <i class="fas fa-envelope nav-icon"></i>
                            <span data-translate="nav.contact">Contact</span>
                        </a></li>
                    </ul>
                </nav>
                
                <div class="mobile-menu-footer">
                    <div class="mobile-weather">
                        <span class="weather-icon">🌤️</span>
                        <span class="weather-temp">--°C</span>
                        <span>Bangkok</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <main id="main-content">
            <!-- Hero Section -->
            <section id="home" class="hero-section">
                <div class="carousel carousel-hero" data-carousel data-autoplay="true" data-interval="6000">
                    <div class="carousel-slide active">
                        <img src="./assets/images/hero-golf-thailand.jpg" alt="Golf en Thaïlande" loading="eager">
                        <div class="slide-content">
                            <h1 data-translate="hero.title">Découvrez le Golf en Thaïlande</h1>
                            <p data-translate="hero.subtitle">Des parcours exceptionnels dans un cadre paradisiaque</p>
                        </div>
                    </div>
                    <div class="carousel-slide">
                        <img src="./assets/images/BlackMountainGolfClubHuHin.jpeg" alt="Black Mountain Golf Club" loading="lazy">
                        <div class="slide-content">
                            <h1>Parcours Premium</h1>
                            <p>Jouez sur les plus beaux greens d'Asie du Sud-Est</p>
                        </div>
                    </div>
                    <div class="carousel-slide">
                        <img src="./assets/images/RedMountainGolfClubPhuket.jpeg" alt="Red Mountain Golf Club" loading="lazy">
                        <div class="slide-content">
                            <h1>Expérience Unique</h1>
                            <p>Séjours golf sur mesure avec nos experts locaux</p>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Courses Section -->
            <section id="courses" class="courses-section" data-animate="fade-in">
                <div class="container">
                    <div class="section-header" data-animate="slide-up">
                        <h2 data-translate="courses.title">Nos Parcours Premium</h2>
                        <p data-translate="courses.subtitle">Découvrez une sélection des plus beaux parcours de golf de Thaïlande</p>
                    </div>
                    
                    <div class="courses-grid" id="courses-grid">
                        <div class="course-card" data-animate="slide-up" data-animate-delay="100">
                            <div class="course-image-container">
                                <img src="./assets/images/BlackMountainGolfClubHuHin.jpeg" alt="Black Mountain Golf Club" class="course-image clickable-image" loading="lazy">
                                <div class="course-badge">Premium</div>
                            </div>
                            <div class="course-content">
                                <h3>Black Mountain Golf Club</h3>
                                <p class="course-location">Hua Hin</p>
                                <p class="course-description">Un parcours de championnat avec des vues spectaculaires sur les montagnes.</p>
                                <div class="course-features">
                                    <span class="feature">18 trous</span>
                                    <span class="feature">Par 72</span>
                                    <span class="feature">6,800m</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="course-card" data-animate="slide-up" data-animate-delay="200">
                            <div class="course-image-container">
                                <img src="./assets/images/RedMountainGolfClubPhuket.jpeg" alt="Red Mountain Golf Club" class="course-image clickable-image" loading="lazy">
                                <div class="course-badge">Populaire</div>
                            </div>
                            <div class="course-content">
                                <h3>Red Mountain Golf Club</h3>
                                <p class="course-location">Phuket</p>
                                <p class="course-description">Parcours unique taillé dans une ancienne mine d'étain.</p>
                                <div class="course-features">
                                    <span class="feature">18 trous</span>
                                    <span class="feature">Par 72</span>
                                    <span class="feature">6,400m</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="course-card" data-animate="slide-up" data-animate-delay="300">
                            <div class="course-image-container">
                                <img src="./assets/images/ChiangMaiHighlandsGolfSpaResortChiangMai.jpeg" alt="Chiang Mai Highlands" class="course-image clickable-image" loading="lazy">
                                <div class="course-badge">Nouveau</div>
                            </div>
                            <div class="course-content">
                                <h3>Chiang Mai Highlands</h3>
                                <p class="course-location">Chiang Mai</p>
                                <p class="course-description">Golf de montagne avec spa intégré dans un cadre naturel exceptionnel.</p>
                                <div class="course-features">
                                    <span class="feature">18 trous</span>
                                    <span class="feature">Par 71</span>
                                    <span class="feature">6,200m</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
        
        <!-- Footer -->
        <footer class="footer">
            <div class="container">
                <div class="footer-content">
                    <div class="footer-section">
                        <h3>GolfinThaï</h3>
                        <p>Votre expert golf en Thaïlande depuis 2020</p>
                    </div>
                    <div class="footer-section">
                        <h4>Contact</h4>
                        <p>Email: <EMAIL></p>
                        <p>Tél: +66 2 123 4567</p>
                    </div>
                </div>
                <div class="footer-bottom">
                    <p>&copy; <span id="current-year">2024</span> GolfinThaï. Tous droits réservés.</p>
                </div>
            </div>
        </footer>
    </div>
    
    <!-- FontAwesome -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/js/all.min.js" crossorigin="anonymous" defer></script>
    
    <!-- Main application script -->
    <script type="module" src="./assets/js/main.min.js"></script>
    
    <!-- Hide loading and show app when ready -->
    <script>
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('app').style.display = 'block';
                document.getElementById('current-year').textContent = new Date().getFullYear();
            }, 500);
        });
        
        // Register service worker
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('./sw.js')
                    .then(registration => console.log('SW registered'))
                    .catch(error => console.log('SW registration failed'));
            });
        }
    </script>
</body>
</html>
