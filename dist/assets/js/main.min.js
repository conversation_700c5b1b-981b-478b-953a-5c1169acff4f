class Logger{constructor(context='App'){this.context=context,this.isDevelopment=!1}formatMessage(level,message,...args){const timestamp=new Date().toISOString().split('T')[1].split('.')[0],prefix=`[${timestamp}] [${this.context}] [${level.toUpperCase()}]`;return[prefix,message,...args]}debug(message,...args){this.isDevelopment&&console.debug(...this.formatMessage('debug',message,...args))}info(message,...args){console.info(...this.formatMessage('info',message,...args))}success(message,...args){console.log(...this.formatMessage('success',message,...args))}warn(message,...args){console.warn(...this.formatMessage('warn',message,...args))}error(message,...args){console.error(...this.formatMessage('error',message,...args))}time(label){this.isDevelopment&&console.time(`[${this.context}] ${label}`)}timeEnd(label){this.isDevelopment&&console.timeEnd(`[${this.context}] ${label}`)}group(label,collapsed=!1){this.isDevelopment&&(collapsed?console.groupCollapsed(`[${this.context}] ${label}`):console.group(`[${this.context}] ${label}`))}groupEnd(){this.isDevelopment&&console.groupEnd()}table(data,columns){this.isDevelopment&&console.table(data,columns)}child(childContext){return new Logger(`${this.context}:${childContext}`)}}class EventEmitter{constructor(){this.events=new Map}on(event,callback){return this.events.has(event)||this.events.set(event,[]),this.events.get(event).push(callback),()=>this.off(event,callback)}once(event,callback){const onceCallback=(...args)=>{callback(...args),this.off(event,onceCallback)};return this.on(event,onceCallback)}off(event,callback){if(!this.events.has(event))return;const callbacks=this.events.get(event),index=callbacks.indexOf(callback);index>-1&&callbacks.splice(index,1),0===callbacks.length&&this.events.delete(event)}emit(event,...args){if(!this.events.has(event))return;this.events.get(event).slice().forEach(callback=>{try{callback(...args)}catch(error){console.error(`Error in event listener for "${event}":`,error)}})}removeAllListeners(event){event?this.events.delete(event):this.events.clear()}eventNames(){return Array.from(this.events.keys())}listenerCount(event){return this.events.has(event)?this.events.get(event).length:0}}class MobileMenuManager{constructor(app){this.app=app,this.logger=new Logger('MobileMenu'),this.isOpen=!1,this.isAnimating=!1,this.menuButton=null,this.closeButton=null,this.menuOverlay=null,this.menuContent=null,this.menuLinks=[],this.config={animationDuration:300,breakpoint:1024,closeOnLinkClick:!0,closeOnOutsideClick:!0},this.handleMenuToggle=this.handleMenuToggle.bind(this),this.handleCloseClick=this.handleCloseClick.bind(this),this.handleOverlayClick=this.handleOverlayClick.bind(this),this.handleLinkClick=this.handleLinkClick.bind(this),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleResize=this.handleResize.bind(this)}async init(){try{this.logger.debug('Initializing mobile menu...'),await this.findElements(),this.setupEventListeners(),this.setupAccessibility(),this.logger.success('Mobile menu initialized')}catch(error){throw this.logger.error('Failed to initialize mobile menu:',error),error}}async findElements(){await this.waitForElement('#mobile-menu-btn'),this.menuButton=document.getElementById('mobile-menu-btn'),this.closeButton=document.getElementById('mobile-menu-close'),this.menuOverlay=document.getElementById('mobile-menu-overlay'),this.menuContent=document.getElementById('mobile-menu-content'),this.menuButton&&this.menuOverlay||function(){throw new Error('Required mobile menu elements not found')}(),this.menuLinks=Array.from(this.menuOverlay.querySelectorAll('a[href^="#"]')),this.logger.debug('Mobile menu elements found:',{button:!!this.menuButton,overlay:!!this.menuOverlay,links:this.menuLinks.length})}waitForElement(selector,timeout=5e3){return new Promise((resolve,reject)=>{const element=document.querySelector(selector);if(element)return void resolve(element);const observer=new MutationObserver((mutations,obs)=>{const element=document.querySelector(selector);element&&(obs.disconnect(),resolve(element))});observer.observe(document.body,{childList:!0,subtree:!0}),setTimeout(()=>{observer.disconnect(),reject(new Error(`Element ${selector} not found within ${timeout}ms`))},timeout)})}setupEventListeners(){this.menuButton?.addEventListener('click',this.handleMenuToggle),this.closeButton?.addEventListener('click',this.handleCloseClick),this.config.closeOnOutsideClick&&this.menuOverlay?.addEventListener('click',this.handleOverlayClick),this.config.closeOnLinkClick&&this.menuLinks.forEach(link=>{link.addEventListener('click',this.handleLinkClick)}),document.addEventListener('keydown',this.handleKeyDown),window.addEventListener('resize',this.handleResize),this.app.on('app:resize',this.handleResize)}setupAccessibility(){this.menuButton&&(this.menuButton.setAttribute('aria-expanded','false'),this.menuButton.setAttribute('aria-controls','mobile-menu-overlay'),this.menuButton.setAttribute('aria-label','Ouvrir le menu de navigation')),this.menuOverlay&&(this.menuOverlay.setAttribute('role','dialog'),this.menuOverlay.setAttribute('aria-modal','true'),this.menuOverlay.setAttribute('aria-label','Menu de navigation'))}async handleMenuToggle(event){event.preventDefault(),this.isAnimating||(this.isOpen?await this.closeMenu():await this.openMenu())}async handleCloseClick(event){event.preventDefault(),await this.closeMenu()}async handleOverlayClick(event){event.target===this.menuOverlay&&await this.closeMenu()}async handleLinkClick(event){setTimeout(async()=>{await this.closeMenu()},150)}async handleKeyDown(event){if(this.isOpen)switch(event.key){case'Escape':event.preventDefault(),await this.closeMenu();break;case'Tab':this.handleTabNavigation(event)}}handleTabNavigation(event){if(!this.menuOverlay)return;const focusableElements=this.menuOverlay.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'),firstElement=focusableElements[0],lastElement=focusableElements[focusableElements.length-1];event.shiftKey?document.activeElement===firstElement&&(event.preventDefault(),lastElement.focus()):document.activeElement===lastElement&&(event.preventDefault(),firstElement.focus())}handleResize(){window.innerWidth>=this.config.breakpoint&&this.isOpen&&this.closeMenu()}async openMenu(){if(this.isOpen||this.isAnimating)return;this.isAnimating=!0,this.logger.debug('Opening mobile menu');try{this.isOpen=!0,this.menuButton?.setAttribute('aria-expanded','true'),this.menuOverlay&&(this.menuOverlay.classList.add('active'),document.body.style.overflow='hidden',setTimeout(()=>{this.menuOverlay.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])')?.focus()},100)),await this.wait(this.config.animationDuration),this.app.emit('mobileMenu:opened')}finally{this.isAnimating=!1}}async closeMenu(){if(!this.isOpen||this.isAnimating)return;this.isAnimating=!0,this.logger.debug('Closing mobile menu');try{this.isOpen=!1,this.menuButton?.setAttribute('aria-expanded','false'),this.menuOverlay&&(this.menuOverlay.classList.remove('active'),document.body.style.overflow='',this.menuButton?.focus()),await this.wait(this.config.animationDuration),this.app.emit('mobileMenu:closed')}finally{this.isAnimating=!1}}async setupMobileMenu(){this.logger.debug('Mobile menu setup completed')}wait(ms){return new Promise(resolve=>setTimeout(resolve,ms))}cleanup(){this.menuButton?.removeEventListener('click',this.handleMenuToggle),this.closeButton?.removeEventListener('click',this.handleCloseClick),this.menuOverlay?.removeEventListener('click',this.handleOverlayClick),this.menuLinks.forEach(link=>{link.removeEventListener('click',this.handleLinkClick)}),document.removeEventListener('keydown',this.handleKeyDown),window.removeEventListener('resize',this.handleResize),document.body.style.overflow='',this.logger.debug('Mobile menu cleaned up')}}class NavigationManager{constructor(app){this.app=app,this.logger=new Logger('Navigation'),this.activeSection='home',this.isScrolling=!1,this.navLinks=[],this.sections=[],this.config={offset:80,smoothScrollDuration:800},this.handleLinkClick=this.handleLinkClick.bind(this),this.handleScroll=this.handleScroll.bind(this),this.handleHashChange=this.handleHashChange.bind(this)}async init(){try{this.logger.debug('Initializing navigation...'),this.findElements(),this.setupEventListeners(),this.updateActiveSection(),this.logger.success('Navigation initialized')}catch(error){throw this.logger.error('Failed to initialize navigation:',error),error}}findElements(){this.navLinks=Array.from(document.querySelectorAll('a[href^="#"]')),this.sections=Array.from(document.querySelectorAll('section[id]')),this.logger.debug(`Found ${this.navLinks.length} nav links and ${this.sections.length} sections`)}setupEventListeners(){this.navLinks.forEach(link=>{link.addEventListener('click',this.handleLinkClick)});let scrollTimeout;window.addEventListener('scroll',()=>{scrollTimeout&&clearTimeout(scrollTimeout),scrollTimeout=setTimeout(this.handleScroll,10)}),window.addEventListener('hashchange',this.handleHashChange)}handleLinkClick(event){const link=event.currentTarget,href=link.getAttribute('href');href&&href.startsWith('#')&&(event.preventDefault(),this.scrollToSection(href.substring(1)))}scrollToSection(sectionId){const targetSection=document.getElementById(sectionId);if(!targetSection)return void this.logger.warning(`Section not found: ${sectionId}`);this.isScrolling=!0;const targetPosition=targetSection.offsetTop-this.config.offset;window.scrollTo({top:targetPosition,behavior:'smooth'}),history.pushState(null,null,`#${sectionId}`),this.setActiveSection(sectionId),setTimeout(()=>{this.isScrolling=!1},this.config.smoothScrollDuration),this.logger.debug(`Scrolled to section: ${sectionId}`)}handleScroll(){if(this.isScrolling)return;const scrollPosition=window.pageYOffset+this.config.offset+50;let currentSection='home';for(const section of this.sections){const sectionTop=section.offsetTop;if(scrollPosition>=sectionTop&&scrollPosition<sectionTop+section.offsetHeight){currentSection=section.id;break}}currentSection!==this.activeSection&&this.setActiveSection(currentSection)}handleHashChange(){const hash=window.location.hash.substring(1);hash&&hash!==this.activeSection&&this.scrollToSection(hash)}setActiveSection(sectionId){this.activeSection=sectionId,this.navLinks.forEach(link=>{const href=link.getAttribute('href'),linkSectionId=href?href.substring(1):'';linkSectionId===sectionId?link.classList.add('active'):link.classList.remove('active')}),this.app.emit('navigation:section-changed',{section:sectionId,previousSection:this.activeSection}),this.logger.debug(`Active section changed to: ${sectionId}`)}updateActiveSection(){const hash=window.location.hash.substring(1),sectionId=hash||'home';this.setActiveSection(sectionId)}async setupNavigation(){this.findElements(),this.updateActiveSection(),this.logger.debug('Navigation setup completed')}cleanup(){this.navLinks.forEach(link=>{link.removeEventListener('click',this.handleLinkClick)}),window.removeEventListener('scroll',this.handleScroll),window.removeEventListener('hashchange',this.handleHashChange),this.logger.debug('Navigation cleaned up')}}class ModalManager{constructor(app){this.app=app,this.logger=new Logger('Modal'),this.activeModal=null,this.isAnimating=!1,this.scrollPosition=0,this.config={animationDuration:300,closeOnOverlayClick:!0,closeOnEscape:!0,preventBodyScroll:!0,focusTrap:!0},this.handleOverlayClick=this.handleOverlayClick.bind(this),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleImageClick=this.handleImageClick.bind(this)}async init(){try{this.logger.debug('Initializing modal system...'),this.setupEventListeners(),this.setupImageModals(),this.logger.success('Modal system initialized')}catch(error){throw this.logger.error('Failed to initialize modal system:',error),error}}setupEventListeners(){document.addEventListener('keydown',this.handleKeyDown),document.addEventListener('click',this.handleImageClick)}setupImageModals(){const clickableImages=document.querySelectorAll('[data-modal-image], .clickable-image, .course-image');clickableImages.forEach(image=>{image.hasAttribute('data-modal-setup')||(this.setupImageModal(image),image.setAttribute('data-modal-setup','true'))}),this.logger.debug(`Setup ${clickableImages.length} image modals`)}setupImageModal(image){image.setAttribute('tabindex','0'),image.setAttribute('role','button'),image.setAttribute('aria-label','Cliquer pour agrandir l\'image'),image.style.cursor='pointer';const handleActivation=event=>{'keydown'===event.type&&!['Enter',' '].includes(event.key)||(event.preventDefault(),this.openImageModal(image))};image.addEventListener('click',handleActivation),image.addEventListener('keydown',handleActivation)}handleImageClick(event){const image=event.target.closest('img');if(!image)return;const shouldOpenModal=image.hasAttribute('data-modal-image')||image.classList.contains('clickable-image')||image.classList.contains('course-image')||image.closest('.course-card')||image.closest('.gallery-item');shouldOpenModal&&!image.hasAttribute('data-modal-setup')&&(event.preventDefault(),this.openImageModal(image))}async openImageModal(image){if(this.isAnimating)return;this.logger.debug('Opening image modal');try{this.isAnimating=!0;const src=image.src||image.dataset.src,alt=image.alt||'Image',title=image.title||image.dataset.title||alt;if(!src)throw new Error('Image source not found');const modalHTML=this.createImageModalHTML(src,alt,title),modal=this.createModal(modalHTML,'image-modal');await this.showModal(modal)}catch(error){this.logger.error('Failed to open image modal:',error)}finally{this.isAnimating=!1}}createImageModalHTML(src,alt,title){return`\n      <div class="modal-content image-modal-content">\n        <button class="modal-close" aria-label="Fermer">\n          <i class="fas fa-times"></i>\n        </button>\n        <div class="image-container">\n          <img src="${src}" alt="${alt}" class="modal-image">\n          ${title?`<div class="image-title">${title}</div>`:''}\n        </div>\n      </div>\n    `}createModal(content,className=''){const modal=document.createElement('div');modal.className=`modal-overlay ${className}`,modal.innerHTML=content;const closeButton=modal.querySelector('.modal-close');return closeButton&&closeButton.addEventListener('click',()=>this.closeModal(modal)),this.config.closeOnOverlayClick&&modal.addEventListener('click',event=>{event.target===modal&&this.closeModal(modal)}),modal}async showModal(modal){this.activeModal&&await this.closeModal(this.activeModal),this.activeModal=modal,this.config.preventBodyScroll&&(this.scrollPosition=window.pageYOffset,document.body.style.position='fixed',document.body.style.top=`-${this.scrollPosition}px`,document.body.style.width='100%'),document.body.appendChild(modal),modal.offsetHeight,modal.classList.add('active'),this.config.focusTrap&&this.setupFocusTrap(modal),await this.wait(this.config.animationDuration),this.app.emit('modal:opened',{modal})}async closeModal(modal){if(!modal||this.isAnimating)return;this.isAnimating=!0;try{modal.classList.remove('active'),await this.wait(this.config.animationDuration),modal.parentNode&&modal.parentNode.removeChild(modal),this.config.preventBodyScroll&&(document.body.style.position='',document.body.style.top='',document.body.style.width='',window.scrollTo(0,this.scrollPosition)),this.activeModal===modal&&(this.activeModal=null),this.app.emit('modal:closed',{modal})}finally{this.isAnimating=!1}}setupFocusTrap(modal){const focusableElements=modal.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');if(0===focusableElements.length)return;const firstElement=focusableElements[0],lastElement=focusableElements[focusableElements.length-1];firstElement.focus();const handleTabKey=event=>{'Tab'===event.key&&(event.shiftKey?document.activeElement===firstElement&&(event.preventDefault(),lastElement.focus()):document.activeElement===lastElement&&(event.preventDefault(),firstElement.focus()))};modal.addEventListener('keydown',handleTabKey)}handleOverlayClick(event){event.target.classList.contains('modal-overlay')&&this.closeModal(event.target)}handleKeyDown(event){this.activeModal&&'Escape'===event.key&&this.config.closeOnEscape&&(event.preventDefault(),this.closeModal(this.activeModal))}async setupModals(){this.setupImageModals(),this.logger.debug('Modal setup completed')}wait(ms){return new Promise(resolve=>setTimeout(resolve,ms))}cleanup(){this.activeModal&&this.closeModal(this.activeModal),document.removeEventListener('keydown',this.handleKeyDown),document.removeEventListener('click',this.handleImageClick),document.body.style.position='',document.body.style.top='',document.body.style.width='',this.logger.debug('Modal system cleaned up')}}class CarouselManager{constructor(app){this.app=app,this.logger=new Logger('Carousel'),this.carousels=new Map,this.config={autoPlay:!0,autoPlayInterval:5e3,pauseOnHover:!0,pauseOnFocus:!0,enableTouch:!0,enableKeyboard:!0,loop:!0,animationDuration:500}}async init(){try{this.logger.debug('Initializing carousel system...'),await this.setupCarousels(),this.logger.success('Carousel system initialized')}catch(error){throw this.logger.error('Failed to initialize carousel system:',error),error}}async setupCarousels(){const carouselElements=document.querySelectorAll('[data-carousel], .carousel');for(const element of carouselElements)try{await this.initializeCarousel(element)}catch(error){this.logger.error('Failed to initialize carousel:',error)}this.logger.debug(`Initialized ${carouselElements.length} carousels`)}async initializeCarousel(element){const id=element.id||`carousel-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;element.id=id;const carousel=new Carousel(element,this.config,this.app);await carousel.init(),this.carousels.set(id,carousel),this.logger.debug(`Carousel ${id} initialized`)}getCarousel(id){return this.carousels.get(id)}cleanup(){for(const[id,carousel]of this.carousels)carousel.cleanup();this.carousels.clear(),this.logger.debug('Carousel system cleaned up')}}class Carousel{constructor(element,config,app){this.element=element,this.config={...config,...this.parseDataAttributes()},this.app=app,this.logger=new Logger(`Carousel:${element.id}`),this.currentIndex=0,this.isAnimating=!1,this.autoPlayTimer=null,this.isPaused=!1,this.slides=[],this.indicators=[],this.prevButton=null,this.nextButton=null,this.indicatorContainer=null,this.touchStartX=0,this.touchEndX=0,this.touchThreshold=50,this.handlePrevClick=this.handlePrevClick.bind(this),this.handleNextClick=this.handleNextClick.bind(this),this.handleIndicatorClick=this.handleIndicatorClick.bind(this),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleTouchStart=this.handleTouchStart.bind(this),this.handleTouchEnd=this.handleTouchEnd.bind(this),this.handleMouseEnter=this.handleMouseEnter.bind(this),this.handleMouseLeave=this.handleMouseLeave.bind(this),this.handleFocus=this.handleFocus.bind(this),this.handleBlur=this.handleBlur.bind(this)}parseDataAttributes(){const data=this.element.dataset;return{autoPlay:'false'!==data.autoplay,autoPlayInterval:parseInt(data.interval)||5e3,loop:'false'!==data.loop,pauseOnHover:'false'!==data.pauseOnHover,enableTouch:'false'!==data.touch,enableKeyboard:'false'!==data.keyboard}}async init(){try{this.setupStructure(),this.setupEventListeners(),this.setupAccessibility(),this.config.autoPlay&&this.startAutoPlay(),this.logger.debug('Carousel initialized')}catch(error){throw this.logger.error('Failed to initialize carousel:',error),error}}setupStructure(){let slidesContainer=this.element.querySelector('.carousel-slides');if(slidesContainer||(slidesContainer=document.createElement('div'),slidesContainer.className='carousel-slides',this.element.appendChild(slidesContainer)),this.slides=Array.from(slidesContainer.children),0===this.slides.length)throw new Error('No slides found in carousel');this.slides.forEach((slide,index)=>{slide.classList.add('carousel-slide'),slide.setAttribute('data-slide-index',index),0===index&&slide.classList.add('active')}),this.createNavigationButtons(),this.createIndicators(),this.updateCarousel(!1)}createNavigationButtons(){this.prevButton=document.createElement('button'),this.prevButton.className='carousel-btn carousel-prev',this.prevButton.innerHTML='<i class="fas fa-chevron-left"></i>',this.prevButton.setAttribute('aria-label','Image précédente'),this.nextButton=document.createElement('button'),this.nextButton.className='carousel-btn carousel-next',this.nextButton.innerHTML='<i class="fas fa-chevron-right"></i>',this.nextButton.setAttribute('aria-label','Image suivante'),this.element.appendChild(this.prevButton),this.element.appendChild(this.nextButton)}createIndicators(){this.slides.length<=1||(this.indicatorContainer=document.createElement('div'),this.indicatorContainer.className='carousel-indicators',this.slides.forEach((e,index)=>{const indicator=document.createElement('button');indicator.className='carousel-indicator',indicator.setAttribute('data-slide-to',index),indicator.setAttribute('aria-label',`Aller à l'image ${index+1}`),0===index&&indicator.classList.add('active'),this.indicators.push(indicator),this.indicatorContainer.appendChild(indicator)}),this.element.appendChild(this.indicatorContainer))}setupEventListeners(){this.prevButton?.addEventListener('click',this.handlePrevClick),this.nextButton?.addEventListener('click',this.handleNextClick),this.indicators.forEach(indicator=>{indicator.addEventListener('click',this.handleIndicatorClick)}),this.config.enableKeyboard&&this.element.addEventListener('keydown',this.handleKeyDown),this.config.enableTouch&&(this.element.addEventListener('touchstart',this.handleTouchStart,{passive:!0}),this.element.addEventListener('touchend',this.handleTouchEnd,{passive:!0})),this.config.pauseOnHover&&(this.element.addEventListener('mouseenter',this.handleMouseEnter),this.element.addEventListener('mouseleave',this.handleMouseLeave)),this.config.pauseOnFocus&&(this.element.addEventListener('focusin',this.handleFocus),this.element.addEventListener('focusout',this.handleBlur))}setupAccessibility(){this.element.setAttribute('role','region'),this.element.setAttribute('aria-label','Carrousel d\'images'),this.element.setAttribute('tabindex','0');const liveRegion=document.createElement('div');liveRegion.className='carousel-live-region sr-only',liveRegion.setAttribute('aria-live','polite'),liveRegion.setAttribute('aria-atomic','true'),this.element.appendChild(liveRegion),this.liveRegion=liveRegion}handlePrevClick(event){event.preventDefault(),this.prev()}handleNextClick(event){event.preventDefault(),this.next()}handleIndicatorClick(event){event.preventDefault();const index=parseInt(event.target.getAttribute('data-slide-to'));this.goTo(index)}handleKeyDown(event){switch(event.key){case'ArrowLeft':event.preventDefault(),this.prev();break;case'ArrowRight':event.preventDefault(),this.next();break;case'Home':event.preventDefault(),this.goTo(0);break;case'End':event.preventDefault(),this.goTo(this.slides.length-1)}}handleTouchStart(event){this.touchStartX=event.touches[0].clientX}handleTouchEnd(event){this.touchEndX=event.changedTouches[0].clientX,this.handleSwipe()}handleSwipe(){const diff=this.touchStartX-this.touchEndX;Math.abs(diff)>this.touchThreshold&&(diff>0?this.next():this.prev())}handleMouseEnter(){this.pause()}handleMouseLeave(){this.resume()}handleFocus(){this.pause()}handleBlur(){this.resume()}prev(){const newIndex=0===this.currentIndex?this.config.loop?this.slides.length-1:0:this.currentIndex-1;this.goTo(newIndex)}next(){const newIndex=this.currentIndex===this.slides.length-1?this.config.loop?0:this.slides.length-1:this.currentIndex+1;this.goTo(newIndex)}async goTo(index){if(index===this.currentIndex||this.isAnimating)return;this.isAnimating=!0;try{const previousIndex=this.currentIndex;this.currentIndex=index,await this.updateCarousel(!0),this.liveRegion&&(this.liveRegion.textContent=`Image ${index+1} sur ${this.slides.length}`),this.app.emit('carousel:slide-changed',{carousel:this,currentIndex:index,previousIndex})}finally{this.isAnimating=!1}}async updateCarousel(animate=!0){this.slides.forEach((slide,index)=>{slide.classList.toggle('active',index===this.currentIndex)}),this.indicators.forEach((indicator,index)=>{indicator.classList.toggle('active',index===this.currentIndex)}),this.config.loop||(this.prevButton?.classList.toggle('disabled',0===this.currentIndex),this.nextButton?.classList.toggle('disabled',this.currentIndex===this.slides.length-1)),animate&&await this.wait(this.config.animationDuration)}startAutoPlay(){this.config.autoPlay&&this.slides.length>1&&(this.autoPlayTimer=setInterval(()=>{this.isPaused||this.next()},this.config.autoPlayInterval))}stopAutoPlay(){this.autoPlayTimer&&(clearInterval(this.autoPlayTimer),this.autoPlayTimer=null)}pause(){this.isPaused=!0}resume(){this.isPaused=!1}wait(ms){return new Promise(resolve=>setTimeout(resolve,ms))}cleanup(){this.stopAutoPlay(),this.prevButton?.removeEventListener('click',this.handlePrevClick),this.nextButton?.removeEventListener('click',this.handleNextClick),this.indicators.forEach(indicator=>{indicator.removeEventListener('click',this.handleIndicatorClick)}),this.element.removeEventListener('keydown',this.handleKeyDown),this.element.removeEventListener('touchstart',this.handleTouchStart),this.element.removeEventListener('touchend',this.handleTouchEnd),this.element.removeEventListener('mouseenter',this.handleMouseEnter),this.element.removeEventListener('mouseleave',this.handleMouseLeave),this.element.removeEventListener('focusin',this.handleFocus),this.element.removeEventListener('focusout',this.handleBlur),this.logger.debug('Carousel cleaned up')}}class LanguageManager{constructor(app){this.app=app,this.logger=new Logger('Language'),this.currentLanguage='fr',this.isLoading=!1,this.languageButton=null,this.languageDropdown=null,this.languageOptions=[],this.translations={fr:{'nav.home':'Accueil','nav.courses':'Parcours','nav.destinations':'Destinations','nav.services':'Services','nav.contact':'Contact','hero.title':'Découvrez le Golf en Thaïlande','hero.subtitle':'Des parcours exceptionnels dans un cadre paradisiaque','courses.title':'Nos Parcours Premium','courses.subtitle':'Découvrez une sélection des plus beaux parcours de golf de Thaïlande'},en:{'nav.home':'Home','nav.courses':'Courses','nav.destinations':'Destinations','nav.services':'Services','nav.contact':'Contact','hero.title':'Discover Golf in Thailand','hero.subtitle':'Exceptional courses in a paradise setting','courses.title':'Our Premium Courses','courses.subtitle':'Discover a selection of the most beautiful golf courses in Thailand'},th:{'nav.home':'หน้าแรก','nav.courses':'สนามกอล์ฟ','nav.destinations':'จุดหมายปลายทาง','nav.services':'บริการ','nav.contact':'ติดต่อ','hero.title':'ค้นพบกอล์ฟในประเทศไทย','hero.subtitle':'สนามกอล์ฟที่ยอดเยี่ยมในสวรรค์','courses.title':'สนามกอล์ฟพรีเมียมของเรา','courses.subtitle':'ค้นพบสนามกอล์ฟที่สวยที่สุดในประเทศไทย'}},this.handleLanguageButtonClick=this.handleLanguageButtonClick.bind(this),this.handleLanguageOptionClick=this.handleLanguageOptionClick.bind(this),this.handleOutsideClick=this.handleOutsideClick.bind(this)}async init(){try{this.logger.debug('Initializing language manager...'),this.findElements(),this.setupEventListeners(),this.loadSavedLanguage(),this.logger.success('Language manager initialized')}catch(error){throw this.logger.error('Failed to initialize language manager:',error),error}}findElements(){this.languageButton=document.querySelector('.language-button'),this.languageDropdown=document.querySelector('.language-dropdown'),this.languageOptions=Array.from(document.querySelectorAll('.language-option')),this.logger.debug(`Found language elements: button=${!!this.languageButton}, dropdown=${!!this.languageDropdown}, options=${this.languageOptions.length}`)}setupEventListeners(){this.languageButton?.addEventListener('click',this.handleLanguageButtonClick),this.languageOptions.forEach(option=>{option.addEventListener('click',this.handleLanguageOptionClick)}),document.addEventListener('click',this.handleOutsideClick)}handleLanguageButtonClick(event){event.preventDefault(),event.stopPropagation();const isOpen=this.languageDropdown?.classList.contains('active');isOpen?this.closeDropdown():this.openDropdown()}handleLanguageOptionClick(event){event.preventDefault(),event.stopPropagation();const option=event.currentTarget,language=option.dataset.lang;language&&language!==this.currentLanguage&&this.changeLanguage(language),this.closeDropdown()}handleOutsideClick(event){event.target.closest('.language-switcher')||!this.languageDropdown?.classList.contains('active')||this.closeDropdown()}openDropdown(){this.languageDropdown?.classList.add('active'),this.languageButton?.setAttribute('aria-expanded','true'),this.languageOptions[0]?.focus()}closeDropdown(){this.languageDropdown?.classList.remove('active'),this.languageButton?.setAttribute('aria-expanded','false')}async changeLanguage(language){if(this.isLoading||language===this.currentLanguage)return;this.isLoading=!0,this.logger.debug(`Changing language to: ${language}`);try{this.currentLanguage=language,localStorage.setItem('golfinthai-language',language),this.updateLanguageButton(language),this.updateLanguageOptions(language),this.translateContent(language),document.documentElement.lang=language,this.app.emit('language:changed',{language}),this.logger.success(`Language changed to: ${language}`)}catch(error){this.logger.error(`Failed to change language: ${error.message}`)}finally{this.isLoading=!1}}updateLanguageButton(language){if(!this.languageButton)return;const flagImg=this.languageButton.querySelector('.flag'),languageCode=this.languageButton.querySelector('.language-code');flagImg&&(flagImg.src=`./assets/images/flags/${language}.svg`,flagImg.alt=this.getLanguageName(language)),languageCode&&(languageCode.textContent=language.toUpperCase())}updateLanguageOptions(language){this.languageOptions.forEach(option=>{const optionLang=option.dataset.lang;optionLang===language?option.classList.add('active'):option.classList.remove('active')})}translateContent(language){const translations=this.translations[language];if(!translations)return;const translatableElements=document.querySelectorAll('[data-translate]');translatableElements.forEach(element=>{const key=element.dataset.translate,translation=translations[key];translation&&('INPUT'===element.tagName&&'submit'===element.type?element.value=translation:element.hasAttribute('placeholder')?element.placeholder=translation:element.textContent=translation)}),this.logger.debug(`Translated ${translatableElements.length} elements`)}getLanguageName(language){const names={fr:'Français',en:'English',th:'ไทย'};return names[language]||language}loadSavedLanguage(){const savedLanguage=localStorage.getItem('golfinthai-language');if(savedLanguage&&this.translations[savedLanguage])this.changeLanguage(savedLanguage);else{const browserLanguage=navigator.language.split('-')[0],supportedLanguage=this.translations[browserLanguage]?browserLanguage:'fr';this.changeLanguage(supportedLanguage)}}addTranslation(language,key,value){this.translations[language]||(this.translations[language]={}),this.translations[language][key]=value}getTranslation(key,language=this.currentLanguage){return this.translations[language]?.[key]||key}cleanup(){this.languageButton?.removeEventListener('click',this.handleLanguageButtonClick),this.languageOptions.forEach(option=>{option.removeEventListener('click',this.handleLanguageOptionClick)}),document.removeEventListener('click',this.handleOutsideClick),this.logger.debug('Language manager cleaned up')}}class WeatherManager{constructor(app){this.app=app,this.logger=new Logger('Weather'),this.weatherData=null,this.isLoading=!1,this.lastUpdate=null,this.weatherWidgets=[],this.config={apiKey:'demo',city:'Bangkok',country:'TH',updateInterval:18e5,timeout:1e4},this.mockWeatherData={temperature:32,condition:'sunny',humidity:65,windSpeed:8,icon:'☀️',description:'Ensoleillé'}}async init(){try{this.logger.debug('Initializing weather manager...'),this.findWeatherWidgets(),await this.loadWeatherData(),this.startAutoUpdate(),this.logger.success('Weather manager initialized')}catch(error){this.logger.error('Failed to initialize weather manager:',error),this.showFallbackWeather()}}findWeatherWidgets(){this.weatherWidgets=Array.from(document.querySelectorAll('.header-weather, .mobile-weather, [data-weather]')),this.logger.debug(`Found ${this.weatherWidgets.length} weather widgets`)}async loadWeatherData(){if(this.isLoading)return;this.isLoading=!0,this.logger.debug('Loading weather data...');try{const weatherData=await this.fetchWeatherData();this.weatherData=weatherData,this.lastUpdate=Date.now(),this.updateWeatherWidgets(),this.logger.success('Weather data loaded successfully')}catch(error){this.logger.error('Failed to load weather data:',error),this.showFallbackWeather()}finally{this.isLoading=!1}}async fetchWeatherData(){return new Promise(resolve=>{setTimeout(()=>{const baseTemp=32,variation=Math.floor(6*Math.random())-3;resolve({...this.mockWeatherData,temperature:baseTemp+variation,timestamp:Date.now()})},500)})}updateWeatherWidgets(){this.weatherData&&this.weatherWidgets.forEach(widget=>{this.updateWeatherWidget(widget)})}updateWeatherWidget(widget){const tempElement=widget.querySelector('.weather-temp, [data-weather="temp"]'),iconElement=widget.querySelector('.weather-icon, [data-weather="icon"]'),locationElement=widget.querySelector('.weather-location, [data-weather="location"]'),descriptionElement=widget.querySelector('.weather-description, [data-weather="description"]');tempElement&&(tempElement.textContent=`${this.weatherData.temperature}°C`),iconElement&&(iconElement.textContent=this.weatherData.icon,iconElement.title=this.weatherData.description),locationElement&&(locationElement.textContent=this.config.city),descriptionElement&&(descriptionElement.textContent=this.weatherData.description),widget.classList.remove('loading'),widget.classList.add('loaded')}showFallbackWeather(){this.weatherData={temperature:'--',icon:'🌤️',description:'Météo indisponible'},this.updateWeatherWidgets()}getWeatherIcon(condition){const icons={clear:'☀️',sunny:'☀️',clouds:'☁️',cloudy:'☁️',rain:'🌧️',drizzle:'🌦️',thunderstorm:'⛈️',snow:'❄️',mist:'🌫️',fog:'🌫️'};return icons[condition.toLowerCase()]||'🌤️'}startAutoUpdate(){this.updateTimer=setInterval(()=>{this.loadWeatherData()},this.config.updateInterval),this.logger.debug('Weather auto-update started')}stopAutoUpdate(){this.updateTimer&&(clearInterval(this.updateTimer),this.updateTimer=null,this.logger.debug('Weather auto-update stopped'))}async refresh(){await this.loadWeatherData()}getCurrentWeather(){return this.weatherData}isDataStale(){if(!this.lastUpdate)return!0;const now=Date.now(),staleThreshold=2*this.config.updateInterval;return now-this.lastUpdate>staleThreshold}async setupWeather(){this.findWeatherWidgets(),this.isDataStale()?await this.loadWeatherData():this.updateWeatherWidgets(),this.logger.debug('Weather setup completed')}cleanup(){this.stopAutoUpdate(),this.logger.debug('Weather manager cleaned up')}}class FormManager{constructor(app){this.app=app,this.logger=new Logger('Form'),this.forms=new Map,this.config={validateOnInput:!0,validateOnBlur:!0,showSuccessMessage:!0,autoHideMessages:5e3}}async init(){try{this.logger.debug('Initializing form manager...'),this.findForms(),this.setupForms(),this.logger.success('Form manager initialized')}catch(error){throw this.logger.error('Failed to initialize form manager:',error),error}}findForms(){const formElements=document.querySelectorAll('form[data-form], .contact-form, .booking-form');formElements.forEach(formElement=>{const formId=formElement.id||`form-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;formElement.id=formId;const form=new Form(formElement,this.config,this.app);this.forms.set(formId,form)}),this.logger.debug(`Found ${this.forms.size} forms`)}setupForms(){for(const[id,form]of this.forms)try{form.init()}catch(error){this.logger.error(`Failed to setup form ${id}:`,error)}}getForm(id){return this.forms.get(id)}async setupForms(){this.findForms(),this.logger.debug('Form setup completed')}cleanup(){for(const[id,form]of this.forms)form.cleanup();this.forms.clear(),this.logger.debug('Form manager cleaned up')}}class Form{constructor(element,config,app){this.element=element,this.config=config,this.app=app,this.logger=new Logger(`Form:${element.id}`),this.isSubmitting=!1,this.isValid=!1,this.fields=[],this.submitButton=null,this.messageContainer=null,this.validationRules={required:value=>''!==value.trim(),email:value=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),phone:value=>/^[\+]?[1-9][\d]{0,15}$/.test(value.replace(/[\s\-\(\)]/g,'')),minLength:(value,min)=>value.length>=min,maxLength:(value,max)=>value.length<=max},this.handleSubmit=this.handleSubmit.bind(this),this.handleFieldInput=this.handleFieldInput.bind(this),this.handleFieldBlur=this.handleFieldBlur.bind(this)}init(){this.findElements(),this.setupEventListeners(),this.setupValidation(),this.logger.debug('Form initialized')}findElements(){this.fields=Array.from(this.element.querySelectorAll('input, textarea, select')),this.submitButton=this.element.querySelector('button[type="submit"], input[type="submit"]'),this.messageContainer=this.element.querySelector('.form-message'),this.messageContainer||(this.messageContainer=document.createElement('div'),this.messageContainer.className='form-message',this.element.appendChild(this.messageContainer)),this.logger.debug(`Found ${this.fields.length} fields`)}setupEventListeners(){this.element.addEventListener('submit',this.handleSubmit),this.fields.forEach(field=>{this.config.validateOnInput&&field.addEventListener('input',this.handleFieldInput),this.config.validateOnBlur&&field.addEventListener('blur',this.handleFieldBlur)})}setupValidation(){this.fields.forEach(field=>{'email'===field.type&&!field.hasAttribute('pattern')&&field.setAttribute('pattern','[^@\\s]+@[^@\\s]+\\.[^@\\s]+'),field.hasAttribute('required')&&field.setAttribute('aria-required','true')})}async handleSubmit(event){if(event.preventDefault(),this.isSubmitting)return;this.logger.debug('Form submission started');const isValid=this.validateForm();if(!isValid)return void this.showMessage('Veuillez corriger les erreurs dans le formulaire.','error');this.isSubmitting=!0,this.updateSubmitButton(!0);try{const formData=this.getFormData();await this.submitForm(formData),this.config.showSuccessMessage&&this.showMessage('Votre message a été envoyé avec succès !','success'),this.resetForm(),this.app.emit('form:submitted',{form:this,data:formData}),this.logger.success('Form submitted successfully')}catch(error){this.logger.error('Form submission failed:',error),this.showMessage('Une erreur est survenue. Veuillez réessayer.','error')}finally{this.isSubmitting=!1,this.updateSubmitButton(!1)}}handleFieldInput(event){const field=event.target;this.validateField(field)}handleFieldBlur(event){const field=event.target;this.validateField(field)}validateForm(){let isValid=!0;return this.fields.forEach(field=>{this.validateField(field)||(isValid=!1)}),this.isValid=isValid,isValid}validateField(field){const value=field.value.trim(),rules=this.getFieldRules(field);let isValid=!0,errorMessage='';for(const rule of rules){const result=this.applyValidationRule(value,rule);if(!result.isValid){isValid=!1,errorMessage=result.message;break}}return this.updateFieldUI(field,isValid,errorMessage),isValid}getFieldRules(field){const rules=[];field.hasAttribute('required')&&rules.push({type:'required',message:'Ce champ est requis.'}),'email'===field.type&&rules.push({type:'email',message:'Veuillez entrer une adresse email valide.'}),'tel'===field.type&&rules.push({type:'phone',message:'Veuillez entrer un numéro de téléphone valide.'});const minLength=field.getAttribute('minlength');minLength&&rules.push({type:'minLength',value:parseInt(minLength),message:`Ce champ doit contenir au moins ${minLength} caractères.`});const maxLength=field.getAttribute('maxlength');return maxLength&&rules.push({type:'maxLength',value:parseInt(maxLength),message:`Ce champ ne peut pas dépasser ${maxLength} caractères.`}),rules}applyValidationRule(value,rule){const validator=this.validationRules[rule.type];if(!validator)return{isValid:!0};const isValid=void 0!==rule.value?validator(value,rule.value):validator(value);return{isValid,message:isValid?'':rule.message}}updateFieldUI(field,isValid,errorMessage){field.classList.remove('error','valid');const existingError=field.parentNode.querySelector('.field-error');existingError&&existingError.remove(),''!==field.value.trim()&&field.classList.add(isValid?'valid':'error'),!isValid&&errorMessage&&(function(){const errorElement=document.createElement('div');errorElement.className='field-error',errorElement.textContent=errorMessage,field.parentNode.appendChild(errorElement),field.setAttribute('aria-invalid','true'),field.setAttribute('aria-describedby',`${field.id}-error`),errorElement.id=`${field.id}-error`}(),field.removeAttribute('aria-invalid'),field.removeAttribute('aria-describedby'))}getFormData(){const formData=new FormData(this.element),data={};for(const[key,value]of formData.entries())data[key]=value;return data}async submitForm(data){return new Promise(resolve=>{setTimeout(()=>{console.log('Form data submitted:',data),resolve()},1e3)})}showMessage(message,type='info'){this.messageContainer.className=`form-message ${type}`,this.messageContainer.textContent=message,this.messageContainer.style.display='block',this.config.autoHideMessages&&setTimeout(()=>{this.hideMessage()},this.config.autoHideMessages)}hideMessage(){this.messageContainer.style.display='none'}updateSubmitButton(isSubmitting){this.submitButton&&(isSubmitting?(this.submitButton.disabled=!0,this.submitButton.textContent='Envoi en cours...',this.submitButton.classList.add('loading')):(this.submitButton.disabled=!1,this.submitButton.textContent=this.submitButton.dataset.originalText||'Envoyer',this.submitButton.classList.remove('loading')))}resetForm(){this.element.reset(),this.fields.forEach(field=>{field.classList.remove('error','valid'),field.removeAttribute('aria-invalid'),field.removeAttribute('aria-describedby');const errorElement=field.parentNode.querySelector('.field-error');errorElement&&errorElement.remove()}),this.hideMessage()}cleanup(){this.element.removeEventListener('submit',this.handleSubmit),this.fields.forEach(field=>{field.removeEventListener('input',this.handleFieldInput),field.removeEventListener('blur',this.handleFieldBlur)}),this.logger.debug('Form cleaned up')}}class AnimationManager{constructor(app){this.app=app,this.logger=new Logger('Animation'),this.observers=new Map,this.animatedElements=new Set,this.config={threshold:.1,rootMargin:'0px 0px -50px 0px',animationDelay:100,staggerDelay:150},this.animationClasses={'fade-in':'animate-fade-in','slide-up':'animate-slide-up','slide-down':'animate-slide-down','slide-left':'animate-slide-left','slide-right':'animate-slide-right','scale-in':'animate-scale-in','rotate-in':'animate-rotate-in'}}async init(){try{this.logger.debug('Initializing animation manager...'),this.setupIntersectionObserver(),this.findAnimatableElements(),this.setupScrollAnimations(),this.logger.success('Animation manager initialized')}catch(error){throw this.logger.error('Failed to initialize animation manager:',error),error}}setupIntersectionObserver(){if(!('IntersectionObserver'in window))return void this.logger.warning('IntersectionObserver not supported, animations disabled');const observer=new IntersectionObserver(entries=>{entries.forEach(entry=>{entry.isIntersecting&&this.animateElement(entry.target)})},{threshold:this.config.threshold,rootMargin:this.config.rootMargin});this.observers.set('main',observer)}findAnimatableElements(){const selectors=['[data-animate]','.animate-on-scroll','.fade-in-on-scroll','.slide-up-on-scroll','.course-card','.section-header','.testimonial-card'],elements=document.querySelectorAll(selectors.join(', '));elements.forEach(element=>{this.prepareElement(element)}),this.logger.debug(`Found ${elements.length} animatable elements`)}prepareElement(element){const animationType=element.dataset.animate||'fade-in';element.classList.add('animate-initial'),element.dataset.animationType=animationType;const observer=this.observers.get('main');observer&&observer.observe(element)}animateElement(element){if(this.animatedElements.has(element))return;const animationType=element.dataset.animationType||'fade-in',delay=parseInt(element.dataset.animateDelay)||0;this.animatedElements.add(element),setTimeout(()=>{element.classList.remove('animate-initial'),element.classList.add('animate-in');const animationClass=this.animationClasses[animationType];animationClass&&element.classList.add(animationClass),this.app.emit('animation:element-animated',{element,animationType})},delay);const observer=this.observers.get('main');observer&&observer.unobserve(element),this.logger.debug(`Animated element with type: ${animationType}`)}setupScrollAnimations(){const parallaxElements=document.querySelectorAll('[data-parallax]');parallaxElements.length>0&&this.setupParallaxAnimation(parallaxElements),this.setupHeaderScrollEffects()}setupParallaxAnimation(elements){let ticking=!1;const updateParallax=()=>{const scrollY=window.pageYOffset;elements.forEach(element=>{const speed=parseFloat(element.dataset.parallax)||.5,yPos=-(scrollY*speed);element.style.transform=`translateY(${yPos}px)`}),ticking=!1},handleScroll=()=>{ticking||(requestAnimationFrame(updateParallax),ticking=!0)};window.addEventListener('scroll',handleScroll,{passive:!0}),this.logger.debug(`Setup parallax for ${elements.length} elements`)}setupHeaderScrollEffects(){const header=document.querySelector('.header');if(!header)return;let lastScrollY=0,ticking=!1;const updateHeader=()=>{const scrollY=window.pageYOffset;scrollY>50?header.classList.add('scrolled'):header.classList.remove('scrolled'),scrollY>lastScrollY&&scrollY>100?header.classList.add('header-hidden'):header.classList.remove('header-hidden'),lastScrollY=scrollY,ticking=!1},handleScroll=()=>{ticking||(requestAnimationFrame(updateHeader),ticking=!0)};window.addEventListener('scroll',handleScroll,{passive:!0}),this.logger.debug('Header scroll effects setup')}animateStaggered(elements,animationType='fade-in'){elements.forEach((element,index)=>{const delay=index*this.config.staggerDelay;setTimeout(()=>{element.classList.remove('animate-initial'),element.classList.add('animate-in');const animationClass=this.animationClasses[animationType];animationClass&&element.classList.add(animationClass)},delay)}),this.logger.debug(`Animated ${elements.length} elements with stagger`)}addAnimation(name,className){this.animationClasses[name]=className}triggerAnimation(element,animationType='fade-in'){'string'==typeof element&&(element=document.querySelector(element)),element&&this.animateElement(element)}resetAnimation(element){'string'==typeof element&&(element=document.querySelector(element)),element&&(Object.values(this.animationClasses).forEach(className=>{element.classList.remove(className)}),element.classList.remove('animate-in'),element.classList.add('animate-initial'),this.animatedElements.delete(element),this.observers.get('main')?.observe(element))}async setupAnimations(){this.findAnimatableElements(),this.logger.debug('Animation setup completed')}cleanup(){for(const[name,observer]of this.observers)observer.disconnect();this.observers.clear(),this.animatedElements.clear(),this.logger.debug('Animation manager cleaned up')}}class App extends EventEmitter{constructor(config={}){super(),this.config={debug:!1,version:'2.0.0',name:'GolfinThaï',...config},this.logger=new Logger('App'),this.isInitialized=!1,this.managers=new Map,this.handleResize=this.handleResize.bind(this),this.handleVisibilityChange=this.handleVisibilityChange.bind(this)}async init(){if(this.isInitialized)return void this.logger.warn('App already initialized');try{this.logger.info('Initializing application...'),await this.initializeManagers(),this.setupEventListeners(),await this.initializeComponents(),this.isInitialized=!0,this.emit('app:ready'),this.logger.success('Application initialized successfully')}catch(error){throw this.logger.error('Failed to initialize application:',error),error}}async initializeManagers(){const managerConfigs=[{name:'navigation',class:NavigationManager},{name:'mobileMenu',class:MobileMenuManager},{name:'modal',class:ModalManager},{name:'carousel',class:CarouselManager},{name:'language',class:LanguageManager},{name:'weather',class:WeatherManager},{name:'form',class:FormManager},{name:'animation',class:AnimationManager}];for(const{name,class:ManagerClass}of managerConfigs)try{const manager=new ManagerClass(this);this.managers.set(name,manager),'function'==typeof manager.init&&await manager.init(),this.logger.debug(`${name} manager initialized`)}catch(error){this.logger.error(`Failed to initialize ${name} manager:`,error)}}setupEventListeners(){window.addEventListener('resize',this.handleResize),document.addEventListener('visibilitychange',this.handleVisibilityChange),window.addEventListener('hashchange',()=>{this.getManager('navigation')?.handleHashChange()}),window.addEventListener('beforeunload',()=>{this.cleanup()})}async initializeComponents(){'loading'===document.readyState&&await new Promise(resolve=>{document.addEventListener('DOMContentLoaded',resolve)});const initPromises=[this.initializeNavigation(),this.initializeMobileMenu(),this.initializeModals(),this.initializeCarousels(),this.initializeForms(),this.initializeAnimations()];await Promise.allSettled(initPromises)}async initializeNavigation(){const navigation=this.getManager('navigation');navigation&&await navigation.setupNavigation()}async initializeMobileMenu(){const mobileMenu=this.getManager('mobileMenu');mobileMenu&&await mobileMenu.setupMobileMenu()}async initializeModals(){const modal=this.getManager('modal');modal&&await modal.setupModals()}async initializeCarousels(){const carousel=this.getManager('carousel');carousel&&await carousel.setupCarousels()}async initializeForms(){const form=this.getManager('form');form&&await form.setupForms()}async initializeAnimations(){const animation=this.getManager('animation');animation&&await animation.setupAnimations()}getManager(name){return this.managers.get(name)}handleResize(){clearTimeout(this.resizeTimeout),this.resizeTimeout=setTimeout(()=>{this.emit('app:resize',{width:window.innerWidth,height:window.innerHeight})},100)}handleVisibilityChange(){document.hidden?this.emit('app:hidden'):this.emit('app:visible')}cleanup(){window.removeEventListener('resize',this.handleResize),document.removeEventListener('visibilitychange',this.handleVisibilityChange);for(const[name,manager]of this.managers)'function'==typeof manager.cleanup&&(function(){try{manager.cleanup()}catch(error){this.logger.error(`Error cleaning up ${name} manager:`,error)}}(),this.managers.clear(),this.isInitialized=!1,this.logger.info('Application cleaned up'))}getInfo(){return{name:this.config.name,version:this.config.version,initialized:this.isInitialized,managers:Array.from(this.managers.keys())}}}const config={debug:!1,version:'2.0.0',name:'GolfinThaï Modern'};document.addEventListener('DOMContentLoaded',async()=>{try{console.log('🚀 Initializing GolfinThaï Modern Application');const app=new App(config);await app.init(),config.debug&&(window.__GOLFINTHAI_APP__=app,console.log('App instance available at window.__GOLFINTHAI_APP__')),console.log('✅ Application initialized successfully')}catch(error){console.error('❌ Failed to initialize application:',error),function(message){const errorDiv=document.createElement('div');errorDiv.className='error-message',errorDiv.innerHTML=`\n    <div class="error-content">\n      <h3>Erreur</h3>\n      <p>${message}</p>\n      <button onclick="location.reload()">Recharger la page</button>\n    </div>\n  `,document.body.appendChild(errorDiv),setTimeout(()=>{errorDiv.parentNode&&errorDiv.parentNode.removeChild(errorDiv)},1e4)}('Une erreur est survenue lors du chargement de l\'application.')}}),window.addEventListener('error',event=>{console.error('Global error:',event.error)}),window.addEventListener('unhandledrejection',event=>{console.error('Unhandled promise rejection:',event.reason),event.preventDefault()}),'performance'in window&&window.addEventListener('load',()=>{setTimeout(()=>{const perfData=performance.getEntriesByType('navigation')[0];console.log('Performance metrics:',{domContentLoaded:Math.round(perfData.domContentLoadedEventEnd-perfData.domContentLoadedEventStart),loadComplete:Math.round(perfData.loadEventEnd-perfData.loadEventStart),totalTime:Math.round(perfData.loadEventEnd-perfData.fetchStart)})},0)});
