<!-- Navigation Header -->
<header role="banner">
    <nav class="nav-bar" id="main-navigation" role="navigation" aria-label="Navigation principale">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20 lg:h-24">
                <!-- Logo & Brand -->
                <div class="flex items-center space-x-3" itemscope itemtype="https://schema.org/Organization">
                    <div class="w-12 h-12 lg:w-16 lg:h-16 rounded-full overflow-hidden ring-2 ring-luxury-emerald shadow-lg">
                        <img src="./assets/images/logo-golfinthai.jpg" 
                             alt="Logo GolfinThaï - Voyages Golf Thaïlande" 
                             class="w-full h-full object-cover"
                             width="64" height="64"
                             loading="eager"
                             fetchpriority="high"
                             itemprop="logo">
                    </div>
                    <div>
                        <h1 class="font-display text-xl lg:text-2xl font-bold text-luxury-emerald" itemprop="name">
                            GolfinThaï
                        </h1>
                        <p class="text-xs text-luxury-emerald-light hidden sm:block" itemprop="description">
                            Voyages Golfiques Thaïlande
                        </p>
                    </div>
                </div>
                
                <!-- Weather Widget Desktop -->
                <div class="hidden xl:block weather-widget-mini" role="complementary" aria-label="Météo Bangkok">
                    <div id="weather-display" class="flex items-center space-x-2">
                        <i id="weather-icon" class="fas fa-sun text-luxury-emerald weather-icon" aria-hidden="true"></i>
                        <span id="weather-temp" class="weather-temp" aria-label="Température actuelle à Bangkok">--°C</span>
                        <span class="weather-location">Bangkok</span>
                    </div>
                </div>
                
                <!-- Weather Widget Mobile -->
                <div class="block xl:hidden weather-widget-mobile" role="complementary" aria-label="Météo Bangkok">
                    <div id="weather-display-mobile" class="flex flex-col items-center justify-center">
                        <div class="flex items-center space-x-1">
                            <i id="weather-icon-mobile" class="fas fa-sun text-luxury-emerald weather-icon text-sm" aria-hidden="true"></i>
                            <span id="weather-temp-mobile" class="weather-temp-mobile" aria-label="Température actuelle à Bangkok">--°</span>
                        </div>
                        <span class="weather-location-mobile">BKK</span>
                    </div>
                </div>
                
                <!-- Desktop Menu -->
                <nav class="hidden lg:flex items-center space-x-6" role="navigation" aria-label="Menu principal">
                    <a href="#home" class="nav-link" aria-label="Aller à la section Accueil">
                        <span data-translate="nav.home">Accueil</span>
                    </a>
                    <a href="#destinations" class="nav-link" aria-label="Aller à la section Destinations">
                        <span data-translate="nav.destinations">Destinations</span>
                    </a>
                    <a href="#apropos" class="nav-link" aria-label="Aller à la section À Propos">
                        <span data-translate="nav.about">À Propos</span>
                    </a>
                    <a href="#temoignages" class="nav-link" aria-label="Aller à la section Témoignages">
                        <span data-translate="nav.testimonials">Témoignages</span>
                    </a>
                    <a href="#contact" class="btn-primary" aria-label="Nous contacter">
                        <span data-translate="nav.contact">Contactez-Nous</span>
                    </a>
                    
                    <!-- Language Switcher Desktop -->
                    <div class="language-switcher-desktop">
                        <button id="lang-toggle-desktop" class="lang-btn" aria-label="Changer de langue" aria-expanded="false">
                            <span class="flag-icon">🇫🇷</span>
                            <span class="lang-text" data-translate="lang.current">FR</span>
                            <i class="fas fa-chevron-down lang-arrow"></i>
                        </button>
                        <div class="lang-dropdown" id="lang-dropdown-desktop" style="display: none;">
                            <button class="lang-option" data-lang="fr" aria-label="Français">
                                <span class="flag-icon">🇫🇷</span>
                                <span>Français</span>
                            </button>
                            <button class="lang-option" data-lang="en" aria-label="English">
                                <span class="flag-icon">🇬🇧</span>
                                <span>English</span>
                            </button>
                        </div>
                    </div>
                </nav>
                
                <!-- Mobile Controls -->
                <div class="lg:hidden flex items-center space-x-3">
                    <!-- Language Switcher Mobile -->
                    <div class="language-switcher-mobile">
                        <button id="lang-toggle-mobile" class="lang-btn-mobile" aria-label="Changer de langue" aria-expanded="false">
                            <span class="flag-icon">🇫🇷</span>
                            <span class="lang-text-mobile" data-translate="lang.current">FR</span>
                        </button>
                        <div class="lang-dropdown-mobile" id="lang-dropdown-mobile" style="display: none;">
                            <button class="lang-option-mobile" data-lang="fr" aria-label="Français">
                                <span class="flag-icon">🇫🇷</span>
                                <span>FR</span>
                            </button>
                            <button class="lang-option-mobile" data-lang="en" aria-label="English">
                                <span class="flag-icon">🇬🇧</span>
                                <span>EN</span>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Mobile Menu Button -->
                    <button class="text-luxury-emerald text-2xl p-2" 
                            id="mobile-menu-btn" 
                            aria-label="Ouvrir le menu mobile"
                            aria-expanded="false"
                            aria-controls="mobile-menu">
                        <i class="fas fa-bars" aria-hidden="true"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Mobile Menu Overlay -->
    <nav class="mobile-menu-overlay" 
         id="mobile-menu" 
         role="navigation" 
         aria-label="Menu mobile"
         aria-hidden="true">
        <!-- Bouton fermer en haut à droite -->
        <button class="mobile-menu-close" 
                id="close-menu"
                aria-label="Fermer le menu mobile">
            <i class="fas fa-times" aria-hidden="true"></i>
        </button>
        
        <!-- Contenu du menu centré -->
        <div class="flex flex-col items-center justify-center h-full space-y-8">
            <a href="#home" class="mobile-menu-link">Accueil</a>
            <a href="#destinations" class="mobile-menu-link">Destinations</a>
            <a href="#apropos" class="mobile-menu-link">À Propos</a>
            <a href="#temoignages" class="mobile-menu-link">Témoignages</a>
            <a href="#contact" class="mobile-menu-link">Contact</a>
        </div>
    </nav>
</header>
