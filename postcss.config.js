module.exports = {
  plugins: {
    // Autoprefixer for vendor prefixes
    autoprefixer: {
      overrideBrowserslist: [
        '> 1%',
        'last 2 versions',
        'not dead',
        'not ie 11'
      ]
    },
    
    // CSS optimization for production
    ...(process.env.NODE_ENV === 'production' ? {
      cssnano: {
        preset: ['default', {
          // Preserve important comments
          discardComments: {
            removeAll: false
          },
          // Normalize whitespace
          normalizeWhitespace: true,
          // Merge rules
          mergeRules: true,
          // Optimize font weights
          minifyFontValues: true,
          // Optimize gradients
          minifyGradients: true,
          // Remove unused at-rules
          discardUnused: true,
          // Optimize calc() expressions
          calc: true,
          // Convert colors to shorter formats
          colormin: true,
          // Optimize SVG
          svgo: {
            plugins: [
              {
                name: 'preset-default',
                params: {
                  overrides: {
                    removeViewBox: false
                  }
                }
              }
            ]
          }
        }]
      }
    } : {})
  }
};
